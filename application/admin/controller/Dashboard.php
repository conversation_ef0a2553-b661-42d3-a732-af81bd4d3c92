<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Config;
use think\Db;

/**
 * 控制台
 *
 * @icon fa fa-dashboard
 * @remark 用于展示当前系统中的统计数据、统计报表及重要实时数据
 */
class Dashboard extends Backend
{

    /**
     * 查看
     */
    public function index()
    {
		/* 获取会员总数 */
		$user_count = Db::name('user')->count();
		/* 获取订单总数 */
		$order_count = Db::name('df_order')->where('order_status','1')->count();
		/* 获取今日会员注册 */
		$day_user_count = Db::name('user')->whereTime('jointime','>','today')->count();
		$user_login = Db::name('user_login_log')->whereTime('createtime','>','today')->group('user_id')->count();
		/* 获取今日订单总数 */
		$day_order_count = Db::name('df_order')->whereTime('createtime','>','today')->count();
		/* 获取未处理订单 */
		$untreated_map = array(
			'order_status' => '0',
			'pay_type' => '2',
		);
		$untreated_count = Db::name('df_order')->where($untreated_map)->count();
		/* 用户申诉 */
		$appeal_count = Db::name('appeal')->where('is_read','1')->count();
		/* 获取部分退款订单 */
		$part_count = Db::name('aliapy_manual_refund')->where('status','0')->count();
		/* 获取今日成交额 */
		$deal_count = Db::name('count_order')->where('daytime',date('Ymd',time()))->field('sum(all_tb_money) as all_tb_money,sum(all_rmb_money) as all_rmb_money')->find();
		$yesterday = date("Ymd",strtotime("-1 day"));
		$yester_count = Db::name('count_order')->where('daytime',$yesterday)->field('sum(all_tb_money) as all_tb_money,sum(all_rmb_money) as all_rmb_money')->find();
		$all_count = Db::name('count_order')->field('sum(all_tb_money) as all_tb_money,sum(all_rmb_money) as all_rmb_money')->find();
		/* 获取转人工订单 */
		$artificial_order = Db::name('df_order')
					->where('order_status','2')
					//->where('pay_type','2')
					->order('createtime','desc')
					->limit('5')
					->select();
		/* 获取暫停订单 */
		$suspend_order = Db::name('df_order')
					->where('order_status','3')
					->order('createtime','desc')
					->limit('5')
					->select();
		/* 获取多支付订单 */
		$pay_order = Db::name('df_order')
					->order('createtime','desc')
					->where('pay_type','3')
					->where('order_status','0')
					->limit(5)
					->whereTime('createtime','>',date('Y-m-d',time()))
					->select();
		/* 身份证一次认证 */
		$card = Db::view('user_card','*')
					->view('user',['username','mobile'],'user.id=user_card.user_id')
					->order('createtime','desc')
					->where('is_state','4')
					->limit(5)
					->select();
		/* 身份证二次认证 */
		 $card_two = Db::view('user_card_two','*')
					->view('user',['username','mobile'],'user.id=user_card_two.user_id')
					->order('createtime','desc')
					->where('is_state','3')
					->limit(5)
					->select(); 
			/*$card_two = Db::view('user_bank','*')
					->view('user',['username','mobile'],'user.id=user_bank.user_id')
					->order('createtime','desc')
					->where('is_state','3')
					->limit(5)
					->select();*/
		/*集运委托后*/
		$jiyun_weituo = Db::name('express')
					->order('createtime','desc')
					->where('type','0')
					->limit(5)
					->select();
		/*集运付款后*/
		$jiyun_order = Db::name('jy_order')
					->order('createtime','desc')
					->where('order_status','3')
					->limit(5)
					->select();
		/* 待认证超商 */
		$super_quotient = Db::view('super_quotient','*')
					->view('user',['username','mobile'],'user.id=super_quotient.user_id')
					->order('createtime','desc')
					->where('type','0')
					->limit(5)
					->select();
		/* usdt待处理 */
		$usdt = Db::view('virtual_order','*')
					->order('createtime','desc')
					->where('pay_type','2')
					->where('order_status','2')
					->limit(5)
					->select();
		/* 回收发票待审核 */
		$invoice = Db::name('recovery_invoice','*')
					->order('createtime','desc')
					->where('invoice_status','1')
					->limit(5)
					->select();
		/* 中间表格 */
        $seventtime = \fast\Date::unixtime('day', -7);
		$all_order = Db::name('df_order')
					->order('createtime','desc')
					->whereTime('createtime','>',date('Y-m-d',$seventtime))
					->select();
		/*获取新业务待处理订单*/
		$order_live = Db::name('order_live','*')
					->order('createtime','desc')
					->where('order_status','in',[0,2])
					->where('pay_type','>=','2')
					->limit(5)
					->select();
		$order_game = Db::name('order_game','*')
					->order('createtime','desc')
					->where('order_status','in',[0,2])
					->where('pay_type','>=','2')
					->limit(5)
					->select();
		$order_other = Db::name('order_other','*')
					->order('createtime','desc')
					->where('order_status','in',[0,2])
					->where('pay_type','>=','2')
					->limit(5)
					->select();
		$order_new_df = Db::name('order_new_df','*')
					->order('createtime','desc')
					->where('order_status','in',[0,2])
					->where('pay_type','>=','2')
					->limit(5)
					->select();

		$reg_df_count = Db::name('user')->alias('u')
		->join('df_order df', 'u.id=df.user_id', 'LEFT')
		->field('u.username, u.mobile, df.*')
		->where('df.order_status', 1)
		->whereTime('u.createtime', 'yesterday')
		->count();
	

		$createlist = $paylist = [];
		foreach($all_order as $ls){
			$_day = date("Y-m-d", $ls['createtime']);
			$createlist[$_day] = empty($createlist[$_day])?'0':$createlist[$_day];
			$paylist[$_day] = empty($paylist[$_day])?'0':$paylist[$_day];
			if($ls['order_status'] == '1'){
				$paylist[$_day] += 1;
			}
			$createlist[$_day] += 1;
		}
		
        $day_array = $_paylist = $_createlist = [];
        for ($i = 0; $i < 7; $i++)
        {
            $day = date("Y-m-d", $seventtime + ($i * 86400));
            $_createlist[] = empty($createlist[$day])?'0':$createlist[$day];
            $_paylist[] = empty($paylist[$day])?'0':$paylist[$day];
			$day_array[] = $day;
        }
        $hooks = config('addons.hooks');
        $uploadmode = isset($hooks['upload_config_init']) && $hooks['upload_config_init'] ? implode(',', $hooks['upload_config_init']) : 'local';
        $addonComposerCfg = ROOT_PATH . '/vendor/karsonzhang/fastadmin-addons/composer.json';
		Config::parse($addonComposerCfg, "json", "composer");
        $config = Config::get("composer");
        $addonVersion = isset($config['version']) ? $config['version'] : __('Unknown');
        $this->view->assign([
            'totalviews'       => 219390,
            'totalorderamount' => 174800,
            'user_count'          => $user_count,
            'user_login'          => $user_login,
            'order_count'       => $order_count,
            'all_count'       => $all_count,
            'day_user_count'       => $day_user_count,
            'day_order_count'       => $day_order_count,
            'untreated_count'       => $untreated_count,
            'part_count'       => $part_count,
            'deal_count'       => $deal_count,
            'yester_count'       => $yester_count,
            'artificial_order'       => $artificial_order,
            'suspend_order'       => $suspend_order,
            'pay_order'       => $pay_order,
            'appeal_count'       => $appeal_count,
            'pay_type'       => Config::get('site.pay_type'),
            'uploadmode'       => $uploadmode,
            'invoice'       => $invoice,
            'order_live'       => $order_live,
            'order_game'       => $order_game,
            'order_other'       => $order_other,
            'order_new_df'       => $order_new_df,
            'jiyun_order'       => $jiyun_order,
            'jiyun_weituo'       => $jiyun_weituo,
            // 'paylist'       => $paylist,
            // 'createlist'       => $createlist,
            'card'       => $card,
            'card_two'       => $card_two,
            'super_quotient'       => $super_quotient,
            'usdt'       => $usdt,
            'createlist'       => json_encode($_createlist),
            'paylist'       => json_encode($_paylist),
            'day_array'       => json_encode($day_array),
			'reg_df_count'	=>	$reg_df_count,
        ]);
		/* 中间表格 */
        return $this->view->fetch();
    }



	public function yreg_order(){

		$map = [];
		$data = $this->request->param();

		if( empty($data['num']) ){
			$data['num'] = 20;
		}

		// // 订单状态
		if( !empty($data['order_status']) ){
			$map = ['df.order_status' =>$data['order_status']];
		}else{
			$data['order_status'] = '';
		}

		// 订单创建时间
		if( !empty($data['cr_time']) ){
			$time = $data['cr_time'];
			$map['df.createtime'] = array('between', array(strtotime($time . ' 00:00:00'),strtotime($time . ' 23:59:59')));
		}else{
			$data['cr_time'] = '';
		}

		// 订单完成时间
		if( !empty($data['cp_time']) ){
			$time = $data['cp_time'];
			$map['df.completetime'] = array('between', array(strtotime($time . ' 00:00:00'),strtotime($time . ' 23:59:59')));
		}else{
			$data['cp_time'] = '';
		}

		// // 用户注册时间
		if( empty($data['rg_time']) ){
			$data['rg_time'] = '';
			$map['u.createtime'] = array('between', array(strtotime('yesterday 00:00:00'),strtotime('yesterday 23:59:59')));
		}else{
			$time = $data['rg_time'];
			$map['u.createtime'] = array('between', array(strtotime($time . ' 00:00:00'),strtotime($time . ' 23:59:59')));
		}


		$reg_df_rows = Db::name('user')->alias('u')
		->join('df_order df', 'u.id=df.user_id', 'LEFT')
		->field('u.id uid, u.username, u.mobile, u.createtime rg_time, u.source, df.order_no, df.order_status, df.tb_money, df.createtime cr_time, df.completetime cp_time')
		->where($map)
		->paginate($data['num'], false, ['query' =>['cr_time' =>$data['cr_time'],
											'cp_time' =>$data['cp_time'],
											'rg_time' =>$data['rg_time'],
											'order_status' =>$data['order_status'],
											'num' 	=>$data['num']
										]]);	
										
		// $sql = Db::name('user')->getlastsql();
		// return $sql;

		if( strlen($data['order_status']) ){
			$this->view->assign('order_status', $data['order_status']);
		}
		if( strlen($data['cr_time']) ){
			$this->view->assign('cr_time', $data['cr_time']);
		}
		if( strlen($data['cp_time']) ){
			$this->view->assign('cp_time', $data['cp_time']);
		}
		if( strlen($data['rg_time']) ){
			$this->view->assign('rg_time', $data['rg_time']);
		}


		$status_arr = Config::get('site.order_status');
		$searchlist = [];
        foreach ($status_arr as $key => $value) {
			$searchlist[] = ['id' => $key, 'name' => $value];
        }

		$people_num = 0;
		$order_num = 0;
		$order_money = 0;
		if(count($reg_df_rows) > 0){
			$df_Array = $reg_df_rows->toArray();
			$df_data = $df_Array['data'];
			$id_list = array_column($df_data, 'uid');
			$id_arr = array_unique($id_list);
			$id_arr =  array_values($id_arr);

			$people_num = count($id_arr);
			$order_num = count($df_data);

			$money_list = array_column($df_data, 'tb_money');
			$order_money = array_sum($money_list);
		}

		// $list = [];
		// if(count($reg_df_rows) > 0){
		// 	$df_Array = $reg_df_rows->toArray();
		// 	$df_data = $df_Array['data'];
		// 	$id_list = array_column($df_data, 'uid');
			
		// 	$id_arr = array_unique($id_list);
		// 	$id_arr =  array_values($id_arr);
			
		// 	for($i=0;$i<count($id_arr);$i++){
		// 		$id = $id_arr[$i];
		// 		$items = array_filter($df_data,  function($row) use($id){
		// 			return $row['uid'] == $id;
		// 		});
		// 		$items = array_values($items);
		// 		$number = count($items);
		// 		$money_list = array_column($items, 'tb_money');
		// 		$total = array_sum($money_list);
		// 		$list[] = array('id'=>$id, 'name'=>$items[0]['username'], 'mobile'=>$items[0]['mobile'], 'number'=>$number, 'total'=>$total
		// 					);
		// 	}
		// }

		$this->view->assign('people_num', $people_num);
		$this->view->assign('order_num', $order_num);
		$this->view->assign('order_money', $order_money);


		$this->view->assign('num', $data['num']);
		$this->view->assign('num_list', array(20, 50, 100, 200));
		$this->view->assign('status_list', $searchlist);
        $this->view->assign('rows', $reg_df_rows);
		// $this->view->assign('list', $list);
        return $this->view->fetch('detail');
	}


}
