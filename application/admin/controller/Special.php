<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;

/**
 * 服务类型
 *
 * @icon fa fa-circle-o
 */
class Special extends Backend
{
    
    /**
     * Special模型对象
     * @var \app\admin\model\Special
     */
    protected $model = null;
	protected $multiFields = 'is_maintain';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Special;
		$this->view->assign("maintainList", $this->model->getMaintainList());
		$this->view->assign("statusList", $this->model->getStatusList());
		/*获取父级*/
		$categroy = Db::name('special')->where('pid','0')->select();
		foreach($categroy as $ls){
			$this->categroy[$ls['id']] = $ls;
			$this->pid_array[$ls['id']] = $ls['name'];
		}

    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
     /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                ->where($where)
                ->where('pid','>','0')
                ->order($sort, $order)
                ->count();

            $list = $this->model
                ->where($where)
				->where('pid','>','0')
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();
			$pid_select = $this->categroy;
			foreach ($list as $key=>$row) {
				$list[$key]['pid_id'] = $row['pid'];
				$list[$key]['pid'] = $pid_select[$row['pid']]['name'];
            }
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
		$this->view->assign('pid_array',$this->pid_array);
        return $this->view->fetch();
    }
	/**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
		$this->view->assign('pid_array',$this->pid_array);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }
                    $result = $row->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
		$this->view->assign('pid_array',$this->pid_array);
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }
	/*字段配置*/
	public function set_one($ids = null){
		$row = $this->model->get($ids);
		$info = Db::name('set_special_one')->where('key_id',$row['key_id'])->find();
		if($this->request->isPost()){
			$_data = $this->request->post();
			$data = array(
				'key_id' => $_data['key_id'],
				'admin_id' => $this->auth->id,
				'admin_name' => $this->auth->username,
				'createtime' => time(),
				'updatetime' => time(),
			);
			if(!empty($_data['set_id'])){
			    $data['set_id'] = json_encode($_data['set_id']);
			}else{
			    $data['set_id'] = '';
			}
			if($info){
				$result = Db::name('set_special_one')->where('key_id',$_data['key_id'])->update($data);
			}else{
				$result = Db::name('set_special_one')->insert($data);
			}
			Db::name('special')->where('id',$ids)->update(['type'=>'1']);
			if($result){
				$this->success(__('Operation completed'));
			}else{
				$this->error(__('Operation failed'));
			}
		}
        if (!$row) {
            $this->error(__('No Results were found'));
        }
		$this->view->assign("row", $row);
		$this->view->assign("info", $info);
		return $this->view->fetch('set_one');
	}
	/*联动配置*/
	public function set_two($ids = null){
		$row = $this->model->get($ids);
		$info = Db::name('set_special_two')->where('key_id',$row['key_id'])->find();
		if ($this->request->isPost()) {
			$_data = $this->request->post();
			$data = array(
				'key_id' => $_data['key_id'],
				'set_id' => $_data['set_id'],
				'admin_id' => $this->auth->id,
				'admin_name' => $this->auth->username,
				'createtime' => time(),
				'updatetime' => time(),
			);
			if($info){
				$result = Db::name('set_special_two')->where('key_id',$_data['key_id'])->update($data);
			}else{
				$result = Db::name('set_special_two')->insert($data);
			}
			Db::name('special')->where('id',$ids)->update(['type'=>'1']);
			if($result){
				$this->success(__('Operation completed'));
			}else{
				$this->error(__('Operation failed'));
			}
		}
		$this->view->assign("row", $row);
		$this->view->assign("info", $info);
		return $this->view->fetch('set_two');
	}
	/*父级id*/
	public function category(){
		$result = $this->pid_array;
		$searchlist = [];
        foreach ($result as $key => $value) {
            $searchlist[] = ['id' => $key, 'name' => $value];
        }
		$data = ['searchlist' => $searchlist];
        $this->success('', null, $data);
	}
}
