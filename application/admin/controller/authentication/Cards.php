<?php

namespace app\admin\controller\authentication;
use app\common\model\User as Users;
use think\Db;
use think\Config;
use app\common\controller\Backend;

/**
 * 会员身份证一次认证
 *
 * @icon fa fa-circle-o
 */
class Cards extends Backend
{
    
    /**
     * Cards模型对象
     * @var \app\admin\model\authentication\Cards
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\authentication\Cards;
        $this->view->assign("typeList", $this->model->getTypeList());
        $this->view->assign("isStateList", $this->model->getIsStateList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax())
        {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField'))
            {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                    ->with(['admin','user'])
                    ->where($where)
                    ->order($sort, $order)
                    ->count();

            $list = $this->model
                    ->with(['admin','user'])
                    ->where($where)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();

            foreach ($list as $row) {
                
                $row->getRelation('admin')->visible(['username','nickname']);
                $row->getRelation('user')->visible(['username','mobile']);
            }
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        $is_state2=request()->param('is_state')!=NULL?request()->param('is_state'):''; //获取url 里的参数
        $this->assignconfig("is_state",$is_state2); //在这里透传给js
        return $this->view->fetch();
    }
	/**
     * 审核
     */
    public function examine($ids)
    {
		if($this->request->isPost()){
			$id = $this->request->post('id');
			$info = $this->model->get($ids);
			$is_state = $this->request->post('is_state');
			$check_text = $this->request->post('check_text');
			if($is_state == '2'){
				if(!$check_text){
					$this->error(__('Reason for audit failure must be filled in'));
				}
			}
			$data = array(
				'check_text' => $check_text,
				'check_time' => time(),
				'admin_id' => $this->auth->id,
			);
			if($is_state){
				$data['is_state'] = $is_state;
			}
			if($is_state == '1'){
				Db::name('user')->where('id',$info['user_id'])->update(['is_card'=>'1']);
				/* 获取用户信息 */
				$user = Db::name('user')->where('id',$info['user_id'])->find();
				$pid = Users::recommender_ids($user['pid']);
				if($pid){
                    /*赠送购物金*/
					Users::give_gold($pid,Config::get('site.give3'),$info['user_id']);
                    if(Config::get('site.coupon_open')){
                        /*赠送优惠卷*/
                        $map = [
                            'rule_type' => 1,
                            'is_check' => 1,
                        ];
                        $use_rule = Db::name('use_rule')->where($map)->find();
                        $this->give_coupon($user['pid'],$use_rule['coupon_id']);
                    }
				}
				Users::give_gold($user,Config::get('site.give1'),$user['pid']);
			}
			if(Db::name('user_card')->where('id',$id)->update($data)){
				 $this->success(__('Operation completed'));
			}else{
				$this->error(__('Operation failed'));
			}
		}
        $row = $this->model->get(['id' => $ids]);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
		if($row['is_state'] == '1'){
			$this->error(__('The ID card has been authenticated successfully, no need to re authenticate'));
		}
        if ($this->request->isAjax()) {
            $this->success("Ajax请求成功", null, ['id' => $ids]);
        }
        $this->view->assign("row", $row->toArray());
        return $this->view->fetch();
    }
}
