<?php

namespace app\admin\controller\authentication;
use think\Db;
use app\common\controller\Backend;

/**
 * 超商申请管理
 *
 * @icon fa fa-circle-o
 */
class Chao extends Backend
{
    
    /**
     * Chao模型对象
     * @var \app\admin\model\authentication\Chao
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\authentication\Chao;
        $this->view->assign("statusList", $this->model->getStatusList());
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax())
        {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField'))
            {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                    ->with(['user'])
                    ->where($where)
                    ->order($sort, $order)
                    ->count();

            $list = $this->model
                    ->with(['user'])
                    ->where($where)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();

            foreach ($list as $row) {
                
                $row->getRelation('user')->visible(['username','mobile']);
            }
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        $type2=request()->param('type')!=NULL?request()->param('type'):''; //获取url 里的参数
        $this->assignconfig("type",$type2); //在这里透传给js
        return $this->view->fetch();
    }
	/**
     * 审核
     */
    public function examine($ids)
    {
		if($this->request->isPost()){
			$id = $this->request->post('id');
			$type = $this->request->post('type');
			$check_text = $this->request->post('check_text');
			$user_id = $this->request->post('user_id');
			if($type == '2'){
				if(!$check_text){
					$this->error(__('Reason for audit failure must be filled in'));
				}
			}
			$data = array(
				'check_text' => $check_text,
				'check_time' => time(),
				'admin_id' => $this->auth->id,
			);
			if($type){
				$data['type'] = $type;
			}
			Db::startTrans();
			if($type == '1'){
				Db::name('user')->where('id',$user_id)->update(['is_super_quotien'=>'1']);
			}
			if($type == '2'){
				Db::name('user')->where('id',$user_id)->update(['is_super_quotien'=>'0']);
			}
			if(Db::name('super_quotient')->where('id',$id)->update($data)){
				Db::commit();
				$this->success(__('Operation completed'));
			}else{
				Db::rollback();
				$this->error(__('Operation failed'));
			}
		}
        $row = $this->model->get(['id' => $ids]);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isAjax()) {
            $this->success("Ajax请求成功", null, ['id' => $ids]);
        }
        $this->view->assign("row", $row->toArray());
        return $this->view->fetch();
    }
}
