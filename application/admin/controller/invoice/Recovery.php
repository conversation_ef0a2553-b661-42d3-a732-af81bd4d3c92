<?php

namespace app\admin\controller\invoice;
use think\Config;
use think\Db;
use app\common\controller\Backend;

/**
 * 回收发票列管理
 *
 * @icon fa fa-circle-o
 */
class Recovery extends Backend
{
    
    /**
     * Recovery模型对象
     * @var \app\admin\model\invoice\Recovery
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\invoice\Recovery;
        $this->view->assign("statusList", $this->model->getStatusList());
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax())
        {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField'))
            {
                return $this->selectpage();
            }
			$invoice_status = request()->param('invoice_status');
			$map = array();
			if(strlen($invoice_status)){
				$map['invoice_status'] = $invoice_status;
			}
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                    ->with(['invoicecompany'])
                    ->where($where)
					->where($map)
                    ->order($sort, $order)
                    ->count();

            $list = $this->model
                    ->with(['invoicecompany'])
                    ->where($where)
					->where($map)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();
			$configuration = Config::get('site');
			$type_title = $configuration['invoice_status'];
            foreach ($list as $key=>$row) {
                $list[$key]['invoice_status'] = $type_title[$row['invoice_status']];
                $row->getRelation('invoicecompany')->visible(['title']);
            }
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }
	/*审核发票*/
	public function examine($ids){
		if($this->request->isPost()){
			$id = $this->request->post('id');
			$is_state = $this->request->post('is_state');
			$check_text = $this->request->post('check_text');
			$invoice_status = '2';
			if($is_state == '2'){
				$invoice_status == '0';
				if(!$check_text){
					$this->error('审核失败必须填写原因');
				}
			}
			$data = array(
				'invoice_status' => $invoice_status,
				'invoice_content' => $check_text,
				'admin_log' => $this->auth->username .date('y-m-d H:i:s').'进行审核操作',
			);
			if(Db::name('recovery_invoice')->where('id',$id)->update($data)){
				$this->success(__('Operation completed'));
			}else{
				$this->error(__('Operation failed'));
			}
		}
        $row = $this->model->get(['id' => $ids]);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isAjax()) {
            $this->success("Ajax请求成功", null, ['id' => $ids]);
        }
        $this->view->assign("row", $row->toArray());
        return $this->view->fetch();
	}
	/*返利*/
	public function rebate($ids){
		if($this->request->isPost()){
			/* 开启事务 */
			Db::startTrans();
			try {
				$rebate = $this->request->post('rebate');
				$row = $this->model->get(['id' => $ids]);
				if($row['invoice_status'] != '2'){
					$this->error('只有审核成功的才能返利');
				}
				/* 获取订单 */
				$order = Db::name('df_order')->where('id',$row['id'])->find();
				/*获取用户信息*/
				$user = Db::name('user')->where('id',$row['order_id'])->find();
				$this->taobiLog('14',$rebate,'0','',$user);
				$data = array(
					'rebate_money' => $rebate,
					'invoice_status' => '4',
					'rebate_time' => time(),
					'admin_log' => $this->auth->username .date('y-m-d H:i:s').'进行返利操作',
				);
				$result = Db::name('recovery_invoice')->where('id',$ids)->update($data);
				if($result){
					Db::commit();
					$this->success(__('Operation completed'),url('invoice/recovery/index'));
				}else{
					Db::rollback();
					$this->error(__('Operation failed'));
				}
			}catch (Exception $e) {
				$this->error(__('Network exception, please refresh and try again'));
				Db::rollback();
			}
		}
        $row = $this->model->get(['id' => $ids]);
		/*获取公司返利利率*/
		$info = Db::name('invoice_company')->where('id',$row['company_id'])->find();
		$rebate = $row['money']*$info['rebate']/100;
		/**进一*/
		$rebate = sprintf("%.2f", ceil($rebate*100)/100);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isAjax()) {
            $this->success("Ajax请求成功", null, ['id' => $ids]);
        }
		$this->view->assign('rebate',$rebate);
		$this->view->assign('info',$info);
		$this->view->assign("row", $row->toArray());
        return $this->view->fetch();
	}
    /* 发票状态 */
	public function invoice_status(){
		$result = Config::get('site.invoice_status');
		$searchlist = [];
        foreach ($result as $key => $value) {
            $searchlist[] = ['id' => $key, 'name' => $value];
        }
		$data = ['searchlist' => $searchlist];
        $this->success('', null, $data);
	}
}
