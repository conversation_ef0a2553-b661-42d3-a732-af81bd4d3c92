<?php

namespace app\admin\controller\jiyun;

use app\common\controller\Backend;
use think\Config;
use think\Db;

/**
 * 集运
 *
 * @icon fa fa-circle-o
 */
class Index extends Backend
{
    
    /**
     * Index模型对象
     * @var \app\admin\model\jiyun\Index
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\jiyun\Index;
        $this->view->assign("statusList", $this->model->getStatusList());
        $this->view->assign("typeList", $this->model->getTypeList());
        $this->view->assign("saleList", $this->model->getSaleList());
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax())
        {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField'))
            {
                return $this->selectpage();
            }
			$type = request()->param('type');
			$map = array();
			if(strlen($type)){
				$map['type'] = $type;
			}
			$configuration = Config::get('site');
			$jiyun = $configuration['jiyun'];
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                    ->with(['user'])
                    ->where($where)
					->where($map)
                    ->order($sort, $order)
                    ->count();

            $list = $this->model
                    ->with(['user'])
                    ->where($where)
					->where($map)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();
            foreach ($list as $key=>$row) {
                $list[$key]['type_id'] = $row['type'];
				$list[$key]['type'] = $jiyun[$row['type']];
                $row->getRelation('user')->visible(['username','mobile']);
            }
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }
	/*详情*/
	public function details($ids){
		$info = Db::name('express')->where('id',$ids)->find();
		/*获取会员详情*/
		$user = Db::name('user')->where('id',$info['user_id'])->find();
		$y = date('Y',$info['createtime']);
		if($y < '2022'){
			$table = 'jy_order_log';
		}else{
			$table = 'jy_order_log_'.$y;
		}
		$express_log = Db::name($table)->field('*')->where('p_order_id', $ids)->select();
		$this->view->assign("info", $info);
		$this->view->assign("user", $user);
		$this->view->assign("express_log", $express_log);
		return $this->view->fetch();
	}
	/*取消*/
	public function cancel($ids){
		if(Db::name('express')->where('id',$ids)->update(['type'=>'5'])){
			$this->success('操作成功');
		}else{
			$this->error('操作失败');
		}
	}
	/*快递查看*/
	public function kuaidi($ids){
		$info = Db::name('express')->where('id',$ids)->find();
		$kuaidi = $this->get_kuaidi($info);
		//if($kuaidi['returnCode'] == '401'){
		//	$this->error($kuaidi['message']);
		//}
		$this->view->assign('info',$info);
		$this->view->assign('kuaidi',$kuaidi);
		return $this->view->fetch();
	}
	/*快递查询*/
	public function get_kuaidi($_data){
		//$key = 'nQaokPvZ8324';                        //客户授权key
		//$customer = 'B5194F1DACB809C5EEC9A7F4F48469A7';                   //查询公司编号
		$key = 'SPPgHIwt2937';                        //客户授权key
		$customer = 'BFE3271FF1FC5FD40DF892C6B679C089';                   //查询公司编号
		$param = array (
			'com' => $_data['waybill_name'],             //快递公司编码
			'num' => $_data['waybill'],     //快递单号
		);
		//请求参数
		$post_data = array();
		$post_data["customer"] = $customer;
		$post_data["param"] = json_encode($param);
		$sign = md5($post_data["param"].$key.$post_data["customer"]);
		$post_data["sign"] = strtoupper($sign);
		$url = 'http://poll.kuaidi100.com/poll/query.do';    //实时查询请求地址
		$params = "";
		foreach ($post_data as $k=>$v) {
			$params .= "$k=".urlencode($v)."&";              //默认UTF-8编码格式
		}
		$post_data = substr($params, 0, -1);
		//发送post请求
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		$result = curl_exec($ch);
		$data = str_replace("\"", '"', $result );
		$data = json_decode($data,true);
		return $data;
	}
	/*称重*/
	public function weighs($ids){
		$info = Db::name('express')->where('id',$ids)->find();
		if($this->request->isPost()){
			$scale = $this->request->post('scale');
			$price = $this->request->post('price');
			$dispatch = $this->request->post('dispatch');
			$beyond = $this->request->post('beyond');
			$is_sale = $this->request->post('is_sale');
			$is_type = $this->request->post('is_type');
			$data = array(
				'type' => '1',
				'scale' => $scale,
				'price' => $price,
				'beyond' => $beyond,
				'dispatch' => $dispatch,
				'is_sale' => $is_sale,
				'is_type' => $is_type,
				'arrivetime' => time(),
			);
			if(Db::name('express')->where('id',$ids)->update($data)){
				$user = Db::name('user')->where('id',$info['user_id'])->find();
				$msg_txt = '您的運單'.$info['waybill'].'已到集運倉，請及時出貨！';
				$res = $this->sendSMS($user['mobile'],$msg_txt);
				$remarks = $this->auth->username .'进行称重';
				$this->orderLog($remarks,$ids,'jy_order','1');
				$this->success('操作成功');
			}else{
				$this->error('操作失败');
			}
		}
		$row = $this->model->get($ids);
		$this->view->assign('row',$row);
		return $this->view->fetch();
	}
	/*更改状态*/
	public function change_status($ids){
		if($this->request->isPost()){
			$type = $this->request->post('type');
			if(Db::name('express')->where('id',$ids)->update(array('type'=>$type))){
				$this->success('操作成功');
			}else{
				$this->error('操作失败');
			}
		}
		$status = Config::get('site.jiyun');
		$this->view->assign('status',$status);
		return $this->view->fetch();
	}
	/* 订单状态 */
	public function typeStatus(){
		$result = Config::get('site.jiyun');
		$searchlist = [];
        foreach ($result as $key => $value) {
            $searchlist[] = ['id' => $key, 'name' => $value];
        }
		$data = ['searchlist' => $searchlist];
        $this->success('', null, $data);
	}
}
