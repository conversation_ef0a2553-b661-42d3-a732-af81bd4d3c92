<?php

namespace app\admin\controller\marketing;

use app\common\controller\Backend;
use think\Db;

/**
 *  优惠卷规则
 *
 * @icon fa fa-circle-o
 */
class Userule extends Backend
{
    
    /**
     * Userule模型对象
     * @var \app\admin\model\marketing\Userule
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\marketing\Userule;
        $this->view->assign("statusList", $this->model->getStatusList());
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    /**
     * 查看
     * 备注：此处id在用户注册时引用，改代码时请注意
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            $coupon = Db::name('coupon')->select();
            foreach($coupon as $k=>$v){
                $coupon[$v['id']] = $v['title'].'-'.$v['use_num'].'天';
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                ->where($where)
                ->order($sort, $order)
                ->count();

            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();
            foreach ($list as $key=>$row) {
                $coupon_id = explode(',',$row['coupon_id']);
                $coupon_title = '';
                foreach($coupon_id as $k=>$v){
                    $coupon_title .= $coupon[$v].', ';
                }
                $list[$key]['coupon_id'] = $coupon_title;
            }
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }
    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }
    /*获取优惠卷*/
    public function getcoupon(){
        $name = $this->request->request("name");
		$map = array(
			'title' => array('like','%'.$name.'%'),
		);
		$row = Db::name('coupon')->where($map)->select();
        $data = [];
		foreach($row as $ls){
			$data[] = array(
				'id' => $ls['id'],
				'name' =>$ls['title'].'-'.$ls['use_num'].'天',
			);
		}
		return json(['list' => $data]);
    }
}
