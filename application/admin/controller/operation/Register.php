<?php

namespace app\admin\controller\operation;

use app\common\controller\Backend;
use think\Db;

/**
 * 
 *
 * @icon fa fa-circle-o
 */
class Register extends Backend
{
    
    /**
     * Register模型对象
     * @var \app\admin\model\operation\Register
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\operation\Register;

    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax())
        {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField'))
            {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                    ->with(['user'])
                    ->where($where)
                    ->order($sort, $order)
                    ->count();

            $list = $this->model
                    ->with(['user'])
                    ->where($where)
                    ->order($sort, $order)
                    ->limit($offset, $limit)
                    ->select();
            /*获取运营人员账号*/
            // $operation = Db::name('operation')->select();
            // $operation_array = [];
            // foreach ($operation as $key => $value) {
            //     $operation_array[$value['suffix']] =$value['name'].$value['remarks'];
            //     // $operation_array[$value['suffix']] =$value['name'];
            // }
            foreach ($list as $key=>$row) {
                $row->getRelation('user')->visible(['mobile','is_card','is_card_img']);
                // if(!empty($operation_array[$row['mask']])){
                //     $list[$key]['mask'] = $operation_array[$row['mask']];
                // }else{
                //     $list[$key]['mask'] = '自然流量';
                // }
            }
            $list = collection($list)->toArray();
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }
}
