<?php

namespace app\admin\controller\operation;

use app\common\controller\Backend;
use think\Db;
use think\Config;

/**
 * 统计图表示例
 *
 * @icon   fa fa-charts
 * @remark 展示在FastAdmin中使用Echarts展示丰富多彩的统计图表
 */
class Statistics extends Backend
{
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = model('AdminLog');
        $operation_array = Config::get('site.operation_array');
        $this->view->assign("operation_array", $operation_array);
    }

    /**
     * 查看
     */
    public function index()
    {
        // 优化：将多次查询合并为一次查询
        $statistics = Db::name('operation_statistics')
            ->field([
                'sum(user_id_count) as user_count',
                'sum(is_card_1_count) as is_card_1_count',
                'sum(is_card_img_1_count) as is_card_img_1_count',
                'sum(order_count) as order_count',
                'sum(tb_money) as tb_money'
            ])->find();
		$operation_statistics_list = Db::name('operation_statistics')->order('createtime', 'desc')->select();
        $start_time = request()->param('start_date')?request()->param('start_date'):date('Ymd',time());
        $end_time = request()->param('end_date')?request()->param('end_date'):date('Ymd',time());
		$statistics_data = $this->statistics_data($start_time,$end_time);
        $time_date = $start_time.'-'.$end_time;
		$this->assign('statistics',$statistics);
		$this->assign('day_time',$time_date);
		$this->assign('statistics_data',$statistics_data);
		$this->assign('operation_statistics_list',$operation_statistics_list);
        return $this->view->fetch();
    }
	/*上方统计筛选*/
    public function count_statistics(){
        $type = request()->param('type');
        $map = [];
        if($type > 0){
            $map['type'] = $type;
        }
        $statistics = Db::name('operation_statistics')
        ->field([
            'sum(user_id_count) as user_count',
            'sum(is_card_1_count) as is_card_1_count',
            'sum(is_card_img_1_count) as is_card_img_1_count',
            'sum(order_count) as order_count',
            'sum(tb_money) as tb_money'
        ])->where($map)->find();
        $this->assign('statistics',$statistics);
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch();
    }
	/*下方列表筛选*/
    public function statistics_data_list(){
        $type = request()->param('type');
        $map = [];
        if($type > 0){
            $map['type'] = $type;
        }
        $operation_statistics_list = Db::name('operation_statistics')->order('createtime', 'desc')->where($map)->select();
        $this->assign('operation_statistics_list',$operation_statistics_list);
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch();
    }
    /*搜索*/
    public function tabledata(){
        $start_time = strtotime(request()->param('start_date'));
        $end_time = strtotime(request()->param('end_date'));
        $statistics = Db::name('operation_statistics')->whereTime('createtime', 'between', [date('Y-m-d 00:00:00', $start_time), date('Y-m-d 23:59:59', $end_time)])->select();
        $statistics_data = [];
        foreach($statistics as $ls){
            $statistics_data[$ls['title']]['title'] = $ls['title'];
            if(!empty($statistics_data[$ls['title']]['user_id_count'])){
                $statistics_data[$ls['title']]['user_id_count'] += $ls['user_id_count'];
            }else{
                $statistics_data[$ls['title']]['user_id_count'] = $ls['user_id_count'];
            }
            if(!empty($statistics_data[$ls['title']]['is_card_0_count'])){
                $statistics_data[$ls['title']]['is_card_0_count'] += $ls['is_card_0_count'];
            }else{
                $statistics_data[$ls['title']]['is_card_0_count'] = $ls['is_card_0_count'];
            }
            if(!empty($statistics_data[$ls['title']]['is_card_1_count'])){
                $statistics_data[$ls['title']]['is_card_1_count'] += $ls['is_card_1_count'];
            }else{
                $statistics_data[$ls['title']]['is_card_1_count'] = $ls['is_card_1_count'];
            }
            if(!empty($statistics_data[$ls['title']]['is_card_img_0_count'])){
                $statistics_data[$ls['title']]['is_card_img_0_count'] += $ls['is_card_img_0_count'];
            }else{
                $statistics_data[$ls['title']]['is_card_img_0_count'] = $ls['is_card_img_0_count'];
            }
            if(!empty($statistics_data[$ls['title']]['is_card_img_1_count'])){
                $statistics_data[$ls['title']]['is_card_img_1_count'] += $ls['is_card_img_1_count'];
            }else{
                $statistics_data[$ls['title']]['is_card_img_1_count'] = $ls['is_card_img_1_count'];
            }
            if(!empty($statistics_data[$ls['title']]['order_count'])){
                $statistics_data[$ls['title']]['order_count'] += $ls['order_count'];
            }else{
                $statistics_data[$ls['title']]['order_count'] = $ls['order_count'];
            }
            if(!empty($statistics_data[$ls['title']]['tb_money'])){
                $statistics_data[$ls['title']]['tb_money'] += $ls['tb_money'];
            }else{
                $statistics_data[$ls['title']]['tb_money'] = $ls['tb_money'];
            }
            $statistics_data[$ls['title']]['day_time'] = date('Y-m-d', $start_time).''.date('Y-m-d', $end_time);
        }
        $this->assign('statistics_data',$statistics_data);
		$this->view->engine->layout('layout/empty');
		return $this->view->fetch('table_data');
    }
	public function statistics_data($start_time,$end_time){
		// 修改时间为 2025-03-19
		//$day_time = $start_time.'-'.$end_time;
       $start_time = strtotime($start_time);
       $end_time = strtotime($end_time);
		// 初始化数组
		$operation = Db::name('operation')->select();
		$statistics = $original = $mask_array = $mask_suffix = $user_array = $mask_type = [];
		foreach ($operation as $key => $value) {
		    $mask_array[$value['suffix']] = $value['name'] . $value['remarks'];
		    $mask_type[$value['suffix']] = $value['type'];
		    $mask_suffix[] = $value['suffix'];
		}
		/*获取注册和认证数据*/
		$user_operation = Db::table('t_user_operation')
		    ->alias('o') // 给users表设置别名
		    // 左连接 user 表，通过 o.user_id 和 u.id 进行关联
		    ->join('user u', 'o.user_id = u.id') 
		    // 选择字段和计算订单数量
		    ->field('o.mask,o.user_id,u.username,u.mobile,u.is_card,u.is_card_img')
		    // 修改时间条件，查询 2025-03-19 当天的数据
		    ->whereTime('o.createtime', 'between', [date('Y-m-d 00:00:00', $start_time), date('Y-m-d 23:59:59', $end_time)])
		    ->select();
         $tables = [
             'df_order' => [
                 'table' => 't_df_order',
                 'fields' => 't_user_operation.mask,t_user_operation.user_id,t_df_order.id,t_df_order.tb_money,t_df_order.rmb_money,t_df_order.receipts_money,t_df_order.profit_money',
                 'conditions' => ['t_df_order.order_status' => '1']
             ],
             'game_order' => [
                 'table' => 't_order_game',
                 'fields' => 't_user_operation.mask,t_user_operation.user_id,t_order_game.id,t_order_game.tb_money,t_order_game.rmb_money,t_order_game.receipts_money,t_order_game.profit_money',
                 'conditions' => ['t_order_game.order_status' => '1']
             ],
             'live_order' => [
                 'table' => 't_order_live',
                 'fields' => 't_user_operation.mask,t_user_operation.user_id,t_order_live.id,t_order_live.tb_money,t_order_live.rmb_money,t_order_live.receipts_money,t_order_live.profit_money',
                 'conditions' => ['t_order_live.order_status' => '1']
             ],
             'order_new_df' => [
                 'table' => 't_order_new_df',
                 'fields' => 't_user_operation.mask,t_user_operation.user_id,t_order_new_df.id,t_order_new_df.tb_money,t_order_new_df.rmb_money,t_order_new_df.receipts_money,t_order_new_df.profit_money',
                 'conditions' => ['t_order_new_df.order_status' => '1']
             ],
             'order_other' => [
                 'table' => 't_order_new_df',
                 'fields' => 't_user_operation.mask,t_user_operation.user_id,t_order_new_df.id,t_order_new_df.tb_money,t_order_new_df.rmb_money,t_order_new_df.receipts_money,t_order_new_df.profit_money',
                 'conditions' => ['t_order_new_df.order_status' => '1']
             ]
         ];
     
         $data = [];
         foreach ($tables as $key => $table) {
             $query = Db::table('t_user_operation')
                 // 不使用别名，直接使用表名
                 ->join($table['table'], 't_user_operation.user_id = '.$table['table'].'.user_id', 'INNER')
                 ->field($table['fields'])
                 // Modify the time condition to query based on the createtime of table u
                 ->whereTime($table['table'].'.createtime', 'between', [date('Y-m-d 00:00:00', $start_time), date('Y-m-d 23:59:59', $end_time)]);
             foreach ($table['conditions'] as $field => $value) {
                 $query->where($field, $value);
             }
             $data[$key] = $query->select();
         }
         // 原始数据归类
         $mask_suffix[] = 'organic';
		 $mask_type['organic'] = '100';
		 $mask_array['organic'] = '自然流量';
         /*原始数据归类---有的mask当天没有产生数据，默认为0*/
         foreach ($mask_suffix as $mask) {
             $statistics[$mask] = [
                 'title' => $mask_array[$mask],
                 'type' => $mask_type[$mask],
                 'user_id_count' => 0,
                 'is_card_0_count' => 0,
                 'is_card_1_count' => 0,
                 'is_card_img_0_count' => 0,
                 'is_card_img_1_count' => 0,
                 'order_count' => 0,
                 'tb_money' => 0,
                 'rmb_money' => 0,
                 'receipts_money' => 0,
                 'profit_money' => 0,
                 'day_time' => $start_time,
                 'createtime' => $end_time,
             ];
         }
         // 用户注册数量和认证数据进行归类
         foreach ($user_operation as $item) {
             $mask = $item['mask'];
             // 统计 user_id 数量
            $statistics[$mask]['user_id_count']++;
            // 统计 is_card 数量
            if ($item['is_card'] == 0) {
                $statistics[$mask]['is_card_0_count']++;
            } else {
                $statistics[$mask]['is_card_1_count']++;
            }
            // 统计 is_card_img 数量
            if ($item['is_card_img'] == 0) {
                $statistics[$mask]['is_card_img_0_count']++;
            } else {
                $statistics[$mask]['is_card_img_1_count']++;
            }
         }
         // 订单数据进行归类
         $orderTables = ['df_order', 'game_order', 'live_order', 'order_new_df', 'order_other'];
         foreach ($orderTables as $table) {
             foreach ($data[$table] as $item) {
                 $mask = $item['mask'];
                 if($mask){
                    $statistics[$mask]['order_count']++;
                    $statistics[$mask]['tb_money'] += $item['tb_money'];
                    $statistics[$mask]['rmb_money'] += $item['rmb_money'];
                    $statistics[$mask]['receipts_money'] += $item['receipts_money'];
                    $statistics[$mask]['profit_money'] += $item['profit_money'];
                 }
                
             }
         }
		 $statistics_data = array_values($statistics);
		 return $statistics_data;
	}
}
