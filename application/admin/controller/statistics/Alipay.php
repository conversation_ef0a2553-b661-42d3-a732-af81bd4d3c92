<?php

namespace app\admin\controller\statistics;

use app\common\controller\Backend;
use think\Db;
/**
 * 
 *
 * @icon fa fa-circle-o
 */
class Alipay extends Backend
{
    public function index()
    {
        $time_arr = [];
        $updatetime = $this->request->get('updatetime');
        if( !empty($updatetime) )
        {
            $time_arr = explode(' - ',$updatetime);
        }

        $page_size_arr = array(10, 25, 50);
        $page_size = $this->request->get('ps');
        $page = $this->request->get('page');
        if( empty($page_size) )
        {
            $page_size = 10;
        }
        if( empty($page) )
        {
            $page = 1;
        }

        $seach_index = ($page - 1) * $page_size;
        if( count($time_arr) === 0 )
        {
            $total_record = Db::query("SELECT COUNT(*) FROM t_sys_alipay_trade_detail as t_d INNER JOIN (SELECT MAX(t_sa.ct) as ct FROM (SELECT *,unix_timestamp(create_time) as ct FROM t_sys_alipay_trade_detail) as t_sa GROUP BY DATE_FORMAT(t_sa.create_time,'%Y-%m-%d'),t_sa.alipay_account) as t_sat ON unix_timestamp(t_d.create_time)=t_sat.ct GROUP BY t_d.alipay_account,DATE_FORMAT(t_d.create_time, '%Y-%m-%d')");
        }else{
            $total_record = Db::query("SELECT COUNT(*) FROM t_sys_alipay_trade_detail as t_d INNER JOIN (SELECT MAX(t_sa.ct) as ct FROM (SELECT *,unix_timestamp(create_time) as ct FROM t_sys_alipay_trade_detail where addtime>=? and addtime<=?) as t_sa GROUP BY DATE_FORMAT(t_sa.create_time,'%Y-%m-%d'),t_sa.alipay_account) as t_sat ON unix_timestamp(t_d.create_time)=t_sat.ct GROUP BY t_d.alipay_account,DATE_FORMAT(t_d.create_time, '%Y-%m-%d')", [strtotime($time_arr[0]), strtotime($time_arr[1])]);
        }
        $count_record = count($total_record);
        $page_num = ceil(($count_record/$page_size));
        if( count($time_arr) === 0 )
        {
            //$query_list = Db::query("SELECT * FROM (SELECT DATE_FORMAT(t_sa.create_time,'%Y-%m-%d') as day_time, t_sa.id,t_sa.alipay_account,t_sa.create_time, t_sa.addtime,t_sa.balance,t_s.all_in,t_s.all_out FROM t_sys_alipay_trade_detail as t_sa INNER JOIN ( SELECT MAX(addtime) as addtime, SUM(in_money) as all_in,SUM(out_money) as all_out FROM t_sys_alipay_trade_detail GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d'),alipay_account) as t_s ON t_sa.addtime=t_s.addtime GROUP BY t_s.addtime ORDER BY t_sa.id ASC) as t_sat ORDER BY t_sat.addtime DESC LIMIT ?,?", [$seach_index,$page_size + $page_size]);
            $query_list = Db::query("SELECT * FROM (SELECT DATE_FORMAT(t_sa.create_time,'%Y-%m-%d') as day_time, t_sa.id,t_sa.alipay_account,t_sa.create_time, t_sa.addtime,t_sa.balance,t_s.all_in,t_s.all_out FROM t_sys_alipay_trade_detail as t_sa INNER JOIN ( SELECT alipay_account as aa, MAX(addtime) as addtime, SUM(in_money) as all_in,SUM(out_money) as all_out FROM t_sys_alipay_trade_detail GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d'),alipay_account) as t_s ON t_sa.alipay_account=t_s.aa and t_sa.addtime=t_s.addtime GROUP BY t_s.addtime ORDER BY t_sa.id ASC) as t_sat ORDER BY t_sat.addtime DESC LIMIT ?,?", [$seach_index,$page_size + $page_size]);

        }else{
            $time_arr[0] = strtotime($time_arr[0]) - 3600 * 24 ;
            $time_arr[1] = strtotime($time_arr[1]);
            $query_list = Db::query("SELECT * FROM (SELECT DATE_FORMAT(t_sa.create_time,'%Y-%m-%d') as day_time, t_sa.id,t_sa.alipay_account,t_sa.create_time, t_sa.addtime,t_sa.balance,t_s.all_in,t_s.all_out FROM t_sys_alipay_trade_detail as t_sa INNER JOIN ( SELECT alipay_account as aa, MAX(addtime) as addtime, SUM(in_money) as all_in,SUM(out_money) as all_out FROM t_sys_alipay_trade_detail WHERE addtime>=? and addtime<=? GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d'),alipay_account) as t_s ON t_sa.alipay_account=t_s.aa and t_sa.addtime=t_s.addtime GROUP BY t_s.addtime ORDER BY t_sa.id ASC) as t_sat ORDER BY t_sat.addtime DESC LIMIT ?,?", [$time_arr[0], $time_arr[1], $seach_index,$page_size + $page_size]);
        }

        if( count($query_list) > 0 )
        {
            $day_list = $this->date_group($query_list);
            $list = $this->bank_account_day_statistics($day_list);
            $list_count = count($list);
        }else{
            $list = null;
            $list_count = null;
        }

        if( !empty($updatetime) )
        {
            $this->view->assign('updatetime', $updatetime);
        }

        $this->view->assign([
            'size_arr'          => $page_size_arr,
            'list'       => $list,
            'list_count'       => $list_count,
            'count' => $count_record,
            'page_size' => $page_size,
            'page_num'   => $page_num,
            'page'  => $page,
            'begin' => $seach_index + 1,
            'end' => $seach_index + $page_size
        ]);
        /* 中间表格 */
        return $this->view->fetch();
    }

//    private function bank_account_day_statistics($day_list)
//    {
//        $list_data = array();
//        $index = count($day_list);
//
//        for ($i = 0; $i < $index; $i++)
//        {
//            if( isset($day_list[$i+1]))
//            {
//                $tmp = $this->account_day_statistics( $day_list[$i], $day_list[$i+1]);
//                $list_data = array_merge($list_data, $tmp);
//            }else{
//                $tmp = $this->account_day_statistics( $day_list[$i], null);
//                $list_data = array_merge($list_data, $tmp);
//            }
//        }
//        return $list_data;
//    }

    private function bank_account_day_statistics($day_list)
    {
        $list_data = array();
        $index = count($day_list);

        for ($i = 0; $i < $index; $i++)
        {
            if( isset($day_list[$i+1]))
            {
                $tmp = $this->account_day_statistics( $day_list[$i], $day_list[$i+1]);
                $list_data = array_merge($list_data, $tmp);
            }
        }
        return $list_data;
    }

    private function account_day_statistics( $curr_record, $last_record )
    {
        $day =  $curr_record['day'];
        $data = $curr_record['data'];

        $new_data = array();
        foreach ( $data as $item )
        {
            $account_last_record = $this->get_data_by_last_record($last_record, $item['alipay_account'] );
            if( empty($account_last_record) )
            {
                $account_last_record = ['day'=>$day,'alipay_account'=>$item['alipay_account'], 'all_in'=>0, 'all_out'=>0, 'create_time'=>$item['create_time'], 'last_balance'=>0, 'balance'=>0,
                    'due_balance'=>0, 'difference'=> 0];
            }
            $tmp = ['day'=>$day, 'alipay_account'=>$item['alipay_account'], 'all_in'=>$item['all_in'], 'all_out'=>$item['all_out'], 'create_time'=>$item['create_time'], 'last_balance'=>$account_last_record['balance'], 'balance'=>$item['balance'],
                'due_balance'=>$account_last_record['balance'] + $item['all_in'] - $item['all_out'], 'difference'=>bcsub( $item['balance'], ($account_last_record['balance'] + $item['all_in'] - $item['all_out']),2)];
            array_push($new_data, $tmp);
        }

        $key_arrays = array();
        foreach ($new_data as $val )
        {
            $key_arrays[]=$val['alipay_account'];
        }
        array_multisort($key_arrays,SORT_ASC,SORT_NUMERIC,$new_data);
        return $new_data;
    }

    private function get_data_by_last_record($src, $account)
    {
        $return_data = null;
        if( !empty($src) )
        {
            $data = $src['data'];
            foreach ($data as $item)
            {
                if( $item['alipay_account'] == $account )
                {
                    $return_data = $item;
                    break;
                }
            }
        }
        return $return_data;
    }

    private function date_group($list)
    {
        $day_time = $list[0]['day_time'];
        $data_arr = array();
        $item_arr = array();

        foreach ($list as $row)
        {
            if( $row['day_time'] == $day_time )
            {
                $item_arr[] = $row;
                continue;
            }
            $data_arr[] = array('day'=>$day_time, 'data'=>$item_arr);
            $item_arr = array();
            $day_time =  date("Y-m-d", strtotime($day_time) - 3600 * 24);
            $item_arr[] = $row;
        }
        $data_arr[] = array('day'=>$day_time, 'data'=>$item_arr);
        return $data_arr;
    }
}
