<?php

namespace app\admin\controller\statistics;

use app\common\controller\Backend;
use think\Db;

/**
 * 
 *
 * @icon fa fa-circle-o
 */
class Alipaysort extends Backend
{

    public function index()
    {
        $time_arr = [];
        $updatetime = $this->request->get('updatetime');
        if( !empty($updatetime) )
        {
            $time_arr = explode(' - ',$updatetime);
        }

        $page_size_arr = array(10, 25, 50);
        $page_size = $this->request->get('ps');
        $page = $this->request->get('page');
        if( empty($page_size) )
        {
            $page_size = 10;
        }
        if( empty($page) )
        {
            $page = 1;
        }

        $seach_index = ($page - 1) * $page_size;
        /*支付宝每个账号每日的代付与退款详情 --> 一般不会出错*/
        if( count($time_arr) === 0 )
        {
            $total_record = Db::query("SELECT COUNT(*) total FROM t_sys_alipay INNER JOIN (SELECT id, DATE_FORMAT(create_time, '%Y-%m-%d') day_time, t_sa.alipay_account, t_sa.create_time, SUM(t_sa.in_money) all_in, SUM(t_sa.out_money) all_out FROM t_sys_alipay_trade_detail as t_sa WHERE type in (0,1) GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d'),alipay_account) as t_sa ON t_sys_alipay.sys_account=t_sa.alipay_account ORDER BY t_sa.id DESC");
        }else{
            $total_record = Db::query("SELECT COUNT(*) total FROM t_sys_alipay INNER JOIN (SELECT id, DATE_FORMAT(create_time, '%Y-%m-%d') day_time, t_sa.alipay_account, t_sa.create_time, SUM(t_sa.in_money) all_in, SUM(t_sa.out_money) all_out FROM t_sys_alipay_trade_detail as t_sa WHERE type in (0,1) and addtime>=? and addtime<=? GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d'),alipay_account) as t_sa ON t_sys_alipay.sys_account=t_sa.alipay_account ORDER BY t_sa.id DESC", [strtotime($time_arr[0]), strtotime($time_arr[1])]);
        }
        $count_record = $total_record[0]['total'];
        $page_num = ceil(($count_record/$page_size));


        if( count($time_arr) === 0 )
        {
            $detail_record = Db::query("SELECT t_sa.*,t_sys_alipay.id as sys_alipay_id FROM t_sys_alipay INNER JOIN (SELECT id, DATE_FORMAT(create_time, '%Y-%m-%d') day_time, t_sa.alipay_account, t_sa.create_time, SUM(t_sa.in_money) all_in, SUM(t_sa.out_money) all_out FROM t_sys_alipay_trade_detail as t_sa WHERE type in (0,1) GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d'),alipay_account) as t_sa ON t_sys_alipay.sys_account=t_sa.alipay_account ORDER BY t_sa.id DESC LIMIT ?,?", [$seach_index,$page_size]);
        }else{
            $detail_record = Db::query("SELECT t_sa.*,t_sys_alipay.id as sys_alipay_id FROM t_sys_alipay INNER JOIN (SELECT id, DATE_FORMAT(create_time, '%Y-%m-%d') day_time, t_sa.alipay_account, t_sa.create_time, SUM(t_sa.in_money) all_in, SUM(t_sa.out_money) all_out FROM t_sys_alipay_trade_detail as t_sa WHERE type in (0,1) and addtime>=? and addtime<=? GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d'),alipay_account) as t_sa ON t_sys_alipay.sys_account=t_sa.alipay_account ORDER BY t_sa.id DESC LIMIT ?,?", [strtotime($time_arr[0]), strtotime($time_arr[1]), $seach_index,$page_size]);
        }

        $first_ele = reset($detail_record);
        $end_ele = end($detail_record);
        $begin_time = $end_ele['day_time'];
        $end_time = $first_ele['day_time'] . ' 23:59:59';


        /* 支付宝代付成功数据*/
        $suc_record = Db::query("SELECT t_ao.*,t_sys_alipay.sys_account FROM t_sys_alipay INNER JOIN (SELECT id, DATE_FORMAT(update_time, '%Y-%m-%d') day_time, SUM(price) money, sys_alipay_id, status FROM t_alipay_order WHERE status=5 and unix_timestamp(update_time)>=? and unix_timestamp(update_time)<=? GROUP BY DATE_FORMAT(update_time, '%Y-%m-%d'), sys_alipay_id) as t_ao on t_sys_alipay.id=t_ao.sys_alipay_id ORDER BY unix_timestamp(t_ao.day_time) DESC", [strtotime($begin_time), strtotime($end_time)]);

        /*支付宝代付退款数据*/
        $refund_record = Db::query("SELECT t_ao.*,t_sys_alipay.sys_account FROM t_sys_alipay INNER JOIN (SELECT id, DATE_FORMAT(update_time, '%Y-%m-%d') day_time, SUM(price) money, sys_alipay_id, status FROM t_alipay_order WHERE status=4 and unix_timestamp(update_time)>=? and unix_timestamp(update_time)<=? GROUP BY DATE_FORMAT(update_time, '%Y-%m-%d'), sys_alipay_id) as t_ao on t_sys_alipay.id=t_ao.sys_alipay_id ORDER BY unix_timestamp(t_ao.day_time) DESC", [strtotime($begin_time), strtotime($end_time)]);

        /*支付宝代付部分退款数据*/
        $part_refund_record = Db::query("SELECT t_ao.id,t_ao.sys_alipay_id,SUM(t_ao.price) price,t_ao.update_time,SUM(t_aq.price) money,DATE_FORMAT(t_ao.update_time, '%Y-%m-%d') day_time FROM t_alipay_order as t_ao INNER JOIN t_alipay_order_goods as t_aq ON t_ao.id=t_aq.alipay_order_id WHERE t_ao.status=7 and unix_timestamp(t_ao.update_time)>=? and unix_timestamp(t_ao.update_time)<=? and t_aq.status=4 GROUP BY DATE_FORMAT(t_ao.update_time, '%Y-%m-%d'), t_ao.sys_alipay_id ORDER BY unix_timestamp(day_time) DESC",[strtotime($begin_time), strtotime($end_time)]);

        /*支付宝未识别退款数据*/
        $not_rcg_refund_record = Db::query("SELECT t_rf.*, t_sys_alipay.sys_account FROM t_sys_alipay INNER JOIN (SELECT t_a.id, DATE_FORMAT(t_a.create_time, '%Y-%m-%d') day_time,SUM(t_a.price) money,t_a.sys_alipay_id FROM t_df_order INNER JOIN (SELECT t_amr.id, t_amr.price,t_amr.create_time,t_ao.order_id, t_ao.sys_alipay_id FROM t_aliapy_manual_refund as t_amr INNER JOIN t_alipay_order as t_ao on t_amr.alipay_order_id=t_ao.id) as t_a on t_df_order.id=t_a.order_id WHERE t_df_order.type in (0,1) and unix_timestamp(t_a.create_time)>=? and unix_timestamp(t_a.create_time)<=? GROUP BY DATE_FORMAT(t_a.create_time, '%Y-%m-%d'),t_a.sys_alipay_id) as t_rf on t_sys_alipay.id=t_rf.sys_alipay_id ORDER BY t_rf.id DESC",[strtotime($begin_time), strtotime($end_time)]);


                                        //        /*U盾支付订单数据, U盾完成后自动修改订单为成功状态*/
                                        //        $ukey_pay_record = Db::query("SELECT t_up.*, t_sys_alipay.id sys_alipay_id FROM t_sys_alipay INNER JOIN (SELECT id, alipay_account, SUM(price) as money, DATE_FORMAT(create_time, '%Y-%m-%d') as day_time FROM t_ukey_pay WHERE status=1 and remark='代付成功' and unix_timestamp(create_time)>=? and unix_timestamp(create_time)<=? GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d'), alipay_account) as t_up on t_sys_alipay.sys_account=t_up.alipay_account ORDER BY t_up.id DESC", [strtotime($begin_time), strtotime($end_time)]);



        $new_detail_record = $this->date_group($detail_record);
        $new_suc_record = $this->date_group($suc_record);
        $new_refund_record = $this->date_group($refund_record);
        $new_part_refund_record = $this->date_group($part_refund_record);
        $new_not_rcg_refund_record= $this->date_group($not_rcg_refund_record);
                                        //         $new_ukey_pay_recode = $this->date_group($ukey_pay_record);

        $list = $this->merge_data($new_detail_record, $new_suc_record, $new_refund_record, $new_part_refund_record, $new_not_rcg_refund_record);
        if( count($list) > 0 )
        {
            $list_count = count($list);
        }else{
            $list = null;
            $list_count = null;
        }

        if( !empty($updatetime) )
        {
            $this->view->assign('updatetime', $updatetime);
        }

        $this->view->assign([
            'size_arr'          => $page_size_arr,
            'list'       => $list,
            'list_count'       => $list_count,
            'count' => $count_record,
            'page_size' => $page_size,
            'page_num'   => $page_num,
            'page'  => $page,
            'begin' => $seach_index + 1,
            'end' => $seach_index + $page_size
        ]);

        return $this->view->fetch();
    }


    public function detail()
    {
        //SELECT * FROM t_sys_alipay_trade_detail WHERE goods_trade_no not in (SELECT t_alipay_order_goods.goods_trade_no FROM t_alipay_order INNER JOIN t_alipay_order_goods on t_alipay_order.id=t_alipay_order_goods.alipay_order_id WHERE t_alipay_order.sys_alipay_id=6 and t_alipay_order.status in (4,5) and DATE_FORMAT(t_alipay_order.update_time, '%Y-%m-%d')='2020-11-16') and alipay_account='<EMAIL>' and addtime>=********** and addtime<=********** and type in (0,1)

        $account = $this->request->get('account');
        $daytime = $this->request->get('daytime');

        $sys_alipay = Db::name('sys_alipay')->where('sys_account', $account)->find();
        $begin_time = $daytime;
        $end_time = $daytime . ' 23:59:59';


        $rows = Db::query("SELECT * FROM t_sys_alipay_trade_detail WHERE goods_trade_no not in (SELECT t_alipay_order_goods.goods_trade_no FROM t_alipay_order INNER JOIN t_alipay_order_goods on t_alipay_order.id=t_alipay_order_goods.alipay_order_id WHERE t_alipay_order.sys_alipay_id=? and t_alipay_order.status in (4,5) and DATE_FORMAT(t_alipay_order.update_time, '%Y-%m-%d')=?) and alipay_account=? and addtime>=? and addtime<=? and type in (0,1)",
            [$sys_alipay['id'], $daytime, $account, strtotime($begin_time), strtotime($end_time)]);

        $in_arr = array_column($rows, 'in_money');
        $out_arr = array_column($rows, 'out_money');

        $total_in = array_sum( array_values($in_arr) );
        $toal_out = array_sum( array_values($out_arr) );

        $this->view->assign([
            'list'       => $rows,
            'all_in'     => $total_in,
            'all_out'     =>$toal_out
        ]);
        return $this->view->fetch('statistics/alipaysort/detail');
    }



    private function merge_data($detail_record, $suc_record, $refund_record, $part_refund_record, $not_rcg_refund_record)
    {
            $list = array();
            if( count($detail_record) <= 0 )
            {
                return $list;
            }
            //Db::name('debugging')->insertGetId(['msg'=>json_encode($ukey_pay_recode), 'title'=>'$ukey_pay_recode', 'add_time'=>time()]);

            for ($i=0;$i<count($detail_record);$i++)
            {
                $detail_row = $detail_record[$i];
                $day_time = $detail_row['day'];

                $suc_row = $this->get_fixed_time_data( $suc_record, $day_time);
                $refund_row = $this->get_fixed_time_data( $refund_record, $day_time);
                $part_refund__row = $this->get_fixed_time_data( $part_refund_record, $day_time);
                $not_rcg_refund_row = $this->get_fixed_time_data( $not_rcg_refund_record, $day_time);
                //$ukey_pay_row = $this->get_fixed_time_data( $ukey_pay_recode, $day_time);


                $detail_list = $detail_row['data'];
                if( empty($detail_list) )
                {
                    continue;
                }

                $day_list = array();
                for ($j=0;$j<count($detail_list);$j++)
                {
                    $suc = $this->get_sys_alipay_data($detail_list[$j]['sys_alipay_id'] , $suc_row);
                    $refund = $this->get_sys_alipay_data($detail_list[$j]['sys_alipay_id'] , $refund_row);
                    $part_refund = $this->get_sys_alipay_data($detail_list[$j]['sys_alipay_id'] , $part_refund__row);
                    $not_rcg_refund = $this->get_sys_alipay_data($detail_list[$j]['sys_alipay_id'] , $not_rcg_refund_row);
                    //$ukey_pay = $this->get_sys_alipay_data($detail_list[$j]['sys_alipay_id'] , $ukey_pay_row);

                    $suc_money = empty($suc) ? 0 : $suc['money'];
                    $refund_money = empty($refund) ? 0 : $refund['money'];
                    $part_refund_money = empty($part_refund) ? 0 : $part_refund['money'];
                    $not_rcg_money = empty($not_rcg_refund) ? 0 : $not_rcg_refund['money'];
                    //$ukey_pay_money = empty($ukey_pay) ? 0 : $ukey_pay['money'];

                    //$temp = ['day_time'=>$detail_list[$j]['day_time'], 'alipay_account'=>$detail_list[$j]['alipay_account'], 'all_in'=>$detail_list[$j]['all_in'], 'all_out'=>$detail_list[$j]['all_out'], 'suc_money'=>$suc_money, 'refund_money'=>$refund_money, 'part_refund_money'=>$part_refund_money, 'not_rcg_money'=>$not_rcg_money, 'ukey_pay_money'=>$ukey_pay_money];
                    $temp = ['day_time'=>$detail_list[$j]['day_time'], 'alipay_account'=>$detail_list[$j]['alipay_account'], 'all_in'=>$detail_list[$j]['all_in'], 'all_out'=>$detail_list[$j]['all_out'], 'suc_money'=>$suc_money, 'refund_money'=>$refund_money, 'part_refund_money'=>$part_refund_money, 'not_rcg_money'=>$not_rcg_money];
                    array_push($day_list, $temp);
                }

                $key_arrays = array();
                foreach ($day_list as $val )
                {
                    $key_arrays[]=$val['alipay_account'];
                }
                array_multisort($key_arrays,SORT_ASC,SORT_NUMERIC, $day_list);

                $list = array_merge($list, $day_list);
                if( isset($detail_record[$i+1]) )
                {
                    $day_time = $detail_record[$i+1]['day'];
                }
            }

            return $list;
    }

    private function get_fixed_time_data($data, $day_time)
    {
        $res = null;
        if( empty($data) )
        {
            return $res;
        }
        foreach ( $data as $item )
        {
            if( $item['day'] == $day_time )
            {
                $res = $item['data'];
                break;
            }

        }
        return $res;
    }

    private function  get_sys_alipay_data($sys_alipay_id, $query_data)
    {
        if( empty($query_data) )
        {
            return null;
        }

        $res = null;
        foreach ( $query_data as $item )
        {
            if ( $item['sys_alipay_id'] === $sys_alipay_id )
            {
                $res = $item;
                break;
            }
        }
        return $res;
    }


    private function date_group($list)
    {
        $data_arr = array();
        $item_arr = array();

        if( count($list) <= 0 )
        {
            return $data_arr;
        }

        $day_time = $list[0]['day_time'];
        foreach ($list as $row)
        {
            if( $row['day_time'] == $day_time )
            {
                $item_arr[] = $row;
                continue;
            }

            if( count($item_arr) > 0 )
            {
                $data_arr[] = array('day'=>$day_time, 'data'=>$item_arr);
            }
            $item_arr = array();
            $day_time = date("Y-m-d", strtotime($row['day_time']));
            $item_arr[] = $row;
        }
        $data_arr[] = array('day'=>$day_time, 'data'=>$item_arr);
        return $data_arr;
    }



}
