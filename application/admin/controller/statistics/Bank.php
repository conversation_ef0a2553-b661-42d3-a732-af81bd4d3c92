<?php

namespace app\admin\controller\statistics;

use app\common\controller\Backend;
use think\Config;
use think\Db;

/**
 * 银行流水管理
 *
 * @icon fa fa-circle-o
 */
class Bank extends Backend
{
    
    /**
     * Bank模型对象
     * @var \app\admin\model\statistics\Bank
     */
//    protected $model = null;
//
//    public function _initialize()
//    {
//        parent::_initialize();
//        $this->model = new \app\admin\model\statistics\Bank;
//
//    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    public function index()
    {
        $time_arr = [];
        $updatetime = $this->request->get('updatetime');
        if( !empty($updatetime) )
        {
            $time_arr = explode(' - ',$updatetime);
        }

        $page_size_arr = array(10, 25, 50);
        $page_size = $this->request->get('ps');
        $page = $this->request->get('page');
        if( empty($page_size) )
        {
            $page_size = 10;
        }
        if( empty($page) )
        {
            $page = 1;
        }

        $seach_index = ($page - 1) * $page_size;

        if(count($time_arr) === 0)
        {
            $total_record = Db::query("SELECT COUNT(*) as total  FROM (SELECT tbt.bank_title,tbt.account,tbt.date_time,tbt.balance,DATE_FORMAT(tbt.date_time,'%Y-%m-%d') as day_time FROM t_bank_trade as tbt INNER JOIN (SELECT max(t.day_time) as dt FROM (SELECT *,unix_timestamp(date_time) as day_time FROM t_bank_trade) as t GROUP BY DATE_FORMAT(t.date_time,'%Y-%m-%d'),t.account ORDER BY id DESC) as bt on unix_timestamp(tbt.date_time)=bt.dt GROUP BY tbt.account, tbt.date_time) as t");
        }else{
            $total_record = Db::query("SELECT COUNT(*) as total FROM (SELECT tbt.bank_title,tbt.account,tbt.date_time,tbt.balance,DATE_FORMAT(tbt.date_time,'%Y-%m-%d') as day_time FROM t_bank_trade as tbt INNER JOIN (SELECT max(t.day_time) as dt FROM (SELECT *,unix_timestamp(date_time) as day_time FROM t_bank_trade WHERE unix_timestamp(date_time)>=? and unix_timestamp(date_time)<=? ) as t GROUP BY DATE_FORMAT(t.date_time,'%Y-%m-%d'),t.account ORDER BY id DESC) as bt on unix_timestamp(tbt.date_time)=bt.dt GROUP BY tbt.account, tbt.date_time) as t", [strtotime($time_arr[0]), strtotime($time_arr[1])]);
        }
        $count_record = $total_record[0]['total'];
        $page_num = ceil(($count_record/$page_size));

        if(count($time_arr) === 0)
        {
            $query_list = Db::query("SELECT tbt.bank_title, tbt.account, tbt.date_time, bt.save_all, bt.taken_all, tbt.balance, DATE_FORMAT(tbt.date_time, '%Y-%m-%d') as day_time FROM t_bank_trade as tbt INNER JOIN ( SELECT max(t.day_time) as dt, SUM(save) as save_all, SUM(taken) as taken_all FROM ( SELECT *, unix_timestamp(date_time) as day_time FROM t_bank_trade ) as t GROUP BY DATE_FORMAT(t.date_time, '%Y-%m-%d'), t.account ) as bt on unix_timestamp(tbt.date_time)= bt.dt GROUP BY tbt.account, tbt.date_time ORDER BY tbt.id DESC  LIMIT ?,?", [$seach_index,$page_size + $page_size]);
        }else{
            $time_arr[0] = strtotime($time_arr[0]) - 3600 * 24 ;
            $time_arr[1] = strtotime($time_arr[1]);
            $query_list = Db::query("SELECT tbt.bank_title, tbt.account, tbt.date_time, bt.save_all, bt.taken_all, tbt.balance, DATE_FORMAT(tbt.date_time, '%Y-%m-%d') as day_time FROM t_bank_trade as tbt INNER JOIN ( SELECT max(t.day_time) as dt, SUM(save) as save_all, SUM(taken) as taken_all FROM ( SELECT *, unix_timestamp(date_time) as day_time FROM t_bank_trade ) as t GROUP BY DATE_FORMAT(t.date_time, '%Y-%m-%d'), t.account ) as bt on unix_timestamp(tbt.date_time)= bt.dt WHERE unix_timestamp(tbt.date_time)>=? and unix_timestamp(tbt.date_time)<=? GROUP BY tbt.account, tbt.date_time ORDER BY tbt.id DESC LIMIT ?,?", [$time_arr[0], $time_arr[1], $seach_index,$page_size + $page_size]);
        }

        if( count($query_list) > 0 )
        {
            $day_list = $this->date_group($query_list);
            $list = $this->bank_account_day_statistics($day_list);
            $list_count = count($list);
        }else{
            $list = null;
            $list_count = null;
        }

        if( !empty($updatetime) )
        {
            $this->view->assign('updatetime', $updatetime);
        }

        $this->view->assign([
            'size_arr'          => $page_size_arr,
            'list'       => $list,
            'list_count'       => $list_count,
            'count' => $count_record,
            'page_size' => $page_size,
            'page_num'   => $page_num,
            'page'  => $page,
            'begin' => $seach_index + 1,
            'end' => $seach_index + $page_size
        ]);
        /* 中间表格 */
        return $this->view->fetch();
    }


    private function bank_account_day_statistics($day_list)
    {
        $list_data = array();
        $index = count($day_list);

        for ($i = 0; $i < $index; $i++)
        {
            if( isset($day_list[$i+1]))
            {
                $tmp = $this->account_day_statistics( $day_list[$i], $day_list[$i+1]);
                $list_data = array_merge($list_data, $tmp);
            }
        }
        return $list_data;
    }


    private function account_day_statistics( $curr_record, $last_record )
    {
            $day =  $curr_record['day'];
            $data = $curr_record['data'];

            $new_data = array();
            foreach ( $data as $item )
            {
                 $account_last_record = $this->get_data_by_last_record($last_record, $item['account'] );
                 if( empty($account_last_record) )
                 {
                     $account_last_record = ['day'=>$day,'bank_title'=>$item['bank_title'], 'account'=>$item['account'],'save_all'=>0, 'taken_all'=>0, 'date_time'=>$item['date_time'], 'last_balance'=>0, 'balance'=>0,
                         'due_balance'=>0, 'difference'=> 0];
                 }

                $tmp = ['day'=>$day,'bank_title'=>$item['bank_title'], 'account'=>$item['account'], 'save_all'=>$item['save_all'], 'taken_all'=>$item['taken_all'], 'date_time'=>$item['date_time'], 'last_balance'=>$account_last_record['balance'], 'balance'=>$item['balance'],
                    'due_balance'=>$account_last_record['balance'] + $item['save_all'] - $item['taken_all'], 'difference'=>$item['balance'] - ($account_last_record['balance'] + $item['save_all'] - $item['taken_all'])];
                array_push($new_data, $tmp);
            }

            $key_arrays = array();
            foreach ($new_data as $val )
            {
                $key_arrays[]=$val['bank_title'];
            }
            array_multisort($key_arrays,SORT_ASC,SORT_NUMERIC,$new_data);
            return $new_data;
    }

    private function get_data_by_last_record($src, $account)
    {
        $return_data = null;
        $data = $src['data'];
        foreach ($data as $item)
        {
            if( $item['account'] == $account )
            {
                $return_data = $item;
                break;
            }
        }
        return $return_data;
    }

    private function date_group($list)
    {
        $day_time = $list[0]['day_time'];
        $data_arr = array();
        $item_arr = array();

        foreach ($list as $row)
        {
            if( $row['day_time'] == $day_time )
            {
                $item_arr[] = $row;
                continue;
            }
            $data_arr[] = array('day'=>$day_time, 'data'=>$item_arr);
            $item_arr = array();
            $day_time =  date("Y-m-d", strtotime($day_time) - 3600 * 24);
            $item_arr[] = $row;
        }
        $data_arr[] = array('day'=>$day_time, 'data'=>$item_arr);
        return $data_arr;
    }

}
