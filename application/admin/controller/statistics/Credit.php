<?php

namespace app\admin\controller\statistics;

use app\common\controller\Backend;
use think\Db;

/**
 * 信用卡流水
 *
 * @icon fa fa-circle-o
 */
class Credit extends Backend
{

    public function index()
    {
        $time_arr = [];
        $updatetime = $this->request->get('updatetime');
        if( !empty($updatetime) )
        {
            $time_arr = explode(' - ',$updatetime);
        }

        $page_size_arr = array(10, 25, 50);
        $page_size = $this->request->get('ps');
        $page = $this->request->get('page');
        if( empty($page_size) )
        {
            $page_size = 10;
        }
        if( empty($page) )
        {
            $page = 1;
        }

        $seach_index = ($page - 1) * $page_size;
        if( count($time_arr) === 0 )
        {
                $total_record = Db::query("SELECT t_cc.id FROM (SELECT t_credit_card.id, t_credit_card.amount, t_df_order.rmb_money, DATE_FORMAT(t_credit_card.transtime,'%Y-%m-%d') as day_time FROM t_credit_card INNER JOIN t_df_order ON t_credit_card.order_id=t_df_order.id WHERE t_credit_card.is_recorded=1) as t_cc GROUP BY day_time");
        }else{
                $total_record = Db::query("SELECT t_cc.id FROM (SELECT t_credit_card.id, t_credit_card.amount, t_df_order.rmb_money, DATE_FORMAT(t_credit_card.transtime,'%Y-%m-%d') as day_time FROM t_credit_card INNER JOIN t_df_order ON t_credit_card.order_id=t_df_order.id WHERE t_credit_card.is_recorded=1 and t_credit_card.createtime>=? and t_credit_card.createtime<=?) as t_cc GROUP BY day_time", [strtotime($time_arr[0]), strtotime($time_arr[1])]);
        }
        $count_record = count($total_record);
        $page_num = ceil(($count_record/$page_size));
        if( count($time_arr) === 0 )
        {
                $query_list = Db::query("SELECT COUNT(*) as num, SUM(t_cc.amount) as tb, SUM(t_cc.rmb_money) as rmb, t_cc.day_time FROM (SELECT t_credit_card.id, t_credit_card.amount, t_df_order.rmb_money, DATE_FORMAT(t_credit_card.transtime,'%Y-%m-%d') as day_time FROM t_credit_card INNER JOIN t_df_order ON t_credit_card.order_id=t_df_order.id WHERE t_credit_card.is_recorded=1) as t_cc GROUP BY day_time ORDER BY id DESC LIMIT ?,?", [$seach_index,$page_size + $page_size]);
        }else{
                $query_list = Db::query("SELECT COUNT(*) as num, SUM(t_cc.amount) as tb, SUM(t_cc.rmb_money) as rmb, t_cc.day_time FROM (SELECT t_credit_card.id, t_credit_card.amount, t_df_order.rmb_money, DATE_FORMAT(t_credit_card.transtime,'%Y-%m-%d') as day_time FROM t_credit_card INNER JOIN t_df_order ON t_credit_card.order_id=t_df_order.id WHERE t_credit_card.is_recorded=1 and t_credit_card.createtime>=? and t_credit_card.createtime<=?) as t_cc GROUP BY day_time ORDER BY id DESC LIMIT ?,?", [strtotime($time_arr[0]), strtotime($time_arr[1]), $seach_index,$page_size + $page_size]);
        }

        if( count($query_list) > 0 )
        {
                $list = $query_list;
                $list_count = count($query_list);
                if( count($time_arr) === 0 )
                {
                    $query_money = Db::query("SELECT SUM(t_credit_card.amount) as tb, SUM(t_df_order.rmb_money) as rmb FROM t_credit_card INNER JOIN t_df_order ON t_credit_card.order_id=t_df_order.id WHERE t_credit_card.is_recorded=1");
                }else{
                    $query_money = Db::query("SELECT SUM(t_credit_card.amount) as tb, SUM(t_df_order.rmb_money) as rmb FROM t_credit_card INNER JOIN t_df_order ON t_credit_card.order_id=t_df_order.id WHERE t_credit_card.is_recorded=1 and t_credit_card.createtime>=? and t_credit_card.createtime<=?", [strtotime($time_arr[0]), strtotime($time_arr[1])]);
                }
                $total_money['tb'] = $query_money[0]['tb'];
                $total_money['rmb'] = $query_money[0]['rmb'];
        }else{
                $list = null;
                $list_count = null;
                $total_money['tb'] = 0;
                $total_money['rmb'] = 0;
        }

        if( !empty($updatetime) )
        {
            $this->view->assign('updatetime', $updatetime);
        }

        $this->view->assign([
            'total_money'       => $total_money,
            'size_arr'          => $page_size_arr,
            'list'       => $list,
            'list_count'       => $list_count,
            'count' => $count_record,
            'page_size' => $page_size,
            'page_num'   => $page_num,
            'page'  => $page,
            'begin' => $seach_index + 1,
            'end' => $seach_index + $page_size
        ]);
        /* 中间表格 */
        return $this->view->fetch();
    }


}
