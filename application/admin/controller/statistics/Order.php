<?php

namespace app\admin\controller\statistics;

use app\common\controller\Backend;
use think\Db;

/**
 * 代付订单
 *
 * @icon fa fa-circle-o
 */
class Order extends Backend
{

    public function index()
    {

        $time_arr = [];
        $updatetime = $this->request->get('updatetime');
        if( !empty($updatetime) )
        {
            $time_arr = explode(' - ',$updatetime);
        }

        $page_size_arr = array(10, 25, 50);
        $page_size = $this->request->get('ps');
        $page = $this->request->get('page');
        if( empty($page_size) )
        {
            $page_size = 10;
        }
        if( empty($page) )
        {
            $page = 1;
        }

        if( count($time_arr) === 0 )
        {
            $total_record = Db::query("SELECT count(*) as total FROM (SELECT id FROM t_df_order GROUP BY from_unixtime(createtime, '%Y-%m-%d')) as do");
        }else{
            $total_record = Db::query("SELECT count(*) as total FROM (SELECT id FROM t_df_order WHERE createtime>=? and createtime<=? GROUP BY from_unixtime(createtime, '%Y-%m-%d')) as do", [strtotime($time_arr[0]), strtotime($time_arr[1])]);
        }

        $count_record = $total_record[0]['total'];
        $page_num = ceil(($count_record/$page_size));
        $seach_index = ($page - 1) * $page_size;

        if( count($time_arr) === 0 )
        {
            $date_record = Db::query("SELECT from_unixtime(createtime, '%Y-%m-%d') as day_time FROM t_df_order WHERE createtime !=0 GROUP BY from_unixtime(createtime, '%Y-%m-%d') ORDER BY id DESC LIMIT ?,?", [$seach_index,$page_size]);
            $first_ele = reset($date_record);
            $end_ele = end($date_record);
            $begin_time = $end_ele['day_time'];
            $end_time = $first_ele['day_time'] . ' 23:59:59';
            $query_list = Db::query("SELECT tdo.day_time, tdo.type, COUNT(*) as num, SUM(tdo.tb_money) as tb,SUM(tdo.rmb_money) as rmb,SUM(tdo.profit_money) as profit FROM (SELECT * FROM t_df_order WHERE createtime>=unix_timestamp(?) and createtime <= unix_timestamp(?) and pay_type=2 and order_status=1) as tdo GROUP BY tdo.day_time,tdo.type ORDER BY tdo.id DESC", [$begin_time,$end_time]);
        }else{
            $query_list = Db::query("SELECT tdo.day_time, tdo.type, COUNT(*) as num, SUM(tdo.tb_money) as tb,SUM(tdo.rmb_money) as rmb,SUM(tdo.profit_money) as profit FROM (SELECT * FROM t_df_order WHERE createtime>=unix_timestamp(?) and createtime <= unix_timestamp(?) and pay_type=2 and order_status=1) as tdo GROUP BY tdo.day_time,tdo.type ORDER BY tdo.id DESC", [$time_arr[0],$time_arr[1]]);
        }

        if(count($query_list) > 0)
        {
            $day_list = $this->date_group($query_list);
            $list = $this->everyday_order_format($day_list);
            $list_count = count($list);
        }else{
            $list = null;
            $list_count = null;
        }

        $this->view->assign([
            'size_arr'          => $page_size_arr,
            'list'       => $list,
            'list_count'       => $list_count,
            'count' => $count_record,
            'page_size' => $page_size,
            'page_num'   => $page_num,
            'page'  => $page,
            'begin' => $seach_index + 1,
            'end' => $seach_index + $page_size
        ]);
        /* 中间表格 */
        return $this->view->fetch();
    }



    private function everyday_order_format($list)
    {
        $return_list = array();
        foreach ($list as $item)
        {
                $day_time = $item['day'];
                $format_arr = $this->order_type_array_format($item['data']);
                $number_arr = array_values( array_column($format_arr, 'num') );
                $tb_arr = array_values( array_column($format_arr, 'tb') );
                $rmb_arr = array_values( array_column($format_arr, 'rmb') );
                $profit_arr = array_values( array_column($format_arr, 'profit') );

                $tmp = array('day'=>$day_time, 'type'=>$format_arr, 'total'=>array('num'=>array_sum($number_arr), 'tb'=>array_sum($tb_arr), 'rmb'=>array_sum($rmb_arr), 'profit'=>array_sum($profit_arr)));
                array_push($return_list, $tmp);
        }
        return $return_list;
    }

    private function order_type_array_format( $data_arr )
    {
        $day_time = $data_arr[0]['day_time'];
        $return_data = $data_arr;
        $type_arr = array_values( array_column($data_arr, 'type') );

        for ( $i=0;$i<=7;$i++ )
        {
            if( !in_array($i, $type_arr) )
            {
                $return_data[] = array('day_time'=>$day_time, 'type'=>$i, 'num'=>0, 'tb'=>0, 'rmb'=>0, 'profit'=>0);
            }
        }

        $key_arrays = array();
        foreach ($return_data as $val )
        {
            $key_arrays[]=$val['type'];
        }
        array_multisort($key_arrays,SORT_ASC,SORT_NUMERIC,$return_data);

        return $return_data;
    }

    private function date_group($list)
    {
        $day_time = $list[0]['day_time'];
        $data_arr = array();
        $item_arr = array();

        foreach ($list as $row)
        {
            if( $row['day_time'] == $day_time )
            {
                $item_arr[] = $row;
                continue;
            }

            $data_arr[] = array('day'=>$day_time, 'data'=>$item_arr);
            $item_arr = array();
            $day_time =  date("Y-m-d", strtotime($day_time) - 3600 * 24);
            $item_arr[] = $row;
        }
        $data_arr[] = array('day'=>$day_time, 'data'=>$item_arr);
        return $data_arr;
    }

}
