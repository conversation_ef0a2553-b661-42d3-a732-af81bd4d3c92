<?php

namespace app\admin\controller\statistics;

use app\common\controller\Backend;
use think\Db;

/**
 * 超商申请码
 *
 * @icon fa fa-circle-o
 */
class Supercode extends Backend
{

    public function index()
    {
        $time_arr = [];
        $updatetime = $this->request->get('updatetime');
        if( !empty($updatetime) )
        {
            $time_arr = explode(' - ',$updatetime);
        }

        $page_size_arr = array(10, 25, 50);
        $page_size = $this->request->get('ps');
        $page = $this->request->get('page');
        if( empty($page_size) )
        {
            $page_size = 10;
        }
        if( empty($page) )
        {
            $page = 1;
        }

        $seach_index = ($page - 1) * $page_size;
        if( count($time_arr) === 0 )
        {
            $total_record = Db::query("SELECT t_cs.id FROM (SELECT t_super_code.id, t_super_code.trade_amt, t_df_order.rmb_money, from_unixtime(t_super_code.createtime,'%Y-%m-%d') as day_time FROM t_super_code INNER JOIN t_df_order ON t_super_code.order_id=t_df_order.id) as t_cs GROUP BY day_time");
        }else{
            $total_record = Db::query("SELECT t_cs.id FROM (SELECT t_super_code.id, t_super_code.trade_amt, t_df_order.rmb_money, from_unixtime(t_super_code.createtime,'%Y-%m-%d') as day_time FROM t_super_code INNER JOIN t_df_order ON t_super_code.order_id=t_df_order.id WHERE t_super_code.createtime>=? and t_super_code.createtime<=? ) as t_cs GROUP BY day_time", [strtotime($time_arr[0]), strtotime($time_arr[1])]);
        }
        $count_record = count($total_record);
        $page_num = ceil(($count_record/$page_size));
        if( count($time_arr) === 0 )
        {
            $query_list = Db::query("SELECT COUNT(*) as num, SUM(t_cs.trade_amt) as tb, SUM(t_cs.rmb_money) as rmb, t_cs.day_time FROM (SELECT t_super_code.id, t_super_code.trade_amt, t_df_order.rmb_money, from_unixtime(t_super_code.createtime,'%Y-%m-%d') as day_time FROM t_super_code INNER JOIN t_df_order ON t_super_code.order_id=t_df_order.id) as t_cs GROUP BY day_time ORDER BY id DESC LIMIT ?,?", [$seach_index,$page_size + $page_size]);
        }else{
            $query_list = Db::query("SELECT COUNT(*) as num, SUM(t_cs.trade_amt) as tb, SUM(t_cs.rmb_money) as rmb, t_cs.day_time FROM (SELECT t_super_code.id, t_super_code.trade_amt, t_df_order.rmb_money, from_unixtime(t_super_code.createtime,'%Y-%m-%d') as day_time FROM t_super_code INNER JOIN t_df_order ON t_super_code.order_id=t_df_order.id WHERE t_super_code.createtime>=? and t_super_code.createtime<=?) as t_cs GROUP BY day_time ORDER BY id DESC LIMIT ?,?", [strtotime($time_arr[0]), strtotime($time_arr[1]), $seach_index, $page_size + $page_size]);
        }

        if( count($query_list) > 0 )
        {
            $list = $query_list;
            $list_count = count($query_list);
            if( count($time_arr) === 0 )
            {
                $query_money = Db::query("SELECT SUM(t_df_order.rmb_money) as rmb,SUM(t_super_code.trade_amt) as tb FROM t_super_code INNER JOIN t_df_order ON t_super_code.order_id=t_df_order.id");
            }else{
                $query_money = Db::query("SELECT SUM(t_df_order.rmb_money) as rmb,SUM(t_super_code.trade_amt) as tb FROM t_super_code INNER JOIN t_df_order ON t_super_code.order_id=t_df_order.id WHERE t_super_code.createtime>=? and t_super_code.createtime<=?", [strtotime($time_arr[0]), strtotime($time_arr[1])]);
            }
            $total_money['tb'] = $query_money[0]['tb'];
            $total_money['rmb'] = $query_money[0]['rmb'];
        }else{
            $list = null;
            $list_count = null;
            $total_money['tb'] = 0;
            $total_money['rmb'] = 0;
        }

        if( !empty($updatetime) )
        {
            $this->view->assign('updatetime', $updatetime);
        }

        $this->view->assign([
            'total_money'       => $total_money,
            'size_arr'          => $page_size_arr,
            'list'       => $list,
            'list_count'       => $list_count,
            'count' => $count_record,
            'page_size' => $page_size,
            'page_num'   => $page_num,
            'page'  => $page,
            'begin' => $seach_index + 1,
            'end' => $seach_index + $page_size
        ]);
        /* 中间表格 */
        return $this->view->fetch();
    }
    

}
