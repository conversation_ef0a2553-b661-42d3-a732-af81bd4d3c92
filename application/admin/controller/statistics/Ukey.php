<?php

namespace app\admin\controller\statistics;

use app\common\controller\Backend;
use think\Db;
/**
 * 
 *
 * @icon fa fa-circle-o
 */
class Ukey extends Backend
{

    public function index()
    {
        $time_arr = [];
        $updatetime = $this->request->get('updatetime');
        if( !empty($updatetime) )
        {
            $time_arr = explode(' - ',$updatetime);
        }

        $page_size_arr = array(10, 25, 50);
        $page_size = $this->request->get('ps');
        $page = $this->request->get('page');
        if( empty($page_size) )
        {
            $page_size = 10;
        }
        if( empty($page) )
        {
            $page = 1;
        }

        $seach_index = ($page - 1) * $page_size;
        if( count($time_arr) === 0 )
        {
            $total_record = Db::query("SELECT COUNT(*) num FROM t_ukey_pay WHERE status=1 and remark='代付成功' GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d'), alipay_account ORDER BY id DESC");
        }else{
            $total_record = Db::query("SELECT COUNT(*) num FROM t_ukey_pay WHERE status=1 and remark='代付成功' and unix_timestamp(create_time)>=? and unix_timestamp(create_time)<=? GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d'), alipay_account ORDER BY id DESC", [strtotime($time_arr[0]), strtotime($time_arr[1])]);
        }
        $count_record = count($total_record);
        $page_num = ceil(($count_record/$page_size));
        if( count($time_arr) === 0 )
        {
            $query_list = Db::query("SELECT alipay_account, COUNT(*) as num, SUM(price) as money, DATE_FORMAT(create_time, '%Y-%m-%d') as day_time FROM t_ukey_pay WHERE status=1 and remark='代付成功' GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d'), alipay_account ORDER BY id DESC LIMIT ?,?", [$seach_index,$page_size + $page_size]);
        }else{
            $query_list = Db::query("SELECT alipay_account, COUNT(*) as num, SUM(price) as money, DATE_FORMAT(create_time, '%Y-%m-%d') as day_time FROM t_ukey_pay WHERE status=1 and remark='代付成功' and unix_timestamp(create_time)>=? and unix_timestamp(create_time)<=? GROUP BY DATE_FORMAT(create_time, '%Y-%m-%d'), alipay_account ORDER BY id DESC LIMIT ?,?", [strtotime($time_arr[0]), strtotime($time_arr[1]), $seach_index,$page_size + $page_size]);
        }

        if( count($query_list) > 0 )
        {
            $list = $query_list;
            $list_count = count($query_list);
            if( count($time_arr) === 0 )
            {
                $query_money = Db::query("SELECT SUM(price) as money FROM t_ukey_pay WHERE status=1 and remark='代付成功'");
            }else{
                $query_money = Db::query("SELECT SUM(price) as money FROM t_ukey_pay WHERE status=1 and remark='代付成功' and unix_timestamp(create_time)>=? and unix_timestamp(create_time)<=?", [strtotime($time_arr[0]), strtotime($time_arr[1])]);
            }
            $total_money = $query_money[0]['money'];
        }else{
            $list = null;
            $list_count = null;
            $total_money = 0;
        }

        if( !empty($updatetime) )
        {
            $this->view->assign('updatetime', $updatetime);
        }

        $this->view->assign([
            'total_money'       => $total_money,
            'size_arr'          => $page_size_arr,
            'list'       => $list,
            'list_count'       => $list_count,
            'count' => $count_record,
            'page_size' => $page_size,
            'page_num'   => $page_num,
            'page'  => $page,
            'begin' => $seach_index + 1,
            'end' => $seach_index + $page_size
        ]);
        /* 中间表格 */
        return $this->view->fetch();
    }

}
