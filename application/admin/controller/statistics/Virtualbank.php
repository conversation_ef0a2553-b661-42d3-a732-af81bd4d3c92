<?php

namespace app\admin\controller\statistics;

use app\common\controller\Backend;
use think\Db;
/**
 * 
 *
 * @icon fa fa-circle-o
 */
class Virtualbank extends Backend
{


    public function index()
    {
        $time_arr = [];
        $updatetime = $this->request->get('updatetime');
        if( !empty($updatetime) )
        {
            $time_arr = explode(' - ',$updatetime);
        }

        $page_size_arr = array(10, 25, 50);
        $page_size = $this->request->get('ps');
        $page = $this->request->get('page');
        if( empty($page_size) )
        {
            $page_size = 10;
        }
        if( empty($page) )
        {
            $page = 1;
        }

        $seach_index = ($page - 1) * $page_size;
        if( count($time_arr) === 0 )
        {
            $total_record = Db::query("SELECT t_virtual_bank.id FROM t_virtual_bank INNER JOIN t_df_order ON t_virtual_bank.order_id=t_df_order.id WHERE t_virtual_bank.type=1 GROUP BY from_unixtime(t_virtual_bank.createtime, '%Y-%m-%d'), t_virtual_bank.in_account");
        }else{
            $total_record = Db::query("SELECT t_virtual_bank.id FROM t_virtual_bank INNER JOIN t_df_order ON t_virtual_bank.order_id=t_df_order.id WHERE t_virtual_bank.type=1 and t_virtual_bank.createtime>=? and t_virtual_bank.createtime<=? GROUP BY from_unixtime(t_virtual_bank.createtime, '%Y-%m-%d'), t_virtual_bank.in_account", [strtotime($time_arr[0]), strtotime($time_arr[1])]);
        }
        $count_record = count($total_record);
        $page_num = ceil(($count_record/$page_size));
        if( count($time_arr) === 0 )
        {
            $query_list = Db::query("SELECT tt.in_account, COUNT(*) as num, SUM(tt.order_money) as tb, SUM(tt.rmb_money) as rmb, tt.day_time FROM (SELECT t_virtual_bank.id,t_virtual_bank.in_account,t_virtual_bank.category,t_df_order.rmb_money,t_virtual_bank.order_money,from_unixtime(t_virtual_bank.createtime, '%Y-%m-%d') as day_time FROM t_virtual_bank INNER JOIN t_df_order ON t_virtual_bank.order_id=t_df_order.id WHERE t_virtual_bank.type=1) as tt GROUP BY tt.day_time, tt.in_account ORDER BY id DESC LIMIT ?,?", [$seach_index,$page_size + $page_size]);
        }else{
            $query_list = Db::query("SELECT tt.in_account, COUNT(*) as num, SUM(tt.order_money) as tb, SUM(tt.rmb_money) as rmb, tt.day_time FROM (SELECT t_virtual_bank.id,t_virtual_bank.in_account,t_virtual_bank.category,t_df_order.rmb_money,t_virtual_bank.order_money,from_unixtime(t_virtual_bank.createtime, '%Y-%m-%d') as day_time FROM t_virtual_bank INNER JOIN t_df_order ON t_virtual_bank.order_id=t_df_order.id WHERE t_virtual_bank.type=1 and t_virtual_bank.createtime>=? and t_virtual_bank.createtime<=?) as tt GROUP BY tt.day_time, tt.in_account ORDER BY id DESC LIMIT ?,?", [strtotime($time_arr[0]), strtotime($time_arr[1]), $seach_index, $page_size + $page_size]);
        }

        if( count($query_list) > 0 )
        {
            $list = $query_list;
            $list_count = count($query_list);
            if( count($time_arr) === 0 )
            {
                $query_money = Db::query("SELECT SUM(t_df_order.rmb_money) as rmb,SUM(t_virtual_bank.order_money) as tb FROM t_virtual_bank INNER JOIN t_df_order ON t_virtual_bank.order_id=t_df_order.id WHERE t_virtual_bank.type=1");
            }else{
                $query_money = Db::query("SELECT SUM(t_df_order.rmb_money) as rmb,SUM(t_virtual_bank.order_money) as tb FROM t_virtual_bank INNER JOIN t_df_order ON t_virtual_bank.order_id=t_df_order.id WHERE t_virtual_bank.type=1 and t_virtual_bank.createtime>=? and t_virtual_bank.createtime<=?", [strtotime($time_arr[0]), strtotime($time_arr[1])]);
            }
            $total_money['tb'] = $query_money[0]['tb'];
            $total_money['rmb'] = $query_money[0]['rmb'];
        }else{
            $list = null;
            $list_count = null;
            $total_money['tb'] = 0;
            $total_money['rmb'] = 0;
        }

        if( !empty($updatetime) )
        {
            $this->view->assign('updatetime', $updatetime);
        }

        $this->view->assign([
            'total_money'       => $total_money,
            'size_arr'          => $page_size_arr,
            'list'       => $list,
            'list_count'       => $list_count,
            'count' => $count_record,
            'page_size' => $page_size,
            'page_num'   => $page_num,
            'page'  => $page,
            'begin' => $seach_index + 1,
            'end' => $seach_index + $page_size
        ]);
        /* 中间表格 */
        return $this->view->fetch();
    }





    

}
