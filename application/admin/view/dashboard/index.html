<style type="text/css">
    .sm-st {
        background: #fff;
        padding: 20px;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
        margin-bottom: 20px;
        -webkit-box-shadow: 0 1px 0px rgba(0, 0, 0, 0.05);
        box-shadow: 0 1px 0px rgba(0, 0, 0, 0.05);
    } 

    .sm-st-icon {
        width: 60px;
        height: 60px;
        display: inline-block;
        line-height: 60px;
        text-align: center;
        font-size: 30px;
        background: #eee;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        border-radius: 5px;
        float: left;
        margin-right: 10px;
        color: #fff;
    }

    .sm-st-info {
        font-size: 12px;
        padding-top: 2px;
    }

    .sm-st-info span {
        display: block;
        font-size: 24px;
        font-weight: 600;
    }

    .orange {
        background: #fa8564 !important;
    }

    .tar {
        background: #45cf95 !important;
    }

    .sm-st .green {
        background: #86ba41 !important;
    }

    .pink {
        background: #AC75F0 !important;
    }

    .yellow-b {
        background: #fdd752 !important;
    }

    .stat-elem {

        background-color: #fff;
        padding: 18px;
        border-radius: 40px;

    }

    .stat-info {
        text-align: center;
        background-color: #fff;
        border-radius: 5px;
        margin-top: -5px;
        padding: 8px;
        -webkit-box-shadow: 0 1px 0px rgba(0, 0, 0, 0.05);
        box-shadow: 0 1px 0px rgba(0, 0, 0, 0.05);
        font-style: italic;
    }

    .stat-icon {
        text-align: center;
        margin-bottom: 5px;
    }

    .st-red {
        background-color: #F05050;
    }

    .st-green {
        background-color: #27C24C;
    }

    .st-violet {
        background-color: #7266ba;
    }

    .st-blue {
        background-color: #23b7e5;
    }

    .stats .stat-icon {
        color: #28bb9c;
        display: inline-block;
        font-size: 26px;
        text-align: center;
        vertical-align: middle;
        width: 50px;
        float: left;
    }

    .stat {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
        margin-right: 10px;
    }

    .stat .value {
        font-size: 20px;
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 500;
    }

    .stat .name {
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .stat.lg .value {
        font-size: 26px;
        line-height: 28px;
    }

    .stat.lg .name {
        font-size: 16px;
    }

    .stat-col .progress {
        height: 2px;
    }

    .stat-col .progress-bar {
        line-height: 2px;
        height: 2px;
    }

    .item {
        padding: 30px 0;
    }
</style>
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null, false)}
        <ul class="nav nav-tabs">
            <li class="active"><a href="#one" data-toggle="tab">{:__('Dashboard')}</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">

                <div class="row">
                    <div class="col-sm-3 col-xs-6">
                        <div class="sm-st clearfix">
                            <span class="sm-st-icon st-red"><i class="fa fa-users"></i></span>
                            <div class="sm-st-info">
                                <span>{$user_count}</span>
                                {:__('Total user')}
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3 col-xs-6">
                        <div class="sm-st clearfix">
                            <span class="sm-st-icon st-violet"><i class="fa fa-book"></i></span>
                            <div class="sm-st-info">
                                <span>{$totalviews}</span>
                                {:__('Total view')}
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3 col-xs-6">
                        <div class="sm-st clearfix">
                            <span class="sm-st-icon st-blue"><i class="fa fa-shopping-bag"></i></span>
                            <div class="sm-st-info">
                                <span>{$order_count}</span>
                                {:__('Total order')}
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-3 col-xs-6">
                        <div class="sm-st clearfix">
                            <span class="sm-st-icon st-green"><i class="fa fa-cny"></i></span>
                            <div class="sm-st-info">
                                <span>{$all_count.all_tb_money}</span>
                                {:__('Total order amount')}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-8">
                        <div id="echart" style="height:200px;width:100%;"></div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card sameheight-item stats">
                            <div class="card-block">
                                <div class="row row-sm stats-container">
                                    <div class="col-xs-6 stat-col">
                                        <div class="stat-icon"><i class="fa fa-rocket"></i></div>
                                        <div class="stat">
											<a href="<?=url('user/user',array('createtime'=>date('Y-m-d 00:00:00').' - '.date('Y-m-d 23:59:59')))?>">
												<div class="value"> {$day_user_count}</div>
												<div class="name"> {:__('Today user signup')}</div>
											</a>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-success" style="width: 30%"></div>
                                        </div>
                                    </div>
                                    <div class="col-xs-6 stat-col">
                                        <div class="stat-icon"><i class="fa fa-shopping-cart"></i></div>
                                        <div class="stat">
                                            <a href="<?=url('user/user',array('logintime'=>date('Y-m-d 00:00:00').' - '.date('Y-m-d 23:59:59')))?>"></a>
                                                <div class="value"> {$user_login}</div>
                                                <div class="name"> {:__('Today user login')}</div>
                                            </a>
                                        </div>
                                        <div class="progress">2019-12-25 00:00:00 - 2019-12-25 23:59:59
                                            <div class="progress-bar progress-bar-success" style="width: 25%"></div>
                                        </div>
                                    </div>

                                    <div class="col-xs-6  stat-col">
                                        <div class="stat-icon"><i class="fa fa-line-chart"></i></div>
                                        <div class="stat">
											<a href="<?=url('dashboard/yreg_order')?>">
												<div class="value"> {$reg_df_count}</div>
												<div class="name"> {:__('Yesterday reg order')}</div>
											</a>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-success" style="width: 25%"></div>
                                        </div>
                                    </div>


                                    <div class="col-xs-6  stat-col">
                                        <div class="stat-icon"><i class="fa fa-line-chart"></i></div>
                                        <div class="stat">
											<a href="<?=url('oprder/all',array('createtime'=>date('Y-m-d 00:00:00').' - '.date('Y-m-d 23:59:59')))?>">
												<div class="value"> {$day_order_count}</div>
												<div class="name"> {:__('Today order')}</div>
											</a>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-success" style="width: 25%"></div>
                                        </div>
                                    </div>
                                    <div class="col-xs-6  stat-col">
                                        <div class="stat-icon"><i class="fa fa-users"></i></div>
                                        <div class="stat">
											<a href="<?=url('oprder/all',array('pay_type'=>'2','order_status'=>'0'))?>">
												<div class="value"> {$untreated_count}</div>
												<div class="name"> {:__('Unsettle order')}</div>
											</a>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-success" style="width: 25%"></div>
                                        </div>
                                    </div>
                                    <div class="col-xs-6  stat-col">
                                        <div class="stat-icon"><i class="fa fa-dollar"></i></div>
                                        <div class="stat">
											<a href="<?=url('user/appeal',array('is_read'=>1))?>">
												<div class="value"> {$appeal_count}</div>
												<div class="name"> {:__('Appeal_count')}</div>
											</a>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-success" style="width: 25%"></div>
                                        </div>
                                    </div>
                                    <div class="col-xs-6 stat-col">
                                        <div class="stat-icon"><i class="fa fa-dollar"></i></div>
                                        <div class="stat">
											<a href="<?=url('details/refund',array('status'=>'0'))?>">
												<div class="value"> {$part_count}</div>
												<div class="name"> {:__('Refund part')}</div>
											</a>
                                        </div>
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-success" style="width: 25%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row" style="margin-top:15px;">

                    <div class="col-lg-12">
                    </div>
                    <div class="col-xs-6 col-md-3">
                        <div class="panel bg-blue">
                            <div class="panel-body">
                                <div class="panel-title">
                                    <h5>{:__('Today is turnover')} TWD</h5>
                                </div>
                                <div class="panel-content">
                                    <h1 class="no-margins">{$deal_count.all_tb_money}</h1>
                                    <small>{:__('Explain count tips')}</small>
                                </div>
                            </div>
                        </div>
                    </div>
					<div class="col-xs-6 col-md-3">
                        <div class="panel bg-blue">
                            <div class="panel-body">
                                <div class="panel-title">
                                    <h5>{:__('Today is turnover')} RMB</h5>
                                </div>
                                <div class="panel-content">
                                    <h1 class="no-margins">{$deal_count.all_rmb_money}</h1>
                                    <small>{:__('Explain count tips')}</small>
                                </div>
                            </div>
                        </div>
                    </div>
					<div class="col-xs-6 col-md-3">
                        <div class="panel bg-blue">
                            <div class="panel-body">
                                <div class="panel-title">
                                    <h5>{:__('Yesterday is turnover')} TWD</h5>
                                </div>
                                <div class="panel-content">
                                    <h1 class="no-margins">{$yester_count.all_tb_money}</h1>
                                    <small>{:__('Explain count tips')}</small>
                                </div>
                            </div>
                        </div>
                    </div>
					<div class="col-xs-6 col-md-3">
                        <div class="panel bg-blue">
                            <div class="panel-body">
                                <div class="panel-title">
                                    <h5>{:__('Yesterday is turnover')} RMB</h5>
                                </div>
                                <div class="panel-content">
                                    <h1 class="no-margins">{$yester_count.all_rmb_money}</h1>
                                    <small>{:__('Explain count tips')}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-lg-4">
                        <div class="box box-danger">
                            <div class="box-header with-border">
                                <h3 class="box-title">{:__('Conversion to artificial labor')}</h3>

                                <div class="box-tools pull-right">
                                </div>
                            </div>
                            <div class="box-body">
                                <ul class="nav nav-pills nav-stacked">
								<?php
									if($artificial_order){
										foreach($artificial_order as $ls){
								?>
									<li><a href="<?=url('oprder/all',array('order_status'=>$ls['order_status']))?>">订单号：<?=$ls['order_no']?> 状态：<?=$pay_type[$ls['pay_type']]?> 下单时间：<?=date('Y-m-d H:i:s',$ls['createtime'])?></a></li>
								<?php
										}
									}else{
								?>
									<li><a href="javascript:;">暂无订单</a></li>
								<?php
									}
								?>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="box box-success">
                            <div class="box-header with-border">
                                <h3 class="box-title">{:__('suspend')}</h3>

                                <div class="box-tools pull-right">
                                </div>
                            </div>
                            <div class="box-body">
                                <ul class="nav nav-pills nav-stacked">
                                <?php
									if($suspend_order){
										foreach($suspend_order as $ls){
								?>
									<li><a href="<?=url('oprder/all',array('order_status'=>$ls['order_status']))?>">订单号：<?=$ls['order_no']?> 状态：<?=$pay_type[$ls['pay_type']]?> 下单时间：<?=date('Y-m-d H:i:s',$ls['createtime'])?></a></li>
								<?php
										}
									}else{
								?>
									<li><a href="javascript:;">暂无订单</a></li>
								<?php
									}
								?>
                                </ul>
                            </div>
                        </div>
                    </div>
					<div class="col-lg-4">
                        <div class="box box-success">
                            <div class="box-header with-border">
                                <h3 class="box-title">{:__('Multi payment')}</h3>

                                <div class="box-tools pull-right">
                                </div>
                            </div>
                            <div class="box-body">
                                <ul class="nav nav-pills nav-stacked">
                                <?php
									if($pay_order){
										foreach($pay_order as $ls){
								?>
									<li><a href="<?=url('oprder/all',array('order_status'=>$ls['order_status']))?>">订单号：<?=$ls['order_no']?> 状态：<?=$pay_type[$ls['pay_type']]?> 下单时间：<?=date('Y-m-d H:i:s',$ls['createtime'])?></a></li>
								<?php
										}
									}else{
								?>
									<li><a href="javascript:;">暂无订单</a></li>
								<?php
									}
								?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
				<div class="row">
                    <div class="col-lg-4">
                        <div class="box box-danger">
                            <div class="box-header with-border">
                                <h3 class="box-title">直播订单</h3>

                                <div class="box-tools pull-right">
                                </div>
                            </div>
                            <div class="box-body">
                                <ul class="nav nav-pills nav-stacked">
								<?php
									if($order_live){
										foreach($order_live as $ls){
								?>
									<li><a href="<?=url('oprder/live',array('order_status'=>'2'))?>">订单号：<?=$ls['order_no']?> 状态：<?=$pay_type[$ls['pay_type']]?> 下单时间：<?=date('Y-m-d H:i:s',$ls['createtime'])?></a></li>
								<?php
										}
									}else{
								?>
									<li><a href="javascript:;">暂无订单</a></li>
								<?php
									}
								?>
                                </ul>
                            </div>
                        </div>
                    </div>
					<div class="col-lg-4">
                        <div class="box box-danger">
                            <div class="box-header with-border">
                                <h3 class="box-title">其他订单</h3>

                                <div class="box-tools pull-right">
                                </div>
                            </div>
                            <div class="box-body">
                                <ul class="nav nav-pills nav-stacked">
								<?php
									if($order_other){
										foreach($order_other as $ls){
								?>
									<li><a href="<?=url('oprder/other',array('order_status'=>'2'))?>">订单号：<?=$ls['order_no']?> 状态：<?=$pay_type[$ls['pay_type']]?> 下单时间：<?=date('Y-m-d H:i:s',$ls['createtime'])?></a></li>
								<?php
										}
									}else{
								?>
									<li><a href="javascript:;">暂无订单</a></li>
								<?php
									}
								?>
                                </ul>
                            </div>
                        </div>
                    </div>
					<div class="col-lg-4">
                        <div class="box box-danger">
                            <div class="box-header with-border">
                                <h3 class="box-title">游戏订单</h3>

                                <div class="box-tools pull-right">
                                </div>
                            </div>
                            <div class="box-body">
                                <ul class="nav nav-pills nav-stacked">
								<?php
									if($order_game){
										foreach($order_game as $ls){
								?>
									<li><a href="<?=url('oprder/game',array('order_status'=>'2'))?>">订单号：<?=$ls['order_no']?> 状态：<?=$pay_type[$ls['pay_type']]?> 下单时间：<?=date('Y-m-d H:i:s',$ls['createtime'])?></a></li>
								<?php
										}
									}else{
								?>
									<li><a href="javascript:;">暂无订单</a></li>
								<?php
									}
								?>
                                </ul>
                            </div>
                        </div>
                    </div>
				</div>
				
				<div class="row">
					<div class="col-lg-4">
                        <div class="box box-danger">
                            <div class="box-header with-border">
                                <h3 class="box-title">新代付订单</h3>

                                <div class="box-tools pull-right">
                                </div>
                            </div>
                            <div class="box-body">
                                <ul class="nav nav-pills nav-stacked">
								<?php
									if($order_new_df){
										foreach($order_new_df as $ls){
								?>
									<li><a href="<?=url('oprder/newdf',array('order_status'=>$ls['order_status'],'pay_type'=>$ls['pay_type']))?>">订单号：<?=$ls['order_no']?> 状态：<?=$pay_type[$ls['pay_type']]?> 下单时间：<?=date('Y-m-d H:i:s',$ls['createtime'])?></a></li>
								<?php
										}
									}else{
								?>
									<li><a href="javascript:;">暂无订单</a></li>
								<?php
									}
								?>
                                </ul>
                            </div>
                        </div>
                    </div>
					<div class="col-lg-4">
                        <div class="box box-danger">
                            <div class="box-header with-border">
                                <h3 class="box-title">集运委托待称重</h3>

                                <div class="box-tools pull-right">
                                </div>
                            </div>
                            <div class="box-body">
                                <ul class="nav nav-pills nav-stacked">
								<?php
									if($jiyun_weituo){
										foreach($jiyun_weituo as $ls){
								?>
									<li><a href="<?=url('jiyun/index',array('type'=>'0'))?>">委托单号：<?=$ls['entrust_no']?>  下单时间：<?=date('Y-m-d H:i:s',$ls['createtime'])?></a></li>
								<?php
										}
									}else{
								?>
									<li><a href="javascript:;">暂无订单</a></li>
								<?php
									}
								?>
                                </ul>
                            </div>
                        </div>
                    </div>
					<div class="col-lg-4">
                        <div class="box box-danger">
                            <div class="box-header with-border">
                                <h3 class="box-title">集运订单待发货</h3>

                                <div class="box-tools pull-right">
                                </div>
                            </div>
                            <div class="box-body">
                                <ul class="nav nav-pills nav-stacked">
								<?php
									if($jiyun_order){
										foreach($jiyun_order as $ls){
								?>
									<li><a href="<?=url('jiyun/order',array('order_status'=>'3'))?>">委托单号：<?=$ls['order_no']?>  下单时间：<?=date('Y-m-d H:i:s',$ls['createtime'])?></a></li>
								<?php
										}
									}else{
								?>
									<li><a href="javascript:;">暂无订单</a></li>
								<?php
									}
								?>
                                </ul>
                            </div>
                        </div>
                    </div>
				</div>
				<div class="row">
					<div class="col-lg-4">
                        <div class="box box-success">
                            <div class="box-header with-border">
                                <h3 class="box-title">{:__('Card_one')}</h3>

                                <div class="box-tools pull-right">
                                </div>
                            </div>
                            <div class="box-body">
                                <ul class="nav nav-pills nav-stacked">
                                <?php
									if($card){
										foreach($card as $ls){
								?>
									<li><a href="<?=url('authentication/cards',array('is_state'=>$ls['is_state']))?>"><?=$ls['username']?>申请开通身份证一次审核-<?=date('Y-m-d H:i:s',$ls['createtime'])?></a></li>
								<?php
										}
									}else{
								?>
									<li><a href="javascript:;">暂无订单</a></li>
								<?php
									}
								?>
                                </ul>
                            </div>
                        </div>
                    </div>
					<div class="col-lg-4">
                        <div class="box box-success">
                            <div class="box-header with-border">
                                <h3 class="box-title">金融审核</h3>

                                <div class="box-tools pull-right">
                                </div>
                            </div>
                            <div class="box-body">
                                <ul class="nav nav-pills nav-stacked">
                                <?php
									if($card_two){
										foreach($card_two as $ls){
								?>
									<li><a href="<?=url('authentication/cardtwo',array('is_state'=>$ls['is_state']))?>"><?=$ls['username']?>金融审核需要审核-<?=date('Y-m-d H:i:s',$ls['createtime'])?></a></li>
								<?php
										}
									}else{
								?>
									<li><a href="javascript:;">暂无信息</a></li>
								<?php
									}
								?>
                                </ul>
                            </div>
                        </div>
                    </div>
					<div class="col-lg-4">
                        <div class="box box-success">
                            <div class="box-header with-border">
                                <h3 class="box-title">{:__('Super_quotient')}</h3>

                                <div class="box-tools pull-right">
                                </div>
                            </div>
                            <div class="box-body">
                                <ul class="nav nav-pills nav-stacked">
                                <?php
									if($super_quotient){
										foreach($super_quotient as $ls){
								?>
									<li><a href="<?=url('authentication/chao',array('type'=>$ls['type']))?>"><?=$ls['username']?>申请开通超商-<?=date('Y-m-d H:i:s',$ls['createtime'])?></a></li>
								<?php
										}
									}else{
								?>
									<li><a href="javascript:;">暂无信息</a></li>
								<?php
									}
								?>
                                </ul>
                            </div>
                        </div>
                    </div>
				</div>
				<div class="row">
					<div class="col-lg-4">
                        <div class="box box-success">
                            <div class="box-header with-border">
                                <h3 class="box-title">{:__('Usdt')}</h3>

                                <div class="box-tools pull-right">
                                </div>
                            </div>
                            <div class="box-body">
                                <ul class="nav nav-pills nav-stacked">
                                <?php
									if($usdt){
										foreach($usdt as $ls){
								?>
									<li><a href="<?=url('virtual/order',array('pay_type'=>$ls['type'],'order_status'=>$ls['order_status']))?>">订单号：<?=$ls['order_no']?> 状态：<?=$pay_type[$ls['pay_type']]?> 下单时间：<?=date('Y-m-d H:i:s',$ls['createtime'])?></a></li>
								<?php
										}
									}else{
								?>
									<li><a href="javascript:;">暂无订单</a></li>
								<?php
									}
								?>
                                </ul>
                            </div>
                        </div>
                    </div>
					<div class="col-lg-4">
                        <div class="box box-success">
                            <div class="box-header with-border">
                                <h3 class="box-title">發票回收</h3>

                                <div class="box-tools pull-right">
                                </div>
                            </div>
                            <div class="box-body">
                                <ul class="nav nav-pills nav-stacked">
                                <?php
									if($invoice){
										foreach($invoice as $ls){
								?>
									<li><a href="<?=url('invoice/recovery',array('invoice_status'=>$ls['invoice_status']))?>">订单号：<?=$ls['order_no']?> 状态：审核中 下单时间：<?=date('Y-m-d H:i:s',$ls['createtime'])?></a></li>
								<?php
										}
									}else{
								?>
									<li><a href="javascript:;">暂无订单</a></li>
								<?php
									}
								?>
                                </ul>
                            </div>
                        </div>
                    </div>
				</div>
            </div>
            <div class="tab-pane fade" id="two">
                <div class="row">
                    <div class="col-xs-12">
                        {:__('Custom zone')}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
   
	
    var Orderdata = {
        column: <?=$day_array?>,
        paydata: <?=$paylist?>,
        createdata:<?=$createlist?>,
    };
</script>