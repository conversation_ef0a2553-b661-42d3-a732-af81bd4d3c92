<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-type" data-rule="required" class="form-control" name="row[type]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Order_no')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-order_no" data-rule="required" class="form-control" name="row[order_no]" type="text" value="''">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Tb_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-tb_money" data-rule="required" class="form-control" name="row[tb_money]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Rmb_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-rmb_money" data-rule="required" class="form-control" step="0.01" name="row[rmb_money]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Balance_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-balance_money" data-rule="required" class="form-control" step="0.01" name="row[balance_money]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Actual_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-actual_money" data-rule="required" class="form-control" name="row[actual_money]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Receipts_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-receipts_money" data-rule="required" class="form-control" name="row[receipts_money]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Service')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-service" data-rule="required" class="form-control" name="row[service]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Exchange')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-exchange" class="form-control" step="0.00001" name="row[exchange]" type="number" value="NULL">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Site_exchange')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-site_exchange" data-rule="required" class="form-control" step="0.00001" name="row[site_exchange]" type="number" value="0.00000">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Profit_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-profit_money" class="form-control" step="0.01" name="row[profit_money]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Live_url')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-live_url" class="form-control" name="row[live_url]" type="text" value="''">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Live_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-live_id" data-rule="required" data-source="live/index" class="form-control selectpage" name="row[live_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Live_account')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-live_account" class="form-control" name="row[live_account]" type="text" value="''">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Live_pwd')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-live_pwd" class="form-control" name="row[live_pwd]" type="text" value="''">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Live_value')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-live_value" class="form-control" name="row[live_value]" type="text" value="''">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Live_game')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-live_game" class="form-control" name="row[live_game]" type="text" value="''">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Live_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-live_type" class="form-control" name="row[live_type]" type="text" value="''">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Live_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-live_money" class="form-control" name="row[live_money]" type="text" value="''">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Cs_code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-cs_code" data-rule="required" class="form-control" name="row[cs_code]" type="text" value="''">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay_status" data-rule="required" class="form-control" name="row[pay_status]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Pay_type')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-pay_type" data-rule="required" class="form-control" name="row[pay_type]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bank_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_id" data-rule="required" data-source="bank/index" class="form-control selectpage" name="row[bank_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Bank_num')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-bank_num" data-rule="required" class="form-control" name="row[bank_num]" type="text" value="''">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Order_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-order_status" data-rule="required" class="form-control" name="row[order_status]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Is_invoice')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-is_invoice" data-rule="required" class="form-control" name="row[is_invoice]" type="number" value="0">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Invoice_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-invoice_id" data-rule="required" data-source="invoice/index" class="form-control selectpage" name="row[invoice_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="'normal'"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remarks')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-remarks" data-rule="required" class="form-control" name="row[remarks]" type="text" value="''">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Refund_money')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-refund_money" data-rule="required" class="form-control" step="0.01" name="row[refund_money]" type="number" value="0.00">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Completetime')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-completetime" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[completetime]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Group_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-group_id" data-rule="required" data-source="group/index" class="form-control selectpage" name="row[group_id]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Day_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-day_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[day_time]" type="text" value="{:date('Y-m-d H:i:s')}">
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
