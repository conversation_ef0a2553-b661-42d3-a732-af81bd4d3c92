<div class="content">
    <div class="panel panel-default panel-intro">

        <div class="panel-heading">
            {:build_heading(null, false)}
            <ul class="nav nav-tabs">
                <li class="active"><a href="#one" data-toggle="tab">统计</a></li>
            </ul>
        </div>

        <style type="text/css">
            table thead{
                border-bottom:3px solid #C0C0C0;
            }
            table th{
                border-bottom:2px solid #C0C0C0;
            }
        </style>

        <div class="panel-body">
            <div class="panel-body">
                <div id="myTabContent" class="tab-content">
                    <div class="tab-pane fade active in" id="one">
                        <div class="widget-body no-padding">

                            <div class="fixed-table-toolbar">
                                <div class="bs-bars pull-left">
                                    <div id="toolbar" class="toolbar">
                                        <a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$page}" class="btn btn-primary btn-refresh" title="刷新">
                                            <i class="fa fa-refresh">
                                            </i>
                                        </a>
                                        <div class="dropdown btn-group "><a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled">总台币&nbsp;{$total_money.tb|number_format} NT&nbsp;&nbsp;&nbsp;人民币&nbsp;{$total_money.rmb|number_format=2} ¥</a>
                                        </div>
                                    </div>
                                </div>
                                <form class="form" method="get">
                                    <div class="columns-right pull-right" style="margin-top:10px;margin-bottom:10px;">
                                        <button  type="submit" class="btn btn-success" name="commonSearch" title="提交">
                                            <i class="glyphicon glyphicon-search">
                                            </i>
                                        </button>
                                    </div>
                                    <div class="pull-right search">
                                        {if isset($updatetime)}
                                        <div class="form-group col-xs-6 col-sm-3"><div class="col-xs-12"><input type="hidden" class="form-control operate" name="updatetime-operate" data-name="updatetime" value="RANGE" readonly=""><input type="text" style="width:280px;" class="form-control datetimerange" name="updatetime"   value="{$updatetime}" placeholder="选择时间" id="updatetime" data-index="8"></div></div>
                                        {else /}
                                        <div class="form-group col-xs-6 col-sm-3"><div class="col-xs-12"><input type="hidden" class="form-control operate" name="updatetime-operate" data-name="updatetime" value="RANGE" readonly=""><input type="text" style="width:280px;" class="form-control datetimerange" name="updatetime"  placeholder="选择时间" id="updatetime" data-index="8"></div></div>
                                        {/if}
                                    </div>
                                </form>
                            </div>
                            <div class="bootstrap-table">
                                <div class="fixed-table-container" style="padding-bottom: 0px;">
                                    <div class="fixed-table-header" style="display: none;">
                                        <table></table>
                                    </div>
                                    <div class="fixed-table-body">
                                        <table id="table" class="table table-striped table-bordered table-hover table-nowrap" data-operate-del="1" width="100%">
                                            <thead>
                                            <tr>
                                                <th style="text-align: center; vertical-align: middle; " data-field="day_time">
                                                    <div class="th-inner ">日期</div>
                                                    <div class="fht-cell"></div>
                                                </th>
                                                <th style="text-align: center; vertical-align: middle; " data-field="alipay_account">
                                                    <div class="th-inner ">虚拟银行账号</div>
                                                    <div class="fht-cell"></div>
                                                </th>
                                                <th style="text-align: center; vertical-align: middle; " data-field="num">
                                                    <div class="th-inner ">订单</div>
                                                    <div class="fht-cell"></div>
                                                </th>
                                                <th style="text-align: center; vertical-align: middle; " data-field="tb">
                                                    <div class="th-inner ">台币</div>
                                                    <div class="fht-cell"></div>
                                                </th>
                                                <th style="text-align: center; vertical-align: middle; " data-field="rmb">
                                                    <div class="th-inner ">人民币</div>
                                                    <div class="fht-cell"></div>
                                                </th>
                                            </tr>
                                            </thead>
                                            <tbody data-listidx="0">
                                            {for start="0" end="$page_size" name="i"}
                                            {if $i < $list_count}
                                            <tr data-index="{$i}">
                                                <td style="text-align: center; vertical-align: middle; ">{$list[$i]['day_time']}</td>
                                                <td style="text-align: center; vertical-align: middle; ">{$list[$i]['in_account']}</td>
                                                <td style="text-align: center; vertical-align: middle; ">{$list[$i]['num']}</td>
                                                <td style="text-align: center; vertical-align: middle; ">{$list[$i]['tb']|number_format}</td>
                                                <td style="text-align: center; vertical-align: middle; ">{$list[$i]['rmb']|number_format=2}</td>
                                            </tr>
                                            {/if}
                                            {/for}
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="fixed-table-pagination" style="display: block;">
                                        <div class="pull-left pagination-detail">
                                            <span class="pagination-info">显示第 {$begin} 到第 {$end} 条记录，总共 {$count} 条记录</span>
                                            <span class="page-list">每页显示
                                                        <span class="btn-group dropup">
                                                          <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                                            <span class="page-size">{$page_size}</span>
                                                            <span class="caret"></span>
                                                          </button>
                                                          <ul class="dropdown-menu" role="menu">
                                                            {foreach $size_arr as $item}
                                                            <li role="menuitem" {if $item==$page_size}class="active"{/if}>
                                                                {if isset($updatetime)}
                                                                <a href="/beibei.php/statistics/virtualbank?ps={$item}&updatetime={$updatetime}">{$item}</a>
                                                                {else /}
                                                                <a href="/beibei.php/statistics/virtualbank?ps={$item}">{$item}</a>
                                                                {/if}
                                                            </li>
                                                            {/foreach}
                                                          </ul>
                                                        </span>条记录</span>
                                        </div>
                                        <div class="pull-right pagination">
                                            <ul class="pagination">
                                                <li class="page-pre">
                                                    {if $page > 1}
                                                        {if isset($updatetime)}
                                                        <a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$page-1}&updatetime={$updatetime}">上一页</a>
                                                        {else /}
                                                        <a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$page-1}">上一页</a>
                                                        {/if}
                                                    {else /}
                                                    <a href="#">上一页</a>
                                                    {/if}
                                                </li>
                                                {if $page < 5}
                                                {if $page_num<5}
                                                {for start="1" end="$page_num+1" name="i"}
                                                {if $page == $i}
                                                <li class="page-number active"><a href="#">{$i}</a></li>
                                                {else /}
                                                    {if isset($updatetime)}
                                                    <li class="page-number"><a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$i}&updatetime={$updatetime}">{$i}</a></li>
                                                    {else /}
                                                    <li class="page-number"><a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$i}">{$i}</a></li>
                                                    {/if}
                                                {/if}
                                                {/for}
                                                {else /}
                                                {for start="1" end="6" name="i"}
                                                {if $page == $i}
                                                <li class="page-number active"><a href="#">{$i}</a></li>
                                                {else /}
                                                    {if isset($updatetime)}
                                                    <li class="page-number"><a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$i}&updatetime={$updatetime}">{$i}</a></li>
                                                    {else /}
                                                    <li class="page-number"><a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$i}">{$i}</a></li>
                                                    {/if}
                                                {/if}
                                                {/for}
                                                {/if}
                                                {if $page_num>5}
                                                <li class="page-last-separator disabled"><a href="#">...</a></li>
                                                <li class="page-last">
                                                    {if isset($updatetime)}
                                                    <a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$page_num}&updatetime={$updatetime}">{$page_num}</a>
                                                    {else /}
                                                    <a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$page_num}">{$page_num}</a>
                                                    {/if}
                                                </li>
                                                {/if}
                                                {else /}
                                                {if $page >=5 AND $page <= ($page_num - 4) }
                                                    {if isset($updatetime)}
                                                    <li class="page-number"><a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page=1&updatetime={$updatetime}">1</a></li>
                                                    {else /}
                                                    <li class="page-number"><a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page=1">1</a></li>
                                                    {/if}
                                                <li class="page-last-separator disabled"><a href="#">...</a></li>
                                                {for start="$page - 2" end="$page + 3" name="i"}
                                                {if $page == $i }
                                                <li class="page-number active"><a href="#">{$i}</a></li>
                                                {else /}
                                                    {if isset($updatetime)}
                                                    <li class="page-number"><a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$i}&updatetime={$updatetime}">{$i}</a></li>
                                                    {else /}
                                                    <li class="page-number"><a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$i}">{$i}</a></li>
                                                    {/if}
                                                {/if}
                                                {/for}
                                                <li class="page-last-separator disabled"><a href="#">...</a></li>
                                                <li class="page-last">
                                                    {if isset($updatetime)}
                                                    <a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$page_num}&updatetime={$updatetime}">{$page_num}</a>
                                                    {else /}
                                                    <a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$page_num}">{$page_num}</a>
                                                    {/if}
                                                </li>
                                                {elseif ($page_num - $page) <= 5 }
                                                    {if isset($updatetime)}
                                                    <li class="page-number"><a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page=1&updatetime={$updatetime}">1</a></li>
                                                    {else /}
                                                    <li class="page-number"><a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page=1">1</a></li>
                                                    {/if}
                                                <li class="page-last-separator disabled"><a href="#">...</a></li>
                                                {for start="$page_num - 4" end="$page_num + 1" name="i"}
                                                {if $page == $i }
                                                <li class="page-number active"><a href="#">{$i}</a></li>
                                                {else /}
                                                    {if isset($updatetime)}
                                                    <li class="page-number"><a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$i}&updatetime={$updatetime}">{$i}</a></li>
                                                    {else /}
                                                    <li class="page-number"><a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$i}">{$i}</a></li>
                                                    {/if}
                                                {/if}
                                                {/for}
                                                {/if}
                                                {/if}
                                                <li class="page-next">
                                                    {if $page < $page_num}
                                                        {if isset($updatetime)}
                                                        <a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$page+1}&updatetime={$updatetime}">下一页</a>
                                                        {else /}
                                                        <a href="/beibei.php/statistics/virtualbank?ps={$page_size}&page={$page+1}">下一页</a>
                                                        {/if}
                                                    {else /}
                                                    <a href="#">下一页</a>
                                                    {/if}
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
