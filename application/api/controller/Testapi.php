<?php
/**
 * Created by PhpStorm.
 * User: LTS
 * Date: 2019/8/24
 * Time: 10:52
 */

namespace app\api\controller;

use app\common\controller\Frontend;
use app\common\model\DaifuModel;
use app\common\model\User;
use function fast\e;
use think\Db;
use think\Config;
use think\Exception;
use fast\Wxpay;
use think\Request;

class Bankpayzkltool
{
    private function bank_full_number_match($bank_title, $account)
    {
            if( $bank_title !== "玉山商銀" )
            {
                    $wildcard = $account;
                    while (true)
                    {
                            if(  !strstr( $wildcard, '*' ) )
                            {
                                    break;
                            }
                            $wildcard = str_replace('*', '_', $wildcard);
                    }
                    $res =  Db::name('bank_full_number')->where('number', 'like', $wildcard)->where('is_del', 0)->find();

                    if( !empty( $res ) )
                    {
                            return $res;
                    }
            }else{

                    $res =  Db::name('bank_full_number')->where('number', $account)->where('is_del', 0)->find();

                    if( !empty( $res ) )
                    {
                            return $res;
                    }
            }
            return false;
    }


    private  function bank_match_rule($data,$card)
    {
        if( empty($data['bank_title']) || empty($data['in_account']) )
        {
                return false;
        }

        /* 獲取后5碼 */
        $five_num = substr($data['in_account'],-5);
        /* 獲取后6碼 */
        $six_num = substr($data['in_account'],-6);
        /* 玉山補位的賬號 */
        //$position_num = substr($bank_num,-9,6);
        $user_bank_query = null;

        if( $data['bank_title'] == "玉山商銀" )
        {
                $post_office = Db::name('category')->where('name', 'like',$card['code'] . '%')->select();
                if( count($post_office) === 0 )
                {
                        return false;
                }
                $bank_type_ides = array_column($post_office, 'id');
                $user_bank_query['category_id'] = array('in', $bank_type_ides);
                $user_bank_query['account_six'] = $six_num;
        }else{
                $query = empty($card['code']) ? ('%' . $data['bank_title']) : $card['code'];
                $post_office = Db::name('category')->where('name', 'like', $query . '%')->select();
                if( count($post_office) === 0 )
                {
                        return false;
                }
                $bank_type_ides = array_column($post_office, 'id');
                $user_bank_query['category_id'] = array('in', $bank_type_ides);

                if( $data['bank_title'] == "郵局" )
                {
                        $user_bank_query['account_six'] = array('like','%' . $five_num);

                }else if( $data['bank_title'] == "中國信託" )
                {
                        $six_num = str_replace("*","_",$six_num);
                        $user_bank_query['account_six'] = array('like', $six_num);
                }else{
                        $user_bank_query['account_six'] = $six_num;
                }
        }

        // user_bank 表里有两个用户, 尾号分别为654321, 754321.   邮局只查后5位,  这个时候就容易出错,  解决办法:  查看谁有 订单记录未完成
        //$user_bank =  Db::name('user_bank')->where($user_bank_query)->select();
        $user_bank =  Db::name('user_bank')->where($user_bank_query)->find();
        if( empty($user_bank) )
        {
                return false;
        }
        return $user_bank;
    }

    private function bank_trade_check($data, $card)
    {
            $bank_match = $this->bank_full_number_match( $data['bank_title'], $data['in_account'] );

            if( empty($bank_match) )
            {
                    // 没有匹配到 全码信息
                    $user_bank =  $this->bank_match_rule( $data, $card );
                    if( empty($user_bank) )
                    {
                            return false;
                    }
                    if( $data['bank_title'] == "玉山商銀" )
                    {
                            $data_bank = ['username'=>$card['username'], 'code'=>$card['code'], 'number'=>$card['number'], 'user_id'=>$user_bank['user_id'], 'bank_id'=>$user_bank['id']];
                            Db::name('bank_full_number')->insertGetId($data_bank);
                    }else{
//                    $user = Db::name('user')->where('id', $user_bank['user_id'] )->find();
//                    if( $user['is_bank'] === 0 )
//                    {
//                        return false;
//                    }
                    }
                    return $user_bank['user_id'];
            }else{
                    return $bank_match['user_id'];
            }
    }

    private function get_remark_detail( $bank_title, $remark )
    {
        $remark_array = null;
        if( $bank_title == "玉山商銀" )
        {
                $reg = "/(\D+)/";
                preg_match($reg,$remark,$username);
                $remark_sub = str_replace($username[1], '', $remark);
                $user_info = explode('/',$remark_sub);
                $remark_array['username'] =  $username[1];
                $remark_array['code'] = $user_info[0];
                $remark_array['number'] = $user_info[1];
        }else{
                $remark_array['number'] = $this->find_num($remark);
        }
        return $remark_array;
    }

    private function order_bank_receipts_money($order_id, $money, $save)
    {
        try
        {
                $total = intval($money) + intval($save);
                Db::name('df_order')->where('id', $order_id)->update(['receipts_money'=>$total]);
                $this->orderLog("银行转入台币" . $save, $order_id);
        }catch (Exception $e)
        {}
    }

    private function virtual_order_bank_receipts_money($order_id, $money, $save)
    {
        try
        {
                $total = intval($money) + intval($save);
                Db::name('virtual_order')->where('id', $order_id)->update(['receipts_money'=>$total]);
                $this->virtualLog("虚拟订单银行转入台币" . $save, $order_id);
        }catch (Exception $e)
        {}
    }

    /**
     *  代付与储值银行流水的匹配和储存
     */
    public function save_bank()
    {
        try
        {
            $data = $this->get_post_data();

            if( empty($data) )
            {
                    return $this->return_msg(1, '数据错误');
            }
            /* 是否重复入库 */
            $bank_map  = [ 'bank_title' => $data['bk'], 'account' => $data['id'], 'date_time' => $data['date_time'], 'save' => $data['save'], 'balance' => $data['balance'] ];
            $bank_info = Db::name('bank_trade')->where($bank_map)->find();

            if( $bank_info )
            {
                    return $this->return_msg(1, '重复入账');
            }

            if( intval($data['taken']) > 0 && intval($data['save']) <= 0 )
            {
                    $bank_data = ['bank_title' => $data['bk'], 'account' => $data['id'], 'date_time' => $data['date_time'],'version' => $data['version'], 'taken' => $data['taken'], 'save' => $data['save'], 'balance' => $data['balance'], 'abstract' => $data['abstract'], 'remark' => $data['remark'], 'in_account' => '0', 'order_id'=>0, 'user_id'=>0, 'is_normal'=>0 ];
                    $bank_res = Db::name('bank_trade')->insertGetId($bank_data);
                    if( !empty($bank_res) )
                    {
                            return $this->return_msg(0, '银行交易保存成功1');
                    }else{
                            return $this->return_msg(1, '银行交易保存失败1');
                    }
            }

            $card = $this->get_remark_detail($data['bk'], $data['remark']);
            $bank_num  =  $card['number'];
            $data = [ 'bank_title' => $data['bk'],
                'account' => $data['id'],
                'date_time' => $data['date_time'],
                'version' => $data['version'],
                'taken' => $data['taken'] ? $data['taken'] : '0.00',
                'save' => $data['save'],
                'balance' => $data['balance'],
                'abstract' => $data['abstract'],
                'remark' => $data['remark'],
                'in_account' => $bank_num ];
            if( empty($bank_num) )
            {
                    $data['order_id'] = 0;
                    $data['user_id'] = 0;
                    $data['is_normal'] = 1;
                    $bank_res = Db::name('bank_trade')->insertGetId($data);
                    if($bank_res)
                    {
                            return $this->return_msg(0, '银行交易保存成功2');
                    }else{
                            return $this->return_msg(0, '银行交易保存失败2');
                    }
            }

            // 收账银行核对,  确定平台收账银行后添加代码
            $sys_bank =  Db::name('sys_bank')->where('account_num', $data['account'])->find();
            if( empty($sys_bank) )
            {
                    $data['order_id'] = 0;
                    $data['user_id'] = 0;
                    $data['is_normal'] = 1;
                    $bank_res = Db::name('bank_trade')->insertGetId($data);
                    if( !empty($bank_res) )
                    {
                            return $this->return_msg(0, '银行交易保存成功3');
                    }else{
                            return $this->return_msg(1, '银行交易保存失败3');
                    }
            }

            $user_id =  $this->bank_trade_check($data, $card);
            if( !$user_id )
            {
                    $data['order_id'] = 0;
                    $data['user_id'] = 0;
                    $data['is_normal'] = 1;
                    $bank_res = Db::name('bank_trade')->insertGetId($data);
                    if( $bank_res )
                    {
                            return $this->return_msg(0, '银行交易保存成功4');
                    }else{
                            return $this->return_msg(0, '银行交易保存失败4');
                    }
            }

            // 给user_bank表添加全码
            if( $data['bank_title'] == "玉山商銀" )
            {
                    $account_six = substr($card['number'], -6);
                    Db::name('user_bank')->where('user_id', $user_id)->where('account_six', $account_six)->update(['all_account'=>$card['number']]);
                    Db::name('user')->where('id', $user_id)->update(['is_bank'=>1]);
            }

            $order_map = [ 'user_id' => $user_id, 'pay_status' => 1,'pay_type' => ['in',[0, 1] ], 'order_status'=> '0' ];
            $order = Db::name('df_order')->where($order_map)->find();
            $last_sql = Db::name('df_order')->getLastSql();
            
            if( empty($order) )
            {
                    $data['order_id'] = 0;
                    $data['user_id'] = $user_id;
            }else{
                    $data['order_id'] = $order['id'];
                    $data['user_id'] = $user_id;
            }
            if( empty($order) )
            {
                    $data['is_normal'] = 1;
                    $_bank_trade_id = Db::name('bank_trade')->insertGetId($data);
                    Db::name('bank_trade_log')->insertGetId(['bank_trade_id'=>$_bank_trade_id, 'status'=>0,'remark'=>'未匹配到订单: ' . $last_sql]);
                    return $this->return_msg(0,'未找到订单,转人工' );
            }

            Db::startTrans();
            $_bank_trade_id = Db::name('bank_trade')->insertGetId($data);
            // 静态函数
            $user  = Db::name('user')->where('id', $user_id)->find();
            if( empty($user) )
            {
                    Db::rollback();
                    return $this->return_msg(1,'用戶不存在' );
            }

            $frozen_price = $data['save'] + $order['receipts_money'];
            // 冻结金 - 实际需要支付
            $beyond = $frozen_price - $order['actual_money'];
            $track_map = [ 'user_id' => $user['id'], 'bank_trade_id' => $_bank_trade_id,'order_id' => $order['id'], 'frozen_price' =>$frozen_price, 'remark' => '' ];

            // 订单实际银行收款金额
            $this->order_bank_receipts_money($order['id'], $order['receipts_money'], $data['save']);
            if( $beyond < 0 )
            {
                    $order_data['pay_type'] = 1;
                    $_track = Db::name( 'frozen_trace' )->insertGetId($track_map);
                    if( $order['pay_type'] == 1 )
                    {
                            $_order = 1;
                    }else{
                            $_order = Db::name( 'df_order' )->where( 'id',$order['id'] )->update( $order_data );
                    }

                    if ( !empty($_bank_trade_id) && !empty($_track) && !empty($_order) )
                    {
                            Db::commit();
                            Db::name('bank_trade_log')->insertGetId(['bank_trade_id'=>$_bank_trade_id, 'status'=>0,'remark'=>'部分支付']);
                            return $this->return_msg(0, '部分支付更新成功');
                    }
                    Db::rollback();
                    Db::name('bank_trade_log')->insertGetId(['bank_trade_id'=>$_bank_trade_id, 'status'=>1,'remark'=>'部分支付']);
                    return $this->return_msg(1, '部分支付更新失败');
            }

            if( $beyond > 0 )
            {
                    $order_data['pay_type'] = 3;
                    $order_data['order_status'] = 2;
                    $track_map['remark'] =  '订单冻结: ' . $frozen_price;
                    $_track = Db::name( 'frozen_trace' )->insertGetId($track_map);
                    $_order = Db::name( 'df_order' )->where( 'id',$order['id'] )->update( $order_data );
                    if (  !empty($_bank_trade_id) && !empty($_track) && !empty($_order) )
                    {
                            Db::commit();
                            Db::name('bank_trade_log')->insertGetId(['bank_trade_id'=>$_bank_trade_id, 'status'=>0,'remark'=>'多支付转人工']);
                            return $this->return_msg(0, '更新成功');
                    }
                    Db::rollback();
                    Db::name('bank_trade_log')->insertGetId(['bank_trade_id'=>$_bank_trade_id, 'status'=>1,'remark'=>'多支付转人工更新失败']);
                    return $this->return_msg(1, '更新失败');
            }else{
                    $order_data['pay_type'] = 2;
            }

            $frozen = $frozen_price - $beyond;
            $track_map['remark'] =  '订单冻结: ' . $frozen;
            $track_map['frozen_price'] = $frozen;
            $_track = Db::name('frozen_trace')->insertGetId($track_map);
            $_order = Db::name('df_order')->where( 'id',$order['id'] )->update($order_data);
            $_task = false;

            /* 发票更新 */
            $this->update_invoice($data['bank_title'], $order['invoice_id']);

            /*检查卡号是否被锁*/
            if( $data['bank_title'] == "郵局" )
            {
                    $account_six = substr($bank_num,-5);
            }else{
                    $account_six = substr($bank_num,-6);
            }
            $bank_full_number = '%' . $account_six;
            $bank_full_number_row = Db::name('bank_full_number')->where("user_id", $user_id)->where("number", 'like', $bank_full_number)->find();
            /* 如果全码表没有记录 */
            if ( empty($bank_full_number_row) )
            {
                $account_six = strlen($account_six) == 5 ? '_' . $account_six : $account_six;
                $user_bank_row = Db::name('user_bank')->where("user_id", $user_id)->where("account_six", 'like', $account_six)->find();
                if( empty($user_bank_row) )
                {
                    Db::name('df_order')->where( 'id',$order['id'] )->update(['order_status'=>2, 'remarks'=>'卡号ID不存在']);
                    Db::name('debugging')->insertGetId(['msg'=>'$six_num: ' . $account_six . ', $user_id: ' . $user_id, 'title'=>$_bank_trade_id . '_islock', 'add_time'=>time()]);
                    Db::commit();
                    return $this->return_msg(0, '检查银行卡号封锁状态获取数据为空');
                }else{
                    if( $user_bank_row['is_lock'] === 1 )
                    {
                        Db::name('df_order')->where( 'id',$order['id'] )->update(['order_status'=>2, 'remarks'=>'新增银行卡号']);
                        Db::name('debugging')->insertGetId(['msg'=>'用户银行卡id: ' . $user_bank_row['id'] . ', $six_num: ' . $account_six . ', $user_id: ' . $user_id, 'title'=>$_bank_trade_id . '_islock', 'add_time'=>time()]);
                        Db::commit();
                        return $this->return_msg(0, '新增银行卡号转人工');
                    }
                }
            }

            /* 阿里天猫代付 */
            if( $order['type'] === 0 || $order['type'] === 1 )
            {
                    $sys_alipay = Db::name( 'alipay_order' )->where( 'order_id', $order['id'] )->group('sys_alipay_id')->select();
                    foreach ( $sys_alipay as $item)
                    {
                            $guid = $this->create_guid();
                            $_order_py_return = Db::name( 'df_order_pay' )->insert([ 'guid'=>$guid, 'sys_alipay_id'=>$item['sys_alipay_id'], 'order_id'=>$order['id'] ]);
                            if( empty($_order_py_return) )
                            {
                                    $_task = false;
                                    break;
                            }
                            $_task = true;
                    }
            }
            /* 支付宝微信储值 */
            $auto_transfer = false;
            if( $order['type'] == 3 || $order['type'] == 4 )
            {
                if( $user['is_transfer'] )
                {
                        // 自动转账
                        $result = null;
                        if( $order['type'] == 3 )
                        {
                                $user_alipay = Db::name('user_alipay')->where('user_id', $user['id'])->where('ali_account', $order['alipay_account'])->find();
                                $result = Frontend::alipayWithDraw($order['order_no'], $user['id'], $order['rmb_money'], $order['alipay_account'], $user_alipay['name'], 2);
                        }else if( $order['type'] == 4 )
                        {
                                $wxpay = new Wxpay();
                                $result = $wxpay->sendMoney($order['rmb_money'],$order['wechat_account'],'付唄');
                        }
                        if ($result['status'] == 1)
                        {
                                // 转账成功
                                Db::name('sv_record')->insertGetId(['order_id'=>$order['id'], 'status'=>1, 'result'=>json_encode($result, JSON_UNESCAPED_UNICODE)]);
                                $_df_order_status = Db::name('df_order')->where('id', $order['id'])->update(['order_status' => 1, 'completetime'=>time()]);
                                $_task = true;
                                $auto_transfer = true;
                        }else
                        {
                                Db::name('sv_record')->insertGetId(['order_id'=>$order['id'], 'status'=>0, 'result'=>json_encode($result, JSON_UNESCAPED_UNICODE)]);
                                if($result['status'] == 2)
                                {
                                    $this->orderLog('API异常: ' . $result['msg'] . ', 转人工', $order['id']);
                                }else{
                                    $this->orderLog("自动转账失败, 转人工", $order['id']);
                                }
                                $_df_order_status = Db::name('df_order')->where('id', $order['id'])->update(['order_status' => 2]);
                                $_task = empty($_df_order_status) ? false : true;
                        }
                }else{
                        // 不是自动转账, 转人工
                        $this->orderLog("不是自动转账, 转人工", $order['id']);
                        $_df_order_status = Db::name('df_order')->where('id', $order['id'])->update(['order_status' => 2]);
                        $_task = empty($_df_order_status) ? false : true;
                }
            }
            /*淘币储值*/
            if( $order['type'] == 7 )
            {
                    $_taobi = $this->taobiLog($user['id'], '8', $order['rmb_money'], $order['id'], "", "");
                    $_df_order_status = Db::name('df_order')->where('id', $order['id'])->update(['order_status' => 1, 'completetime'=>time()]);
                    $_task = ( !empty($_taobi) && !empty($_df_order_status) ) ? true : false;
            }
            /* 吱口令*/
            if( $order['type'] == 2 )
            {
                    $_zkl = Db::name('zkl')->where('order_id', $order['id'])->update(['status'=>1]);
                    if( !empty($_zkl) )
                    {
                            $_task = true;
                    }
            }

            if( $order['type'] == 5 || $order['type'] == 6 )
            {
                    $this->orderLog("转人工", $order['id']);
                    $_df_order_status = Db::name('df_order')->where('id', $order['id'])->update(['order_status' => 2]);
                    $_task = empty($_df_order_status) ? false : true;
            }

            if ( $_bank_trade_id &&  $_track && $_order && $_task)
            {
                    if( $order['type'] == 7  || ($order['type'] == 3 && $user['is_transfer'] && $auto_transfer) || ($order['type'] == 4 && $user['is_transfer'] && $auto_transfer) )
                    {
                            $this->order_processing($order['id']);
                    }
                    Db::commit();
                    Db::name('bank_trade_log')->insertGetId(['bank_trade_id'=>$_bank_trade_id, 'status'=>0,'remark'=>'完全支付']);
                    return $this->return_msg('0', '成功');
            }

            //file_put_contents("/var/www/haigou/runtime/http_header.txt", json_encode($arr) . '\n', FILE_APPEND | LOCK_EX);
            Db::rollback();

            $arr = array('bank_trade_id'=>$_bank_trade_id,'track'=>$_track,'order'=>$_order,'task'=>$_task,);
            Db::name('debugging')->insertGetId(['msg'=>json_encode($arr, JSON_UNESCAPED_UNICODE), 'title'=>'银行转账匹配失败', 'add_time'=>time()]);
            Db::name('bank_trade')->where('id', $_bank_trade_id)->update(['is_normal'=>1]);
            return $this->return_msg('1', '失敗');
        }catch (Exception $e){
            return $this->return_msg('1', '异常: ' . $e->getMessage());
        }
    }


    /**
     *  虚拟订单相关的银行流水匹配与储存                  虚拟订单使用
     */
    public function save_other_bank()
    {
        try
        {
            $data = $this->get_post_data();
            if( empty($data) )
            {
                    return $this->return_msg(1, '数据错误');
            }
            /* 是否重复入库 */
            $bank_map  = [ 'bank_title' => $data['bk'], 'account' => $data['id'], 'date_time' => $data['date_time'], 'save' => $data['save'], 'balance' => $data['balance'] ];
            $bank_info = Db::name('other_bank_trade')->where($bank_map)->find();
            if( $bank_info )
            {
                    return $this->return_msg(1, '重复入账');
            }
            if( intval($data['taken']) > 0 && intval($data['save']) <= 0 )
            {
                    $bank_data = ['bank_title' => $data['bk'], 'account' => $data['id'], 'date_time' => $data['date_time'],'version' => $data['version'], 'taken' => $data['taken'], 'save' => $data['save'], 'balance' => $data['balance'], 'abstract' => $data['abstract'], 'remark' => $data['remark'], 'in_account' => '0', 'order_id'=>0, 'user_id'=>0, 'is_normal'=>0 ];
                    $bank_res = Db::name('other_bank_trade')->insertGetId($bank_data);
                    if( !empty($bank_res) )
                    {
                            return $this->return_msg(0, '银行交易保存成功1');
                    }else{
                            return $this->return_msg(1, '银行交易保存失败1');
                    }
            }
            $card = $this->get_remark_detail($data['bk'], $data['remark']);
            $bank_num  =  $card['number'];

            $data = [ 'bank_title' => $data['bk'],
                'account' => $data['id'],
                'date_time' => $data['date_time'],
                'version' => $data['version'],
                'taken' => $data['taken'] ? $data['taken'] : '0.00',
                'save' => $data['save'],
                'balance' => $data['balance'],
                'abstract' => $data['abstract'],
                'remark' => $data['remark'],
                'in_account' => $bank_num ];
            if( empty($bank_num) )
            {
                    $data['order_id'] = 0;
                    $data['user_id'] = 0;
                    $data['is_normal'] = 1;
                    $bank_res = Db::name('other_bank_trade')->insertGetId($data);
                    if($bank_res)
                    {
                            return $this->return_msg(0, '银行交易保存成功2');
                    }else{
                            return $this->return_msg(0, '银行交易保存失败2');
                    }
            }
            // 收账银行核对,  确定平台收账银行后添加代码
            $sys_bank =  Db::name('sys_bank')->where('account_num', $data['account'])->find();
            if( empty($sys_bank) )
            {
                    $data['order_id'] = 0;
                    $data['user_id'] = 0;
                    $data['is_normal'] = 1;
                    $bank_res = Db::name('other_bank_trade')->insertGetId($data);
                    if( !empty($bank_res) )
                    {
                            return $this->return_msg(0, '银行交易保存成功3');
                    }else{
                            return $this->return_msg(1, '银行交易保存失败3');
                    }
            }

            $user_id =  $this->bank_trade_check($data, $card);
            if( !$user_id )
            {
                    $data['order_id'] = 0;
                    $data['user_id'] = 0;
                    $data['is_normal'] = 1;
                    $bank_res = Db::name('other_bank_trade')->insertGetId($data);
                    if( $bank_res )
                    {
                            return $this->return_msg(0, '银行交易保存成功4');
                    }else{
                            return $this->return_msg(0, '银行交易保存失败4');
                    }
            }

            $order_map = [ 'user_id' => $user_id, 'pay_status' => 1,'pay_type' => ['in',[0, 1] ], 'order_status'=> '0' ];
            $order = Db::name('virtual_order')->where($order_map)->find();
            $last_sql = Db::name('virtual_order')->getLastSql();

            if( empty($order) )
            {
                    $data['order_id'] = 0;
                    $data['user_id'] = $user_id;
            }else{
                    $data['order_id'] = $order['id'];
                    $data['user_id'] = $user_id;
            }
            if( empty($order) )
            {
                    $data['is_normal'] = 1;
                    $_bank_trade_id = Db::name('other_bank_trade')->insertGetId($data);
                    return $this->return_msg(0,'未找到订单,转人工' );
            }

            Db::startTrans();
            $_bank_trade_id = Db::name('other_bank_trade')->insertGetId($data);

            // 静态函数
            $user  = Db::name('user')->where('id', $user_id)->find();
            if( empty($user) )
            {
                    Db::rollback();
                    return $this->return_msg(1,'用戶不存在' );
            }

            $frozen_price = $data['save'] + $order['receipts_money'];
            // 冻结金 - 实际需要支付
            $beyond = $frozen_price - $order['actual_money'];

            // 订单实际银行收款金额
            $this->virtual_order_bank_receipts_money($order['id'], $order['receipts_money'], $data['save']);
            if( $beyond < 0 )
            {

                    $order_data['pay_type'] = 1;
                    if( $order['pay_type'] == 1 )
                    {
                            $_order = 1;
                    }else{
                            $_order = Db::name( 'virtual_order' )->where( 'id',$order['id'] )->update( $order_data );
                    }

                    if ( !empty($_bank_trade_id) && !empty($_order) )
                    {
                            Db::commit();
                            return $this->return_msg(0, '部分支付更新成功');
                    }
                    Db::rollback();
                    return $this->return_msg(1, '部分支付更新失败');
            }

            if( $beyond > 0 )
            {
                    $order_data['pay_type'] = 3;
                    $order_data['order_status'] = 2;
                    $_order = Db::name( 'virtual_order' )->where( 'id',$order['id'] )->update( $order_data );
                    if (  !empty($_bank_trade_id) && !empty($_order) )
                    {
                            Db::commit();
                            return $this->return_msg(0, '更新成功');
                    }
                    Db::rollback();
                    return $this->return_msg(1, '更新失败');

            }else{
                    $order_data['pay_type'] = 2;
            }

            $order_data['order_status'] = 2;
            $_order = Db::name('virtual_order')->where( 'id',$order['id'] )->update($order_data);

            /* 发票更新 */
            //$this->update_invoice($data['bank_title'], $order['invoice_id']);

            if ( $_bank_trade_id && $_order )
            {
                    Db::commit();
                    return $this->return_msg('0', '成功');
            }
            Db::rollback();
            return $this->return_msg('1', '失敗');
        }catch (Exception $e){
            return $this->return_msg('1', '异常: ' . $e->getMessage());
        }
    }


    public function trade_update()
    {
        try
        {
            Db::name('debugging')->insert(['title' => 'trade_update', 'msg' => '--1']);
            $post_data = $this->get_post_data();
            if( empty($post_data) )
            {
                    return $this->return_msg(1, '数据错误');
            }
            Db::name('debugging')->insert(['title' => 'trade_update', 'msg' => '--2']);
            Db::name('debugging')->insert(['title' => 'trade_update', 'msg' => json_encode( $post_data, JSON_UNESCAPED_UNICODE )]);
            Db::name('debugging')->insert(['title' => 'trade_update', 'msg' => $post_data['status']]);

            Db::startTrans();

            Db::name('debugging')->insert(['title' => 'trade_update', 'msg' => '--2222']);

            switch ( $post_data['status'] )
            {
                    case '已申请代付':
                        Db::name('debugging')->insert(['title' => 'trade_update', 'msg' => '---0']);
                        $_return = $this->apply_payment($post_data);
                        break;
                    case '代付关闭':
                        Db::name('debugging')->insert(['title' => 'trade_update', 'msg' => '---1']);
                        $_return = $this->close_payment($post_data);
                        break;
                    case '代付成功':
                        Db::name('debugging')->insert(['title' => 'trade_update', 'msg' => '---2']);
                        $_return = $this->success_payment($post_data);
                        break;
                    case '退款成功':
                        Db::name('debugging')->insert(['title' => 'trade_update', 'msg' => '---3']);
                        $_return = $this->refund_payment($post_data);
                        break;
                    case '代付已取消':
                        Db::name('debugging')->insert(['title' => 'trade_update', 'msg' => '---4']);
                        $_return = $this->cancel_payment($post_data);
                        break;
            }
            if( $_return === true )
            {
                    Db::commit();
                    return $this->return_msg('0', '保存成功' );
            }
            Db::rollback();
            return $this->return_msg('1', $_return );
        }catch (Exception $e){
            Db::name('debugging')->insert(['title' => 'trade_update_s', 'msg' => $e->getMessage()]);
        }
    }

//    public function get_web_pay_data()
//    {
//        $post_data = $this->get_post_data();
//        $alipay_acc = $this->get_sys_alipay( $post_data['alipay_account'] );
//        if( empty($alipay_acc) )
//        {
//                return $this->return_msg(1, '账号不存在');
//        }
//        if( $alipay_acc['status'] != 'normal' )
//        {
//                return $this->return_msg(1, '账号异常状态');
//        }
//
//        $order_pay = Db::name('df_order_pay')->where(['sys_alipay_id' => $alipay_acc['id'], 'type'=>0])->order('update_time asc')->find();
//        if( empty($order_pay) )
//        {
//                return $this->return_msg(1, '无可付款订单');
//        }
//
//        $df_order = Db::name('df_order')->where('id', $order_pay['order_id'] )->find();
//        if( $df_order['order_status'] !== 0 )
//        {
//                Db::name('df_order_pay')->where('id', $order_pay['id'])->update(['type'=>2, 'update_time' => date("Y-m-d H:i:s",time())]);
//                return $this->return_msg(1, '订单为不可支付状态');
//        }
//
//        //如果是有两个支付宝账号来执行这些任务, 那么下面的sql会把所有的任务都发给两个支付宝都去执行一遍, 而不是把本来应该是他们去执行的任务分给他们
//        //$goods = Db::query('SELECT og.title AS title, og.price AS price, og.goods_trade_no AS goods_trade_no, og.alipay_order_id AS alipay_order_id, og.create_time AS create_time , og.update_time AS update_time, og.type AS type, og.status AS status, og.is_del AS is_del, og.id AS id , ao.trade_no AS trade_no, ao.price AS all_price, ao.title AS name, ao.msg AS msg, ao.sys_alipay_id AS sys_alipay_id , ao.peerpay_link AS peerpay_link, ao.detail_link AS detail_link, ao.other_name AS other_name, ao.order_id AS order_id, df.user_id AS user_id , df.status AS order_status, df.pay_status AS pay_status, df.pay_type AS pay_type FROM t_alipay_order as ao LEFT JOIN t_alipay_order_goods as og ON og.alipay_order_id = ao.id JOIN t_df_order as df ON ao.order_id = df.id and df.id = ?', [$order_pay['order_id']]);
//
//
//        $words = Config::get('site.filtering');
//        Db::name('debugging')->insert(['title' => '$words', 'msg' => $words]);
//
//        $goods = Db::query('SELECT og.title AS title, og.price AS price, og.goods_trade_no AS goods_trade_no, og.alipay_order_id AS alipay_order_id, og.create_time AS create_time , og.update_time AS update_time, og.type AS type, og.status AS status, og.is_del AS is_del, og.id AS id , ao.trade_no AS trade_no, ao.price AS all_price, ao.title AS name, ao.msg AS msg, ao.sys_alipay_id AS sys_alipay_id , ao.peerpay_link AS peerpay_link, ao.detail_link AS detail_link, ao.other_name AS other_name, ao.order_id AS order_id, df.user_id AS user_id , df.status AS order_status, df.pay_status AS pay_status, df.pay_type AS pay_type FROM t_alipay_order as ao LEFT JOIN t_alipay_order_goods as og ON og.alipay_order_id = ao.id JOIN t_df_order as df ON ao.order_id = df.id and df.id = ? and ao.sys_alipay_id=?', [$order_pay['order_id'],$alipay_acc['id']]);
//        $data_array = array();
//        foreach ($goods as $key=>$value)
//        {
//            if(strpos( $value['trade_no'], 'M') !== false)
//            {
//                $trade_no_array = array_column($data_array, 'number');
//                if( !in_array( $value['trade_no'],$trade_no_array ) )
//                {
//                    $goods_data = array('number' => $value['trade_no'],
//                        'detail_link' => $value['detail_link'],
//                        'peerpay_link' => $value['peerpay_link'],
//                        'price' => $value['all_price'],
//                    );
//                    array_push($data_array, $goods_data );
//                }
//            }else{
//                $goods_data = array('number' => $value['trade_no'],
//                    'detail_link' => $value['detail_link'],
//                    'peerpay_link' => $value['peerpay_link'],
//                    'price' => $value['price'],
//                );
//                array_push($data_array, $goods_data );
//            }
//        }
//        Db::startTrans();
//        $_update_result = Db::name('df_order_pay')->where('id', $order_pay['id'])->update(['type'=>1]);
//        if( $_update_result )
//        {
//            Db::commit();
//            return $this->return_msg('0', array( 'guid'=>$order_pay['guid'], 'data'=>$data_array ) );
//        }
//        Db::rollback();
//        return $this->return_msg('1','获取失败' );
//    }

    public function get_web_pay_data()
    {
        $post_data = $this->get_post_data();
        $alipay_acc = $this->get_sys_alipay( $post_data['alipay_account'] );
        if( empty($alipay_acc) )
        {
            return $this->return_msg(1, '账号不存在');
        }
        if( $alipay_acc['status'] != 'normal' )
        {
            return $this->return_msg(1, '账号异常状态');
        }

        $order_pay = Db::name('df_order_pay')->where(['sys_alipay_id' => $alipay_acc['id'], 'type'=>0])->order('update_time asc')->find();
        if( empty($order_pay) )
        {
            return $this->return_msg(1, '无可付款订单');
        }

        $df_order = Db::name('df_order')->where('id', $order_pay['order_id'] )->find();
        if( $df_order['order_status'] !== 0 )
        {
            Db::name('debugging')->insertGetId(['msg'=>'order_id: ' . $order_pay['order_id'] . ', order_status: ' . $df_order['order_status'], 'title'=>'订单下发', 'add_time'=>time()]);

            Db::name('df_order_pay')->where('id', $order_pay['id'])->update(['type'=>2, 'update_time' => date("Y-m-d H:i:s",time())]);
            return $this->return_msg(1, '订单为不可支付状态');
        }

        //如果是有两个支付宝账号来执行这些任务, 那么下面的sql会把所有的任务都发给两个支付宝都去执行一遍, 而不是把本来应该是他们去执行的任务分给他们
        //$goods = Db::query('SELECT og.title AS title, og.price AS price, og.goods_trade_no AS goods_trade_no, og.alipay_order_id AS alipay_order_id, og.create_time AS create_time , og.update_time AS update_time, og.type AS type, og.status AS status, og.is_del AS is_del, og.id AS id , ao.trade_no AS trade_no, ao.price AS all_price, ao.title AS name, ao.msg AS msg, ao.sys_alipay_id AS sys_alipay_id , ao.peerpay_link AS peerpay_link, ao.detail_link AS detail_link, ao.other_name AS other_name, ao.order_id AS order_id, df.user_id AS user_id , df.status AS order_status, df.pay_status AS pay_status, df.pay_type AS pay_type FROM t_alipay_order as ao LEFT JOIN t_alipay_order_goods as og ON og.alipay_order_id = ao.id JOIN t_df_order as df ON ao.order_id = df.id and df.id = ?', [$order_pay['order_id']]);

        $words = Config::get('site.filtering');
        $wordArr = explode(',', $words);


        $goods = Db::query('SELECT og.title AS title, og.price AS price, og.goods_trade_no AS goods_trade_no, og.alipay_order_id AS alipay_order_id, og.create_time AS create_time , og.update_time AS update_time, og.type AS type, og.status AS status, og.is_del AS is_del, og.id AS id , ao.trade_no AS trade_no, ao.price AS all_price, ao.title AS name, ao.msg AS msg, ao.sys_alipay_id AS sys_alipay_id , ao.peerpay_link AS peerpay_link, ao.detail_link AS detail_link, ao.other_name AS other_name, ao.order_id AS order_id, df.user_id AS user_id , df.status AS order_status, df.pay_status AS pay_status, df.pay_type AS pay_type FROM t_alipay_order as ao LEFT JOIN t_alipay_order_goods as og ON og.alipay_order_id = ao.id JOIN t_df_order as df ON ao.order_id = df.id and df.id = ? and ao.sys_alipay_id=?', [$order_pay['order_id'],$alipay_acc['id']]);
        $data_array = array();
        foreach ($goods as $key=>$value)
        {
            if(strpos( $value['trade_no'], 'M') !== false)
            {
                if( $this->strArray_check($wordArr, $value['title']) == false )
                {
                    $trade_no_array = array_column($data_array, 'number');
                    if( !in_array( $value['trade_no'],$trade_no_array ) )
                    {
                        $goods_data = array('number' => $value['trade_no'],
                            'detail_link' => $value['detail_link'],
                            'peerpay_link' => $value['peerpay_link'],
                            'price' => $value['all_price'],
                        );
                        array_push($data_array, $goods_data );
                    }

                }else{
                    Db::name('alipay_order')->where('id', $value['alipay_order_id'])->update(['status'=>2, 'alipay_status'=>10]);
                    $data_array = array();
                }
            }else{

               if( $this->strArray_check($wordArr, $value['title']) == false )
               {
                   $goods_data = array('number' => $value['trade_no'],
                       'detail_link' => $value['detail_link'],
                       'peerpay_link' => $value['peerpay_link'],
                       'price' => $value['price'],
                   );
                   array_push($data_array, $goods_data );
               }else{
                    Db::name('alipay_order')->where('id', $value['alipay_order_id'])->update(['status'=>2, 'alipay_status'=>10]);
               }
            }
        }

        if( count($data_array) == 0 )
        {
            $_update_result = Db::name('df_order_pay')->where('id', $order_pay['id'])->update(['type'=>2]);
            return $this->return_msg('1','获取失败' );
        }

        Db::startTrans();
        $_update_result = Db::name('df_order_pay')->where('id', $order_pay['id'])->update(['type'=>1]);
        if( $_update_result )
        {
            Db::commit();
            return $this->return_msg('0', array( 'guid'=>$order_pay['guid'], 'data'=>$data_array ) );
        }
        Db::rollback();
        return $this->return_msg('1','获取失败' );
    }

    protected function strArray_check($wordArr, $word)
    {
        foreach ($wordArr as $item)
        {
            if( strlen($item) > 0)
            {
                if (strpos($word, $item) !== false){
                    return true;
                }
            }
        }
        return false;
    }



    protected function alipay_status( $status )
    {
        switch ( $status )
        {
            case '等待付款':
                $_result = 0;
                break;
            case '代付关闭':
                $_result = 2;
                break;
            case '代付已取消':
                $_result = 3;
                break;
            case '金额修改':
                $_result = 4;
                break;
            case '账号锁定':
                $_result = 5;
                break;
            case '手机验证码':
                $_result = 6;
                break;
            case '余额不足':
                $_result = 7;
                break;
            case '非余额支付方式':
                $_result = 8;
                break;
            case '过滤关键字':
                $_result = 10;
                break;
        }
        return $_result;
    }

    public function update_web_pay_status()
    {
        try
        {
            $post_data = $this->get_post_data();
            $alipay_acc = $this->get_sys_alipay( $post_data['alipay_account'] );

            $map = [ 'trade_no' => $post_data['number'], 'sys_alipay_id' => $alipay_acc['id'], 'peerpay_link' => $post_data['trade_pay_link'] ];
            $alipay_order = Db::name('alipay_order')->where($map)->find();
            $order = Db::name('df_order')->where('id', $alipay_order['order_id'])->find();
            if( empty($order) )
            {
                return $this->return_msg(1, '未查询到该笔代付');
            }

            Db::startTrans();
            $order_pay = Db::name('df_order_pay')->where('guid', $post_data['guid'] )->find();
            $status_array = json_decode( $order_pay['status'], true );
            $status_array['order'][] = array( 'trade_no'=>$post_data['number'], 'status'=>$post_data['status'], 'cause'=>$post_data['trade_status'] );

            $_order_pay = Db::name('df_order_pay')->where('guid', $post_data['guid'])->update( [ 'status'=>json_encode( $status_array, JSON_UNESCAPED_UNICODE )] );

            if( $post_data['status'] === 0 )
            {
                $order_status = 5;
                $alipay_status = 1;
                $goods_status = 5;
                $this->orderLog("" . $post_data['number'] . ": 付款成功", $order['id']);
            }else{
                $goods_status = 2;
                $order_status = 2;
                $alipay_status = empty($post_data['remark']) ? 0 : $this->alipay_status($post_data['remark']);
                $this->orderLog("支付宝:" . $post_data['number'] . ": 付款失败", $order['id']);
            }

            // 2代付关闭 3代付取消 4金额修改 5账号锁定, 直接退款
            $_refund_trace = 1;
            if( $alipay_status == 2 || $alipay_status == 3 || $alipay_status == 4 || $alipay_status == 5 )
            {
                $order_status = 4;
                $goods_status = 4;
                $this->taobiLog($order['user_id'], '11', $alipay_order['price'], $order['id'], "无法支付的订单", "");

                $refund_trace_data = [ 'order_id'=>$order['id'], 'trade_no'=>$alipay_order['trade_no'], 'goods_trade_no'=>'', 'order_refund'=>$alipay_order['price'], 'goods_refund'=>'', 'remark'=>'' ];
                $_refund_trace = Db::name('refund_trace')->insert($refund_trace_data);
            }

            $_alipay_order = Db::name('alipay_order')->where('trade_no', $post_data['number'])->update(['status'=> $order_status, 'alipay_status'=>$alipay_status, 'update_time'=>date('Y-m-d H:i:s',time())]);
            $_alipay_order_goods = Db::name('alipay_order_goods')->where('alipay_order_id', $alipay_order['id'])->update(['status'=>$goods_status]);

            //if( $_order_pay == 1 && $_refund_trace == 1 && $_alipay_order == 1 && $_alipay_order_goods >= 1 )
            if( $_order_pay == 1 && $_refund_trace == 1 && $_alipay_order == 1 )
            {
                Db::commit();
                return $this->return_msg(0,'保存成功');
            }
            Db::rollback();
            $arr = array('guid'=>$post_data['guid'], 'order_pay'=>$_order_pay,'refund_trace'=>$_refund_trace,'alipay_order'=>$_alipay_order,'alipay_order_goods'=>$_alipay_order_goods,);
            file_put_contents("/var/www/haigou/runtime/update_web_pay_status.txt", json_encode($arr) . '\r\n', FILE_APPEND | LOCK_EX);
            return $this->return_msg(1, '操作失败');
        }catch (Exception $e)
        {
            return $this->return_msg('1','异常: ' .  $e->getMessage());
        }
    }

//    public function update_order_pay_type()
//    {
//        $post_data = $this->get_post_data();
//        $_order_pay = Db::name('df_order_pay')->where('guid', $post_data['guid'])->update([ 'type'=>2 ]);
//        $order_pay = Db::name('df_order_pay')->where('guid', $post_data['guid'])->find();
//        $order_pay_array = Db::name('df_order_pay')->where('order_id', $order_pay['order_id'])->select();
//
//        $order_status = 1;
//        foreach ( $order_pay_array as $item )
//        {
//            $status = json_decode($item['status'], true);
//            $order_array = $status['order'];
//
//            $alipay_order_pay_status = array_column($order_array, 'status');
//            if( in_array(1,$alipay_order_pay_status) )
//            {
//                $order_status = 2;
//                break;
//            }
//        }
//
//        if( $order_status == 1 )
//        {
//            $_order = Db::name('df_order')->where('id', $order_pay['order_id'])->update(['order_status'=>$order_status]);
//            $this->order_processing($order_pay['order_id']);
//        }else{
//            $alipay_order_array = Db::name('alipay_order')->where('order_id', $order_pay['order_id'])->select();
//            $alipay_order_status_array = array_column($alipay_order_array, 'status');
//            $num_count = array_count_values($alipay_order_status_array);
//            $total_number = count($alipay_order_status_array);
//            $pay_suc = $this->get_array_value($num_count, 5);
//            $pay_refund = $this->get_array_value($num_count, 4);
//            $pay_failed = $this->get_array_value($num_count, 2);
//
//            $pay_close= $this->get_array_value($num_count, 3);
//            $pay_cancel = $this->get_array_value($num_count, 6);
//
//            if( $pay_refund === $total_number )
//            {
//                $order_status = 8;
//            }
//            else if( $pay_close === $total_number || $pay_cancel === $total_number )
//            {
//                $order_status = 9;
//            }
//            else if( $pay_suc > 0 && $pay_suc < $total_number && $pay_refund > 0 && $pay_refund < $total_number )
//            {
//                if( $pay_failed === 0 )
//                {
//                    $order_status = 7;
//                }else{
//                    $alipay_status_array = array_column($alipay_order_array, 'alipay_status');
//                    $status_count = array_count_values($alipay_status_array);
//                    $phone_verify = $this->get_array_value($status_count, 6);
//                    $balance_not_enough = $this->get_array_value($status_count, 7);
//                    $not_balance_payment = $this->get_array_value($status_count, 8);
//
//                    if( $balance_not_enough != 0 || $phone_verify != 0 || $not_balance_payment != 0 )
//                    {
//                        $order_status = 10;
//                    }else{
//                        $order_status = 2;
//                    }
//                }
//            }
//            else{
//                $order_status = 2;
//            }
//            $df_order = Db::name('df_order')->where('id', $order_pay['order_id'])->find();
//            if( $df_order['order_status'] === $order_status )
//            {
//                $_order = 1;
//            }else{
//                $_order = Db::name('df_order')->where('id', $order_pay['order_id'])->update(['order_status'=>$order_status]);
//            }
//        }
//        if( !empty($_order_pay) && !empty($_order) )
//        {
//            return $this->return_msg(0, '更新成功');
//        }
//        return $this->return_msg(1, '更新失败');
//    }

    public function update_order_pay_type()
    {
        $post_data = $this->get_post_data();
        $_order_pay = Db::name('df_order_pay')->where('guid', $post_data['guid'])->update([ 'type'=>2 ]);
        $order_pay = Db::name('df_order_pay')->where('guid', $post_data['guid'])->find();
        $order_pay_array = Db::name('df_order_pay')->where('order_id', $order_pay['order_id'])->select();

        $order_status = 1;
        foreach ( $order_pay_array as $item )
        {
            $status = json_decode($item['status'], true);
            $order_array = $status['order'];

            $alipay_order_pay_status = array_column($order_array, 'status');
            if( in_array(1,$alipay_order_pay_status) )
            {
                $order_status = 2;
                break;
            }
        }

        if( $order_status == 1 )
        {
            $_order = Db::name('df_order')->where('id', $order_pay['order_id'])->update(['order_status'=>$order_status]);
            $this->order_processing($order_pay['order_id']);
        }else{
            $alipay_order_array = Db::name('alipay_order')->where('order_id', $order_pay['order_id'])->select();
            $alipay_order_status_array = array_column($alipay_order_array, 'status');
            $num_count = array_count_values($alipay_order_status_array);
            $total_number = count($alipay_order_status_array);
            $pay_suc = $this->get_array_value($num_count, 5);
            $pay_refund = $this->get_array_value($num_count, 4);
            $pay_failed = $this->get_array_value($num_count, 2);

            $sys_alipay_id_arr = array_column($alipay_order_array, 'sys_alipay_id');
            $sys_alipay_arr = array_values($sys_alipay_id_arr);

            if( $pay_refund === $total_number )
            {
                $order_status = 8;
            }

            //U盾正式使用时候使用
//            else if($pay_failed > 0)
//            {
//                $order_status = 11;
//            }

            // U盾测试时候使用
//            else if($pay_failed > 0 && ( in_array(11, $sys_alipay_arr) || in_array(5, $sys_alipay_arr)))
//            {
//                $_pay_fail = Db::name('alipay_order')->where('order_id', $order_pay['order_id'])->where('price', '>', 300)->where('status', 2)->find();
//                $order_status = empty($_pay_fail) ? 11 : 2;
//            }

            else if($pay_failed > 0)
            {
                $order_status = 2;
            }
            else if( $pay_suc > 0 && $pay_refund > 0 && ($pay_suc + $pay_refund) == count($alipay_order_array) )
            {
                $order_status = 7;
            }


            $df_order = Db::name('df_order')->where('id', $order_pay['order_id'])->find();
            if( $df_order['order_status'] === $order_status )
            {
                $_order = 1;
            }else{
                $_order = Db::name('df_order')->where('id', $order_pay['order_id'])->update(['order_status'=>$order_status]);
            }
        }
        if( !empty($_order_pay) && !empty($_order) )
        {
            return $this->return_msg(0, '更新成功');
        }
        return $this->return_msg(1, '更新失败');
    }

    private function get_array_value($arr, $key)
    {
        try
        {
            $value = $arr[$key];
            return $value;
        }catch (Exception $e)
        {}
        return 0;
    }

    private function alipay_order_money_check( $data )
    {
        $order_money = $data['price'];
        $goods_total_money = 0.0;

        foreach( $data['trade_detail']['ware_list'] as $key => $ls )
        {
            $price = $ls['trans_price'];
            $goods_total_money  += floatval($price);
        }
        $order_money = floatval($order_money);
        if( abs($order_money - $goods_total_money ) < 0.00001 )
        {
            return true;
        }
        return false;
    }

    protected function apply_payment( $data )
    {
        if( !$this->alipay_order_money_check($data) )
        {
            $alipay_status = 8;
        }else{
            $alipay_status = 0;
        }
        $this->alipay_order_trace( $data['number'], '',0, $data['payfor_createtime'], '' );
        $alipay_acc = $this->get_sys_alipay( $data['alipay_account'] );
        $alipay_order_data = [ 'trade_no' => $data['number'], 'sys_alipay_id' => $alipay_acc['id'],  'price' => $data['price'] ];
        $info = Db::name('alipay_order')->where($alipay_order_data)->find();

        if( empty($info) )
        {
            $alipay_order_post_data = [ 'peerpay_link' => $data['trade_detail']['trade_pay_link'],
                'detail_link' => $data['detail_url'],
                'sys_alipay_id' => $alipay_acc['id'],
                'trade_no' => $data['number'],
                'price' => $data['price'],
                'other_name' => $data['other'],
                'other_account' => $data['other2'],
                'title'=> $data['name'],
                'type'=> ( strpos($data['number'], 'M') !== false ? 1 : 0 ),
                'msg'=> $data['trade_detail']['trade_msg'],
                'create_time'=> $data['payfor_createtime'],
                'status' => 0,
                'alipay_status' => $alipay_status,
                'verson' => 1
            ];
            $alipay_order_id = Db::name('alipay_order')->insertGetId($alipay_order_post_data);
            foreach( $data['trade_detail']['ware_list'] as $key => $ls )
            {
                $row = ['title'=>$ls['trans_name'], 'price'=>$ls['trans_price'], 'goods_trade_no'=>$ls['trans_number'], 'alipay_order_id'=>$alipay_order_id, 'create_time'=>$ls['trans_time'], 'status'=>0];
                $alipay_goods_data[] = $row;
            }

            $_alipay_order_goods = Db::name('alipay_order_goods')->insertAll($alipay_goods_data);

            if( $_alipay_order_goods > 0 && !empty($alipay_order_id) )
            {
                return true;
            }else{
                return '存入数据失败';
            }
        }else{
            return '不应该存在的错误';
        }
        return '请不要重复提交';
    }

    protected function close_payment( $data )
    {
        $this->alipay_order_trace( $data['number'], '',4, $data['payfor_createtime'], '' );
        $alipay_acc = $this->get_sys_alipay( $data['alipay_account'] );
        $info = Db::name('alipay_order')->where(['trade_no' => $data['number'], 'sys_alipay_id' => $alipay_acc['id'], 'price' => $data['price']])->find();

        if( $info['order_id'] === 0 )
        {
            $_alipay_order = Db::name( 'alipay_order' )->where( 'id', $info['id'] )->update( ['alipay_status'=>2] );
            return true;
        }

        $order = Db::name( 'df_order' )->where( ['id' => $info['order_id'], 'order_status'=>['in',[0,1,2,7]] ] )->find();
        if( empty($order) )
        {
            return '订单匹配失败';
        }


        Db::name( 'alipay_order' )->where( 'id', $info['id'] )->update( ['status'=>3,'alipay_status'=>2] );
        Db::name( 'alipay_order_goods' )->where( 'alipay_order_id', $info['id'] )->update( ['status'=>3] );

        $this->orderLog("" . $data['number'] . ": 代付关闭", $order['id']);

        $all_alipay_order = Db::name('alipay_order')->where('order_id', $order['id'])->select();
        $alipay_order_status = array_column($all_alipay_order, 'alipay_status');
        $num_count = array_count_values($alipay_order_status);
        if( count($alipay_order_status) == $num_count[2] )
        {
            Db::name('df_order')->where('id', $order['id'])->update(['order_status'=>9]);
        }

        $pay_type = $order['pay_type'];
        if( $pay_type === 0 )
        {
            return true;
        }else if($pay_type == 1)
        {
            // 不做任何处理, 等待用户付款,或者取消
            return true;
        }else if($pay_type == 2)
        {
            if( Db::name('refund_trace')->where('trade_no', $data['number'])->find() )
            {
                return '该订单已经退过款了';
            }

            $this->taobiLog($order['user_id'], '11', $info['price'], $order['id'], "代付关闭退还", "");
            $refund_trace_data = [ 'order_id'=>$info['order_id'], 'trade_no'=>$data['number'], 'goods_trade_no'=>'', 'payfor_createtime'=>$data['payfor_createtime'], 'order_refund'=>$info['price'], 'goods_refund'=>'', 'remark'=>'' ];
            $_refund_trace = Db::name('refund_trace')->insert($refund_trace_data);

            if(  !empty($_refund_trace) )
            {
                $user = Db::name('user')->where('id', $order['user_id'])->find();
                $content =  '【付唄】您的代付關閉，退款金額' . $info['price']  .  ' 已幫您轉到F幣賬戶中~請您注意查收~如有問題請您提交申訴';
                $this->sendSMS($user['mobile'], $content);
                return true;
            }
        }
        return '代付关闭退款失败';
    }

    protected function success_payment( $data )
    {

        $this->alipay_order_trace( $data['number'], '',1, $data['payfor_createtime'], '' );
        $alipay_acc = $this->get_sys_alipay( $data['alipay_account'] );
        $map = [ 'trade_no' => $data['number'],'sys_alipay_id' => $alipay_acc['id'],'price' => $data['price'], 'status'=>1];

        $info = Db::name('alipay_order')->where($map)->find();

        if( empty($info) )
        {
            return '未找到支付宝订单信息';
        }

        if( $info['status'] == 5)
        {
            return '已经是代付成功状态';
        }
        if( $info )
        {

            $this->orderLog("" . $data['number'] . ": 代付成功", $info['order_id']);
            $_alipay_order = Db::name('alipay_order')->where('trade_no', $data['number'])->update(['status'=> 5, 'alipay_status'=>1]);
            $_alipay_order_goods = Db::name('alipay_order_goods')->where('alipay_order_id', $info['id'])->update(['status' => 5]);

            if( !empty($_alipay_order) && !empty($_alipay_order_goods) )
            {
                return true;
            }

        }
        return "未知错误";
    }

    // protected function refund_payment( $data )
    // {
    //     try
    //     {
    //         $type = array_key_exists('type',$data) ? $data['type'] : 0;
    //         Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '1']);
    //         if( $type === 0 )
    //         {
    //             Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '2']);
    //             $alipay_order = Db::name('alipay_order')->where('trade_no', $data['number'])->find();
    //             $refund = Db::name('refund_trace')->where( ['goods_trade_no' => $data['number2'], 'goods_refund'=>$data['price'] ] )->find();
    //             if( !empty($refund) )
    //             {
    //                 Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '3']);
    //                 $manual_refund = Db::name('aliapy_manual_refund')->where('trade_no', $data['number'])->where('time', $data['payfor_createtime'])->where('refund_no', $data['number2'])->where('price', $data['price'])->find();
    //                 if( empty($manual_refund) )
    //                 {
    //                     Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '4']);
    //                     $insert_data = ['type'=>$type, 'alipay_order_id'=>$alipay_order['id'], 'trade_no'=>$data['number'], 'time'=>$data['payfor_createtime'], 'name'=>$data['name'], 'refund_no'=>$data['number2'], 'other'=>$data['other'], 'price'=>$data['price'], 'log_status'=>$data['status'], 'status'=>0];
    //                     $_manual_refund = Db::name('aliapy_manual_refund')->insertGetId($insert_data);
    //                     return empty($_manual_refund) ? 'manual_refund 保存失败' : true;
    //                 }
    //                 return "refund_trace 与 aliapy_manual_refund 都有记录";
    //             }
    //         }else{
    //             $data['price'] = str_replace(",", "", $data['price']);
    //             $refund = Db::name('refund_trace')->where( ['goods_trade_no' => $data['detail_no'], 'payfor_createtime'=>date('Y-m-s h:i:s',$data['payfor_createtime']), 'goods_refund'=>$data['price'] ] )->find();
    //             if( !empty($refund) )
    //             {
    //                 return "吱口令 refund_trace已有记录";
    //             }
    //             //$refund_data = ['type'=>1,'alipay_order_id'=>$zkl['id'],'trade_no'=>$data['number'],'time'=>date('Y-m-s h:i:s',$data['payfor_createtime']),'name'=>$data['name'],'refund_no'=>$data['detail_no'],'other'=>$data['other1'],'price'=>$data['price'],'log_status'=>$data['status'], 'status'=>0];
    //             $manual_refund = Db::name('aliapy_manual_refund')->where('trade_no', $data['number'])->where('time', date('Y-m-s h:i:s',$data['payfor_createtime']))->where('refund_no', $data['detail_no'])->where('price', $data['price'])->find();
    //             if( !empty($manual_refund) )
    //             {
    //                 return "吱口令 aliapy_manual_refund已有记录";
    //             }
    //         }

    //         Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '5']);
    //         // PC 退款
    //         if( !empty($data['alipay_account']) )
    //         {
    //             Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '6']);
    //             $number = $this->get_goods_trade_no( $data );
    //             $map = ['goods_trade_no' => $number, 'status' => '5'];
    //             $info = Db::name('alipay_order_goods')->where( $map )->find();

    //             if( empty($info) )
    //             {
    //                 Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '7']);
    //                 $insert_data = ['type'=>$type, 'alipay_order_id'=>$alipay_order['id'], 'trade_no'=>$data['number'], 'time'=>$data['payfor_createtime'], 'name'=>$data['name'], 'refund_no'=>$data['number2'], 'other'=>$data['other'], 'price'=>$data['price'], 'log_status'=>$data['status'], 'status'=>0];
    //                 $_manual_refund = Db::name('aliapy_manual_refund')->insertGetId($insert_data);
    //                 return empty($_manual_refund) ? 'manual_refund 保存失败2' : true;
    //             }
    //             Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '8']);
    //             return $this->pc_refund( $info, $data );
    //         }

    //         // 手机退款
    //         if( !empty($data['pay_own']) )
    //         {
    //             return $this->zkl_refund( $data );
    //         }
    //     }catch (Exception $e)
    //     {
    //         return $e->getMessage();
    //     }
    // }

    // protected function get_goods_trade_no( $data )
    // {
    //     $number = NULL;
    //     foreach ( $data['trade_detail']['ware_list'] as $key => $value )
    //     {
    //         if( bccomp( $data['price'], $value['trans_price'], 2 ) != 0 )
    //         {
    //             continue;
    //         }
    //         $number = $value['trans_number'];
    //         break;
    //     }
    //     return $number;
    // }


    protected function refund_payment( $data )
    {
        try
        {
            Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '1']);
            $type = array_key_exists('type',$data) ? $data['type'] : 0;
            if( $type === 0 )
            {
                Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '2']);
                $refund = Db::name('refund_trace')->where( ['goods_trade_no' => $data['number2'], 'order_refund'=>$data['price'] ] )->find();
                
                if( !empty($refund) )
                {
                    Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '3']);
                    // 存在退款订单号相同, 退款金额相同的订单
                    $manual_refund = Db::name('aliapy_manual_refund')->where('trade_no', $data['number'])->where('time', $data['payfor_createtime'])->where('refund_no', $data['number2'])->where('price', $data['price'])->find();
                    if( empty($manual_refund) )
                    {
                        Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '4']);
                        // 如果为空则添加
                        $insert_data = ['type'=>$type, 'alipay_order_id'=>$alipay_order['id'], 'trade_no'=>$data['number'], 'time'=>$data['payfor_createtime'], 'name'=>$data['name'], 'refund_no'=>$data['number2'], 'other'=>$data['other'], 'price'=>$data['price'], 'log_status'=>$data['status'], 'status'=>0];
                        $_manual_refund = Db::name('aliapy_manual_refund')->insertGetId($insert_data);
                        return empty($_manual_refund) ? 'manual_refund 保存失败' : true;

                    }else{
                        // 否则则返回已经添加提示
                        return "refund_trace 与 aliapy_manual_refund 都有记录";
                    }

                }
            }else if( $type === 1 ){
                Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '5']);
                $data['price'] = str_replace(",", "", $data['price']);
                $refund = Db::name('refund_trace')->where( ['goods_trade_no' => $data['detail_no'], 'payfor_createtime'=>date('Y-m-s h:i:s',$data['payfor_createtime']), 'order_refund'=>$data['price'] ] )->find();
                if( !empty($refund) )
                {
                    return "吱口令 refund_trace已有记录";
                }

                $manual_refund = Db::name('aliapy_manual_refund')->where('trade_no', $data['number'])->where('time', date('Y-m-s h:i:s',$data['payfor_createtime']))->where('refund_no', $data['detail_no'])->where('price', $data['price'])->find();
                if( !empty($manual_refund) )
                {
                    return "吱口令 aliapy_manual_refund已有记录";
                }
            }

            Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '6']);
            if( !empty($data['alipay_account']) )
            {
                $compare = $this->amount_compare($data);
                if ( !$compare )
                {
                    Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '7']);
                    $alipay_order = Db::name('alipay_order')->where('trade_no', $data['number'])->find();
                    Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '71']);
                    if ( empty($alipay_order) )
                    {
                        return "alipay_order id 没有找到";
                    }
                    Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '72']);
                    $manual_refund = Db::name('aliapy_manual_refund')->where('trade_no', $data['number'])->where('time', $data['payfor_createtime'])->where('refund_no', $data['number2'])->where('price', $data['price'])->find();
                    Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '73']);
                    if( empty($manual_refund) )
                    {
                        Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '74']);
                        // 退款金额与所有商品都不同, 插入不能识别退款表
                        $insert_data = ['type'=>$type, 'alipay_order_id'=>$alipay_order['id'], 'trade_no'=>$data['number'], 'time'=>$data['payfor_createtime'], 'name'=>$data['name'], 'refund_no'=>$data['number2'], 'other'=>$data['other'], 'price'=>$data['price'], 'log_status'=>$data['status'], 'status'=>0];
                        $_manual_refund = Db::name('aliapy_manual_refund')->insertGetId($insert_data);
                        return empty($_manual_refund) ? 'manual_refund 保存失败2' : true;    
                    }else{
                        Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '75']);
                        return "refund_trace 与 aliapy_manual_refund 都有记录";
                    }
                }

                Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '8']);
                return $this->pc_refund( $alipay_order, $data );
            }

            Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => '9']);
            if( !empty($data['pay_own']) )
            {
                return $this->zkl_refund( $data );
            }

        }catch (Exception $e)
        {
            return $e->getMessage();
        }    
    }

    protected function amount_compare( $data )
    {
        foreach ( $data['trade_detail']['ware_list'] as $key => $value )
        {
            if( bccomp( $data['price'], $value['trans_price'], 2 ) != 0 )
            {
                continue;
            }
            return true;
        }
        return false;
    }

    protected function cancel_payment( $data )
    {

        $this->alipay_order_trace( $data['number'], '',4, $data['payfor_createtime'], '' );
        $alipay_acc = $this->get_sys_alipay( $data['alipay_account'] );

        $info = Db::name('alipay_order')->where(['trade_no' => $data['number'], 'sys_alipay_id' => $alipay_acc['id'], 'price' => $data['price']])->find();

        if( $info['order_id'] === 0 )
        {
            $_alipay_order = Db::name( 'alipay_order' )->where( 'id', $info['id'] )->update( ['alipay_status'=>3] );
            return true;
        }

        $order = Db::name('df_order')->where(['id' => $info['order_id'], 'status' => ['in', [0,1,2,7]]])->find();
        Db::name( 'alipay_order')->where('id', $info['id'])->update(['status'=>6, 'alipay_status'=>3] );
        Db::name( 'alipay_order_goods')->where( 'alipay_order_id', $info['id'])->update(['status'=>6] );

        if( empty($order) )
        {
            return '用户订单匹配失败';
        }

        $this->orderLog("" . $data['number'] . ": 代付取消", $order['id']);
        $all_alipay_order = Db::name('alipay_order')->where('order_id', $order['id'])->select();
        $alipay_order_status = array_column($all_alipay_order, 'alipay_status');
        $num_count = array_count_values($alipay_order_status);
        if( count($alipay_order_status) == $num_count[3] )
        {
            Db::name('df_order')->where('id', $order['id'])->update(['order_status'=>10]);
        }

        $pay_type = $order['pay_type'];
        if( $pay_type === 0 )
        {
            return true;
        }else if( $pay_type == 1 )
        {
            // 不做任何处理, 等待用户付款,或者取消
            return true;
        }else if( $pay_type == 2 )
        {
            if( Db::name('refund_trace')->where('trade_no', $data['number'])->find() )
            {
                return '该订单已经退过款了';
            }
            $this->taobiLog($order['user_id'], '11', $info['price'], $order['id'], "代付取消退还", "");
            $refund_trace_data = ['order_id'=>$info['order_id'], 'trade_no'=>$data['number'], 'goods_trade_no'=>'', 'payfor_createtime'=>$data['payfor_createtime'], 'order_refund'=>$info['all_price'], 'goods_refund'=>'', 'remark'=>''];
            $_refund_trace = Db::name('refund_trace')->insert($refund_trace_data);

            if(  !empty($_refund_trace) )
            {
                $user = Db::name('user')->where('id', $order['user_id'])->find();
                $content =  '【付唄】您的代付已取消，退款金額' . $info['price']  .  ' 已幫您轉到F幣賬戶中~請您注意查收~如有問題請您提交申訴';
                $this->sendSMS($user['mobile'], $content);
                return true;
            }
        }
        return '代付取消退款失败';
    }

    protected function pc_refund( $info, $data )
    {
        Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => 'a']);
        if( empty($info) )
        {
            return '未找到退款代付订单商品';
        }
        Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => 'b']);
        $goods_number = count( $data['trade_detail']['ware_list'] );
        if( $goods_number > 1 )
        {
            $order_count = Db::name('alipay_order')->where ( ['order_id'=>$info['order_id'], 'status'=>4] )->count();
            $order_status = (($goods_number - $order_count) == 1) ? 8 : 7;
        }else{
            $order_status = 8;
        }

        Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => 'c']);
        $alipay_order = $info;
        $_alipay_order_goods = Db::name('alipay_order_goods')->where('alipay_order_id', $info['id'])->update(['status'=>4]);
        $_alipay_order = Db::name('alipay_order')->where('id', $info['id'])->update(['status'=>4]);

        $_df_order_status = Db::name('df_order')->where('id', $alipay_order['order_id'])->update(['order_status'=> $order_status]);
        $df_order = Db::name('df_order')->where('id', $alipay_order['order_id'])->find();
        Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => 'd']);
        
        $this->orderLog("" . $data['number'] . '/' . $data['number2'] . ": 退款成功¥" . $info['price'],  $df_order['id']);
        $_log = $this->taobiLog($df_order['user_id'], '11', $data['price'], $df_order['id'], "支付宝官方退还", "");
        Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => 'e']);


        $refund_trace_data = ['order_id'=>$df_order['id'], 'trade_no'=>$data['number'], 'goods_trade_no'=>$data['number2'], 'payfor_createtime'=>$data['payfor_createtime'], 'goods_id'=>$info['id'],'order_refund'=>$data['price'], 'goods_refund'=>'', 'type'=>0, 'remark'=>''];
        $_refund_trace = Db::name('refund_trace')->insert($refund_trace_data);
        Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => 'f']);
        if( !empty($_alipay_order_goods)  &&  !empty($_alipay_order) &&  !empty($_df_order_status) &&  $_log &&  !empty($_refund_trace) )
        {
            return true;
        }
        Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => 'g']);
        $log_arr = array('$order_status'=>$order_status, '$_alipay_order'=>$_alipay_order, '$_alipay_order_goods'=>$_alipay_order_goods, '$_df_order_status'=>$_df_order_status, '$_log'=>$_log, '$_refund_trace'=>$_refund_trace);
        file_put_contents("/var/www/haigou/runtime/pc_refund_" . $info['id'] . ".txt", json_encode($log_arr) . '\n', FILE_APPEND | LOCK_EX);
        Db::name('debugging')->insert(['title' => 'refund_payment', 'msg' => 'h']);
        return '更新退款状态失败';
    }


    public function get_ukey_pay_task()
    {
        $post_data = $this->get_post_data();


        //临时添加的条件
        //$map['sys_alipay_id'] = 11;
        $map['sys_alipay_id'] = array('in',array(5, 11));;


        $map['status'] = 2;
        $map['alipay_status'] = array('in',array(0,6,7,8));

        //获取2分钟内的付款失败订单

        $limited_amount = intval($post_data['limited_amount']);
        $time_count = intval($post_data['time_count']);
        $due_time = [date('Y-m-d H:i:s',time() - 60 * $time_count), date('Y-m-d H:i:s',time())];
        //$df_task = Db::name('alipay_order')->where($map)->where('update_time', 'between time', $due_time)->find();

        if( $limited_amount === 0 )
        {
            $df_task = Db::name('alipay_order')->where($map)->where('update_time', 'between time', $due_time)->find();
        }else{
            $df_task = Db::name('alipay_order')->where($map)->where('update_time', 'between time', $due_time)->where('price', '<=', $limited_amount)->find();
        }

        if( empty($df_task) )
        {
            return $this->return_msg(1, '没有符合要求的订单');
        }
        $alipay = Db::name('sys_alipay')->where('id', $df_task['sys_alipay_id'])->find();
        return $this->return_msg(0, array('alipay_account'=>$alipay['sys_account'], 'peerpay_link'=>$df_task['peerpay_link'], 'price'=>$df_task['price']));
    }

    /**
     *  修改后
     */
    public function update_ukey_pay()
    {
        try
        {
            $post_data = $this->get_post_data();
            $exist_record = Db::name('ukey_pay')->where('alipay_account', $post_data['alipay_account'])->where('peerpay_link', $post_data['peerpay_link'])->where('status', $post_data['status'])->find();

            if( !empty($exist_record) )
            {
                return $this->return_msg(1, '重复的记录');
            }

            $alipay_account = Db::name('sys_alipay')->where('sys_account', $post_data['alipay_account'])->find();
            if( empty($alipay_account) )
            {
                return $this->return_msg(1, '错误的支付宝账号');
            }
            $alipay_order = Db::name('alipay_order')->where('peerpay_link', $post_data['peerpay_link'])->where('sys_alipay_id', $alipay_account['id'])->find();
            //1 插入ukey_pay记录
            $insert_data = ['order_id'=>$alipay_order['order_id'], 'alipay_account'=>$post_data['alipay_account'], 'trade_no'=>$alipay_order['trade_no'], 'peerpay_link'=>$post_data['peerpay_link'], 'price'=>$alipay_order['price'],'pay_tool'=>$post_data['pay_tool'], 'status'=>$post_data['status'], 'remark'=>$post_data['remark']];
            $_insert_res = Db::name('ukey_pay')->insertGetId($insert_data);

            if( $post_data['status'] == "0" && $post_data['remark'] == "交易账户余额不足" )
            {
                Db::name('df_order')->where('id', $alipay_order['order_id'])->update(['order_status'=>2]);
                return $this->return_msg(1, 'U盾余额不足');;
            }

            Db::startTrans();
            //2 alipay_order 订单表状态修改
            // status == 1代付成功

            if( $post_data['status'] == "1" )
            {
                $order_status = 5;
                $alipay_status = 1;
                $goods_status = 5;
            }
            else{
                $order_status = 4;
                $alipay_status = 9;
                $goods_status = 4;
            }

            Db::name('alipay_order')->where('id', $alipay_order['id'])->update(['status'=>$order_status,'alipay_status'=>$alipay_status]);
            Db::name('alipay_order_goods')->where('alipay_order_id',$alipay_order['id'])->update(['status'=>$goods_status]);

            //4 t_df_order
            $alipy_order_array = Db::name('alipay_order')->where('order_id', $alipay_order['order_id'])->select();
            $order_status_arr = array_column($alipy_order_array, 'status');
            $num_count = array_count_values($order_status_arr);

            if( $post_data['status'] == "1" )
            {
                if( count($order_status_arr) == $num_count[5] )
                {
                    Db::name('df_order')->where('id', $alipay_order['order_id'])->update(['order_status'=>1]);
                }
                else if( $num_count[2] > 0 )
                {
                    // order_status = 10   U盾付款
                    Db::name('df_order')->where('id', $alipay_order['order_id'])->update(['order_status'=>2]);
                }
                else if( $num_count[2] === 0 &&  $num_count[5] > 0 && $num_count[4] > 0 )
                {
                    if( ($num_count[5] + $num_count[4]) == count($alipy_order_array) )
                    {
                        Db::name('df_order')->where('id', $alipay_order['order_id'])->update(['order_status'=>7]);
                    }else{
                        //出现不明情况, 转人工
                        Db::name('df_order')->where('id', $alipay_order['order_id'])->update(['order_status'=>2]);
                    }
                }
            }
            else{
                if( count($order_status_arr) == $num_count[4] )
                {
                    Db::name('df_order')->where('id', $alipay_order['order_id'])->update(['order_status'=>8]);
                }
                else if( $num_count[4] > 0 && $num_count[4] < count($order_status_arr) ){
                    Db::name('df_order')->where('id', $alipay_order['order_id'])->update(['order_status'=>7]);
                }
            }

            //5 代付退款
            $suc = true;
            $_refund_trace = 1;
            if( $post_data['status'] == "0" )
            {
                $df_order = Db::name('df_order')->where('id', $alipay_order['order_id'])->find();
                $refund_trace_data = ['order_id'=>$alipay_order['id'], 'trade_no'=>$alipay_order['trade_no'], 'goods_trade_no'=>'', 'order_refund'=>$alipay_order['price'], 'goods_refund'=>'', 'remark'=>''];
                $_refund_trace = Db::name('refund_trace')->insert($refund_trace_data);
                $suc = $this->taobiLog($df_order['user_id'], '11', $alipay_order['price'], $df_order['id'], $post_data['remark'], "");
            }

            if( $suc == true && !empty($_refund_trace) )
            {
                Db::commit();
                return $this->return_msg(0, 'U盾支付完成');
            }
            Db::rollback();
            return $this->return_msg(1, 'U盾支付异常');
        }catch (Exception $e){
            return $this->return_msg('1', '异常: ' . $e->getMessage());
        }
    }

    /**
     * 吱口令退款
     */
    protected function zkl_refund( $data )
    {
        $data['price'] = str_replace(",", "", $data['price']);
        $zkl = Db::name('zkl')->where('pay_own', $data['pay_own'] )->where('order_no', $data['number'] )->where('status', 1)->order('id', 'DESC')->find();
        if( empty($zkl) )
        {
            return '未找到吱口令订单信息';
        }
        if( $zkl['type'] == 6 )
        {
            return '吱口令訂單已經退過款';
        }
        $merge = strpos( $data['number'], 'M');
        if( $merge === false &&  $zkl['type'] == 4 )
        {
            $_zkl = Db::name('zkl')->where('id', $zkl['id'] )->update(['type'=>6]);
            $_order = Db::name('df_order')->where('id', $zkl['order_id'] )->update(['order_status'=>8]);
            $zkl_goods = Db::name('zkl_goods')->where('zkl_id', $zkl['id'] )->find();
            $_zkl_goods = Db::name('zkl_goods')->where('id', $zkl_goods['id'] )->update(['refund_no'=>$data['detail_no'], 'status'=>4]);
            $_refund_trace = Db::name('refund_trace')->insertGetId(['order_id'=>$zkl['order_id'],'trade_no'=>$data['number'],'goods_trade_no'=>$data['detail_no'], 'goods_id'=>$zkl_goods['id'],'order_refund'=>$data['price'],'type'=>1]);
            $_taobi = $this->taobiLog($zkl['user_id'], '11', $data['price'], $zkl['order_id'], "支付宝APP退还", "" );
            $this->orderLog("吱口令: " . $data['number'] . '/' . $data['detail_no'] . ": 退款成功", $zkl['order_id']);
        }else{
            $zkl_goods = Db::name('zkl_goods')->where('zkl_id', $zkl['id'] )->where('price', $data['price'] )->find();
            $_zkl_goods = Db::name('zkl_goods')->where('id', $zkl_goods['id'] )->update(['refund_no'=>$data['detail_no'],'status'=>4]);
            if( empty($zkl_goods) )
            {
                //退款金额, 不等于商品金额的情况
                $refund_data = ['type'=>1,'alipay_order_id'=>$zkl['id'],'trade_no'=>$data['number'],'time'=>date('Y-m-s h:i:s',$data['payfor_createtime']),'name'=>$data['name'],'refund_no'=>$data['detail_no'],'other'=>$data['other1'],'price'=>$data['price'],'log_status'=>$data['status'], 'status'=>0];
                $_refund_id = Db::name('aliapy_manual_refund')->insertGetId($refund_data);
                $remark = '订单号:' . $data['detail_no'] . '金额:' . $data['price'] . '退款失败';
                $_zkl =  Db::name('zkl')->where('id', $zkl['id'] )->update(['type'=>9, 'remark'=>$remark]);
                return (!empty($_refund_id) && !empty($_zkl)) ? true : "吱口令aliapy_manual_refund金额与商品金额不等添加记录失败";
            }
            if( empty($_zkl_goods) )
            {
                return "更新吱口令商品退款状态失败";
            }

            $_goods =  Db::name('zkl_goods')->where('zkl_id', $zkl['id'])->where('status', 5)->find();
            $order_status = empty($_goods) ? 8 : 7;
            $zkl_type = empty($_goods) ? 6 : 8;
            $_zkl = Db::name('zkl')->where('id', $zkl['id'] )->update(['type'=>$zkl_type]);
            $df_order = Db::name('df_order')->where('id', $zkl['order_id'])->find();
            if( $df_order['order_status'] === $order_status )
            {
                $_order = 1;
            }else{
                $_order = Db::name('df_order')->where('id', $zkl['order_id'] )->update(['order_status'=>$order_status]);
            }
            $_refund_trace = Db::name('refund_trace')->insertGetId(['order_id'=>$zkl['order_id'],'trade_no'=>$data['number'],'goods_trade_no'=>$data['detail_no'], 'payfor_createtime'=>date('Y-m-s h:i:s',$data['payfor_createtime']), 'goods_id'=>$zkl_goods['id'], 'order_refund'=>$data['price'],'type'=>1]);
            $_taobi = $this->taobiLog($zkl['user_id'], '11', $data['price'], $zkl['order_id'],"支付宝APP退还","" );
            $this->orderLog("吱口令: " . $data['number'] . '/' . $data['detail_no'] . ": 退款成功¥" . $data['price'], $zkl['order_id']);
        }

        if( !empty($_zkl) && !empty($_order) && !empty($_zkl_goods) && !empty($_refund_trace) && !empty($_taobi) )
        {
            return true;
        }

        $log_arr = array('$_zkl'=>$_zkl, '$_order'=>$_order, '$_zkl_goods'=>$_zkl_goods, '$_refund_trace'=>$_refund_trace, '$_taobi'=>$_taobi);
        file_put_contents("/var/www/haigou/runtime/" . $zkl['zkl_msg'] . ".txt", json_encode($log_arr) . '\n', FILE_APPEND | LOCK_EX);
        return false;
    }

    protected function alipay_order_trace($trade_no, $goods_trade_no, $status, $time, $remark)
    {
        $data = ['trade_no'=>$trade_no, 'goods_trade_no'=>$goods_trade_no, 'status'=> $status, 'time'=> $time, 'remark'=> $remark];
        $_result = Db::name('alipay_order_trace')->insert($data);
        return $_result == 1 ? true : false;
    }


    protected function alipay_order_describe( $status )
    {
        switch ( $status )
        {
            case 3:
                $_result = '代付关闭';
                break;
            case 4:
                $_result = '代付退款';
                break;
            case 5:
                $_result = '代付成功';
                break;
            case 6:
                $_result = '代付已取消';
                break;
        }
        return $_result;
    }

    protected function get_sys_alipay( $account )
    {
        $_result = Db::name( 'sys_alipay' )->where( 'sys_account', $account )->find();
        return $_result;
    }

    protected function get_post_data()
    {
        $post_data = file_get_contents('php://input', 'r' );
        $post_data = trim( $post_data,chr(239).chr(187).chr(191) );
        $post_data = stripslashes( $post_data );
        $data = json_decode( $post_data,true );
        return $data;
    }

    protected function return_msg( $code,$msg )
    {
        $data = array( 'code' => $code,'data' => $msg );
        return json_encode( $data, JSON_UNESCAPED_UNICODE );
    }

    /*返回字符串中的数字*/
    function find_num($str)
    {
        if(empty($str)){return '';}
        $temp=array('1','2','3','4','5','6','7','8','9','0','*');
        $result='';
        for($i=0;$i<strlen($str);$i++){
            if(in_array($str[$i],$temp)){
                $result.=$str[$i];
            }
        }
        return $result;
    }

    function create_guid()
    {
        $charid = strtoupper(md5(uniqid(mt_rand(), true)));
        $hyphen = chr(45);// "-"
        $uuid = substr($charid, 0, 8).$hyphen
            .substr($charid, 8, 4).$hyphen
            .substr($charid,12, 4).$hyphen
            .substr($charid,16, 4).$hyphen
            .substr($charid,20,12);
        return $uuid;
    }

    public function consumption_type($type){
        if($type == '8' || $type == '10' || $type == '11' || $type == '12' || $type == '13'){
            return true;
        }
        if($type == '0' || $type == '1' || $type == '2' || $type == '3' || $type == '4' || $type == '5' || $type == '6' || $type == '7' || $type == '9'){
            return false;
        }
    }

    /**
     * 淘币日志
     */

    public function taobiLog($user_id, $type,$money, $id, $msg="",$info="")
    {
        if(intval($money)){
            $return = $this->consumption_type($type);
            if(!$info){
                $info = Db::name('user')->where('id',$user_id)->find();
            }
            if($return === true){
                $price = $info['money'] + $money;
                // $surplus_price = $info['money'];
            }else{
                $price = $info['money'] - $money;
                // $surplus_price = $info['money'];
            }
            $data = array(
                'type' => $type,
                'order_id' => $id,
                'user_id' => $info['id'],
                'price' => $money,
                'surplus_price' => $price,
                'msg' => $msg,
                'createtime' => time(),
                'updatetime' => time(),
            );
            $time = date('Ymd',time());
            $_map = array(
                'user_id' => $info['id'],
                'day_time' => $time,
            );
            $info_count = Db::name('taobi_count')->where($_map)->find();
            if($info_count){
                Db::name('taobi_count')->where($_map)->update(['money' => $price]);
            }else{
                $_map['money'] = $price;
                $_map['createtime'] = time();
                $_map['updatetime'] = time();
                Db::name('taobi_count')->insert($_map);
            }
            $_log = Db::name('taobi_log')->insert($data);
            $_user = Db::name('user')->where('id', $info['id'])->update(['money' => $price]);

            if(  !empty($_log) &&  !empty($_user) )
            {
                return true;
            }
            return false;
        }
    }

    /**
     * 购物金日志
     */

    public function goldLog($user_id,$type,$money,$id="",$msg='',$info="")
    {
        if(intval($money)){
            $return = $this->consumption_type($type);
            if(!$info){
                $info = Db::name('user')->where('id',$user_id)->find();
            }
            if($return === true){
                $price = $info['buy_gold'] + $money;
                // $surplus_price = $info['money'];
            }else{
                $price = $info['buy_gold'] - $money;
                // $surplus_price = $info['money'];
            }
            $data = array(
                'type' => $type,
                'order_id' => $id,
                'user_id' => $info['id'],
                'price' => $money,
                'surplus_price' => $price,
                'msg' => $msg,
                'createtime' => time(),
                'updatetime' => time(),
            );
            $time = date('Ymd',time());
            $_map = array(
                'user_id' => $info['id'],
                'day_time' => $time,
            );
            $info_count = Db::name('gold_count')->where($_map)->find();
            if($info_count){
                Db::name('gold_count')->where($_map)->update(['money' => $price]);
            }else{
                $_map['money'] = $price;
                $_map['createtime'] = time();
                $_map['updatetime'] = time();
                Db::name('gold_count')->insert($_map);
            }
            Db::name('gold_log')->insert($data);
            Db::name('user')->where('id', $info['id'])->update(['buy_gold' => $price]);
        }
    }

    /**
    订单日志
     */
    public function orderLog($msg, $order_id){
        $data = array(
            'order_id' => $order_id,
            'msg' => $msg,
            'createtime' => time(),
            'updatetime' => time(),
        );
        Db::name('df_order_log')->insert($data);
    }

    /**
    虚拟订单日志
     */
    public function virtualLog($msg,$order_id){
        // Db::name('debugging')->insert(['title' => '订单日志', 'msg' => '22']);
        $data = array(
            'order_id' => $order_id,
            'msg' => $msg,
            'createtime' => time(),
            'updatetime' => time(),
        );
        Db::name('virtual_order_log')
            ->insert($data);
    }

    public function order_processing($order_id){
        Db::name('debugging')->insert(['title' => '订单完成', 'msg' => json_encode($order_id)]);
        /* 获取订单 */
        $order = Db::name('df_order')->field('*')->where(['id'=>$order_id])->find();
        /* 获取用户信息 */
        $info = Db::name('user')->field('*')->where('id', $order['user_id'])->find();
        /* 判斷用戶是否是第一次完成完成訂單 */
        if($info['is_order'] == 0){
            /* 第一次完成訂單獎勵購物金 */
            if($info['pid']){
                /* 获取推荐人信息 */
                $_info = Db::name('user')->field('*')->where('id', $info['pid'])->find();
                User::give_gold($_info,Config::get('site.give4'),$info['id']);
            }
        }
        /* 判斷用戶累計消費金額是否達到贈送購物金條件 */
        $cumulative_rmb = $info['cumulative_rmb'];
        $rmb_mobey = $order['rmb_money'] - $order['balance_money'];
        $count_gold = $cumulative_rmb + $rmb_mobey;
        $rules = Config::get('site.give_gold');

        /* 获取上一次奖励等级 */
        $old_give = array_flip(array_filter($rules, function($rules) use($cumulative_rmb) { return $cumulative_rmb >= $rules; }));
        /* 获取本次奖励等级 */
        $give = array_flip(array_filter($rules, function($rules) use($count_gold) { return $count_gold >= $rules; }));
        sort($old_give);
        sort($give);
        $old_give_str = implode('a',$old_give);
        $give_str = implode('a',$give);
        /* 两次奖励等级不一致给与奖励 */
        if($old_give_str != $give_str){
            $gold_rules = Db::name('gold_rules')->field('*')->where('id',$give['0'])->find();
            $this->goldLog($info['id'], '8',$gold_rules['money'],$order_id,$gold_rules['title'],$info);
        }
        /* 計算用戶是否達到升級要求 */
        $count_exp = $info['score'] + $rmb_mobey;
        /* 更新等級 */
        $level = User::nextlevel($count_exp);
        $data = array(
            'level' => $level,
            'frozen_price' => '0',
            'is_order' => '1',
            'score' => $count_exp,
            'cumulative_rmb' => $count_gold,
            'cumulative_twb' => $info['cumulative_twb'] + $order['actual_money'],
        );
        Db::name('user')->where('id', $info['id'])->update($data);
        Db::name('df_order')->where('id', $order_id)->update(['completetime' => time()]);
        $this->orderLog('订单完成',$order_id);
        /* 添加统计数据 */
        $time = date('Ymd',time());
        $count_map = array(
            'daytime' => date('Ymd',$time),
            'user_id' => $info['id'],
        );
        $info_count = Db::name('count_order')->where($count_map)->find();
        if($info_count){
            /* 存在数据更新 */
            $count_data = array(
                'all_tb_money' => $info_count['all_tb_money'] + $order['actual_money'],
                'all_rmb_money' => $info_count['all_rmb_money'] + ($order['rmb_money'] - $order['balance_money']),
                'all_order' => $info_count + 1,
                'updatetime' => time(),
            );
            Db::name('count_order')->where('user_id', $info_count['user_id'])->update($count_data);
        }else{
            /* 不存在数据添加 */
            $count_data = array(
                'user_id' => $info['id'],
                'all_tb_money' => $order['actual_money'],
                'all_rmb_money' => $order['rmb_money'] - $order['balance_money'],
                'all_order' => '1',
                'daytime' => $time,
                'createtime' => time(),
                'updatetime' => time(),
            );
            Db::name('count_order')->insert($count_data);
        }
    }

    /**
     *  吱口令保存信息
     */
    /*保存吱口令的商品信息*/
    public function zkl_save_info(){
        /*獲取數據*/
        $post_data = $this->get_post_data();
        if( $post_data['zkl_valid'] === 1 )
        {
            $zkl_data = array(
                'pay_own' => $post_data['pay_own'],
                'price' => $post_data['payfor_price'],
                'payfor_goods' => strtr($post_data['payfor_goods'],'\\','-'),
                'payfor_other' => $post_data['payfor_other'],
                'order_no' => $post_data['order_no'],
                'type' => 2,
                'status' => 0,
                'is_usable'=> 0
            );
            $_zkl =  Db::name('zkl')->where('zkl_msg', trim($post_data['zkl']))->update($zkl_data);
            $zkl = Db::name('zkl')->where('zkl_msg', trim($post_data['zkl']))->where('pay_own', $post_data['pay_own'])->find();
            $zkl_goods = $post_data['goodslist'];
            $goods_data = null;
            foreach ( $zkl_goods as $item )
            {
                $goods = ['zkl_id'=>$zkl['id'], 'title'=>$item['title'], 'price'=>$item['price'], 'number'=>$item['number'] ];
                $goods_data[] = $goods;
            }
            $_zkl_goods = Db::name('zkl_goods ')->insertAll($goods_data);
            if( !empty($_zkl) &&  !empty($_zkl_goods) )
            {
                return $this->return_msg(0,'更新成功');
            }else{
                return $this->return_msg(1,'更新失敗');
            }
        }else{

            $type = 2;
            if( !empty($post_data['order_status']) )
            {
                if( $post_data['order_status'] == '订单已取消' )
                {
                    $type = 1;
                }else if( $post_data['order_status'] == '订单已关闭' ){
                    $type = 5;
                }
            }
            $zkl_data = array(
                'pay_own' => $post_data['pay_own'],
                'type' => $type,
                'status' => 0,
                'price' => $post_data['payfor_price'],
                'payfor_goods' => strtr($post_data['payfor_goods'],'\\','-'),
                'payfor_other' => $post_data['payfor_other'],
                'order_no' => $post_data['order_no'],
                'pay_remark'=> $post_data['order_status'],
                'is_usable'=> 0,
                'is_hid' => 1
            );
            $_zkl =  Db::name('zkl')->where('zkl_msg', trim($post_data['zkl']))->update($zkl_data);
            if( !empty($_zkl) )
            {
                return $this->return_msg(0,'更新成功');
            }else{
                return $this->return_msg(1,'更新失敗');
            }
        }
    }

    /**
     *  获取吱口令任务
     */

    public function get_zkl_task()
    {
        /*獲取數據*/
        $post_data = $this->get_post_data();
        $system_alipay = Db::name('sys_alipay')->where('sys_account', $post_data['pay_own'])->where('status', 'normal')->find();
        if( empty($system_alipay) )
        {
            return $this->return_msg(1,'賬號異常');
        }
        Db::startTrans();
        $where_array = ['is_usable'=> 0, 'is_hid'=>0, 'is_del'=> 0];
        $zkl_info = Db::name('zkl')->where($where_array)->where('type', 0)->order('update_time asc')->find();

        if( !empty($zkl_info) )
        {
            $_zkl_info = Db::name('zkl')->where('id', $zkl_info['id'] )->update(['is_usable'=>1]);
            if( !empty($_zkl_info) )
            {
                Db::commit();
                $data_array = array(
                    'zkl' => trim($zkl_info['zkl_msg']),
                    'zkl_time' => $zkl_info['add_time'],
                    'pay_own' => $post_data['pay_own'],
                    'type' => '1',
                );
                return $this->return_msg('0', $data_array);
            }
        }
        $pay_zkl_info = Db::query("SELECT t_zkl.id,t_zkl.zkl_msg,t_zkl.add_time,t_zkl.pay_own,t_df_order.rmb_money FROM t_df_order inner join t_zkl on t_df_order.id=t_zkl.order_id and t_df_order.pay_type in (2,3) and t_df_order.order_status=0 and t_zkl.type=3 and t_zkl.status=1 and t_zkl.is_usable=0 and t_zkl.is_hid=0 and t_zkl.is_del=0 LIMIT 1");
        if( count($pay_zkl_info) > 0 )
        {
            $_zkl_info = Db::name('zkl')->where('id', $pay_zkl_info[0]['id'] )->update(['is_usable'=>1]);
            if( !empty($_zkl_info) )
            {
                Db::commit();
                $data_array = array(
                    'zkl' => trim($pay_zkl_info[0]['zkl_msg']),
                    'zkl_time' => $pay_zkl_info[0]['add_time'],
                    'pay_own' => $pay_zkl_info[0]['pay_own'],
                    'price'=> $pay_zkl_info[0]['rmb_money'],
                    'type' => '2',
                );
                return $this->return_msg('0',$data_array);
            }
        }
        if( empty($zkl_info) && count($pay_zkl_info) <= 0 )
        {
            Db::commit();
            return $this->return_msg(1,'暫無吱口令任务');
        }
        Db::rollback();
        return $this->return_msg(1,'未知错误');
    }

    /**
     *   吱口令返回成功信息
     */
    public function zkl_pay_finshed()
    {
        /*獲取數據*/
        $post_data = $this->get_post_data();
        if($post_data['pay_result'] == '1')
        {
                $map = array(
                    'zkl_msg' => trim($post_data['zkl']),
                    'type' => '3',
                    'status' => '1',
                );
                $zkl_info = Db::name('zkl')->where($map)->find();
                if( empty($zkl_info) )
                {
                        return $this->return_msg(1,'吱口令任务丢失');
                }

                $order_info = Db::name('df_order')->where('id', $zkl_info['order_id'] )->find();
                Db::startTrans();
                $data_zkl = array(
                    'pay_own' => $post_data['pay_own'],
                    'type' => '4',
                    'is_usable' => 0
                );
                $_zkl = Db::name('zkl')->where('id', $zkl_info['id'] )->update($data_zkl);
                $_goods = Db::name('zkl_goods')->where('zkl_id',$zkl_info['id'])->update(['status'=>5]);
                $_order = Db::name('df_order')->where('id', $order_info['id'] )->update(['order_status'=> 1]);
                $this->order_processing($order_info['id']);
                if( !empty($_zkl) && !empty($_goods) && !empty($_order) )
                {
                        Db::commit();
                        return $this->return_msg(0,'成功');
                }else{
                        Db::rollback();
                        return $this->return_msg(0,'失败');
                }
        }else{
                Db::name('zkl')->where('zkl_msg', trim($post_data['zkl'] ))->update(['is_usable' => 0]);
        }
    }


    /**
     *  处理无效吱口令
     */
    public function invalid_zkl(){
        /*獲取數據*/
        $post_data = $this->get_post_data();
        $_zkl = Db::name('zkl')->where('zkl_msg', trim($post_data['zkl']) )->update(['is_hid'=>1, 'is_usable'=>0]);

        if( !empty($_zkl) ){
                return $this->return_msg('0','不在下發');
        }else{
                return $this->return_msg('1','操作失敗');
        }
    }

    /**
     *  重置吱口令
     */
    public function reset_zkl_task(){
        /*獲取數據*/
        $post_data = $this->get_post_data();
        $_zkl = Db::name('zkl')->where('zkl_msg', trim($post_data['zkl']) )->update(['is_usable'=>0]);

        if( !empty($_zkl) ){
                return $this->return_msg('0','重置成功');
        }else{
                return $this->return_msg('1','重置失敗');
        }
    }


    /**
     * 处理无法支付吱口令
     */
    public function caution_zkl(){
        $post_data = $this->get_post_data();
        $_zkl = Db::name('zkl')->where('zkl_msg', trim($post_data['zkl']) )->where('pay_own', $post_data['pay_own'] )->update(['type'=>7, 'pay_remark'=>$post_data['pay_remark'],'is_usable'=>0]);

        $zkl_info = Db::name('zkl')->where('zkl_msg', trim($post_data['zkl']))->find();
        if( $zkl_info['status'] === 1 )
        {
                Db::startTrans();
                $order = Db::name('df_order')->where('id', $zkl_info['order_id'] )->find();
                $_zkl = Db::name('zkl')->where('id', $zkl_info['id'] )->update(['type'=>7, 'pay_remark'=> $post_data['pay_remark'], 'is_hid'=>1, 'is_usable'=>0]);
                $_order = Db::name('df_order')->where('id', $order['id'])->update(['order_status'=>8]);
                $res = $this->taobiLog($order['user_id'], '11', $order['rmb_money'], $order['id'], "支付宝APP无法付款退还", "" );
                $_refund_trace = Db::name('refund_trace')->insertGetId(['order_id'=>$zkl_info['order_id'],'trade_no'=>$zkl_info['order_no'],'goods_trade_no'=>'', 'order_refund'=>$zkl_info['price'],'type'=>1]);

                if( !empty($_zkl) && $_order &&  $res && !empty($_refund_trace) )
                {
                        Db::commit();
                        return $this->return_msg('0','更新成功');
                }else{
                        Db::rollback();
                        return $this->return_msg('0','更新失败');
                }
        }
        if($zkl_info['status'] === 0)
        {
                $_zkl = Db::name('zkl')->where('id', $zkl_info['id'] )->update(['type'=>7, 'pay_remark'=> $post_data['pay_remark'], 'is_hid'=>1, 'is_usable'=>0]);
                $_order = Db::name('df_order')->where('id', $zkl_info['order_id'] )->update(['order_status'=>9]);
                return $this->return_msg('0','不在下發');
        }
    }

    /**
     * 吱口令支付失败
     */
    public function zkl_failure(){
        $post_data = $this->get_post_data();
        $_map = array(
            'zkl_msg' => trim($post_data['zkl']),
            'type' => '3'
        );
        $zkl_info = Db::name('zkl')->where($_map)->find();
        if($zkl_info['status'] === 1)
        {
                Db::startTrans();
                $order = Db::name('df_order')->where('id', $zkl_info['order_id'] )->find();
                $_zkl = Db::name('zkl')->where('id', $zkl_info['id'] )->update(['type'=>5, 'pay_remark'=> $post_data['pay_remark'], 'is_hid'=>1, 'is_usable'=>0]);
                $_order = Db::name('df_order')->where('id', $order['id'])->update(['order_status'=>8]);

                if( strpos($post_data['pay_remark'], "付款异常") !== false )
                {
                    $res = 1;
                    $refund_data = ['type'=>1,'alipay_order_id'=>$zkl_info['id'],'trade_no'=>$zkl_info['number'],'time'=>'','name'=>'','refund_no'=>'','other'=>$zkl_info['payfor_other'],'price'=>$zkl_info['price'],'log_status'=>$post_data['pay_remark'], 'status'=>0];
                    $_refund_trace = Db::name('aliapy_manual_refund')->insertGetId($refund_data);

                }else{
                    if( $zkl_info['price'] >= 1000 )
                    {
                        $res = 1;
                        $refund_data = ['type'=>1,'alipay_order_id'=>$zkl_info['id'],'trade_no'=>$zkl_info['number'],'time'=>'','name'=>'','refund_no'=>'','other'=>$zkl_info['payfor_other'],'price'=>$zkl_info['price'],'log_status'=>$post_data['pay_remark'], 'status'=>0];
                        $_refund_trace = Db::name('aliapy_manual_refund')->insertGetId($refund_data);
                    }else{
                        $res = $this->taobiLog($order['user_id'], '11', $order['rmb_money'], $order['id'], "支付宝APP付款失败退还", "" );
                        $_refund_trace = Db::name('refund_trace')->insertGetId(['order_id'=>$zkl_info['order_id'],'trade_no'=>$zkl_info['order_no'],'goods_trade_no'=>'', 'order_refund'=>$zkl_info['price'],'type'=>1]);
                    }
                }
                if( !empty($_zkl) && $_order &&  $res && !empty($_refund_trace) )
                {
                        Db::commit();
                        return $this->return_msg('0','更新成功');
                }else{
                        Db::rollback();
                        return $this->return_msg('0','更新失败');
                }
        }
        if($zkl_info['status'] === 0)
        {
                $_zkl = Db::name('zkl')->where('id', $zkl_info['id'] )->update(['type'=>5, 'pay_remark'=> $post_data['pay_remark'], 'is_hid'=>1, 'is_usable'=>0]);
                $_order = Db::name('df_order')->where('id', $zkl_info['order_id'] )->update(['order_status'=>9]);
                return $this->return_msg('0','更新成功');
        }
    }

    public function reset_zkl_usable()
    {
            $post_data = $this->get_post_data();
            $_zkl = Db::name('zkl')->where('zkl_msg', trim($post_data['zkl']) )->update(['is_usable'=>0]);

            if( !empty($_zkl) ){
                    return $this->return_msg('0','重设成功');
            }else{
                    return $this->return_msg('1','重设失败');
            }
    }

    /**
     * 更新账号余额
     */
    public function update_system_alipay_balance()
    {
            $post_data = $this->get_post_data();
            $date_time = date('Y-m-d',time());
            //Db::name('debugging')->insertGetId(['msg'=>'alipay_account: ' . $post_data['alipay_account'] . ', balance: ' . $post_data['balance'], 'title'=>'alipay_balance']);

            $_sys_alipay = Db::name('sys_alipay')->where('sys_account', $post_data['alipay_account'] )->update(['reserve_money'=>$post_data['balance'], 'updatetime'=>time()]);
            $day_row = Db::name('sys_alipay_balance')->where('account', $post_data['alipay_account'])->where('datetime', $date_time)->find();
            if( empty($day_row) )
            {
                    $_row = Db::name('sys_alipay_balance')->insertGetId(['account'=>$post_data['alipay_account'], 'balance'=>$post_data['balance'], 'datetime'=>$date_time, 'updatetime'=>time()]);
            }else{
                    $_row = Db::name('sys_alipay_balance')->where('account', $post_data['alipay_account'])->where('datetime', $date_time)->update(['balance'=>$post_data['balance'], 'updatetime'=>time()]);
            }

            if( !empty($_sys_alipay) && !empty($_row) )
            {
                    return $this->return_msg(0,'更新成功');
            }else{
                    return $this->return_msg(1,'更新失败');
            }
    }

    private function get_alipay_trade_detail_type($type)
    {
        if( strpos($type, "在线支付") !== false )
        {
            $_return = 0;
        }else if( strpos($type, "退款") !== false )
        {
            $_return = 1;
        }else if( strpos($type, "转账") !== false )
        {
            $_return = 2;
        }else{
            $_return = 3;
        }
        return $_return;
    }

    /**
     *  支付宝消费详情
     */
    public function alipay_trade_detail()
    {
        try
        {
                $post_data = $this->get_post_data();
                $type = $this->get_alipay_trade_detail_type($post_data['type']);
                $map = ['alipay_account'=>$post_data['alipay_account'], 'create_time'=>$post_data['time'], 'goods_trade_no'=>$post_data['goods_trade_no'], 'bus_order_no'=>$post_data['bus_order_no'], 'type'=>$type, 'balance'=>$post_data['balance']];
                $row = Db::name('sys_alipay_trade_detail')->where($map)->find();
                if( !empty($row) )
                {
                        return $this->return_msg(1,'记录已经存在');
                }

                $map['addtime'] = strtotime($post_data['time']);
                $map['biz_full_name'] = $post_data['biz_full_name'];
                $map['other_name'] = $post_data['other_name'];
                $map['other_email'] = $post_data['other_email'];
                $map['goods_title'] = $post_data['goods_title'];
                $map['in_money'] = $post_data['in_money'];
                $map['out_money'] = $post_data['out_money'];

                $_detail = Db::name('sys_alipay_trade_detail')->insertGetId($map);
                if( empty($_detail) )
                {
                        return $this->return_msg(0,'插入失败');
                }
                return $this->return_msg(0,'插入成功');
        }
        catch (Exception $e)
        {
                return $this->return_msg(1,'异常: ' .  $e->getMessage());
        }
    }

    /**
     *  提交二维码URL
     */
    public function submit_qrcode()
    {
            $post_data = $this->get_post_data();
            $alipay_qrcode = Db::name('alipay_qrcode')->where('alipay_account', $post_data['alipay_account'])->find();
            if( empty($alipay_qrcode) )
            {
                    $_res = Db::name('alipay_qrcode')->insertGetId(['alipay_account'=>$post_data['alipay_account'], 'qr_url'=>$post_data['qr_url']]);
            }else{
                    $data = ['alipay_account'=>$post_data['alipay_account'], 'qr_url'=>$post_data['qr_url'],'status'=>0,'update_time'=>date('Y-m-d H:i:s', time())];
                    $_res = Db::name('alipay_qrcode')->where('id', $alipay_qrcode['id'])->update($data);
            }
            if( !empty($_res) )
            {
                    return $this->return_msg(0,'更新成功');
            }
            return $this->return_msg(1,'更新失败');
    }

    public function complete_qrcode()
    {
        $post_data = $this->get_post_data();
        $_qrcode = Db::name('alipay_qrcode')->where('alipay_account', $post_data['alipay_account'])->update(['status'=>2, 'update_time'=>date('Y-m-d H:i:s', time())]);
        if( !empty($_qrcode) )
        {
                return $this->return_msg(0,'更新成功');
        }
        return $this->return_msg(1,'更新失败');
    }

    public function get_qrcode_status()
    {
        $post_data = $this->get_post_data();
        $qrcode = Db::name('alipay_qrcode')->where('alipay_account', $post_data['alipay_account'])->find();
        if( !empty($qrcode) )
        {
                return $this->return_msg(0, array('status'=>$qrcode['status']) );
        }
        return $this->return_msg(1,'获取失败');
    }

    public function  get_qrcode_url()
    {
        $post_data = $this->get_post_data();
        $qrcode = Db::name('alipay_qrcode')->where('alipay_account', $post_data['alipay_account'])->find();
        if( !empty($qrcode) )
        {
                if( $qrcode['status'] === 0 )
                {
                        $_qrcode = Db::name('alipay_qrcode')->where('id', $qrcode['id'])->update(['status'=>1, 'update_time'=>date('Y-m-d H:i:s', time())]);
                        if( empty($_qrcode) )
                        {
                                return $this->return_msg(1,'更新二维码状态失败');
                        }
                        return $this->return_msg(0, array('alipay_account'=>$qrcode['alipay_account'], 'qr_url'=>$qrcode['qr_url']));
                }else{
                        return $this->return_msg(1,'暂无二维码需要处理');
                }
        }
        return $this->return_msg(1,'无二维码任务');
    }

    /**
     *  更新后的扫码接口
     */
    public function  get_qrcode_task()
    {
            $post_data = $this->get_post_data();
            $qrcode = Db::name('ali_qrcode')->where('alipay_account', $post_data['alipay_account'])->find();
            if( !empty($qrcode) )
            {
                    if( $qrcode['status'] === 0 )
                    {
                            $_qrcode = Db::name('ali_qrcode')->where('id', $qrcode['id'])->update(['status'=>1, 'update_time'=>date('Y-m-d H:i:s', time())]);
                            if( empty($_qrcode) )
                            {
                                    return $this->return_msg(1,'更新二维码状态失败');
                            }
                            return $this->return_msg(0, array('alipay_account'=>$qrcode['alipay_account'], 'type'=>$qrcode['type'],'qr_url'=>$qrcode['qr_url']));
                    }else{
                            return $this->return_msg(1,'暂无二维码需要处理');
                    }
            }
            return $this->return_msg(1,'无二维码任务');
    }

    public function get_ali_qrcode_status()
    {
            $post_data = $this->get_post_data();
            $qrcode = Db::name('ali_qrcode')->where('alipay_account', $post_data['alipay_account'])->find();
            if( !empty($qrcode) )
            {
                    return $this->return_msg(0, array('status'=>$qrcode['status']) );
            }
            return $this->return_msg(1,'获取失败');
    }

    public function finish_qrcode()
    {
            $post_data = $this->get_post_data();
            $_qrcode = Db::name('ali_qrcode')->where('alipay_account', $post_data['alipay_account'])->update(['status'=>2, 'update_time'=>date('Y-m-d H:i:s', time())]);
            if( !empty($_qrcode) )
            {
                    return $this->return_msg(0,'更新成功');
            }
            return $this->return_msg(1,'更新失败');
    }

    public function submit_ali_qrcode()
    {
            $post_data = $this->get_post_data();
            $type = $post_data['type'];
            $pos = strpos($post_data['qr_url'], 'securityId=');
            $security_id = substr($post_data['qr_url'], $pos + 11);

            if( $type != 1 && $type != 2 )
            {
                    return $this->return_msg(1,'缺少type参数');
            }

            $alipay_qrcode = Db::name('ali_qrcode')->where('alipay_account', $post_data['alipay_account'])->find();
            if( empty($alipay_qrcode) )
            {
                    if( !empty($security_id) )
                    {
                            $_res = Db::name('ali_qrcode')->insertGetId(['alipay_account'=>$post_data['alipay_account'], 'qr_url'=>$post_data['qr_url'], 'type'=>$type, 'status'=>0]);
                    }
            }else{
                    if( !empty($security_id)  )
                    {
                            $data = ['alipay_account'=>$post_data['alipay_account'], 'qr_url'=>$post_data['qr_url'], 'type'=>$type, 'status'=>0,'update_time'=>date('Y-m-d H:i:s', time())];
                            $_res = Db::name('ali_qrcode')->where('id', $alipay_qrcode['id'])->update($data);
                    }else{
                            $data = ['alipay_account'=>$post_data['alipay_account'], 'qr_url'=>$post_data['qr_url'], 'type'=>$type, 'status'=>2,'update_time'=>date('Y-m-d H:i:s', time())];
                            $_res = Db::name('ali_qrcode')->where('id', $alipay_qrcode['id'])->update($data);
                    }
            }
            if( !empty($_res) )
            {
                    return $this->return_msg(0,'更新成功');
            }
            return $this->return_msg(1,'更新失败');
    }

    /**
     * 获取过滤关键字
     */
    public function get_taboo_keywords()
    {
        $keywords = Config::get('site.filtering');
        return $this->return_msg(0, array('keywords'=>$keywords));
    }

    /**
     * 获取商品标题
     */
    public function get_product_title()
    {
        $post_data = $this->get_post_data();
        if( empty($post_data) )
        {
            return $this->return_msg(1, '数据错误');
        }
        $goods = Db::query('SELECT t_aog.title FROM t_alipay_order_goods t_aog INNER JOIN (SELECT * FROM t_alipay_order WHERE trade_no=?) t_ao on t_aog.alipay_order_id=t_ao.id', [$post_data['number']]);
        return $this->return_msg(0, array('title'=>$goods));
    }


    public function get_device_account()
    {
        $post_data = $this->get_post_data();
        $imei = $post_data['imei'];
        $device = Db::name('device_account')->where('imei', $imei)->find();
        if( !empty($device) )
        {
            return $this->return_msg(0,array('account'=>$device['account']));
        }
        return $this->return_msg(1,'匹配失败');
    }


    public function get_submit_url()
    {
        $post_data = $this->get_post_data();
        $select_config = Db::name("config")->where("name", "sc_server_select")->find();
        $select_config_value = $select_config['value'];
        $select_config_content = $select_config['content'];

        $content_json = json_decode($select_config_content, true);
        $value = $content_json[$select_config_value];

        $url_config = Db::name("config")->where("name", "server_url")->find();
        $server_url_value = $url_config['value'];
        $url_json = json_decode($server_url_value, true);
        $return_url = $url_json[$value];
        if( !empty($return_url) )
        {
            return $this->return_msg(0,array('server_url'=>$return_url));
        }
        return $this->return_msg(1,'获取URL失败');
    }

    private function update_invoice($bank_title, $invoice_id)
    {
        try
        {
                if( $bank_title == "玉山商銀" )
                {
                        Db::name('invoice')->where('id', $invoice_id)->update(['invoice_type'=>3]);
                }
        }catch (Exception $e){
        }
    }

    private function sendSMS($mobile, $content)
    {
            $url = 'http://smsb2c.mitake.com.tw/b2c/mtk/SmSend?';
            $url .= '&username=**********';
            $url .= '&password=686868';
            $url .= '&dstaddr='.$mobile;
            $url .= '&smbody='.urlencode($content);
            $url .= '&CharsetURL=UTF-8';
            // echo $url;exit;
            $curl = curl_init();
            curl_setopt($curl, CURLOPT_URL, $url);
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
            $output = curl_exec($curl);
            curl_close($curl);
            if(count(explode('statuscode=1',$output)) > 1){
                return true;
            }else{
                return false;
            }
    }

}