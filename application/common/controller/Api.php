<?php

namespace app\common\controller;

use app\common\library\Auth;
use think\Config;
use think\exception\HttpResponseException;
use think\exception\ValidateException;
use think\Hook;
use think\Lang;
use think\Loader;
use think\Request;
use think\Response;
use think\Route;
use app\common\model\User;
use think\Db;

/**
 * API控制器基类
 */
class Api
{

    /**
     * @var Request Request 实例
     */
    protected $request;

    /**
     * @var bool 验证失败是否抛出异常
     */
    protected $failException = false;

    /**
     * @var bool 是否批量验证
     */
    protected $batchValidate = false;

    /**
     * @var array 前置操作方法列表
     */
    protected $beforeActionList = [];

    /**
     * 无需登录的方法,同时也就不需要鉴权了
     * @var array
     */
    protected $noNeedLogin = [];

    /**
     * 无需鉴权的方法,但需要登录
     * @var array
     */
    protected $noNeedRight = [];

    /**
     * 权限Auth
     * @var Auth
     */
    protected $auth = null;

    /**
     * 默认响应输出类型,支持json/xml
     * @var string
     */
    protected $responseType = 'json';

    /**
     * 构造方法
     * @access public
     * @param Request $request Request 对象
     */
    public function __construct(Request $request = null)
    {
        $this->request = is_null($request) ? Request::instance() : $request;

        // 控制器初始化
        $this->_initialize();

        // 前置操作方法
        if ($this->beforeActionList) {
            foreach ($this->beforeActionList as $method => $options) {
                is_numeric($method) ?
                    $this->beforeAction($options) :
                    $this->beforeAction($method, $options);
            }
        }
    }

    /**
     * 初始化操作
     * @access protected
     */
    protected function _initialize()
    {
        if (Config::get('url_domain_deploy')) {
            $domain = Route::rules('domain');
            if (isset($domain['api'])) {
                if (isset($_SERVER['HTTP_ORIGIN'])) {
                    header("Access-Control-Allow-Origin: " . $this->request->server('HTTP_ORIGIN'));
                    header('Access-Control-Allow-Credentials: true');
                    header('Access-Control-Max-Age: 86400');
                }
                if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
                    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_METHOD'])) {
                        header("Access-Control-Allow-Methods: GET, POST, OPTIONS");
                    }
                    if (isset($_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS'])) {
                        header("Access-Control-Allow-Headers: {$_SERVER['HTTP_ACCESS_CONTROL_REQUEST_HEADERS']}");
                    }
                }
            }
        }

        //移除HTML标签
        $this->request->filter('trim,strip_tags,htmlspecialchars');

        $this->auth = Auth::instance();

        $modulename = $this->request->module();
        $controllername = strtolower($this->request->controller());
        $actionname = strtolower($this->request->action());

        // token
        $token = $this->request->server('HTTP_TOKEN', $this->request->request('token', \think\Cookie::get('token')));

        $path = str_replace('.', '/', $controllername) . '/' . $actionname;
        // 设置当前请求的URI
        $this->auth->setRequestUri($path);
        // 检测是否需要验证登录
        if (!$this->auth->match($this->noNeedLogin)) {
            //初始化
            $this->auth->init($token);
            //检测是否登录
            if (!$this->auth->isLogin()) {
                $this->error(__('Please login first'), null, 401);
            }
            // 判断是否需要验证权限
            if (!$this->auth->match($this->noNeedRight)) {
                // 判断控制器和方法判断是否有对应权限
                if (!$this->auth->check($path)) {
                    $this->error(__('You have no permission'), null, 403);
                }
            }
        } else {
            // 如果有传递token才验证是否登录状态
            if ($token) {
                $this->auth->init($token);
            }
        }

        $upload = \app\common\model\Config::upload();

        // 上传信息配置后
        Hook::listen("upload_config_init", $upload);

        Config::set('upload', array_merge(Config::get('upload'), $upload));

        // 加载当前控制器语言包
        $this->loadlang($controllername);
    }

    /**
     * 加载语言文件
     * @param string $name
     */
    protected function loadlang($name)
    {
        Lang::load(APP_PATH . $this->request->module() . '/lang/' . $this->request->langset() . '/' . str_replace('.', '/', $name) . '.php');
    }

    /**
     * 操作成功返回的数据
     * @param string $msg    提示信息
     * @param mixed  $data   要返回的数据
     * @param int    $code   错误码，默认为1
     * @param string $type   输出类型
     * @param array  $header 发送的 Header 信息
     */
    protected function success($msg = '', $data = null, $code = 1, $type = null, array $header = [])
    {
        $this->result($msg, $data, $code, $type, $header);
    }

    /**
     * 操作失败返回的数据
     * @param string $msg    提示信息
     * @param mixed  $data   要返回的数据
     * @param int    $code   错误码，默认为0
     * @param string $type   输出类型
     * @param array  $header 发送的 Header 信息
     */
    protected function error($msg = '', $data = null, $code = 0, $type = null, array $header = [])
    {
        $this->result($msg, $data, $code, $type, $header);
    }

    /**
     * 返回封装后的 API 数据到客户端
     * @access protected
     * @param mixed  $msg    提示信息
     * @param mixed  $data   要返回的数据
     * @param int    $code   错误码，默认为0
     * @param string $type   输出类型，支持json/xml/jsonp
     * @param array  $header 发送的 Header 信息
     * @return void
     * @throws HttpResponseException
     */
    protected function result($msg, $data = null, $code = 0, $type = null, array $header = [])
    {
        $result = [
            'code' => $code,
            'msg'  => $msg,
            'time' => Request::instance()->server('REQUEST_TIME'),
            'data' => $data,
        ];
        // 如果未设置类型则自动判断
        $type = $type ? $type : ($this->request->param(config('var_jsonp_handler')) ? 'jsonp' : $this->responseType);

        if (isset($header['statuscode'])) {
            $code = $header['statuscode'];
            unset($header['statuscode']);
        } else {
            //未设置状态码,根据code值判断
            $code = $code >= 1000 || $code < 200 ? 200 : $code;
        }
        $response = Response::create($result, $type, $code)->header($header);
        throw new HttpResponseException($response);
    }

    /**
     * 前置操作
     * @access protected
     * @param  string $method  前置操作方法名
     * @param  array  $options 调用参数 ['only'=>[...]] 或者 ['except'=>[...]]
     * @return void
     */
    protected function beforeAction($method, $options = [])
    {
        if (isset($options['only'])) {
            if (is_string($options['only'])) {
                $options['only'] = explode(',', $options['only']);
            }

            if (!in_array($this->request->action(), $options['only'])) {
                return;
            }
        } elseif (isset($options['except'])) {
            if (is_string($options['except'])) {
                $options['except'] = explode(',', $options['except']);
            }

            if (in_array($this->request->action(), $options['except'])) {
                return;
            }
        }

        call_user_func([$this, $method]);
    }

    /**
     * 设置验证失败后是否抛出异常
     * @access protected
     * @param bool $fail 是否抛出异常
     * @return $this
     */
    protected function validateFailException($fail = true)
    {
        $this->failException = $fail;

        return $this;
    }

    /**
     * 验证数据
     * @access protected
     * @param  array        $data     数据
     * @param  string|array $validate 验证器名或者验证规则数组
     * @param  array        $message  提示信息
     * @param  bool         $batch    是否批量验证
     * @param  mixed        $callback 回调方法（闭包）
     * @return array|string|true
     * @throws ValidateException
     */
    protected function validate($data, $validate, $message = [], $batch = false, $callback = null)
    {
        if (is_array($validate)) {
            $v = Loader::validate();
            $v->rule($validate);
        } else {
            // 支持场景
            if (strpos($validate, '.')) {
                list($validate, $scene) = explode('.', $validate);
            }

            $v = Loader::validate($validate);

            !empty($scene) && $v->scene($scene);
        }

        // 批量验证
        if ($batch || $this->batchValidate) {
            $v->batch(true);
        }
        // 设置错误信息
        if (is_array($message)) {
            $v->message($message);
        }
        // 使用回调验证
        if ($callback && is_callable($callback)) {
            call_user_func_array($callback, [$v, &$data]);
        }

        if (!$v->check($data)) {
            if ($this->failException) {
                throw new ValidateException($v->getError());
            }

            return $v->getError();
        }

        return true;
    }
	
	public function order_processing($order_id){
        /* 获取订单 */
        $order = Db::name('df_order')->field('*')->where(['id'=>$order_id])->find();
        /* 获取用户信息 */
        $info = Db::name('user')->field('*')->where('id', $order['user_id'])->find();
        /* 判斷用戶是否是第一次完成完成訂單 */
        if($info['is_order'] == 0){
            /* 第一次完成訂單獎勵購物金 */
            if($info['pid']){
                /* 获取推荐人信息 */
                $_info = Db::name('user')->field('*')->where('id', $info['pid'])->find();
                User::give_gold($_info,Config::get('site.give4'),$info['id']);
            }
        }
        /* 判斷用戶累計消費金額是否達到贈送購物金條件 */
        $cumulative_rmb = $info['cumulative_rmb'];
        $rmb_mobey = $order['rmb_money'] - $order['balance_money'];
        $count_gold = $cumulative_rmb + $rmb_mobey;
        $rules = Config::get('site.give_gold');

        /* 获取上一次奖励等级 */
        $old_give = array_flip(array_filter($rules, function($rules) use($cumulative_rmb) { return $cumulative_rmb >= $rules; }));
        /* 获取本次奖励等级 */
        $give = array_flip(array_filter($rules, function($rules) use($count_gold) { return $count_gold >= $rules; }));
        sort($old_give);
        sort($give);
        $old_give_str = implode('a',$old_give);
        $give_str = implode('a',$give);
        /* 两次奖励等级不一致给与奖励 */
        if($old_give_str != $give_str){
            $gold_rules = Db::name('gold_rules')->field('*')->where('id',$give['0'])->find();
            $this->goldLog($info['id'], '8',$gold_rules['money'],$order_id,$gold_rules['title'],$info);
        }
        /* 計算用戶是否達到升級要求 */
        $count_exp = $info['score'] + $rmb_mobey;
        /* 更新等級 */
        $level = User::nextlevel($count_exp);
        $data = array(
            'level' => $level,
            'frozen_price' => '0',
            'is_order' => '1',
            'score' => $count_exp,
            'cumulative_rmb' => $count_gold,
            'cumulative_twb' => $info['cumulative_twb'] + $order['actual_money'],
        );
        Db::name('user')->where('id', $info['id'])->update($data);
        Db::name('df_order')->where('id', $order_id)->update(['completetime' => time()]);
        $this->orderLog('订单完成',$order_id);
        /* 添加统计数据 */
        $time = date('Ymd',time());
        $count_map = array(
            'daytime' => date('Ymd',$time),
            'user_id' => $info['id'],
        );
        $info_count = Db::name('count_order')->where($count_map)->find();
        if($info_count){
            /* 存在数据更新 */
            $count_data = array(
                'all_tb_money' => $info_count['all_tb_money'] + $order['actual_money'],
                'all_rmb_money' => $info_count['all_rmb_money'] + ($order['rmb_money'] - $order['balance_money']),
                'all_order' => $info_count + 1,
                'updatetime' => time(),
            );
            Db::name('count_order')->where('user_id', $info_count['user_id'])->update($count_data);
        }else{
            /* 不存在数据添加 */
            $count_data = array(
                'user_id' => $info['id'],
                'all_tb_money' => $order['actual_money'],
                'all_rmb_money' => $order['rmb_money'] - $order['balance_money'],
                'all_order' => '1',
                'daytime' => $time,
                'createtime' => time(),
                'updatetime' => time(),
            );
            Db::name('count_order')->insert($count_data);
        }
    }
	/**
	订单日志
	*/
	public function orderLog($msg,$order_id,$table='df_order'){
		$data = array(
			'order_id' => $order_id,
			'msg' => $msg,
			'createtime' => time(),
			'updatetime' => time(),
		);
		/*获取年份*/
		$y = date('Y');
		$table = $table.'_log_'.$y;
		/*检查表是否存在*/
		$t_table = 't_'.$table;
		if(count(db()->query('SHOW TABLES LIKE '."'".$table."'"))){
			/*存在*/
			Db::name($table)->insert($data);
		}else{
			/*不存在*/
			$sql = 'CREATE TABLE if not exists '.$t_table.' LIKE t_df_order_log';
			Db::query($sql);
			Db::name($table)->insert($data);
		}
	}
	/**
	* 购物金日志
	*/
	public function goldLog($type,$money,$id="",$msg='',$info=""){
		if(intval($money)){
			$return = $this->consumption_type($type);
			if(!$info){
				$info = $this->auth->getUser();
			}
			if($return === true){
				$price = $info['buy_gold'] + $money;
				// $surplus_price = $info['money'];
			}else{
				$price = $info['buy_gold'] - $money;
				// $surplus_price = $info['money'];
			}
			$data = array(
				'type' => $type,
				'order_id' => $id,
				'user_id' => $info['id'],
				'price' => $money,
				'surplus_price' => $price,
				'msg' => $msg,
				'createtime' => time(),
				'updatetime' => time(),
			);
			$time = date('Ymd',time());
			$_map = array(
				'user_id' => $info['id'],
				'day_time' => $time,
			);
			$info_count = Db::name('gold_count')->where($_map)->find();
			if($info_count){
				Db::name('gold_count')->where($_map)->update(['money' => $price]);
			}else{
				$_map['money'] = $price;
				$_map['createtime'] = time();
				$_map['updatetime'] = time();
				Db::name('gold_count')->insert($_map);
			}
			Db::name('gold_log')->insert($data);
			Db::name('user')->where('id', $info['id'])->update(['buy_gold' => $price]);
		}
	}
	/**
	* 淘币日志
	*/
	public function taobiLog($user_id, $type,$money, $id, $msg="",$info="")
    {
        if(intval($money)){
            $return = $this->consumption_type($type);
            if(!$info){
                $info = Db::name('user')->where('id',$user_id)->find();
            }
            if($return === true){
                $price = $info['money'] + $money;
                // $surplus_price = $info['money'];
            }else{
                $price = $info['money'] - $money;
                // $surplus_price = $info['money'];
            }
            $data = array(
                'type' => $type,
                'order_id' => $id,
                'user_id' => $info['id'],
                'price' => $money,
                'surplus_price' => $price,
                'msg' => $msg,
                'createtime' => time(),
                'updatetime' => time(),
            );
            $time = date('Ymd',time());
            $_map = array(
                'user_id' => $info['id'],
                'day_time' => $time,
            );
            $info_count = Db::name('taobi_count')->where($_map)->find();
            if($info_count){
                Db::name('taobi_count')->where($_map)->update(['money' => $price]);
            }else{
                $_map['money'] = $price;
                $_map['createtime'] = time();
                $_map['updatetime'] = time();
                Db::name('taobi_count')->insert($_map);
            }
            $_log = Db::name('taobi_log')->insert($data);
            $_user = Db::name('user')->where('id', $info['id'])->update(['money' => $price]);

            if(  !empty($_log) &&  !empty($_user) )
            {
                return true;
            }
            return false;
        }
    }
	/**
	* 消费类型
	* 8系統增加,10推廣任務獎勵',11取消訂單退還',12用戶取消退款',13返利提現',
	
	* 9系統減少',0阿里巴巴代付消費',1淘寶天貓代付消費',2淘寶天貓吱口令消費',
	* 3支付唄儲值消費',4微信儲值消費',5遊戲視頻儲值消費',6其他儲值消費',7淘幣儲值消費'
	*/
	public function consumption_type($type){
		if($type == '8' || $type == '10' || $type == '11' || $type == '12' || $type == '13'){
			return true;
		}else{
			return false;
		}
	}
	
}
