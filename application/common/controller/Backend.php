<?php

namespace app\common\controller;

use app\admin\library\Auth;
use think\Config;
use think\Controller;
use think\Hook;
use think\Lang;
use think\Loader;
use think\Session;
use think\Db;
use fast\Tree;
use app\common\model\User;

/**
 * 后台控制器基类
 */
class Backend extends Controller
{

    /**
     * 无需登录的方法,同时也就不需要鉴权了
     * @var array
     */
    protected $noNeedLogin = [];

    /**
     * 无需鉴权的方法,但需要登录
     * @var array
     */
    protected $noNeedRight = [];

    /**
     * 布局模板
     * @var string
     */
    protected $layout = 'default';

    /**
     * 权限控制类
     * @var Auth
     */
    protected $auth = null;

    /**
     * 模型对象
     * @var \think\Model
     */
    protected $model = null;

    /**
     * 快速搜索时执行查找的字段
     */
    protected $searchFields = 'id';

    /**
     * 是否是关联查询
     */
    protected $relationSearch = false;

    /**
     * 是否开启数据限制
     * 支持auth/personal
     * 表示按权限判断/仅限个人
     * 默认为禁用,若启用请务必保证表中存在admin_id字段
     */
    protected $dataLimit = false;

    /**
     * 数据限制字段
     */
    protected $dataLimitField = 'admin_id';

    /**
     * 数据限制开启时自动填充限制字段值
     */
    protected $dataLimitFieldAutoFill = true;

    /**
     * 是否开启Validate验证
     */
    protected $modelValidate = false;

    /**
     * 是否开启模型场景验证
     */
    protected $modelSceneValidate = false;

    /**
     * Multi方法可批量修改的字段
     */
    protected $multiFields = 'status';

    /**
     * Selectpage可显示的字段
     */
    protected $selectpageFields = '*';

    /**
     * 前台提交过来,需要排除的字段数据
     */
    protected $excludeFields = "";

    /**
     * 导入文件首行类型
     * 支持comment/name
     * 表示注释或字段名
     */
    protected $importHeadType = 'comment';

    /**
     * 引入后台控制器的traits
     */
    use \app\admin\library\traits\Backend;

    public function _initialize()
    {
        $modulename = $this->request->module();
        $controllername = Loader::parseName($this->request->controller());
        $actionname = strtolower($this->request->action());

        $path = str_replace('.', '/', $controllername) . '/' . $actionname;

        // 定义是否Addtabs请求
        !defined('IS_ADDTABS') && define('IS_ADDTABS', input("addtabs") ? true : false);

        // 定义是否Dialog请求
        !defined('IS_DIALOG') && define('IS_DIALOG', input("dialog") ? true : false);

        // 定义是否AJAX请求
        !defined('IS_AJAX') && define('IS_AJAX', $this->request->isAjax());

        $this->auth = Auth::instance();

        // 设置当前请求的URI
        $this->auth->setRequestUri($path);
        // 检测是否需要验证登录
        if (!$this->auth->match($this->noNeedLogin)) {
            //检测是否登录
            if (!$this->auth->isLogin()) {
                Hook::listen('admin_nologin', $this);
                $url = Session::get('referer');
                $url = $url ? $url : $this->request->url();
                if ($url == '/') {
                    $this->redirect('index/login', [], 302, ['referer' => $url]);
                    exit;
                }
                $this->error(__('Please login first'), url('index/login', ['url' => $url]));
            }
            // 判断是否需要验证权限
            if (!$this->auth->match($this->noNeedRight)) {
                // 判断控制器和方法判断是否有对应权限
                if (!$this->auth->check($path)) {
                    Hook::listen('admin_nopermission', $this);
                    $this->error(__('You have no permission'), '');
                }
            }
        }

        // 非选项卡时重定向
        if (!$this->request->isPost() && !IS_AJAX && !IS_ADDTABS && !IS_DIALOG && input("ref") == 'addtabs') {
            $url = preg_replace_callback("/([\?|&]+)ref=addtabs(&?)/i", function ($matches) {
                return $matches[2] == '&' ? $matches[1] : '';
            }, $this->request->url());
            if (Config::get('url_domain_deploy')) {
                if (stripos($url, $this->request->server('SCRIPT_NAME')) === 0) {
                    $url = substr($url, strlen($this->request->server('SCRIPT_NAME')));
                }
                $url = url($url, '', false);
            }
            $this->redirect('index/index', [], 302, ['referer' => $url]);
            exit;
        }

        // 设置面包屑导航数据
        $breadcrumb = $this->auth->getBreadCrumb($path);
        array_pop($breadcrumb);
        $this->view->breadcrumb = $breadcrumb;

        // 如果有使用模板布局
        if ($this->layout) {
            $this->view->engine->layout('layout/' . $this->layout);
        }

        // 语言检测
        $lang = strip_tags($this->request->langset());

        $site = Config::get("site");

        $upload = \app\common\model\Config::upload();

        // 上传信息配置后
        Hook::listen("upload_config_init", $upload);

        // 配置信息
        $config = [
            'site'           => array_intersect_key($site, array_flip(['name', 'indexurl', 'cdnurl', 'version', 'timezone', 'languages'])),
            'upload'         => $upload,
            'modulename'     => $modulename,
            'controllername' => $controllername,
            'actionname'     => $actionname,
            'jsname'         => 'backend/' . str_replace('.', '/', $controllername),
            'moduleurl'      => rtrim(url("/{$modulename}", '', false), '/'),
            'language'       => $lang,
            'fastadmin'      => Config::get('fastadmin'),
            'referer'        => Session::get("referer")
        ];
        $config = array_merge($config, Config::get("view_replace_str"));

        Config::set('upload', array_merge(Config::get('upload'), $upload));

        // 配置信息后
        Hook::listen("config_init", $config);
        //加载当前控制器语言包
        $this->loadlang($controllername);
        //渲染站点配置
        $this->assign('site', $site);
        //渲染配置信息
        $this->assign('config', $config);
        //渲染权限对象
        $this->assign('auth', $this->auth);
        //渲染管理员对象
        $this->assign('admin', Session::get('admin'));
    }

    /**
     * 加载语言文件
     * @param string $name
     */
    protected function loadlang($name)
    {
        Lang::load(APP_PATH . $this->request->module() . '/lang/' . $this->request->langset() . '/' . str_replace('.', '/', $name) . '.php');
    }

    /**
     * 渲染配置信息
     * @param mixed $name  键名或数组
     * @param mixed $value 值
     */
    protected function assignconfig($name, $value = '')
    {
        $this->view->config = array_merge($this->view->config ? $this->view->config : [], is_array($name) ? $name : [$name => $value]);
    }

    /**
     * 生成查询所需要的条件,排序方式
     * @param mixed   $searchfields   快速查询的字段
     * @param boolean $relationSearch 是否关联查询
     * @return array
     */
    protected function buildparams($searchfields = null, $relationSearch = null)
    {
        $searchfields = is_null($searchfields) ? $this->searchFields : $searchfields;
        $relationSearch = is_null($relationSearch) ? $this->relationSearch : $relationSearch;
        $search = $this->request->get("search", '');
        $filter = $this->request->get("filter", '');
        $op = $this->request->get("op", '', 'trim');
        $sort = $this->request->get("sort", !empty($this->model) && $this->model->getPk() ? $this->model->getPk() : 'id');
        $order = $this->request->get("order", "DESC");
        $offset = $this->request->get("offset", 0);
        $limit = $this->request->get("limit", 0);
        $filter = (array)json_decode($filter, true);
        $op = (array)json_decode($op, true);
        $filter = $filter ? $filter : [];
        $where = [];
        $tableName = '';
        if ($relationSearch) {
            if (!empty($this->model)) {
                $name = \think\Loader::parseName(basename(str_replace('\\', '/', get_class($this->model))));
                $tableName = $name . '.';
            }
            $sortArr = explode(',', $sort);
            foreach ($sortArr as $index => & $item) {
                $item = stripos($item, ".") === false ? $tableName . trim($item) : $item;
            }
            unset($item);
            $sort = implode(',', $sortArr);
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $where[] = [$tableName . $this->dataLimitField, 'in', $adminIds];
        }
        if ($search) {
            $searcharr = is_array($searchfields) ? $searchfields : explode(',', $searchfields);
            foreach ($searcharr as $k => &$v) {
                $v = stripos($v, ".") === false ? $tableName . $v : $v;
            }
            unset($v);
            $where[] = [implode("|", $searcharr), "LIKE", "%{$search}%"];
        }
        foreach ($filter as $k => $v) {
            $sym = isset($op[$k]) ? $op[$k] : '=';
            if (stripos($k, ".") === false) {
                $k = $tableName . $k;
            }
            $v = !is_array($v) ? trim($v) : $v;
            $sym = strtoupper(isset($op[$k]) ? $op[$k] : $sym);
            switch ($sym) {
                case '=':
                case '<>':
                    $where[] = [$k, $sym, (string)$v];
                    break;
                case 'LIKE':
                case 'NOT LIKE':
                case 'LIKE %...%':
                case 'NOT LIKE %...%':
                    $where[] = [$k, trim(str_replace('%...%', '', $sym)), "%{$v}%"];
                    break;
                case '>':
                case '>=':
                case '<':
                case '<=':
                    $where[] = [$k, $sym, intval($v)];
                    break;
                case 'FINDIN':
                case 'FINDINSET':
                case 'FIND_IN_SET':
                    $where[] = "FIND_IN_SET('{$v}', " . ($relationSearch ? $k : '`' . str_replace('.', '`.`', $k) . '`') . ")";
                    break;
                case 'IN':
                case 'IN(...)':
                case 'NOT IN':
                case 'NOT IN(...)':
                    $where[] = [$k, str_replace('(...)', '', $sym), is_array($v) ? $v : explode(',', $v)];
                    break;
                case 'BETWEEN':
                case 'NOT BETWEEN':
                    $arr = array_slice(explode(',', $v), 0, 2);
                    if (stripos($v, ',') === false || !array_filter($arr)) {
                        continue 2;
                    }
                    //当出现一边为空时改变操作符
                    if ($arr[0] === '') {
                        $sym = $sym == 'BETWEEN' ? '<=' : '>';
                        $arr = $arr[1];
                    } elseif ($arr[1] === '') {
                        $sym = $sym == 'BETWEEN' ? '>=' : '<';
                        $arr = $arr[0];
                    }
                    $where[] = [$k, $sym, $arr];
                    break;
                case 'RANGE':
                case 'NOT RANGE':
                    $v = str_replace(' - ', ',', $v);
                    $arr = array_slice(explode(',', $v), 0, 2);
                    if (stripos($v, ',') === false || !array_filter($arr)) {
                        continue 2;
                    }
                    //当出现一边为空时改变操作符
                    if ($arr[0] === '') {
                        $sym = $sym == 'RANGE' ? '<=' : '>';
                        $arr = $arr[1];
                    } elseif ($arr[1] === '') {
                        $sym = $sym == 'RANGE' ? '>=' : '<';
                        $arr = $arr[0];
                    }
                    $where[] = [$k, str_replace('RANGE', 'BETWEEN', $sym) . ' time', $arr];
                    break;
                case 'LIKE':
                case 'LIKE %...%':
                    $where[] = [$k, 'LIKE', "%{$v}%"];
                    break;
                case 'NULL':
                case 'IS NULL':
                case 'NOT NULL':
                case 'IS NOT NULL':
                    $where[] = [$k, strtolower(str_replace('IS ', '', $sym))];
                    break;
                default:
                    break;
            }
        }
        $where = function ($query) use ($where) {
            foreach ($where as $k => $v) {
                if (is_array($v)) {
                    call_user_func_array([$query, 'where'], $v);
                } else {
                    $query->where($v);
                }
            }
        };
        return [$where, $sort, $order, $offset, $limit];
    }

    /**
     * 获取数据限制的管理员ID
     * 禁用数据限制时返回的是null
     * @return mixed
     */
    protected function getDataLimitAdminIds()
    {
        if (!$this->dataLimit) {
            return null;
        }
        if ($this->auth->isSuperAdmin()) {
            return null;
        }
        $adminIds = [];
        if (in_array($this->dataLimit, ['auth', 'personal'])) {
            $adminIds = $this->dataLimit == 'auth' ? $this->auth->getChildrenAdminIds(true) : [$this->auth->id];
        }
        return $adminIds;
    }

    /**
     * Selectpage的实现方法
     *
     * 当前方法只是一个比较通用的搜索匹配,请按需重载此方法来编写自己的搜索逻辑,$where按自己的需求写即可
     * 这里示例了所有的参数，所以比较复杂，实现上自己实现只需简单的几行即可
     *
     */
    protected function selectpage()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'htmlspecialchars']);

        //搜索关键词,客户端输入以空格分开,这里接收为数组
        $word = (array)$this->request->request("q_word/a");
        //当前页
        $page = $this->request->request("pageNumber");
        //分页大小
        $pagesize = $this->request->request("pageSize");
        //搜索条件
        $andor = $this->request->request("andOr", "and", "strtoupper");
        //排序方式
        $orderby = (array)$this->request->request("orderBy/a");
        //显示的字段
        $field = $this->request->request("showField");
        //主键
        $primarykey = $this->request->request("keyField");
        //主键值
        $primaryvalue = $this->request->request("keyValue");
        //搜索字段
        $searchfield = (array)$this->request->request("searchField/a");
        //自定义搜索条件
        $custom = (array)$this->request->request("custom/a");
        //是否返回树形结构
        $istree = $this->request->request("isTree", 0);
        $ishtml = $this->request->request("isHtml", 0);
        if ($istree) {
            $word = [];
            $pagesize = 99999;
        }
        $order = [];
        foreach ($orderby as $k => $v) {
            $order[$v[0]] = $v[1];
        }
        $field = $field ? $field : 'name';

        //如果有primaryvalue,说明当前是初始化传值
        if ($primaryvalue !== null) {
            $where = [$primarykey => ['in', $primaryvalue]];
        } else {
            $where = function ($query) use ($word, $andor, $field, $searchfield, $custom) {
                $logic = $andor == 'AND' ? '&' : '|';
                $searchfield = is_array($searchfield) ? implode($logic, $searchfield) : $searchfield;
                foreach ($word as $k => $v) {
                    $query->where(str_replace(',', $logic, $searchfield), "like", "%{$v}%");
                }
                if ($custom && is_array($custom)) {
                    foreach ($custom as $k => $v) {
                        if (is_array($v) && 2 == count($v)) {
                            $query->where($k, trim($v[0]), $v[1]);
                        } else {
                            $query->where($k, '=', $v);
                        }
                    }
                }
            };
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = [];
        $total = $this->model->where($where)->count();
        if ($total > 0) {
            if (is_array($adminIds)) {
                $this->model->where($this->dataLimitField, 'in', $adminIds);
            }
            $datalist = $this->model->where($where)
                ->order($order)
                ->page($page, $pagesize)
                ->field($this->selectpageFields)
                ->select();
            foreach ($datalist as $index => $item) {
                unset($item['password'], $item['salt']);
				$_id = isset($item['id']) ? $item['id'] : '';
				$title = isset($item[$field]) ? $item[$field] : '';
                $list[] = [
                    $primarykey => isset($item[$primarykey]) ? $item[$primarykey] : '',
                    $field      => $title.'('.$_id.')',
                    'pid'       => isset($item['pid']) ? $item['pid'] : 0
                ];
            }
            if ($istree) {
                $tree = Tree::instance();
                $tree->init(collection($list)->toArray(), 'pid');
                $list = $tree->getTreeList($tree->getTreeArray(0), $field);
                if (!$ishtml) {
                    foreach ($list as &$item) {
                        $item = str_replace('&nbsp;', ' ', $item);
                    }
                    unset($item);
                }
            }
        }
        //这里一定要返回有list这个字段,total是可选的,如果total<=list的数量,则会隐藏分页按钮
        return json(['list' => $list, 'total' => $total]);
    }
	/**
	* 发送短信
	*/
	public function sendSMS ($mobile, $content){
		$url = 'http://smsb2c.mitake.com.tw/b2c/mtk/SmSend?';
		$url .= '&username=0966623633';
		$url .= '&password=686868';
		$url .= '&dstaddr='.$mobile;
		$url .= '&smbody='.urlencode($content);
		$url .= '&CharsetURL=UTF-8';
		// echo $url;exit;
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		$output = curl_exec($curl);
		curl_close($curl);
		if(count(explode('statuscode=1',$output)) > 1){
			return true;
		}else{
			return false;
		}
	}
	/**
	订单日志
	*/
	public function orderLog($msg,$order_id,$table='df_order',$type = ''){
		$data = array(
			'order_id' => $order_id,
			'msg' => $msg,
			'createtime' => time(),
			'updatetime' => time(),
		);
		if($type){
			unset($data['order_id']);
			$data['p_order_id'] = $order_id;
		}
		/*获取年份*/
		$y = date('Y');
		$table = $table.'_log_'.$y;
		/*检查表是否存在*/
		$t_table = 't_'.$table;
		if(count(db()->query('SHOW TABLES LIKE '."'".$table."'"))){
			/*存在*/
			Db::name($table)->insert($data);
		}else{
			/*不存在*/
			$sql = 'CREATE TABLE if not exists '.$t_table.' LIKE t_df_order_log';
			Db::query($sql);
			Db::name($table)->insert($data);
		}
	}
	/*获取订单日志*/
	public function get_orderLog($id,$createtime,$table='df_order'){
		$y = date('Y',$createtime);
		if($y < '2022'){
			$table = $table.'_log';
		}else{
			$table = $table.'_log_'.$y;
		}
		return Db::name($table)
				->field('*')
				->where('order_id', $id)
				->select();
	}
	/**
	虚拟订单日志
	*/
	public function virtualLog($msg,$order_id){
		// Db::name('debugging')->insert(['title' => '订单日志', 'msg' => '22']);
		$data = array(
			'order_id' => $order_id,
			'msg' => $msg,
			'createtime' => time(),
			'updatetime' => time(),
		);
		Db::name('virtual_order_log')
			->insert($data);
	}
	/**
	* 淘币日志
	*/
	public function taobiLog($type,$money,$id,$msg="",$info="",$table="df_order"){
		if($money){
			$return = $this->consumption_type($type);
			if(!$info){
				$info = Db::name('user')->where('id',$this->auth->id)->find();
			}
			if($return === true){
				$price = $info['money'] + $money;
				// $surplus_price = $info['money'];
			}else{
				$price = $info['money'] - $money;
				// $surplus_price = $info['money'];
			}
			$data = array(
				'type' => $type,
				'order_id' => $id,
				'user_id' => $info['id'],
				'price' => $money,
				'surplus_price' => $price,
				'msg' => $msg,
				'table_name' => $table,
				'createtime' => time(),
				'updatetime' => time(),
			);
			$time = date('Ymd',time());
			$_map = array(
				'user_id' => $info['id'],
				'day_time' => $time,
			);
			$info_count = Db::name('taobi_count')->where($_map)->find();
			if($info_count){
				Db::name('taobi_count')->where($_map)->update(['money' => $price]);
			}else{
				$_map['money'] = $price;
				$_map['createtime'] = time();
				$_map['updatetime'] = time();
				Db::name('taobi_count')->insert($_map);
			}
			Db::name('taobi_log')->insert($data);
			Db::name('user')->where('id', $info['id'])->update(['money' => $price]);
		}
	}
	/**
	* 购物金日志
	*/
	public function goldLog($type,$money,$id="",$msg='',$info=""){
		if(intval($money)){
			$return = $this->consumption_type($type);
			if(!$info){
				$info = $this->auth->getUser();
			}
			if($return === true){
				$price = $info['buy_gold'] + $money;
				// $surplus_price = $info['money'];
			}else{
				$price = $info['buy_gold'] - $money;
				// $surplus_price = $info['money'];
			}
			$data = array(
				'type' => $type,
				'order_id' => $id,
				'user_id' => $info['id'],
				'price' => $money,
				'surplus_price' => $price,
				'msg' => $msg,
				'createtime' => time(),
				'updatetime' => time(),
			);
			$time = date('Ymd',time());
			$_map = array(
				'user_id' => $info['id'],
				'day_time' => $time,
			);
			$info_count = Db::name('gold_count')->where($_map)->find();
			if($info_count){
				Db::name('gold_count')->where($_map)->update(['money' => $price]);
			}else{
				$_map['money'] = $price;
				$_map['createtime'] = time();
				$_map['updatetime'] = time();
				Db::name('gold_count')->insert($_map);
			}
			Db::name('gold_log')->insert($data);
			Db::name('user')->where('id', $info['id'])->update(['buy_gold' => $price]);
		}
	}
	/**
	* 消费类型
	* 8系統增加,10推廣任務獎勵',11取消訂單退還',12用戶取消退款',13返利提現',14发票返利
	
	* 9系統減少',0阿里巴巴代付消費',1淘寶天貓代付消費',2淘寶天貓吱口令消費',
	* 3支付唄儲值消費',4微信儲值消費',5遊戲視頻儲值消費',6其他儲值消費',7淘幣儲值消費'
	*/
	public function consumption_type($type){
		if($type == '8' || $type == '10' || $type == '11' || $type == '12' || $type == '13' || $type == '14'){
			return true;
		}else{
			return false;
		}
	}
	/* 订单完成 */
	public function order_processing($order_id,$table='df_order'){
		/* 获取订单 */
		$order = Db::name($table)->field('*')->where(['id'=>$order_id])->find();
		/* 获取用户信息 */
		$info = Db::name('user')->field('*')->where('id', $order['user_id'])->find();
		$use_rule = Db::name('use_rule')->where('rule_type','1')->find();
		/* 判斷用戶是否是第一次完成完成訂單 */
		if($info['is_order'] == 0){
			/* 第一次完成訂單獎勵購物金 */
			if($info['pid']){
				/* 获取推荐人信息 */
				$_info = Db::name('user')->field('*')->where('id', $info['pid'])->find();
				User::give_gold($_info,Config::get('site.give4'),$info['id']);
				/*每次下单赠送优惠卷*/
				if(Config::get('site.coupon_open')){
					/*每单赠送优惠卷*/
					if($use_rule['is_order'] == '1'){
						$this->give_coupon($info['pid'],$use_rule['coupon_id']);
					}
				}
			}
		}
		/*每次下单赠送优惠卷*/
		if(Config::get('site.coupon_open')){
			/*每单赠送优惠卷*/
			if($use_rule['is_order'] == '2'){
				$this->give_coupon($info['pid'],$use_rule['coupon_id']);
			}
		}
		$rules = Config::get('site.give_gold');
		$gold_rules = Db::name('gold_rules')->field('*')->select();
		foreach($gold_rules as $val){
			$_gold_rules[$val['id']] = $val;
		}
		if($info['is_order'] == 0 && $order['rmb_money'] > '5000'){
			$rules = Config::get('site.give_gold');
			foreach($rules as $key=>$val){
				$this->goldLog('8',$_gold_rules[$key]['money'],$order_id,$_gold_rules[$key]['title'],$info);
			}
		}else{
			/* 判斷用戶累計消費金額是否達到贈送購物金條件 */
			$cumulative_rmb = $info['cumulative_rmb'];
			$rmb_mobey = $order['rmb_money'] - $order['balance_money'];
			$count_gold = $cumulative_rmb + $rmb_mobey;
			/* 获取上一次奖励等级 */
			$old_give = array_flip(array_filter($rules, function($rules) use($cumulative_rmb) { return $cumulative_rmb >= $rules; }));
			/* 获取本次奖励等级 */
			$give = array_flip(array_filter($rules, function($rules) use($count_gold) { return $count_gold >= $rules; }));
			sort($old_give);
			sort($give);
			$old_give_str = implode('a',$old_give);
			$give_str = implode('a',$give);
			/* 两次奖励等级不一致给与奖励 */
			if($old_give_str != $give_str){
				//$gold_rules = Db::name('gold_rules')->field('*')->where('id',$give['0'])->find();
				$this->goldLog('8',$_gold_rules[$give['0']]['money'],$order_id,$_gold_rules[$give['0']]['title'],$info);
			}
		}
		/* 計算用戶是否達到升級要求 */
		$count_exp = $info['score'] + $rmb_mobey;
		/* 更新等級 */
		$level = User::nextlevel($count_exp);
		$data = array(
			'level' => $level,
			'frozen_price' => '0',
			'is_order' => '1',
			'score' => $count_exp,
			'cumulative_rmb' => $count_gold,
			'cumulative_twb' => $info['cumulative_twb'] + $order['actual_money'],
		);
		Db::name('user')->where('id', $info['id'])->update($data);
		Db::name($table)->where('id', $order_id)->update(['completetime' => time()]);
		$this->orderLog('订单完成',$order_id,$table);
		/* 添加统计数据 */
		$time = date('Ymd',time());
		$count_map = array(
			'daytime' => date('Ymd',$time),
			'user_id' => $info['id'],
		);
		$info_count = Db::name('count_order')->where($count_map)->find();
		if($info_count){
			/* 存在数据更新 */
			$count_data = array(
				'all_tb_money' => $info_count['all_tb_money'] + $order['actual_money'],
				'all_rmb_money' => $info_count['all_rmb_money'] + ($order['rmb_money'] - $order['balance_money']),
				'all_order' => $info_count + 1,
				'updatetime' => time(),
			);
			Db::name('count_order')->where('user_id', $info_count['user_id'])->update($count_data);
		}else{
			/* 不存在数据添加 */
			$count_data = array(
				'user_id' => $info['id'],
				'all_tb_money' => $order['actual_money'],
				'all_rmb_money' => $order['rmb_money'] - $order['balance_money'],
				'all_order' => '1',
				'daytime' => $time,
				'createtime' => time(),
				'updatetime' => time(),
			);
			Db::name('count_order')->insert($count_data);
		}
	}
	/* 虚拟订单订单完成 */
	public function order_processing_virtual($order_id){
		/* 获取订单 */
		$order = Db::name('virtual_order')->field('*')->where(['id'=>$order_id])->find();
		/* 获取用户信息 */
		$info = Db::name('user')->field('*')->where('id', $order['user_id'])->find();
		$use_rule = Db::name('use_rule')->where('rule_type','1')->find();
		/* 判斷用戶是否是第一次完成完成訂單 */
		if($info['is_order'] == 0){
			/* 第一次完成訂單獎勵購物金 */
			if($info['pid']){
				/* 获取推荐人信息 */
				$_info = Db::name('user')->field('*')->where('id', $info['pid'])->find();
				User::give_gold($_info,Config::get('site.give4'),$info['id']);
				/*每次下单赠送优惠卷*/
				if(Config::get('site.coupon_open')){
					/*每单赠送优惠卷*/
					if($use_rule['is_order'] == '1'){
						$this->give_coupon($info['pid'],$use_rule['coupon_id']);
					}
				}
			}
		}
		/*每次下单赠送优惠卷*/
		if(Config::get('site.coupon_open')){
			/*每单赠送优惠卷*/
			if($use_rule['is_order'] == '2'){
				$this->give_coupon($info['pid'],$use_rule['coupon_id']);
			}
		}
		/* 判斷用戶累計消費金額是否達到贈送購物金條件 */
		$cumulative_rmb = $info['cumulative_rmb'];
		$rmb_mobey = $order['rmb_money'];
		$count_gold = $cumulative_rmb + $rmb_mobey;
		$rules = Config::get('site.give_gold');
		/* 获取上一次奖励等级 */
		$old_give = array_flip(array_filter($rules, function($rules) use($cumulative_rmb) { return $cumulative_rmb >= $rules; }));
		/* 获取本次奖励等级 */
		$give = array_flip(array_filter($rules, function($rules) use($count_gold) { return $count_gold >= $rules; }));
		sort($old_give);
		sort($give);
		$old_give_str = implode('a',$old_give);
		$give_str = implode('a',$give);
		/* 两次奖励等级不一致给与奖励 */
		if($old_give_str != $give_str){
			$gold_rules = Db::name('gold_rules')->field('*')->where('id',$give['0'])->find();
			$this->goldLog('8',$gold_rules['money'],$order_id,$gold_rules['title'],$info);
		}
		/* 計算用戶是否達到升級要求 */
		$count_exp = $info['score'] + $rmb_mobey;
		/* 更新等級 */
		$level = User::nextlevel($count_exp);
		$data = array(
			'level' => $level,
			'frozen_price' => '0',
			'is_order' => '1',
			'cumulative_rmb' => $count_gold,
			'cumulative_twb' => $info['cumulative_twb'] + $order['actual_money'],
		);
		Db::name('user')->where('id', $info['id'])->update($data);
		Db::name('virtual_order')->where('id', $order_id)->update(['completetime' => time()]);
		$this->virtualLog('订单完成',$order_id);
		/* 添加统计数据 */
		$time = date('Ymd',time());
		$count_map = array(
			'daytime' => date('Ymd',$time),
			'user_id' => $info['id'],
		);
		$info_count = Db::name('count_order')->where($count_map)->find();
		if($info_count){
			/* 存在数据更新 */
			$count_data = array(
				'all_tb_money' => $info_count['all_tb_money'] + $order['actual_money'],
				'all_rmb_money' => $info_count['all_rmb_money'] + ($order['rmb_money'] - $order['balance_money']),
				'all_order' => $info_count + 1,
				'updatetime' => time(),
			);
			Db::name('count_order')->where('user_id', $info_count['user_id'])->update($count_data);
		}else{
			/* 不存在数据添加 */
			$count_data = array(
				'user_id' => $info['id'],
				'all_tb_money' => $order['actual_money'],
				'all_rmb_money' => $order['rmb_money'],
				'all_order' => '1',
				'daytime' => $time,
				'createtime' => time(),
				'updatetime' => time(),
			);
			Db::name('count_order')->insert($count_data);
		}
	}
	/*微信轉賬*/
    public static function wxWithdrawals($partner_trade_no="",$member_id,$openid,$amount,$type){
        $data['partner_trade_no'] = $partner_trade_no?$partner_trade_no:build_order_no();
        $data['openid'] = $openid;
        $data['money'] = $amount;
        if($type == 1){
            $data['desc'] = '微信賬戶實名認證';
        }else if($type == 2){
            $data['desc'] = '支付貨款';
        }
        vendor('wxpay.WxPay#CompanyPay');
        $CompanyPay = new \CompanyPay();
        $wx = \app\common\conf\Config::get_wx();;
        $wxchat['appid'] = $wx['APPID'];
        $wxchat['mchid'] = $wx['MCHID'];
        $wxchat['key'] = $wx['KEY'];
        $res = $CompanyPay->wxbuild($data,$wxchat);
        if($res['result_code'] == 'SUCCESS'){
//            $new_wx_amount = C('WX_AMOUNT') - $amount;
//            C('WX_AMOUNT',$new_wx_amount);
            return array('status'=>1,'msg'=>'转账成功','res'=>$res);
        }else{
            return array('status'=>0,'msg'=>'微信自動轉賬失敗');
        }
    }
	/*支付宝转账*/
    public static function alipayWithDraw_new($out_biz_no = "", $member_id, $amount, $payee_account, $payee_real_name, $type)
    {
        $map = array(
			'ali_account' => $payee_account,
			'name' => $payee_real_name,
		);
        if ($type == 1) {
            $payer_show_name = '支付唄賬號實名認證';
            $remark = '支付唄賬號實名認證';
			$ali_account = $payee_account;
            // $amount = '0.1';
        } else if ($type == 2) {
            $payer_show_name = '支付貨款';
            $remark = '支付貨款';
            $alipay = Db::name('user_alipay')->where($map)->find();
            $payee_real_name = $alipay['name']?$alipay['name']:'';
			$ali_account = $alipay['ali_account'];
        } else if ($type == 3) {
            $payer_show_name = 'usdt貨款';
            $remark = 'usdt貨款';
            $alipay = Db::name('user_alipay')->where($map)->find();
            $payee_real_name = $alipay['name']?$alipay['name']:'';
			$ali_account = $alipay['ali_account'];
        } else if ($type == 4) {
            $payer_show_name = '平台转账';
            $remark = '平台转账';
			$ali_account = $payee_account;
        }
		
		/*if(!empty($alipay)){
			if($alipay['is_transfer'] == '0'){
				return array('status' => 0, 'msg' => '用户支付宝账号异常');
			}
		}*/
		if( empty($out_biz_no) )
		{
			$out_biz_no = date("Y") . strtoupper(dechex(date('m'))) . date('d') . substr(time(), -5) . substr(microtime(), 2, 7);
		}
		
		$site_alipay = 'site.alipay'.Config::get('site.new_alipay_open');
		$num = Config::get('site.new_alipay_open');
		if($type == '2' && $out_biz_no){
			/*获取订单*/
			$order_info = Db::name('df_order')->where('order_no',$out_biz_no)->find();
			if($order_info['type'] == '3'){
				$group = Db::name('group_pay')->where('id',$order_info['group_id'])->find();
				if($group){
					$site_alipay = 'site.new_alipay_open'.$group['payment_account'];
					$num = $group['payment_account'];
				}
			}
		}
		
		
		Vendor('alipay2.aop.AopCertClient');
		Vendor('alipay2.aop.request.AlipayFundTransUniTransferRequest');
		$c = new \AopCertClient;
		$ali_config = Config::get("$site_alipay");
		$appCertPath = "/var/www/haigou/vendor/cert/zhengshu".$num."/appCertPublicKey.crt";
		$alipayCertPath = "/var/www/haigou/vendor/cert/zhengshu".$num."/alipayCertPublicKey_RSA2.crt";
		$rootCertPath = "/var/www/haigou/vendor/cert/zhengshu".$num."/alipayRootCert.crt";
		$c->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
		$c->appId = $ali_config['appId'];
		$c->rsaPrivateKey = $ali_config['rsaPrivateKey'];
		$c->format = "json";
		$c->charset= 'UTF-8';
		$c->signType= 'RSA2';
		
		//调用getPublicKey从支付宝公钥证书中提取公钥
		$c->alipayrsaPublicKey = $c->getPublicKey($alipayCertPath);
		//是否校验自动下载的支付宝公钥证书，如果开启校验要保证支付宝根证书在有效期内
		$c->isCheckAlipayPublicCert = true;
		//调用getCertSN获取证书序列号
		$c->appCertSN = $c->getCertSN($appCertPath);
		//调用getRootCertSN获取支付宝根证书序列号
		$c->alipayRootCertSN = $c->getRootCertSN($rootCertPath);
		//实例化具体API对应的request类,类名称和接口名称对应,当前调用接口名称：alipay.open.public.template.message.industry.modify
		$request = new \AlipayFundTransUniTransferRequest();
		$request->setBizContent ( "{".
		 "\"out_biz_no\":\"$out_biz_no\",".
		 "\"trans_amount\":$amount,".
		 "\"product_code\":\"TRANS_ACCOUNT_NO_PWD\",".
		 "\"biz_scene\":\"DIRECT_TRANSFER\",".
		 "\"order_title\":\"$payer_show_name\",".
		 "\"payee_info\":{".
		 "\"identity\":\"$ali_account\",".
		 "\"identity_type\":\"ALIPAY_LOGON_ID\",".
		 "\"name\":\"$payee_real_name\",".
		 "    },".
		 "\"remark\":\"$payer_show_name\",".
		 "  }" );
		$result= $c->execute($request);
		$responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";
		$resultCode = $result->$responseNode->code;
        if (!empty($resultCode) && $resultCode == 10000) {
            return array('status' => 1, 'msg' => '转账成功','res'=>$resultCode);
        } else {
            //$result->$responseNode->sub_msg 这个参数 是返回的错误信息
            return array('status' => 0, 'msg' => $result->$responseNode->sub_msg);
        }
    }
	/*支付宝转账-旧*/
	public static function alipayWithDraw($out_biz_no = "", $member_id, $amount, $payee_account, $payee_real_name, $type){
		Vendor('alipay.aop.AopClient');
		Vendor('alipay.aop.request.AlipayFundTransToaccountTransferRequest');
		Vendor('alipay.aop.SignData');
		$map = array(
			'ali_account' => $payee_account,
			'name' => $payee_real_name,
		);
		if ($type == 2) {
            $payer_show_name = '支付貨款';
            $remark = '支付貨款';
            $alipay = Db::name('user_alipay')->where($map)->find();
            $payee_real_name = $alipay['name']?$alipay['name']:'';
			$ali_account = $alipay['ali_account'];
        } else if ($type == 3) {
            $payer_show_name = 'usdt貨款';
            $remark = 'usdt貨款';
            $alipay = Db::name('user_alipay')->where($map)->find();
            $payee_real_name = $alipay['name']?$alipay['name']:'';
			$ali_account = $alipay['ali_account'];
        }
		$site_alipay = 'site.alipay'.Config::get('site.alipay_open');
		if($type == '2' && $out_biz_no){
			/*获取订单*/
			$order_info = Db::name('df_order')->where('order_no',$out_biz_no)->find();
			if($order_info['type'] == '3'){
				$group = Db::name('group_pay')->where('id',$order_info['group_id'])->find();
				if($group){
					$site_alipay = 'site.alipay'.$group['payment_account'];
				}
			}
		}
		$ali_config = Config::get("$site_alipay");
		$aop = new \AopClient();
		$aop->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
		$aop->appId = $ali_config['appId'];//商户appid 在支付宝控制台找
		$aop->rsaPrivateKey = $ali_config['rsaPrivateKey'];//私钥 工具生成的
		$aop->alipayrsaPublicKey = $ali_config['alipayrsaPublicKey'];//支付宝公钥 上传应用公钥后 支付宝生成的支付宝公钥
		$aop->apiVersion = '1.0';
		$aop->signType = 'RSA2';
		$aop->postCharset = 'utf-8';
		$aop->format = 'json';
		$request = new \AlipayFundTransToaccountTransferRequest();
		$request->setBizContent("{" .
				"\"out_biz_no\":\"$out_biz_no\"," .
				"\"payee_type\":\"ALIPAY_LOGONID\"," .
				"\"payee_account\":\"$payee_account\"," .
				"\"amount\":\"$amount\"," .
				"\"payer_show_name\":\"$payer_show_name\"," .
				"\"payee_real_name\":\"$payee_real_name\"," .
				"\"remark\":\"$remark\"" .
				"}");
		$result = $aop->execute($request);
		$responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";
		$resultCode = $result->$responseNode->code;
		if (!empty($resultCode) && $resultCode == 10000) {
			return array('status' => 1, 'msg' => '转账成功','res'=>$resultCode);
		} else {
			//$result->$responseNode->sub_msg 这个参数 是返回的错误信息
			return array('status' => 0, 'msg' => $result->$responseNode->sub_msg);
		}
	}
	/**
	 *  ip 状态设置
	 */
	public static function set_ip_manage( $ip , $status)
	{
		$_res = Db::name('ip_manage')->where('ip', $ip)->update(['status'=> $status]);
		if( empty($_res) )
		{
			return false;
		}
		return true;
	}

	/**
	 *  添加异常ip 记录
	 */
	public static function add_ip_manage( $ip, $remark='' )
	{
		$_res = Db::name('ip_manage')->insertGetId(['ip'=>$ip, 'remark'=> $remark]);
		if( empty($_res) )
		{
			return false;
		}
		return true;
	}
	/**
	* 虚拟码生成
	*/
	public function virtual_code($num){
		$generation = [3,7,1];
		$result = 0;
		$index = 0;
		foreach(str_split($num,1) as $i){
			$result += $i*$generation[$index++ % 3];
		}
		return $result % 10;
	}
	/*虚拟码核验入库*/
	public function generate_code($order_id,$member_id,$price,$group="",$table){
		$virtual_code = $this->generate_get_code($order_id,$member_id,$price,$group="");
		$info = Db::name('virtual_code')->where(array('num'=>$virtual_code,'type'=>'0'))->find();
		if($info){
			$virtual_code = $this->generate_get_code($order_id,$member_id,$price,$group="");
		}
		$result = Db::name($table)->where('id',$order_id)->update(['cs_code' => $virtual_code,'pay_status'=>'3']);
		$data = array(
			'order_id'=>$order_id,
			'type'=>'0',
			'table_name'=>$table,
			'num'=>$virtual_code,
			'createtime'=>time(),
			'updatetime'=>time(),
		);
		$_result = Db::name('virtual_code')->insert($data);
		$this->orderLog('生成虚拟码'.$virtual_code,$order_id,$table);
		if($result && $_result){
			return true;
		}else{
			return false;
		}
	}
	public function generate_get_code($order_id,$member_id,$price,$group=""){
		if($group){
			$a = $group;
		}else{
			if(Config::get('site.virtual_open') == '1'){
				$a = '43869';
			}
			if(Config::get('site.virtual_open') == '2'){
				$a = '92113';
			}
			if(Config::get('site.virtual_open') == '3'){
				$a = '92245';
			}
		}
		$bb = date("z")+4;/*缴款截至期限*/
		$b = str_pad($bb,3,0,STR_PAD_LEFT );
		$year = date('Y',time());
		$c = $year%10;/*缴款截至年份*/
		$member_id = rand('1111','9999');
		$qiye = $a.$c.$b.$member_id;
		$_price = str_pad($price,10,0,STR_PAD_LEFT );
		$a_value = $this->virtual_code($qiye);/*A值6*/
		$b_value = $this->virtual_code($_price);/*B值3*/
		$ab_value = $a_value + $b_value;
		$c_value = $ab_value%10;/*C值*/
		$_check_code = 10 - $c_value;
		$check_code = $_check_code%10;/*检查码*/
		return $qiye.$check_code;
	}
	/*赠送优惠卷*/
	public function give_coupon($user_id,$coupon_id){
        $coupon_array = explode(',',$coupon_id);
        $coupon = Db::name('coupon')->where('id','in',$coupon_array)->select();
        $use_coupon = [];
        foreach($coupon as $k=>$v){
            $use_coupon[$k]['coupon_id'] = $v['id'];
            $use_coupon[$k]['title'] = $v['title'];
            $use_coupon[$k]['reaching_amount'] = $v['reaching_amount'];
            $use_coupon[$k]['reduce_amount'] = $v['reduce_amount'];
            $use_coupon[$k]['start_time'] = time();
            $use_coupon[$k]['end_time'] = strtotime("+{$v['use_num']} days");
            $use_coupon[$k]['is_use'] = '0';
            $use_coupon[$k]['createtime'] = time();
            $use_coupon[$k]['updatetime'] = time();
            $use_coupon[$k]['user_id'] = $user_id;
            Db::name('coupon')->where('id',$v['id'])->setInc('issued_num');
        }
        Db::name('user_coupon')->insertAll($use_coupon);
    }
}
