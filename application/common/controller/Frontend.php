<?php

namespace app\common\controller;

use app\common\library\Auth;
use think\Config;
use think\Controller;
use think\Hook;
use think\Lang;
use think\Loader;
use think\Db;
use fast\Random;
use think\Cookie;
use app\common\model\User;

/**
 * 前台控制器基类
 */
class Frontend extends Controller
{

    /**
     * 布局模板
     * @var string
     */
    protected $layout = '';

    /**
     * 无需登录的方法,同时也就不需要鉴权了
     * @var array
     */
    protected $noNeedLogin = [];

    /**
     * 无需鉴权的方法,但需要登录
     * @var array
     */
    protected $noNeedRight = [];

    /**
     * 权限Auth
     * @var Auth
     */
    protected $auth = null;

    public function _initialize()
    {
		if(Config::get('site.open') == '1'){
			$this->error(Config::get('site.open_text'),'','','0');
		}
        //移除HTML标签
        $this->request->filter('trim,strip_tags,htmlspecialchars');
        $modulename = $this->request->module();
        $controllername = Loader::parseName($this->request->controller());
        $actionname = strtolower($this->request->action());
        // 如果有使用模板布局
        if ($this->layout) {
            $this->view->engine->layout('layout/' . $this->layout);
        }
        $this->auth = Auth::instance();
        // token
        $token = $this->request->server('HTTP_TOKEN', $this->request->request('token', \think\Cookie::get('token')));
        $path = str_replace('.', '/', $controllername) . '/' . $actionname;
        // 设置当前请求的URI
        $this->auth->setRequestUri($path);
        // 检测是否需要验证登录
        if (!$this->auth->match($this->noNeedLogin)) {
            //初始化
            $this->auth->init($token);
            //检测是否登录
            /* if (!$this->auth->isLogin()) {
                $this->error(__('Please login first'), 'index/user/login');
            } */
            // 判断是否需要验证权限
            if (!$this->auth->match($this->noNeedRight)) {
                // 判断控制器和方法判断是否有对应权限
                if (!$this->auth->check($path)) {
                    $this->error(__('You have no permission'));
                }
            }
        } else {
            // 如果有传递token才验证是否登录状态
            if ($token) {
                $this->auth->init($token);
            }
        }
		$map_user_bank = [
			'user_id' => $this->auth->id,
			'is_state' => '1',
		];
		$user_frontend_bank = Db::name('user_bank')->where($map_user_bank)->count();
        $this->view->assign('user', $this->auth->getUser());
        $this->view->assign('user_frontend_bank', $user_frontend_bank);
		/*获取全站公告*/
		$all_own = Db::name('notice')->where(array('type'=>'1','is_own'=>'1'))->order('id desc')->find();
		/* 获取浮窗新闻 */
		switch($path){
			case 'daifu/index':
				$type = '0';
			break;
			case 'daifu/taobao':
				$type = '1';
			break;
			case 'daifu/alipay':
				$type = '3';
			break;
			case 'daifu/wechatpay':
				$type = '4';
			break;
			case 'daifu/game':
				$type = '5';
			break;
			case 'daifu/other':
				$type = '6';
			break;
			case 'daifu/charge':
				$type = '7';
			break;
			default:
				$type = '8';
		}
		$map = array(
			'type' => $type,
			'hot' => '1',
		);
		$list = Db::name('help')->field('*')->where($map)->select();
		$this->view->assign('help_list', $list);
        // 语言检测
        $lang = strip_tags($this->request->langset());
        $site = Config::get("site");
        $upload = \app\common\model\Config::upload();
        // 上传信息配置后
        Hook::listen("upload_config_init", $upload);
        // 配置信息
        $config = [
            'site'           => array_intersect_key($site, array_flip(['name', 'cdnurl', 'version', 'timezone', 'languages'])),
            'upload'         => $upload,
            'modulename'     => $modulename,
            'controllername' => $controllername,
            'actionname'     => $actionname,
            'jsname'         => 'frontend/' . str_replace('.', '/', $controllername),
            'moduleurl'      => rtrim(url("/{$modulename}", '', false), '/'),
            'language'       => $lang
        ];
        $config = array_merge($config, Config::get("view_replace_str"));
        Config::set('upload', array_merge(Config::get('upload'), $upload));
		$seo_url = $this->request->url(true);
		$_seo_url = explode('?',$seo_url);
		$seo = Db::name('seo')->where('url',$seo_url)->find();
		/*运营数据抓取*/
		$this->operational_data($seo);
		/*抽奖*/
		$luck_open =$site['luck_open'];
		if($luck_open){
			/*奖项*/
			$luck_list = Db::name('luck_draw')->where('status','normal')->select();
			/*中奖列表*/
			$luck_user = Db::name('luck_user')->where('status','normal')->limit(30)->select();
			$luck_json = array();
			foreach($luck_user as $ls){
				$luck_json[] = array(
					'name' => substr_replace($ls['username'],'*',0,3),
					'goods' => $ls['title'],
					'time' => $this->time_format($ls['createtime']),
				);
			}
			/*获取用户抽奖资格*/
			$luck_map = array(
				'user_id' => $this->auth->id,
				'day_time' => date('Ymd',time()),
			);
			$qualifications = Db::name('luck_num')->where($luck_map)->find();
			$surplus_num = $qualifications['complete_num'] - $qualifications['use_num'];
			if($qualifications['sys_num']){
				$tips = "您正在使用贈送次數抽獎，本次抽獎贈送次數-1";
			}else if($surplus_num){
				$tips = "您正在使用訂單獎勵抽獎，本次抽獎獎勵次數-1";
			}else if($qualifications['gold_num'] < '5'){
				$tips = "您正在使用抵扣金抽獎，本次抽獎扣取100抵扣金";
			}else{
				$tips = "今日抽獎資格已用完，請明天再來";
			}
			$this->assign('luck_tips', $tips);
			$this->assign('luck_list', $luck_list);
			$this->assign('luck_user', json_encode($luck_json));
		}
		$this->assign('seo', $seo);
        // 配置信息后
        Hook::listen("config_init", $config);
        // 加载当前控制器语言包
        $this->loadlang($controllername);
        $this->assign('site', $site);
        $this->assign('nav_path', $path);
        $this->assign('config', $config);
        $this->assign('luck_open', $luck_open);
        $this->assign('all_own', $all_own);
    }

    /**
     * 加载语言文件
     * @param string $name
     */
    protected function loadlang($name)
    {
        Lang::load(APP_PATH . $this->request->module() . '/lang/' . $this->request->langset() . '/' . str_replace('.', '/', $name) . '.php');
    }

    /**
     * 渲染配置信息
     * @param mixed $name  键名或数组
     * @param mixed $value 值
     */
    protected function assignconfig($name, $value = '')
    {
        $this->view->config = array_merge($this->view->config ? $this->view->config : [], is_array($name) ? $name : [$name => $value]);
    }
	/**
	 * 格式化时间
	 * @param  [type] $time [要格式化的时间戳]
	 * @return [type]       [description]
	 */
	public function time_format ($time) {
		//当前时间
		$now = time();
		//今天零时零分零秒
		$today = strtotime(date('y-m-d', $now));
		//传递时间与当前时秒相差的秒数
		$diff = $now - $time;
		$str = '';
		switch ($time) {
			case $diff < 60 :
				$str = $diff . '秒前';
				break;
			case $diff < 3600 :
				$str = floor($diff / 60) . '分钟前';
				break;
			case $diff < (3600 * 8) :
				$str = floor($diff / 3600) . '小时前';
				break;
			case $time > $today :
				$str = '今天&nbsp;&nbsp;' . date('H:i', $time);
				break;
			default : 
				$str = date('Y-m-d H:i:s', $time);
		}
		return $str;
	}


	/*运营数据抓取*/
	public function operational_data($seo){
		$c_mask = Cookie::get('mask');
		$mask = request()->param('mask');
		if($c_mask){
			$mask = $c_mask;
		}else{
			$mask = $mask;
		}

		if($mask){
			Cookie::set('mask',$mask);
			/*获取运营数据*/
			$info = Db::name('operation')->where('suffix',$mask)->find();
			// $source = $info['suffix'];
			$source = $mask;
			$user_id = $info['id'];
		}else{
			$info = Db::name('operation')->where('suffix', 'organic')->find();
			$source = 'organic';
			$user_id = $info['id'];				
		}
		/*获取当前完整url*/
		$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
		$host = $_SERVER['HTTP_HOST']; // 包含域名和可能的端口号（如 example.com:8080）
		$uri = $_SERVER['REQUEST_URI']; // 包含路径和查询参数（如 /path?key=value）
		$current_url = $protocol . $host . $uri;
		if (!empty($_SERVER['REMOTE_ADDR'])) {
			$ip = $_SERVER['REMOTE_ADDR'];
		} else {
			// for php-cli(phpunit etc.)
			$ip = defined('PHPUNIT_RUNNING') ? '127.0.0.1' : gethostbyname(gethostname());
		}
		$data = [
			'page_url' => $current_url,
			'ip' => $ip,
			'source' => $source,
			'page_name' => $this->auth->id ? $this->auth->mobile : '游客',
			'staff_id' => $user_id,
			'createtime' => time(),
		];
		Db::name('delivery_tracking')->insert($data);
	}
	// public function operational_data($seo){
	// 	$c_mask = Cookie::get('mask');
	// 	$mask = request()->param('mask');
	// 	//if($c_mask || $mask){
	// 		if($c_mask){
	// 			$mask = $c_mask;
	// 		}else{
	// 			$mask = $mask;
	// 		}

	// 		if($mask){
	// 			Cookie::set('mask',$mask);
	// 			/*获取运营数据*/
	// 			$info = Db::name('operation')->where('suffix',$mask)->find();
	// 			$source = $info['name'].'+'.$info['remarks'];
	// 			$user_id = $info['id'];
	// 		}else{
	// 			$source = '自然点击';
	// 			$user_id = '0';
	// 		}
	// 		/*获取当前完整url*/
	// 		$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
	// 		$host = $_SERVER['HTTP_HOST']; // 包含域名和可能的端口号（如 example.com:8080）
	// 		$uri = $_SERVER['REQUEST_URI']; // 包含路径和查询参数（如 /path?key=value）
	// 		$current_url = $protocol . $host . $uri;
	// 		if (!empty($_SERVER['REMOTE_ADDR'])) {
	// 			$ip = $_SERVER['REMOTE_ADDR'];
	// 		} else {
	// 			// for php-cli(phpunit etc.)
	// 			$ip = defined('PHPUNIT_RUNNING') ? '127.0.0.1' : gethostbyname(gethostname());
	// 		}
	// 		$data = [
	// 			'page_url' => $current_url,
	// 			'ip' => $ip,
	// 			'source' => $source,
	// 			'page_name' => $seo['title'].' 会员id'.$this->auth->id,
	// 			'staff_id' => $user_id,
	// 			'createtime' => time(),
	// 		];
	// 		Db::name('delivery_tracking')->insert($data);
	// 	//}
	// }




	/* 
	*@type 类型 0注册 1忘记密码 2使用淘币验证 3修改密码 4添加银行验证 8双重验证
	*/
	public function sendCode($type){
		$mobile = $this->request->post('mobile')?$this->request->post('mobile'):$this->auth->mobile;
		$count = Db::name('code')
				->where('mobile', $mobile)
				->count();
		if($count >= '50'){
			$this->error(__('Your message has reached the limit'));
		}
		$item = Db::name('user')
				->field('id')
				->where('mobile', $mobile)
				->find();
		if($type == '0'){
			if($item){
				$this->error(__('Mobile number already exists'));
			}
		}
		if($type == '1'){
			if(!$item){
				$this->error(__('Mobile phone number is not registered yet. Please register first'));
			}
		}
		if($type == '8'){
			$code = $this->request->post('code');
			if($item){
				$this->error(__('Mobile number already exists'));
			}
			if(!$this->ruleCode($this->auth->mobile,$code,'3')){
				$this->error(__('Short Message Verification Code Error'));
			}
		}
		$code = $code = rand(0,9).rand(0,9).rand(0,9).rand(0,9).rand(0,9).rand(0,9);
		$map = $data = array(
			'mobile' => $mobile,
			'code' => $code,
			'type' => $type,
		);
		unset($map['code']);
		Db::name('code')
			->where($map)
			->delete();
		// $sms = new SMSHttp();
		/* 0注册 1忘记密码 2使用淘币验证 3修改密码 4添加银行验证 */
		$msg_txt = $this->msg_txt($type);
		$content = str_replace('#code#',$code,$msg_txt['title']);
		$result = Db::name('code')->insert($data);
		// $res = '1';
		if(is_numeric($result)){
			$res = $this->sendSMS($mobile,$content);
			if($res == true){
				
				$this->success(__('Verification code has been sent to your mobile phone'));
			}else{
				$this->error(__('Verification code acquisition failed, please refresh and try again'));
			}
		}else{
			$this->error(__('Verification code acquisition failed, please refresh and try again'));
		}
	}
	/**
	* 发送短信
	*/
	public function sendSMS ($mobile, $content){
		$url = 'http://smsb2c.mitake.com.tw/b2c/mtk/SmSend?';
		$url .= '&username=0966623633';
		$url .= '&password=686868';
		$url .= '&dstaddr='.$mobile;
		$url .= '&smbody='.urlencode($content);
		$url .= '&CharsetURL=UTF-8';
		// echo $url;exit;
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		$output = curl_exec($curl);
		curl_close($curl);
		if(count(explode('statuscode=1',$output)) > 1){
			return true;
		}else{
			return false;
		}
	}
	/**
	* 短信内容
	*/
	public function msg_txt($type){
		$data = array(
			/* 註冊賬號 */
			'0' => array(
				'title' => '【付唄】尊敬的用戶，您的註冊驗證碼為：#code#，請不要向任何人透露此驗證碼。',
				'type' => '註冊賬號',
			),
			/*找回密碼*/
			'1' => array(
				'title' => '【付唄】尊敬的用戶，您正嘗試找回密碼，如果您並未進行此操作，請忽略此簡訊，驗證碼：#code#',
				'type' => '找回密碼',
			),
			/* 使用淘幣 */
			'2' => array(
				'title' => '【付唄】您在申請使用F幣支付訂單，請勿將驗證碼碼告知他人（#code#）',
				'type' => '使用淘幣',
			),
			/*修改密碼*/
			'3' => array(
				'title' => '【付唄】尊敬的用戶，您正嘗試修改密碼，如果您並未進行此操作，請忽略此簡訊，驗證碼：#code#',
				'type' => '修改密碼',
			),
			/* 新增銀行賬戶 */
			'4' => array(
				'title' => '【付唄】尊敬的用戶，您正嘗試添加銀行賬戶，如果您並未進行此操作，請忽略此簡訊，驗證碼：#code#',
				'type' => '新增銀行賬戶',
			),
			/* 发送超商代码 */
			'5' => array(
				'title' => '【付唄】F幣儲值，7-11/全家/萊爾富/OK超商繳費代碼：#code#，金額#money#TWD，請在24小時內完成繳費',
				'type' => '发送超商代码',
			),
			/* 修改手机号 */
			'8' => array(
				'title' => '【付唄】尊敬的用戶，您正嘗試修改手機號，如果您並未進行此操作，請忽略此簡訊，驗證碼：#code#',
				'type' => '发送超商代码',
			),
			/* 发送虚拟币超商代码 */
			'9' => array(
				'title' => '【付唄】USTD交易，7-11/全家/萊爾富/OK超商繳費代碼：#code#，金額#money#TWD，請在24小時內完成繳費',
				'type' => '发送超商代码',
			),
			/* 付款方式 超商 */
			// '1' => '【易購寶】R幣儲值，7-11/全家/萊爾富/OK超商繳費代碼：LLL18290658276，金額5980TWD，請在24小時內完成繳費',
			
			/* 訂單已取消/關閉發送 */
			// '3' => '【易購寶】您的代付鏈接因超時或被賣家關閉，代付失敗，請登入會員中心關閉訂單，重新申請代付，如有疑問請提出申訴。',
			/* 不能代付（您不能未此交易做代付）發送的簡訊內容 */
			// '4' => '【易購寶】您的代付金額被賣家修改了，代付失敗，已經幫您退款到淘幣，請登入會員中心重新提交訂單。',
			/* 提示警示 */
			// '5' => '【易購寶】淘寶阻止了我們為您代付，可能因為您在購買虛擬物品或近期淘寶不允許的交易。金額XXX已幫您退款淘幣，請重新申請代付。',
			/* 使用了錯誤的代付賬號 */
			// '6' => '【易購寶】您創建鏈接使用的朋友帳號有誤，代付失敗，已經幫您退款淘幣，請重新申請代付，如有疑問請提出申訴。 ',
			/* 代付訂單遇到退款發送*/
			// '7' => '【易購寶】您的訂單金額退款$money$ 已幫您退款到淘幣賬戶中~請您注意查收~如有問題請您提交申訴',
			/* 修改手机号 */
			// '8' => '【易購寶】尊敬的用戶，您正嘗試修改手機號，如果您並未進行此操作，請忽略此簡訊，驗證碼：#code#',
			
			
			
			/*身份证审核失败*/
			// '12' => '【易購寶】您的審核未通過，請您參照我們平台驗證處示例圖樣本上傳喔，感謝您LINE:@etao',
			/*身份证审核成功*/
			// '13' => '【易購寶】審核已通過，請您在日後使用時用您提交審核的銀行賬戶轉賬哦~感謝您，LINE:@etao',
		);
		return $data[$type];
	}
	/* @mobile 手机号
	@code 验证码
	@type 类型 */
	public function ruleCode($mobile,$code,$type){
		$map = array(
			'mobile' => $mobile,
			'code' => $code,
			'type' => $type,
		);
		$list = Db::name('code')
				->field('id')
				->where($map)
				->select();
		Db::name('code')->where($map)->delete();
		if($list){
			return true;
		}else{
			return false;
		}
	}
	/**
	*@mobile 手机号
	*@code 验证码
	*@type 类型 
	*/
	public function tips($type){
		switch($type){
			case 0:
				return 'Short Message Verification Code Error';
			break;
			default:
				return 'Short Message Verification Code Error';
		}
	}
	/**
	*订单日志
	*/
	public function orderLog($msg,$order_id,$table='df_order'){
		$data = array(
			'order_id' => $order_id,
			'msg' => $msg,
			'createtime' => time(),
			'updatetime' => time(),
		);
		/*获取年份*/
		$y = date('Y');
		$table = $table.'_log_'.$y;
		/*检查表是否存在*/
		$t_table = 't_'.$table;
		if(count(db()->query('SHOW TABLES LIKE '."'".$table."'"))){
			/*存在*/
			Db::name($table)->insert($data);
		}else{
			/*不存在*/
			$sql = 'CREATE TABLE if not exists '.$t_table.' LIKE t_df_order_log';
			Db::query($sql);
			Db::name($table)->insert($data);
		}
	}
	/**
	*更改称重订单日志
	*/
	public function update_orderLog($result,$ids_array){
		$y = date('Y',time());
		$table = 'jy_order_log_'.$y;
		Db::name($table)->where('p_order_id','in',$ids_array)->update(['order_id'=>$result]);
	}
	/**
	*虚拟订单日志
	*/
	public function virtualLog($msg,$order_id){
		// Db::name('debugging')->insert(['title' => '订单日志', 'msg' => '22']);
		$data = array(
			'order_id' => $order_id,
			'msg' => $msg,
			'createtime' => time(),
			'updatetime' => time(),
		);
		Db::name('virtual_order_log')
			->insert($data);
	}
	/**
	* 淘币日志
	*/
	public function taobiLog($type,$money,$id,$msg="",$info="",$table="df_order"){
		// Db::name('debugging')->insert(['title' => 'orderLog淘币日志', 'msg' => '22']);
		if(intval($money)){
			$return = $this->consumption_type($type);
			if(!$info){
				$info = Db::name('user')->where('id',$this->auth->id)->find();
			}
			if($return === true){
				$price = $info['money'] + $money;
				// $surplus_price = $info['money'];
			}else{
				$price = $info['money'] - $money;
				// $surplus_price = $info['money'];
			}
			$data = array(
				'type' => $type,
				'order_id' => $id,
				'user_id' => $info['id'],
				'price' => $money,
				'surplus_price' => $price,
				'msg' => $msg,
				'table_name' => $table,
				'createtime' => time(),
				'updatetime' => time(),
			);
			$time = date('Ymd',time());
			$_map = array(
				'user_id' => $info['id'],
				'day_time' => $time,
			);
			$info_count = Db::name('taobi_count')->where($_map)->find();
			if($info_count){
				Db::name('taobi_count')->where($_map)->update(['money' => $price]);
			}else{
				$_map['money'] = $price;
				$_map['createtime'] = time();
				$_map['updatetime'] = time();
				Db::name('taobi_count')->insert($_map);
			}
			Db::name('taobi_log')->insert($data);
			Db::name('user')->where('id', $info['id'])->update(['money' => $price]);
		}
	}
	/**
	* 购物金日志
	*/
	public function goldLog($type,$money,$id="",$msg='',$info=""){
		if(intval($money)){
			$return = $this->consumption_type($type);
			if(!$info){
				$info = $this->auth->getUser();
			}
			if($return === true){
				$price = $info['buy_gold'] + $money;
				// $surplus_price = $info['money'];
			}else{
				$price = $info['buy_gold'] - $money;
				// $surplus_price = $info['money'];
			}
			$data = array(
				'type' => $type,
				'order_id' => $id,
				'user_id' => $info['id'],
				'price' => $money,
				'surplus_price' => $price,
				'msg' => $msg,
				'createtime' => time(),
				'updatetime' => time(),
			);
			$time = date('Ymd',time());
			$_map = array(
				'user_id' => $info['id'],
				'day_time' => $time,
			);
			$info_count = Db::name('gold_count')->where($_map)->find();
			if($info_count){
				Db::name('gold_count')->where($_map)->update(['money' => $price]);
			}else{
				$_map['money'] = $price;
				$_map['createtime'] = time();
				$_map['updatetime'] = time();
				Db::name('gold_count')->insert($_map);
			}
			Db::name('gold_log')->insert($data);
			Db::name('user')->where('id', $info['id'])->update(['buy_gold' => $price]);
		}
	}
	/**
	* 消费类型
	* 8系統增加,10推廣任務獎勵',11取消訂單退還',12用戶取消退款',13返利提現', 14发票返利
	
	* 9系統減少',0阿里巴巴代付消費',1淘寶天貓代付消費',2淘寶天貓吱口令消費',
	* 3支付唄儲值消費',4微信儲值消費',5遊戲視頻儲值消費',6其他儲值消費',7淘幣儲值消費'
    * 15抽奖消费
	*/
	public function consumption_type($type){
		if($type == '8' || $type == '10' || $type == '11' || $type == '12' || $type == '13'){
			return true;
		}else{
			return false;
		}
	}
	/* 获取购物金规则 */
	public function getGoldRules($id){
		return Db::name('gold_rules')
					->field('*')
					->where('id',$id)
					->find();
	}
	/**
	* 接收图片
	*/
	public function getImg($img){
		//目录的upload文件夹下
        $up_dir = "uploads/".Random::numeric(8) . "/";  //创建目录
        if(!file_exists($up_dir)){
            mkdir($up_dir,0777,true);
        }
        $base64_img = trim($img);
        if(preg_match('/^(data:\s*image\/(\w+);base64,)/', $base64_img, $result)){
            $type = $result[2];
            if(in_array($type,array('pjpeg','jpeg','jpg','gif','bmp','png'))){
                $new_file = $up_dir.time().'.'.$type;
                if(file_put_contents($new_file, base64_decode(str_replace($result[1], '', $base64_img)))){
                    $img_path = str_replace('../../..', '', $new_file);
                    return array('code'=>0,'data'=>'/'.$img_path,'msg'=>'Submit successfully');
                }else{
                    return array('code'=>1,'data'=>'','msg'=>'Picture upload failed');
                }
            }else{
                //文件类型错误
                return array('code'=>1,'data'=>'','msg'=>'Picture upload type error');
            }
		}
	}
	/*快递查询*/
	public function get_kuaidi($_data){
		$key = 'SPPgHIwt2937';                        //客户授权key
		$customer = 'BFE3271FF1FC5FD40DF892C6B679C089';                   //查询公司编号
		$param = array (
			'com' => $_data['waybill_name'],             //快递公司编码
			'num' => $_data['waybill'],     //快递单号
		);
		//请求参数
		$post_data = array();
		$post_data["customer"] = $customer;
		$post_data["param"] = json_encode($param);
		$sign = md5($post_data["param"].$key.$post_data["customer"]);
		$post_data["sign"] = strtoupper($sign);
		
		$url = 'http://poll.kuaidi100.com/poll/query.do';    //实时查询请求地址
		$params = "";
		foreach ($post_data as $k=>$v) {
			$params .= "$k=".urlencode($v)."&";              //默认UTF-8编码格式
		}
		$post_data = substr($params, 0, -1);
		//发送post请求
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		$result = curl_exec($ch);
		$data = str_replace("\"", '"', $result );
		$data = json_decode($data,true);
		return $data;
	}
	/*支付宝转账*/
    public static function alipayWithDraw_new($out_biz_no = "", $member_id, $amount, $payee_account, $payee_real_name, $type)
    {
		$map = array(
			'ali_account' => $payee_account,
			'name' => $payee_real_name,
		);
        if ($type == 1) {
            $payer_show_name = '支付唄賬號實名認證';
            $remark = '支付唄賬號實名認證';
			$ali_account = $payee_account;
            $payee_real_name = $payee_real_name;
        } else if ($type == 2) {
            $payer_show_name = '支付貨款';
            $remark = '支付貨款';
            $alipay = Db::name('user_alipay')->where($map)->find();
            $payee_real_name = $alipay['name']?$alipay['name']:'';
			$ali_account = $alipay['ali_account'];
        } else if ($type == 3) {
            $payer_show_name = 'usdt貨款';
            $remark = 'usdt貨款';
            $alipay = Db::name('user_alipay')->where($map)->find();
            $payee_real_name = $alipay['name']?$alipay['name']:'';
			$ali_account = $alipay['ali_account'];
        } else if ($type == 4) {
            $payer_show_name = '平台转账';
            $remark = '平台转账';
			$ali_account = $payee_account;
        }
		if(!empty($alipay)){
			if($alipay['is_transfer'] == '0'){
				return array('status' => 0, 'msg' => '用户支付宝账号异常');
			}
		}
		if( empty($out_biz_no) )
		{
			$out_biz_no = date("Y") . strtoupper(dechex(date('m'))) . date('d') . substr(time(), -5) . substr(microtime(), 2, 7);
		}
		$site_alipay = 'site.alipay'.Config::get('site.new_alipay_open');
		$num = Config::get('site.new_alipay_open');
		if($type == '2' && $out_biz_no){
			/*获取订单*/
			$order_info = Db::name('df_order')->where('order_no',$out_biz_no)->find();
			if($order_info['type'] == '3'){
				$group = Db::name('group_pay')->where('id',$order_info['group_id'])->find();
				if($group){
					$site_alipay = 'site.new_alipay_open'.$group['payment_account'];
					$num = $group['payment_account'];
				}
			}
		}
		
		
		Vendor('alipay2.aop.AopCertClient');
		Vendor('alipay2.aop.request.AlipayFundTransUniTransferRequest');
		$c = new \AopCertClient;
		$ali_config = Config::get("$site_alipay");
		$appCertPath = "/var/www/haigou/vendor/cert/zhengshu".$num."/appCertPublicKey.crt";
		$alipayCertPath = "/var/www/haigou/vendor/cert/zhengshu".$num."/alipayCertPublicKey_RSA2.crt";
		$rootCertPath = "/var/www/haigou/vendor/cert/zhengshu".$num."/alipayRootCert.crt";
		$c->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
		$c->appId = $ali_config['appId'];
		$c->rsaPrivateKey = $ali_config['rsaPrivateKey'];
		$c->format = "json";
		$c->charset= 'UTF-8';
		$c->signType= 'RSA2';
		
		
		//调用getPublicKey从支付宝公钥证书中提取公钥
		$c->alipayrsaPublicKey = $c->getPublicKey($alipayCertPath);
		//是否校验自动下载的支付宝公钥证书，如果开启校验要保证支付宝根证书在有效期内
		$c->isCheckAlipayPublicCert = true;
		//调用getCertSN获取证书序列号
		$c->appCertSN = $c->getCertSN($appCertPath);
		//调用getRootCertSN获取支付宝根证书序列号
		$c->alipayRootCertSN = $c->getRootCertSN($rootCertPath);
		//实例化具体API对应的request类,类名称和接口名称对应,当前调用接口名称：alipay.open.public.template.message.industry.modify
		$request = new \AlipayFundTransUniTransferRequest();
		$request->setBizContent ( "{".
		 "\"out_biz_no\":\"$out_biz_no\",".
		 "\"trans_amount\":$amount,".
		 "\"product_code\":\"TRANS_ACCOUNT_NO_PWD\",".
		 "\"biz_scene\":\"DIRECT_TRANSFER\",".
		 "\"order_title\":\"$payer_show_name\",".
		 "\"payee_info\":{".
		 "\"identity\":\"$ali_account\",".
		 "\"identity_type\":\"ALIPAY_LOGON_ID\",".
		 "\"name\":\"$payee_real_name\",".
		 "    },".
		 "\"remark\":\"$payer_show_name\",".
		 "  }" );
		$result= $c->execute($request);
		$responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";
		$resultCode = $result->$responseNode->code;
        if (!empty($resultCode) && $resultCode == 10000) {
            return array('status' => 1, 'msg' => '转账成功','res'=>$resultCode);
        } else {
            //$result->$responseNode->sub_msg 这个参数 是返回的错误信息
            return array('status' => 0, 'msg' => $result->$responseNode->sub_msg);
        }
    }
	/*支付宝转账-旧*/
	public static function alipayWithDraw($out_biz_no = "", $member_id, $amount, $payee_account, $payee_real_name, $type){
		Vendor('alipay.aop.AopClient');
		Vendor('alipay.aop.request.AlipayFundTransToaccountTransferRequest');
		Vendor('alipay.aop.SignData');
		$map = array(
			'ali_account' => $payee_account,
			'name' => $payee_real_name,
		);
		if ($type == 1) {
            $payer_show_name = '支付唄賬號實名認證';
            $remark = '支付唄賬號實名認證';
			$ali_account = $payee_account;
            $payee_real_name = $payee_real_name;
        }else if ($type == 2) {
            $payer_show_name = '支付貨款';
            $remark = '支付貨款';
            $alipay = Db::name('user_alipay')->where($map)->find();
            $payee_real_name = $alipay['name']?$alipay['name']:'';
			$ali_account = $alipay['ali_account'];
        } else if ($type == 3) {
            $payer_show_name = 'usdt貨款';
            $remark = 'usdt貨款';
            $alipay = Db::name('user_alipay')->where($map)->find();
            $payee_real_name = $alipay['name']?$alipay['name']:'';
			$ali_account = $alipay['ali_account'];
        }
		$site_alipay = 'site.alipay'.Config::get('site.alipay_open');
		if($type == '2' && $out_biz_no){
			/*获取订单*/
			$order_info = Db::name('df_order')->where('order_no',$out_biz_no)->find();
			if($order_info['type'] == '3'){
				$group = Db::name('group_pay')->where('id',$order_info['group_id'])->find();
				if($group){
					$site_alipay = 'site.alipay'.$group['payment_account'];
				}
			}
		}
		$ali_config = Config::get("$site_alipay");
		$aop = new \AopClient();
		$aop->gatewayUrl = 'https://openapi.alipay.com/gateway.do';
		$aop->appId = $ali_config['appId'];//商户appid 在支付宝控制台找
		$aop->rsaPrivateKey = $ali_config['rsaPrivateKey'];//私钥 工具生成的
		$aop->alipayrsaPublicKey = $ali_config['alipayrsaPublicKey'];//支付宝公钥 上传应用公钥后 支付宝生成的支付宝公钥
		$aop->apiVersion = '1.0';
		$aop->signType = 'RSA2';
		$aop->postCharset = 'utf-8';
		$aop->format = 'json';
		$request = new \AlipayFundTransToaccountTransferRequest();
		if( empty($out_biz_no) )
        {
            $out_biz_no = date("Y") . strtoupper(dechex(date('m'))) . date('d') . substr(time(), -5) . substr(microtime(), 2, 7);
        }
		$request->setBizContent("{" .
				"\"out_biz_no\":\"$out_biz_no\"," .
				"\"payee_type\":\"ALIPAY_LOGONID\"," .
				"\"payee_account\":\"$payee_account\"," .
				"\"amount\":\"$amount\"," .
				"\"payer_show_name\":\"$payer_show_name\"," .
				"\"payee_real_name\":\"$payee_real_name\"," .
				"\"remark\":\"$remark\"" .
				"}");
		$result = $aop->execute($request);
		$responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";
		$resultCode = $result->$responseNode->code;
		if (!empty($resultCode) && $resultCode == 10000) {
			return array('status' => 1, 'msg' => '转账成功','res'=>$resultCode);
		} else {
			//$result->$responseNode->sub_msg 这个参数 是返回的错误信息
			return array('status' => 0, 'msg' => $result->$responseNode->sub_msg);
		}
	}
    /**
     * 返回获得到的数组
     * [getAccessToken description]
     * @param  [type] $code       [description]
     * @param  string $token_file [description]
     * @return [type]             [description]
     */
    protected function getAccessToken($code,$APP_KEY,$APP_SECRET){
        $url="https://api.weixin.qq.com/sns/oauth2/access_token?appid=".$APP_KEY."&secret=".$APP_SECRET."&code=".$code."&grant_type=authorization_code";
        $res = json_decode($this->curl_get($url) , true);
        return $res;
    }
    /**
     * 封装curl,get方式
     * @param string $url 地址
     * @return string 结果
     */
    public function curl_get($url){
        header("Content-type:text/html; charset=UTF-8");
        /*初始化*/
        $ch = curl_init();
        /*设置选项，包括URL*/
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        /*执行并获取HTML文档内容*/
        $output = curl_exec($ch);
        /*释放curl句柄*/
        curl_close($ch);
        /*返回获得的数据*/
        return $output;
    }
    protected function getWxuser($token,$openid){
        $url="https://api.weixin.qq.com/sns/userinfo?access_token=".$token."&openid=".$openid."&lang=zh_CN";
        $res = json_decode($this->curl_get($url) , true);
        return $res ;
    }


    /*微信轉賬 -废弃*/
    public static function wxWithdrawals($partner_trade_no="",$member_id,$openid,$amount,$type){
		Db::name('debugging')->insert(['title' => '进来了', 'msg' => '1']);
	   $data['partner_trade_no'] = $partner_trade_no?$partner_trade_no:Random::numeric(16);
        $data['openid'] = $openid;
        $data['money'] = $amount;
        if($type == 1){
            $data['desc'] = '微信賬戶實名認證';
        }else if($type == 2){
            $data['desc'] = '支付貨款';
        }
        vendor('wxpay.WxPay#CompanyPay');
        $CompanyPay = new \CompanyPay();
        // $wx = \app\common\conf\Config::get_wx();
		$wx = Config::get('site.wechat');
        $wxchat['appid'] = $wx['APPID'];
        $wxchat['mchid'] = $wx['MCHID'];
        $wxchat['key'] = $wx['KEY'];
        $res = $CompanyPay->wxbuild($data,$wxchat);
		Db::name('debugging')->insert(['title' => '回传', 'msg' => json_encode($res)]);
        if($res['result_code'] == 'SUCCESS'){
//            $new_wx_amount = C('WX_AMOUNT') - $amount;
//            C('WX_AMOUNT',$new_wx_amount);
            return array('status'=>1,'msg'=>'转账成功','res'=>$res);
        }else{
            return array('status'=>0,'msg'=>'微信自動轉賬失敗');
        }
    }
	/**
	* 	产生导航树
	*	@param	$list  结果集
	*	@param	$pk ID号
	*	@param	$pid	父id
	*	@param	$child	子树字段
	*	@param	$root	
	*	@param	$key	
	*/
	public function list_to_tree($list, $pk = 'id', $pid = 'pid', $child = 'list', $root = 0, $key = '') {
		// 创建Tree
		$tree = array ();
		if (is_array ( $list )) {
			// 创建基于主键的数组引用
			$refer = array ();
			foreach ( $list as $k => $data ) {
				$refer [$data [$pk]] = & $list [$k];
			}
			foreach ( $list as $k => $data ) {
				// 判断是否存在parent
				$parentId = $data [$pid];
				if ($root == $parentId) {
					if ($key != '') {
						$tree [$data [$key]] = & $list [$k];
					} else {
						$tree [] = & $list [$k];
					}
				} else {
					if (isset ( $refer [$parentId] )) {
						$parent = & $refer [$parentId];
						if ($key != '') {
							$parent [$child] [$data [$key]] = & $list [$k];
						} else {
							$parent [$child] [] = & $list [$k];
						}
					}
				}
			}
		}
		return $tree;
	}
	/* 订单完成 */
	public function order_processing($order_id,$table){
		/* 获取订单 */
		$order = Db::name($table)->field('*')->where(['id'=>$order_id])->find();
		/* 获取用户信息 */
		$info = Db::name('user')->field('*')->where('id', $order['user_id'])->find();
		$use_rule = Db::name('use_rule')->where('rule_type','1')->find();
		/* 判斷用戶是否是第一次完成完成訂單 */
		if($info['is_order'] == 0){
			/* 第一次完成訂單獎勵購物金 */
			if($info['pid']){
				/* 获取推荐人信息 */
				$_info = Db::name('user')->field('*')->where('id', $info['pid'])->find();
				User::give_gold($_info,Config::get('site.give4'),$info['id']);
				if(Config::get('site.coupon_open')){
					/*首单赠送优惠卷*/
					if($use_rule['is_order'] == '1'){
						$this->give_coupon($info['pid'],$use_rule['coupon_id']);
					}
				}
			}
		}
		/*每次下单赠送优惠卷*/
		if(Config::get('site.coupon_open')){
			/*每单赠送优惠卷*/
			if($use_rule['is_order'] == '2'){
				$this->give_coupon($info['pid'],$use_rule['coupon_id']);
			}
		}
		$rules = Config::get('site.give_gold');
		$gold_rules = Db::name('gold_rules')->field('*')->select();
		foreach($gold_rules as $val){
			$_gold_rules[$val['id']] = $val;
		}
		if($info['is_order'] == 0 && $order['rmb_money'] > '5000'){
			$rules = Config::get('site.give_gold');
			foreach($rules as $key=>$val){
				$this->goldLog('8',$_gold_rules[$key]['money'],$order_id,$_gold_rules[$key]['title'],$info);
			}
		}else{
			/* 判斷用戶累計消費金額是否達到贈送購物金條件 */
			$cumulative_rmb = $info['cumulative_rmb'];
			$rmb_mobey = $order['rmb_money'] - $order['balance_money'];
			$count_gold = $cumulative_rmb + $rmb_mobey;
			/* 获取上一次奖励等级 */
			$old_give = array_flip(array_filter($rules, function($rules) use($cumulative_rmb) { return $cumulative_rmb >= $rules; }));
			/* 获取本次奖励等级 */
			$give = array_flip(array_filter($rules, function($rules) use($count_gold) { return $count_gold >= $rules; }));
			sort($old_give);
			sort($give);
			$old_give_str = implode('a',$old_give);
			$give_str = implode('a',$give);
			/* 两次奖励等级不一致给与奖励 */
			if($old_give_str != $give_str){
				//$gold_rules = Db::name('gold_rules')->field('*')->where('id',$give['0'])->find();
				$this->goldLog('8',$_gold_rules[$give['0']]['money'],$order_id,$_gold_rules[$give['0']]['title'],$info);
			}
		}
		
		/* 計算用戶是否達到升級要求 */
		$count_exp = $info['score'] + $rmb_mobey;
		/* 更新等級 */
		$level = User::nextlevel($count_exp);
		$data = array(
			'level' => $level,
			'frozen_price' => '0',
			'is_order' => '1',
			'score' => $count_exp,
			'cumulative_rmb' => $count_gold,
			'cumulative_twb' => $info['cumulative_twb'] + $order['actual_money'],
		);
		Db::name('user')->where('id', $info['id'])->update($data);
		Db::name($table)->where('id', $order_id)->update(['completetime' => time()]);
		$this->orderLog('订单完成',$order_id);
		/* 添加统计数据 */
		$time = date('Ymd',time());
		$count_map = array(
			'daytime' => date('Ymd',$time),
			'user_id' => $info['id'],
		);
		$info_count = Db::name('count_order')->where($count_map)->find();
		if($info_count){
			/* 存在数据更新 */
			$count_data = array(
				'all_tb_money' => $info_count['all_tb_money'] + $order['actual_money'],
				'all_rmb_money' => $info_count['all_rmb_money'] + ($order['rmb_money'] - $order['balance_money']),
				'all_order' => $info_count + 1,
				'updatetime' => time(),
			);
			Db::name('count_order')->where('user_id', $info_count['user_id'])->update($count_data);
		}else{
			/* 不存在数据添加 */
			$count_data = array(
				'user_id' => $info['id'],
				'all_tb_money' => $order['actual_money'],
				'all_rmb_money' => $order['rmb_money'] - $order['balance_money'],
				'all_order' => '1',
				'daytime' => $time,
				'createtime' => time(),
				'updatetime' => time(),
			);
			Db::name('count_order')->insert($count_data);
		}
	}
	/**
	 *  调用淘宝API根据IP查询地址
	 */
	public static function ip_address($ip)
	{
		$durl = 'http://ip-api.com/json/'.$ip.'?lang=zh-CN';
		// 初始化
		$curl = curl_init();
		// 设置url路径
		curl_setopt($curl, CURLOPT_URL, $durl);
		// 将 curl_exec()获取的信息以文件流的形式返回，而不是直接输出。
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true) ;
		// 在启用 CURLOPT_RETURNTRANSFER 时候将获取数据返回
		curl_setopt($curl, CURLOPT_BINARYTRANSFER, true) ;
		// 执行
		$data = curl_exec($curl);
		// 关闭连接
		curl_close($curl);
		// 返回数据
		return json_decode($data,true);
		// if(!mb_check_encoding($data, 'utf-8')) {
			// $data = mb_convert_encoding($data,'UTF-8',['ASCII','UTF-8','GB2312','GBK']);
		// }
		// $start = strpos($data,'{')+1;
		// $trim = substr($data,$start,'-3');
		// $trim_array = explode(',',$trim);
		// foreach($trim_array as $key=>$ls){
			// $array[$key] = explode(':',$ls);
		// }
		// return $array;
	}
	/**
	 *  ip 状态设置
	 */
	public static function set_ip_manage( $ip , $status)
	{
		$_res = Db::name('ip_manage')->where('ip', $ip)->update(['status'=> $status]);
		if( empty($_res) )
		{
			return false;
		}
		return true;
	}

	/**
	 *  添加异常ip 记录
	 */
	public static function add_ip_manage( $ip, $remark='' )
	{
		$_res = Db::name('ip_manage')->insertGetId(['ip'=>$ip, 'remark'=> $remark]);
		if( empty($_res) )
		{
			return false;
		}
		return true;
	}
	
	/**
	* 虚拟码生成
	*/
	public function virtual_code($num){
		$generation = [3,7,1];
		$result = 0;
		$index = 0;
		foreach(str_split($num,1) as $i){
			$result += $i*$generation[$index++ % 3];
		}
		return $result % 10;
	}
	/**
	* 用户关键行为记录
	*/
	public function user_log($msg){
		$data = array(
			'user_id' => $this->auth->id,
			'username' => $this->auth->username,
			'msg' => $msg,
			'createtime' => time(),
			'updatetime' => time(),
		);
		Db::name('user_log')->insert($data);
	}
	/*虚拟码核验入库*/
	public function generate_code($order_id,$member_id,$price,$group="",$table){
		$virtual_code = $this->generate_get_code($order_id,$member_id,$price,$group="");
		$info = Db::name('virtual_code')->where(array('num'=>$virtual_code,'type'=>'0'))->find();
		if($info){
			$virtual_code = $this->generate_get_code($order_id,$member_id,$price,$group="");
		}
		$result = Db::name($table)->where('id',$order_id)->update(['cs_code' => $virtual_code,'pay_status'=>'3']);
		$data = array(
			'order_id'=>$order_id,
			'type'=>'0',
			'table_name'=>$table,
			'num'=>$virtual_code,
			'createtime'=>time(),
			'updatetime'=>time(),
		);
		$_result = Db::name('virtual_code')->insert($data);
		$this->orderLog('生成虚拟码'.$virtual_code,$order_id,$table);
		if($result && $_result){
			return true;
		}else{
			return false;
		}
	}
	public function generate_get_code($order_id,$member_id,$price,$group=""){
		if($group){
			$a = $group;
		}else{
			if(Config::get('site.virtual_open') == '1'){
				$a = '43869';
			}
			if(Config::get('site.virtual_open') == '2'){
				$a = '92113';
			}
			if(Config::get('site.virtual_open') == '3'){
				$a = '92245';
			}
			if(Config::get('site.virtual_open') == '4'){
				$a = '92487';
			}
		}
		$bb = date("z")+4;/*缴款截至期限*/
		$b = str_pad($bb,3,0,STR_PAD_LEFT );
		$year = date('Y',time());
		$c = $year%10;/*缴款截至年份*/
		$member_id = rand('1111','9999');
		$qiye = $a.$c.$b.$member_id;
		$_price = str_pad($price,10,0,STR_PAD_LEFT );
		$a_value = $this->virtual_code($qiye);/*A值6*/
		$b_value = $this->virtual_code($_price);/*B值3*/
		$ab_value = $a_value + $b_value;
		$c_value = $ab_value%10;/*C值*/
		$_check_code = 10 - $c_value;
		$check_code = $_check_code%10;/*检查码*/
		return $qiye.$check_code;
	}
	public function sendrequest($url, $params =[], $method = 'POST', $options = [],$header = []){
		$method = strtoupper($method);
		$protocol = substr($url, 0, 5);
		$query_string = is_array($params) ? http_build_query($params) : $params;
		$ch = curl_init();
		$defaults = [];
		if ('GET' == $method) {
			$geturl = $query_string ? $url.(stripos($url, '?') !== false ? '&' : '?').$query_string : $url;
			$defaults[CURLOPT_URL] = $geturl;
		} else {
			$defaults[CURLOPT_URL] = $url;
			if ($method == 'POST') {
				$defaults[CURLOPT_POST] = 1;
			} else {
				$defaults[CURLOPT_CUSTOMREQUEST] = $method;
			}
			$defaults[CURLOPT_POSTFIELDS] = $query_string;
		}
		$defaults[CURLOPT_HEADER] = false;
		$defaults[CURLOPT_USERAGENT] = request()->header('user-agent');
		$defaults[CURLOPT_FOLLOWLOCATION] = true;
		$defaults[CURLOPT_RETURNTRANSFER] = true;
		//制使用IPV4解析
		$defaults[CURLOPT_IPRESOLVE] = CURL_IPRESOLVE_V4;
		
		// disable 100-continue
		$merge_header=array_merge(['Expect:'],$header);
		curl_setopt($ch, CURLOPT_HTTPHEADER,$merge_header);
		
		if ('https' == $protocol) {
			$defaults[CURLOPT_SSL_VERIFYPEER] = false;
			$defaults[CURLOPT_SSL_VERIFYHOST] = false;
		}
		curl_setopt_array($ch, (array) $options + $defaults);
		
		$return = curl_exec($ch);
		curl_close($ch);
		return json_decode($return,true);
	}
	/* 判断是否添加机器人为好友 */
	public function line_robot(){
		$info = Db::name('user')->where('id',$this->auth->id)->find();
		if($info['lineid']){
			$url = 'https://api.line.me/v2/bot/profile/'.$info['lineid'];
			$headers = array(
				'Content-Type: application/json',
				'Authorization: Bearer s7LE2RGQDR1yODexmJVBXHEeEGpPQvIZuxxQLSD4isd/MhuldNprZxh1BtjhYP0aOY9QsXsoSb/TdqlrBStAuz1DOAPKdR/O85P+kYOveEybr3HI/gN/L7jD0v8JhSQTB/p+D5l9iDq/ON/mCxK4RAdB04t89/1O/w1cDnyilFU='
			);
			$curl = curl_init();
			curl_setopt($curl, CURLOPT_URL, $url);
			curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
			curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
			$response = curl_exec($curl);
			curl_close($curl);
			$return = '0';
			if($response){
				 $return = json_decode($response, true);
			}
			if(count($return) > 1){
				return 1;
			}else{
				return 1;
			}
		}else{
			return 0;
		}
	}
	/* 发送消息 */
	public function send_line($lineid,$title){
		$data = array(
			'to' => $lineid, // Replace with your destination group ID
			'messages' => array(
				array(
					'type' => 'text',
					'text' =>  $title,// Replace with your desired message text
				)
			)
		);
		$headers = array(
			'Content-Type: application/json',
			'Authorization: Bearer s7LE2RGQDR1yODexmJVBXHEeEGpPQvIZuxxQLSD4isd/MhuldNprZxh1BtjhYP0aOY9QsXsoSb/TdqlrBStAuz1DOAPKdR/O85P+kYOveEybr3HI/gN/L7jD0v8JhSQTB/p+D5l9iDq/ON/mCxK4RAdB04t89/1O/w1cDnyilFU='
		);
		$url = "https://api.line.me/v2/bot/message/push";
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_POST, true);
		curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
		$response = curl_exec($curl);
		curl_close($curl);
		//Log::debug($response);
		return $response;
	}
	/*赠送优惠卷*/
   static public function give_coupon($user_id,$coupon_id){
        $coupon_array = explode(',',$coupon_id);
        //获取优惠券信息
        $coupon_info = Db::name('coupon')->where('id','in',$coupon_array)->select();
        $coupon_num = [];
        $coupon = Db::name('coupon')->where('id','in',$coupon_array)->select();
        $use_coupon = [];
        foreach($coupon as $k=>$v){
            $use_coupon[$k]['coupon_id'] = $v['id'];
            $use_coupon[$k]['title'] = $v['title'];
            $use_coupon[$k]['reaching_amount'] = $v['reaching_amount'];
            $use_coupon[$k]['reduce_amount'] = $v['reduce_amount'];
            $use_coupon[$k]['start_time'] = time();
            $use_coupon[$k]['end_time'] = strtotime("+{$v['use_num']} days");
            $use_coupon[$k]['is_use'] = '0';
            $use_coupon[$k]['createtime'] = time();
            $use_coupon[$k]['updatetime'] = time();
            $use_coupon[$k]['user_id'] = $user_id;
            Db::name('coupon')->where('id',$v['id'])->setInc('issued_num');
        }
        Db::name('user_coupon')->insertAll($use_coupon);
    }



	/*赠送限时优惠卷*/
	static public function give_coupon_limit($user_id,$coupon_id,$begtime,$endtime){
        $coupon_array = explode(',',$coupon_id);
        //获取优惠券信息
        $coupon = Db::name('coupon')->where('id','in',$coupon_array)->select();
        $use_coupon = [];
        foreach($coupon as $k=>$v){
            $use_coupon[$k]['coupon_id'] = $v['id'];
            $use_coupon[$k]['title'] = $v['title'];
            $use_coupon[$k]['reaching_amount'] = $v['reaching_amount'];
            $use_coupon[$k]['reduce_amount'] = $v['reduce_amount'];
            $use_coupon[$k]['start_time'] = $begtime;
            $use_coupon[$k]['end_time'] = $endtime;
            $use_coupon[$k]['is_use'] = '0';
            $use_coupon[$k]['createtime'] = time();
            $use_coupon[$k]['updatetime'] = time();
            $use_coupon[$k]['user_id'] = $user_id;
            Db::name('coupon')->where('id',$v['id'])->setInc('issued_num');
        }
       	return Db::name('user_coupon')->insertAll($use_coupon);
    }




}