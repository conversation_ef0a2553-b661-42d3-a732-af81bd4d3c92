<?php

namespace app\common\model;

use think\Model;
use think\Db;
use think\Session;
use think\Loader;

/**
 * 淘宝客模型
 */
class CouponModel extends Model
{

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    // 追加属性
    protected $append = [
        'url',
    ];
    /**
	* 获取淘宝联盟商品
	*/
	public static function taobao_api($num,$p){
		vendor("taobao.TopSdk");
		//请求淘宝客API，实例化类 **注意使用\命名空间**
		$c = new \TopClient;  
		$c->appkey = '28079994';
        $c->secretKey = 'fdfa8204b8e78fda9ecdcb686d0e1057';
		$req = new \TbkDgOptimusMaterialRequest;
		$req->setPageSize($num);
		$req->setPageNo($p);
		$req->setAdzoneId("109726850462");
		$req->setMaterialId("9660");
		// $req = new \TbkUatmFavoritesItemGetRequest;
		// $req->setPlatform("1");
		// $req->setPageSize($num);
		// $req->setAdzoneId("109726850462");
		// $req->setUnid("3456");
		// $req->setFavoritesId("19977056");
		// $req->setPageNo("1");
		// $req->setFields("num_iid,title,pict_url,reserve_price,zk_final_price,click_url,coupon_click_url,coupon_info,volume,tk_rate,coupon_end_time,coupon_start_time");
		$resp = $c->execute($req);
		$taobao = self::object_array($resp);
		return $taobao['result_list']['map_data'];
	}
	/**
	* 搜索淘宝联盟商品
	*/
	public static function seach_product($title,$p){
		vendor("taobao.TopSdk");
		$c = new \TopClient;
		$c->appkey = '27844239';
        $c->secretKey = '4fa34ec12f09417c3caf02639fa71502';
		$req = new \TbkDgMaterialOptionalRequest;
		// $req->setPageSize("6");
		$req->setPageNo($p);
		$req->setQ($title);
		$req->setAdzoneId("109364700395");
		$req->setHasCoupon("true");
		$resp = $c->execute($req);
		$taobao = self::object_array($resp);
		return $taobao['result_list']['map_data'];
	}
	public static function object_array($array) {
		if(is_object($array)) {  
			$array = (array)$array;
		} if(is_array($array)) {  
			 foreach($array as $key=>$value) {  
				$array[$key] = self::object_array($value);  
			}  
		}
		return $array;
	}
	/* 订单查询 */
	public static function get_order(){
		// $c = new TopClient;
		// $c->appkey = $appkey;
		// $c->secretKey = $secret;
		vendor("taobao.TopSdk");
		$c = new \TopClient;
		$c->appkey = '27844239';
        $c->secretKey = '4fa34ec12f09417c3caf02639fa71502';
		$req = new \TbkScOrderDetailsGetRequest;
		$req->setQueryType("1");
		// $req->setPositionIndex("2222_334666");
		$req->setPageSize("20");
		// $req->setMemberType("2");
		$req->setTkStatus("14");
		$req->setEndTime("2019-11-16 12:28:22");
		$req->setStartTime("2019-11-18 12:18:22");
		// $req->setJumpType("1");
		// $req->setPageNo("1");
		// $req->setOrderScene("1");
		$resp = $c->execute($req, $sessionKey);
		$taobao = self::object_array($resp);
		print_r($taobao);exit;
	}
}
