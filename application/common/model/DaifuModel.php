<?php

namespace app\common\model;

use think\Model;
use think\Session;
use fast\Random;
use think\Db;
use think\Exception;
use think\Config;

/**
 * 代付模型
 */
class DaifuModel extends Model
{

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    // 追加属性
    protected $append = [
        'url',
    ];
	/**
	* 获取是否有未支付的订单
	*/
    /**
     * 获取平台账号
     * @return array
     */
    public static function getSysAlipay($type = "")
    {
		$map = array(
			'type_switch' => '0',
			'status' => 'normal',
		);
		if($type == '1'){
			$map['status_switch'] = 1;
		}
		if($type == '2'){
			$map['status_switch'] = 0;
		}
        $sysalipay = Db::name('sys_alipay')
				->field('sys_account')
				->where($map)
				->orderRaw("RAND()")
				->select();
		$data = array();
		foreach($sysalipay as $ls){
			$data[] = $ls['sys_account'];
		}
		return $data;
    }
	/**
     * 通过金额查询代付商品返回用户支付宝账号
     * @return array
     */
    public static function getDaifuProduct($money,$userid,$url,$sys_alipay_id)
    {
		/*判断数据是否已经入库*/
		$url_array = parse_url(html_entity_decode($url));
		parse_str($url_array['query'],$_url_array);
		$alipay_map =array(
			//'price' => $money,
			//'peerpay_link' => html_entity_decode($url),
			'user_id' => $userid,
			'url_id' => $_url_array['id'],
			'sign' => $_url_array['sign'],
		);
		$info = Db::name('alipay_order')->where($alipay_map)->find();
		if($info){
			$time = time()-10800;
			$start_time = date('Y-m-d H:i:s',$time);
			$end_time = date('Y-m-d H:i:s',time());
			$map = array(
				'price' => $money,
				'user_id' => $userid,
				'trade_no' => array('neq',' '),
				'status' => array('in',['0','8']),
				'alipay_status' => '0',
			);
			/*获取绑定支付宝*/
			/*$alipay = Db::name('bonding_alipay')->where('user_id',$userid)->select();
			if($alipay){
				foreach($alipay as $ls){
					$other_name[] = $ls['other_name'];
					$other_account[] = $ls['other_account'];
				}
				$map['other_name'] = array('in',$other_name);
				$map['other_account'] = array('in',$other_account);
			}*/
			$list = Db::name('alipay_order')
					->field('id,other_name,price,other_account')
					->where($map)
					->whereTime('update_time','between',[$start_time,$end_time])
					->select();
			return $list;
		}else{
			$data = array(
				'price' => $money,
				'peerpay_link' => html_entity_decode($url),
				'status' => '0',
				'user_id' => $userid,
				'sys_alipay_id' => $sys_alipay_id,
				'verson' => '1',
				'url_id' => $_url_array['id'],
				'sign' => $_url_array['sign'],
			);
			try {
				Db::name('alipay_order')->insert($data,true);
			}catch (Exception $e) {
				return false;
			}
			return false;
		}
		/*$time = time()-10800;
		$start_time = date('Y-m-d H:i:s',$time);
		$end_time = date('Y-m-d H:i:s',time());
		$map = array(
			'price' => $money,
			'user_id' => $userid,
			'trade_no' => array('neq',' '),
			'status' => array('in',['0','8']),
			'alipay_status' => '0',
		);
		$info = Db::name('alipay_order')->where($map)->find();
		if($info['is_del'] == '1'){
			return 1;
		}
		if(!$info){
			$data = array(
				'price' => $money,
				//'peerpay_link' => html_entity_decode($url),
				'status' => '0',
				'is_del' => array('in',['0','1']),
				'user_id' => $userid,
			);
			$_info  = Db::name('alipay_order')->where($data)->find();
			if(!$_info){
				$data['sys_alipay_id'] = $sys_alipay_id;
				$data['verson'] = '1';
				$data['peerpay_link'] = html_entity_decode($url);
				//$data['user_id'] = $userid;
				Db::name('debugging')->insert(['title' => $money, 'msg' => json_encode($data)]);
				Db::name('alipay_order')->insert($data);
			}
			return 2;
		}*/
		/*获取绑定支付宝*/
		/*$alipay = Db::name('bonding_alipay')->where('user_id',$userid)->select();
		if($alipay){
			foreach($alipay as $ls){
				$other_name[] = $ls['other_name'];
				$other_account[] = $ls['other_account'];
			}
			$map['other_name'] = array('in',$other_name);
			$map['other_account'] = array('in',$other_account);
		}
        $list = Db::name('alipay_order')
				->field('id,other_name,price,other_account')
				->where($map)
				->whereTime('update_time','between',[$start_time,$end_time])
				->select();
		return $list;*/
    }
	/**
	* 绑定支付宝
	*/
	public static function bondingAlipay($find,$user_id){
		/* 查询该支付宝是否绑定过 */
		$map = $data = array(
			'user_id' => $user_id,
			'other_name' => $find['other_name'],
			'other_account' => $find['other_account'],
			'status' => 'normal',
		);
		unset($map['other_name']);
		$_find = Db::name('bonding_alipay')
				->field('id')
				->where($map)
				->find();
		if(empty($_find)){
			$data['createtime'] = time();
			$data['updatetime'] = time();
			Db::name('bonding_alipay')->insert($data);
		}
		return Db::name('bonding_alipay')->where('user_id',$user_id)->select();
	}
	/* 提交吱口令 */
	public static function getZhikouling($money,$user_id){
		$status = '0';
		$goods = '';
		preg_match('/[\w]{11}/',$money,$dir);
		$zhi = $dir[0];
		if(strlen($zhi) != '11'){
			return array(
				'status' => '1',
			);
		}
		$data = $map = array(
			'type' => '0',
			'user_id' => $user_id,
			'daytime' => date('Ymd',time()),
		);
		$count = Db::name('zkl')->where($map)->count();
		if($count>='5'){
			return array(
				'status' => '2',
			);
		}
		$find = Db::name('zkl')
				->field('*')
				->where('zkl_msg',$zhi)
				->find();
		if($find['type'] == '2'){
			$status = '3';
			$_map['zkl_id'] = $find['id'];
			$goods = self::getzklProduct($_map);
		}
		if(!$find){
			$data['zkl_msg'] = $zhi;
			$data['add_time'] = date('Y-m-d H:i:s',time());
			$data['update_time'] = date('Y-m-d H:i:s',time());
			Db::name('zkl')->insert($data);
			Db::name('debugging')->insert(['title' => '检测吱口令是否重复提交', 'msg' => $zhi]);
		}
		return array(
			'status' => $status,
			'goods' => $goods,
			'find' => $find,
		);
	}
	/* 获取吱口令商品 */
	public static function getzklProduct($map){
		return Db::name('zkl_goods')->where($map)->select();
	}
	/* 获取商品信息 */
	public static function getDfProduct($map,$type=""){
		$_data = array();
		/* 获取用户支付宝下面所有需要代付的商品 */
		$time = time()-10800;
		$start_time = date('Y-m-d H:i:s',$time);
		$end_time = date('Y-m-d H:i:s',time());
		$list = Db::view('t_alipay_order_goods','*')
				->view('t_alipay_order',['peerpay_link','sys_alipay_id','trade_no','type','other_name','other_account','status','update_time'],'t_alipay_order.id=t_alipay_order_goods.alipay_order_id')
				->where($map)
				//->whereTime('update_time','between',[$start_time,$end_time])
				->select();
		if(!empty($list)){
			$price = "0";
			foreach($list as $key=>$ls){
				if($type){
					if($ls['status'] == '8'){
						$array = array(
							'status' => '2',
							'product' => $ls,
						);
						return $array;
					}
				}
				$_data['alipay'][$ls['alipay_order_id']] = array(
					'id' => $ls['id'],
					'alipay_order_id' => $ls['alipay_order_id'],
					'peerpay_link' => $ls['peerpay_link'],
					'other_name' => $ls['other_name'],
					'other_account' => $ls['other_account'],
					'status' => $ls['status'],
				);
				$_data['product'][$ls['alipay_order_id']][$ls['id']]['title'] = $ls['title'];
				$_data['product'][$ls['alipay_order_id']][$ls['id']]['price'] = $ls['price'];
				$price = $ls['price']+$price;
				$sys_alipay_id = $ls['sys_alipay_id'];
			}
			$_data['all_price'] = $price;
			$_data['sys_alipay_id'] = $sys_alipay_id;
			$_data['status'] = '1';
		}
		return $_data;
	}
	/**
     * 返回商品信息
     * @return array
     */
    public static function getProduct($id,$user_id,$type="")
    {
		$time = time()-10800;
		$start_time = date('Y-m-d H:i:s',$time);
		$end_time = date('Y-m-d H:i:s',time());
		$_map = array(
			'id' => array('in',$id),
			'status' => '0',
			'alipay_status' => '0',
		);
		if($type){
			$_map['status'] = array('in',['0','8']);
			$_map['alipay_status'] = '0';
		}
        $find = Db::name('alipay_order')
				->field('*')
				->where($_map)
				//->whereTime('update_time','between',[$start_time,$end_time])
				->find();
		if($find){
			$map['alipay_order_id'] = array('in',$id);
			$map['status'] = '0';
			return self::getDfProduct($map);
			/*$result = self::bondingAlipay($find,$user_id);
			if($result){
				foreach($result as $ls){
					$other_name[] = $ls['other_name'];
					$other_account[] = $ls['other_account'];
				}
				$map['other_name'] = array('in',$other_name);
				$map['other_account'] = array('in',$other_account);
				$map['alipay_order_id'] = array('in',$id);
				$map['status'] = '0';
				
				if($type){
					$map['status'] = array('in',['0','8']);
				}
				return self::getDfProduct($map);
			}*/
		}
		return array();
    }
	/**
	*生成订单号
	*type A阿里巴巴代付  B淘宝天猫代付 H吱口令 C支付宝储值 D微信红包 5E游戏视频储值 6F其他 G淘币
	*/
	public static function orderNo($type = ""){
		$data = array(
			'0' => 'A',
			'1' => 'B',
			'3' => 'C',
			'4' => 'D',
			'5' => 'E',
			'6' => 'F',
			'7' => 'G',
			'2' => 'H',
			'10' => 'I',
			'11' => 'J',
			'12' => 'Y',
			'17' => 'O',
			'order_live' => 'W',
			'order_new_df' => 'X',
			'order_game' => 'Y',
			'order_other' => 'Z',
			'df_order' => 'V',
		);
		if(strlen($type)){
			return $data[$type].Random::numeric(16);
		}else{
			return 'N'.Random::numeric(16);
		}
		
	}
	/**
	*返回当前用户汇率和平台汇率
	*user_id 会员id
	*type 0阿里巴巴代付  1淘宝天猫代付 2吱口令 3支付宝储值 4微信红包 5游戏视频储值 6其他 7淘币
	*/
	public static function getExchange($user_id,$type,$money,$status){
		$_map = array(
			'id' => $user_id,
			'status' => 'normal',
		);
		/* 获取用户信息 */
		$info = Db::name('user')
				->field('*')
				->where($_map)
				->find();
		$map = array(
			'type' => $status,
			'status' => 'normal',
		);
		/* 获取用户下一等级 */
		$level = Config::get('site.user_level');
		$next_level = empty($level[$info['level']+1])?$info['level']:$info['level']+1;
		/* 获取汇率主表 */
		$exchange = Db::name('exchange')
				->field('*')
				->where($map)
				->where('FIND_IN_SET(:ty, df_ids)', ['ty'=>$type])
				->find();

		$_map = array(
			'pid' => $exchange['id'],
			'level_id' => array('in',array('1',$next_level,$info['level'])),
			'start_num' => array('ELT',$money),
			'end_num' => array('EGT',$money),
			'status' => 'normal',
		);
		$next_money = $now_money = $end_money = '0';
		/* 获取汇率 */
		$rate = Db::name('rate')
				->field('*')
				->where($_map)
				->select();
		foreach($rate as $ls){
			if($ls['level_id'] == '1'){
				
				$end_money = ceil(bcmul($money,$ls['rate'],'1'));
			}
			if($ls['level_id'] == $next_level){
				$next_money = ceil(bcmul($money,$ls['rate'],'1'));
			}
			if($ls['level_id'] == $info['level']){
				$now_money = ceil(bcmul($money,$ls['rate'],'1'));
				$exchange = $ls['rate'];
			}
		}
		return $data = array(
			'exchange' => $exchange,
			'site_exchange' => Config::get('site.site_exchange'),
			'money' => $now_money,
			'next_rate' => $now_money - $next_money ,
			'save_money' => $now_money - $end_money,
		);
	}
	/* 获取购物金使用范围 */
	public static function goldBuy($money,$user_id=""){
		$map['arrival_money'] = array('ELT',$money);
		$map['status'] = 'normal';
		$info = Db::name('gold_use')
				->field('*')
				->order('id','desc')
				->where($map)
				->find();
		/* 获取用户信息 */
		$_map = array(
			'id' => $user_id,
			'status' => 'normal',
		);
		$user = Db::name('user')
				->field('*')
				->where($_map)
				->find();
		$gold = bcmul($money,$info['proportion'],'5')/100;
		$gold = intval($gold);
		if($user['buy_gold'] <= $gold){
			return $user['buy_gold'];
		}else{
			return $gold;
		}
	}
	/**
	*绑定用户银行账户
	*bank_id银行卡id
	*/
	public static function bondingBank($bank_id,$type,$usdt="0"){
		
		$bank = explode(',',$bank_id);
		$map['id'] = array('in',$bank);
		$map['status'] = 'normal';
		$map['is_state'] = '1';
		/* 获取用户银行账号 */
		$list = Db::name('user_bank')
				->field('*')
				->where($map)
				->select();
		/* 获取平台银行账号 */
		$sys = Db::name('sys_bank')
				->field('*')
				->where('status','normal')
				->where('is_usdt',$usdt)
				->select();
		$_sys = Db::name('sys_bank')
				->field('*')
				->where('account_num',Config::get('site.Initial_account'))
				->find();
		$default = array();
		foreach($sys as $ls){
			$sys_array[$ls['category_id']] = $ls;
			if($ls['is_relation']){
				$default = $ls; 
			}
		}
		$bank = '';
		foreach($list as $key=>$val){
			if($type == '0'){
				$data = array(
					'is_relation' => '1',
					'relation_id' => $_sys['id'],
				);
				Db::name('user_bank')->where('id', $val['id'])->update($data);
			}else{
				if(array_key_exists($val['category_id'],$sys_array)){
					$data = array(
						'is_relation' => '1',
						'relation_id' => $sys_array[$val['category_id']]['id'],
					);
					if(!$val['all_account']){
						$data['relation_id'] = $_sys['id'];
					}
					Db::name('user_bank')->where('id', $val['id'])->update($data);
				}else{
					if($usdt == '0'){
						$data = array(
							'is_relation' => '1',
							'relation_id' => $default['id'],
						);
						if(!$val['all_account']){
							$data['relation_id'] = $_sys['id'];
						}
						Db::name('user_bank')->where('id', $val['id'])->update($data);
					}
				}
			}
			$bank .= $val['account_six'].',';
			
		}
		return $bank;
	}
	/* 超商付款 */
	public static function cs_pay($data){
		// $this->cs_paypay($data);
		vendor('Ecpay.ECPay');
		$obj = new \ECPay();
		$configuration = Config::get('site');
		$cspay = $configuration['cs_pay'];
		$type = $configuration['order_type'];
		//服務參數
		$obj->ServiceURL  = 'https://payment.ecpay.com.tw/Cashier/AioCheckOut/V5';
		$obj->HashKey = $cspay['HashKey'];
		$obj->HashIV = $cspay['HashIV'];
		$obj->MerchantID  = '3255435';
		$obj->EncryptType = '1'; 
		//基本參數(請依系統規劃自行調整)
		/* $obj->Send['ReturnURL']         = U('Api/Index/Receivables/superNotify',array('t',rand()),'',true);     //付款完成通知回傳的網址 */
		$obj->Send['ReturnURL']         = 'https://www.paybei.com.tw/Api/Pay/superNotify';     //付款完成通知回傳的網址
		/* $obj->Send['PaymentInfoURL']    = U('Api/Index/Receivables/getPayMsg',array('t',rand()),'',true);		//獲取超商代碼 */
		$obj->Send['PaymentInfoURL']    = 'https://www.paybei.com.tw/Api/Pay/getPayMsg';		//獲取超商代碼
		$obj->Send['MerchantTradeNo']   = $data['order_no'];                           //訂單編號
		$obj->Send['MerchantTradeDate'] = date('Y/m/d H:i:s');                        //交易時間
		$obj->Send['TotalAmount']       = $data['actual_money']; //交易金額
		$obj->Send['TradeDesc']         = $type[$data['type']]; //交易描述
		$obj->Send['ChoosePayment']     = \ECPay_PaymentMethod::CVS;
		// print_r($obj->Send);exit;
		array_push($obj->Send['Items'], array('Name' => $type[$data['type']], 'Price' => $data['actual_money'],'Currency' => "台币", 'Quantity' => (int) "1", 'URL' => "dedwed"));
		$result = $obj->CheckOut();
		/* 上線更改返回值，線下默認返回true */
		return true;
	}
	/* 信用卡付款*/
	public static function card_pay($data,$id,$url){
		$_url = 'https://www.paybei.com.tw/'.$url.'?id='.$id;
		return 'https://service.payware.com.tw/wpss/authpay.aspx?MerchantId=990005162&TerminalId=914270001&Amount='.$data['actual_money'].'&OrderNo='.$data['order_no'].'&ReturnURL=https://www.paybei.com.tw/Api/Pay/cardNotify&GoBackURL='.$_url.'&PayType=01&Installment=01&Encoding=utf-8';
		
	}
	/* 虚拟币信用卡付款*/
	public static function usdt_card_pay($data,$id){
		return 'https://service.payware.com.tw/wpss/authpay.aspx?MerchantId=990005162&TerminalId=914270001&Amount='.$data['actual_money'].'&OrderNo='.$data['order_no'].'&ReturnURL=https://www.paybei.com.tw/Api/Pay/usdtNotify&PayType=01&Installment=01&Encoding=utf-8';
		
	}
	/* 虚拟币超商付款 */
	public static function cs_virtual_pay($data){
		// $this->cs_paypay($data);
		vendor('Ecpay.ECPay');
		$obj = new \ECPay();
		$configuration = Config::get('site');
		$cspay = $configuration['cs_pay'];
		$type = $configuration['order_type'];
		//服務參數
		$obj->ServiceURL  = 'https://payment.ecpay.com.tw/Cashier/AioCheckOut/V5';
		$obj->HashKey = $cspay['HashKey'];
		$obj->HashIV = $cspay['HashIV'];
		$obj->MerchantID  = '3203303';
		$obj->EncryptType = '1'; 
		//基本參數(請依系統規劃自行調整)
		/* $obj->Send['ReturnURL']         = U('Api/Index/Receivables/superNotify',array('t',rand()),'',true);     //付款完成通知回傳的網址 */
		$obj->Send['ReturnURL']         = 'https://www.paybei.com.tw/Api/Pays/superNotify';     //付款完成通知回傳的網址
		/* $obj->Send['PaymentInfoURL']    = U('Api/Index/Receivables/getPayMsg',array('t',rand()),'',true);		//獲取超商代碼 */
		$obj->Send['PaymentInfoURL']    = 'https://www.paybei.com.tw/Api/Pays/getPayMsg';		//獲取超商代碼
		$obj->Send['MerchantTradeNo']   = $data['order_no'];                           //訂單編號
		$obj->Send['MerchantTradeDate'] = date('Y/m/d H:i:s');                        //交易時間
		$obj->Send['TotalAmount']       = $data['actual_money']; //交易金額
		$obj->Send['TradeDesc']         = $type[$data['type']]; //交易描述
		$obj->Send['ChoosePayment']     = \ECPay_PaymentMethod::CVS;
		// print_r($obj->Send);exit;
		array_push($obj->Send['Items'], array('Name' => $type[$data['type']], 'Price' => $data['actual_money'],'Currency' => "台币", 'Quantity' => (int) "1", 'URL' => "dedwed"));
		$result = $obj->CheckOut();
		/* 上線更改返回值，線下默認返回true */
		return true;
	}
	/**
	* 存储发票信息
	* fp_type开票类型 1电子发票（统编） 2电子发票 3捐给慈善机构
	* number 统一编号
	* fp_title 发票抬头
	* fp_email 电子邮箱
	*/
	public static function invoice($fp_type,$number="",$fp_title="",$fp_email="",$actual_money,$user_id,$order_no,$pay_status){
		return 1;exit;
		$invoice = array(
			'number' => '',
			'fp_title' => '',
			'fp_email' => '',
		);
		if($fp_type == '2' || $fp_type == '1'){
			$invoice['fp_email'] = $fp_email;
		}
		if($fp_type == '1'){
			$invoice['number'] = $number;
			$invoice['fp_title'] = $fp_title;
		}
		$data = array(
			'type' => $fp_type,
			'order_no' => $order_no,
			'invoice_json' => json_encode($invoice),
			'money' => $actual_money,
			'invoice_type' => '0',
			'daytime' => date('Ymd',time()),
			'createtime' => time(),
			'updatetime' => time(),
		);
		if($pay_status == '3'){
			$data['invoice_type'] = '1';
		}
		$invoice_json = json_encode($invoice);
		Db::name('user')->where('id', $user_id)->update(['invoice_json' => $invoice_json]);
		return Db::name('invoice')->insertGetId($data);
	}
	/**
	* 更改代付商品状态
	*/
	public static function productStatus($map){
		$_map['id'] = array('in',$map['alipay_order_id']['1']);
		Db::name('alipay_order')
			->where($_map)
			->update(['order_id' => $map['order_id'],'status'=>'1']);
		$good_map['alipay_order_id'] = array('in',$map['alipay_order_id']['1']);
		Db::name('alipay_order_goods')
			->where($good_map)
			->update(['status'=>'1']);
	}
	/**
	* 更改吱口令状态
	*/
	public static function zklStatus($map,$zkl_id,$order_id){
		Db::name('zkl_goods')
			->where($map)
			->update(['status'=>'1']);
		Db::name('zkl')
			->where('id',$zkl_id)
			->update(['type'=>'3','status'=>'0','is_usable'=>'0','order_id'=>$order_id]);
	}
	/**
	* 集运计算
	*/
	public static function jiyun($ids,$user_id){
		$list = Db::name('express')->where('id','in',$ids)->select();
		$data = $price = $scale = array();
		$all_scale = $all_beyond = "0";
		$is_transport = $sale = $type = $is_sale = [];
		$haiyun = '0';
		foreach($list as $ls){
			$all_scale = (float)$ls['scale'] + $all_scale;
			$all_beyond = $ls['beyond'] + $all_beyond;
			$sale[$ls['is_sale']] = $ls['is_sale'];
			$is_transport[$ls['is_transport']] = $ls['is_transport'];
			if($ls['is_sale']){
				$sale[$ls['is_sale']] = $ls['is_sale'];
			}
			if($ls['is_type']){
				$type[$ls['is_type']] = $ls['is_type'];
			}
			if($ls['is_transport']){
				$haiyun = '1';
			}
		}
		if(count($is_transport) > '1'){
			return 1;
		}
		if(count($sale) > '1'){
			return 2;
		}
		/*外岛派送费*/
		if(count($type)){
			$outer_island = '300';
		}else{
			$outer_island = '0';
		}
		if(count($is_transport) == '1'){
			if($haiyun == '1'){
				if(reset($sale)){
					/*特货*/
					$unit_money = Config::get('site.haisale');
				}else{
					/*普货*/
					$unit_money = Config::get('site.haiordinary');
				}
			}else{
				if(reset($sale)){
					/*特货*/
					$unit_money = Config::get('site.sale');
				}else{
					/*普货*/
					$unit_money = Config::get('site.ordinary');
				}
			}
		}
		/*计算重量遇到小数进1*/
		$_all_scale = ceil($all_scale);
		/*不足10公斤加收配送费*/
		$peisongfei = Config::get('site.peisongfei');
		if($_all_scale >= '10'){
			/*超过10公斤免配送费*/
			$peisongfei = '0';
		}
		/*获取汇率*/
		//$Exchange = self::getExchange($user_id,'8',$all_money,'1');
		$data = array(
			'sale' => $sale,
			'list' => $list,
			'haiyun' => $haiyun,
			'outer_island' => $outer_island,
			'unit_price' => $unit_money,
			'peisongfei' => $peisongfei,
			'all_scale' => $_all_scale,
			'all_beyond' => $all_beyond,
			'all_money' => $unit_money * $_all_scale + $peisongfei + $all_beyond + $outer_island,
		);
		$data['goldBuy'] = self::goldBuy($data['all_money'],$user_id);
		return $data;
	}
	/*使用银行卡付款，关联信息，方便匹配流水*/
	public static function order_bank_matching($bank_id,$table_name,$order_id,$user_id,$bank_num){
		$map = [
			'user_id' => $user_id,
			'bank_id' => $bank_id,
		];
		$info = Db::name('bank_full_number')->where($map)->find();
		if($info){
			$number = $info['number'];
		}else{
			$number = trim($bank_num,',');
		}
		$data = [
			'user_id' => $user_id,
			'order_id' => $order_id,
			'number' => $number,
			'order_table' => $table_name,
			'bank_id' => $bank_id,
		];
		Db::name('order_bank_matching')->where('user_id',$user_id)->delete();
		Db::name('order_bank_matching')->insert($data);
	}
}
