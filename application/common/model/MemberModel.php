<?php

namespace app\common\model;

use think\Model;
use think\Db;
use think\Session;
use think\Config;

/**
 * 会员模型
 */
class MemberModel extends Model
{

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    // 追加属性
    protected $append = [
        'url',
    ];
	/**
	* 获取验证码
	*/
	public static function put_card_num($cookie,$time1){
		include_once(ROOT_PATH .'/includes/lib_transaction.php');
		include_once(ROOT_PATH .'/includes/simple_html_dom.php');
		$content = self::get_content("https://www.ris.gov.tw/apply-idCard/app/idcard/IDCardReissue/", $cookie);
		$html = new \simple_html_dom();
		$html->load($content);
		$captchaKey = $html->find('input[id=captchaKey_captcha-refresh]',0)->value;
		$html->clear();
		$img = self::get_content("https://www.ris.gov.tw/apply-idCard/captcha/image?CAPTCHA_KEY=".$captchaKey."&time=".time(), $cookie);
		file_put_contents('uploads/data/'.$time1.'.jpg',$img);
		$captchaImage = 'uploads/data/'.$time1.".jpg";
		$array = [
			'captchaKey' => $captchaKey,
			'captchaImage' => $captchaImage,
		];
		return $array;
	}
    /**
     * 会员身份一次提交
	 * data
     */
    public static function putCard($data,$cookie,$captchaKey,$time1)
    {
		//$map = array(
		//	'card' => $data['cardid'],
		//	'addtime' => date('Ymd',time()),
		//);
		//$card_map = array(
		//	'cardid' => $data['cardid'],
		//	'is_state' => array('neq','2'),
		//);
		//$card = Db::name('user_card')->where($card_map)->find();
		//if($card){
		//	$is_state = array(
		//		'status' => '0',
		//		'result_str' => '身份證已經提交，請等待系統認證',
		//	);
		//}else{
			$is_state = self::checkCard($data,$cookie,$captchaKey,$time1);
			unset($data['code']);
			$data['check_time'] = time();
			$data['createtime'] = time();
			$data['updatetime'] = time();
			if($is_state['status'] == '1'){
				$data['is_state'] = '1';
				$data['check_text'] = $is_state['result_str'];
			}else{
				$data['is_state'] = $is_state['status'];
				$data['check_text'] = $is_state['result_str'];
			}
			if($is_state['status'] != '2'){
				Db::name('user_card')->insert($data);
			}
		//}
		return $is_state;
    }
	/**
     * 验证身份证合法性
	 * return 1通过 2不通过 3维护中
     */
	public static function checkCard($data,$cookie,$captchaKey,$time1){
		$post = array ( 
			'applyType' => '94CARD',
			'captchaKey' => $captchaKey, 
			'idnum'=>$data['cardid'],
			'applyTWY'=>$data['year'],
			'applyMM'=>$data['month'],
			'applyDD'=>$data['day'],
			'applyReason'=>$data['type'],
			'captchaInput' => $data['code'], 
			'siteId' => '0'
			/* 'submit' => '送出' */
		);
		/* Db::name('debugging')->insert(['title' => '提交数据', 'msg' => json_encode($post)]); */
		$content2 = self::login_post("https://www.ris.gov.tw/apply-idCard/app/idcard/IDCardReissue/query", $cookie, $post);
		/* Db::name('debugging')->insert(['title' => '返回数据', 'msg' => json_encode($content2)]); */
		file_put_contents('uploads/data/captchaImage/'.$time1.'.html',$content2);
		$in = strpos($content2,$data['cardid']);
		$wwResult = substr($content2, $in - 48,6400);
		/*验证码错误*/
		if(strpos($content2,'captchaKey') > 0){
			if(strpos($content2,'驗證碼輸入錯誤') > 0){
				$is_state = array(
					'status' => '0',
					'result_str' => '驗證碼輸入錯誤',
				);
			}
			if(strpos($content2,'國民身分證統一編號格式錯誤') > 0){
				$is_state = array(
					'status' => '0',
					'result_str' => '國民身分證統一編號格式錯誤',
				);
			}
		}else{
			if(strpos($content2,'國民身分證資料與檔存資料相符') > 0){
				$is_state = array(
					'status' => '1',
					'result_str' => '國民身分證資料與檔存資料相符',
				);
			}else{
				/*Db::name('user')->where('id', $data['user_id'])->update(['is_card' => "0"]);*/
				if(strpos($content2,'驗證資料錯誤次數') > 0){
					$is_state = array(
						'status' => '0',
						'result_str' => '驗證資料錯誤',
					);
				}
				if(strpos($content2,'驗證資料錯誤次數已達２次') > 0){
					$is_state = array(
						'status' => '2',
						'result_str' => '驗證資料錯誤次數已達２次,今日無法查詢，請明日再查！！',
					);
				}
			}
		}
		unlink($cookie);
		return $is_state;
	}
	/*图灵识别*/
	public static function tl_upload($yzm_img){
		$url = 'http://www.tulingtech.xyz/tuling/predict';
		$img = file_get_contents($yzm_img);
		$img = base64_encode($img);
		$data = array(
			'username' => 'alumx0924',
			'password' => 'as123456',
			'b64' => $img,
			'ID' => '50801183',
		);
		$postUrl = $url;
		$curlPost = json_encode($data);
		// 初始化curl
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $postUrl);
		curl_setopt($curl, CURLOPT_HEADER, 0);
		// 要求结果为字符串且输出到屏幕上
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		// post提交方式
		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $curlPost);
		// 运行curl
		$data = curl_exec($curl);
		curl_close($curl);
		return $data;
	}
	/*
 * 上传识别函数
 *
 * $yzm_img:[必填]图片相对路径，如'yzmimg/1.jpg'
 * $yzm_mark:[必填]识别类型（https://www.jsdati.com）
 * $yzm_minlen:[非必填]识别最小长度
 * $yzm_maxlen:[非必填]识别最大长度
 */
	public static function lz_upload($yzm_img, $yzm_mark = 0, $yzm_minlen = null, $yzm_maxlen = null) {
		set_time_limit(0);
		if (class_exists('CURLFile')) {
			$data_arr['upload'] = new \CURLFile(realpath($yzm_img));
		} else {
			$data_arr['upload'] = '@'.realpath($yzm_img);
		}
		$data_arr['yzm_minlen'] = $yzm_minlen;
		$data_arr['yzm_maxlen'] = $yzm_maxlen;
		$data_arr['yzmtype_mark'] = $yzm_mark;
		return self::lz_post('upload', $data_arr);
	}
	/*
	 * curl模拟post提交函数
	 */
	public static function lz_post($type, $val = null) {
		$data['user_name'] = 'beifu456999';
		$data['user_pw'] = 'aS832pyyw#@';
		$data['zztool_token'] = 'bf6a2d440e64624975ae090f7aa43435';
		if (is_array($val)) {
			$data = $data + $val;
		}
		$http = curl_init("http://v1-http-api.jsdama.com/api.php?mod=php&act={$type}");
		curl_setopt($http, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($http, CURLOPT_POST, 1);
		curl_setopt($http, CURLOPT_POSTFIELDS, $data);
		$result = curl_exec($http);
		curl_close($http);
		return $result;
	}
	public static function get_content($url, $cookie = '') { 
		$ch = curl_init(); 
		curl_setopt($ch, CURLOPT_URL, $url); 
		curl_setopt($ch, CURLOPT_HEADER, 0); 
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); 
		curl_setopt($ch, CURLOPT_COOKIEJAR, $cookie); //读取cookie 
		curl_setopt($ch, CURLOPT_REFERER, "https://www.ris.gov.tw/apply-idCard/app/idcard/IDCardReissue/"); 
		$rs = curl_exec($ch); //执行cURL抓取页面内容 
		curl_close($ch); 
		return $rs; 
	}
	//模拟登录 
	public static function login_post($url, $cookie, $post) { 
	  $curl = curl_init();//初始化curl模块 
	  curl_setopt($curl, CURLOPT_URL, $url);//登录提交的地址 
	  curl_setopt($curl, CURLOPT_HEADER, 0);//是否显示头信息 
	  // curl_setopt($ch, CURLOPT_TIMEOUT,60);
	  // curl_setopt($ch, CURLOPT_PROXY, "127.0.0.1"); //代理服务器地址  
	  // curl_setopt($ch, CURLOPT_PROXYPORT, 1080); //代理服务器端口
	  curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);//是否自动显示返回的信息 
	  curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);//绕过ssl验证
	  curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
	  curl_setopt($curl, CURLOPT_COOKIEFILE, $cookie); //设置Cookie信息保存在指定的文件中 
	  curl_setopt($curl, CURLOPT_POST, 1);//post方式提交 
	  curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($post));//要提交的信息 
	  $rs = curl_exec($curl);//执行cURL 
	  curl_close($curl);//关闭cURL资源，并且释放系统资源 
	  return $rs; 
	}
	 /**
     * 会员身份二次提交
	 * data
     */
	public static function putCardTwo($user_id,$img,$img2){
		$data = array(
			'user_id' => $user_id,
			'card_img' => $img,
			'card_img_img' => $img2,
			'is_state' => '3',
			'createtime' => time(),
			'updatetime' => time(),
		);
		return Db::name('user_card_two')->insert($data);
	}
	/**
	* 更改用户认证状态
	*/
	public static function userStatus($user_id,$filed){
		Db::name('user')
			->where('id', $user_id)
			->update([$filed => "1"]);
	}
	/**
	* 获取会员银行账户
	*/
	public static function getUserBank($user_id){
		$map = array(
			'user_id' => $user_id,
			'user_bank.is_state' => '1',
			'user_bank.status' => 'normal',
		);
		return Db::view('user_bank','*')
				->view('category',['name'],'category.id = user_bank.category_id')
				->where($map)
				->select();
	}
	/**
	* 获取会员企业银行账户
	*/
	public static function getUserQiyebank($user_id){
		$map = array(
			'user_id' => $user_id,
			'status' => 'normal',
		);
		return Db::name('qiye_bank')
				->where($map)
				->select();
	}
	/**
	* 获取会员指定企业银行账户
	*/
	public static function getinfoUserQiyebank($id){
		$map = array(
			'id' => $id,
			'status' => 'normal',
		);
		return Db::name('qiye_bank')
				->where($map)
				->find();
	}
	/**
	* 获取会员收货地址
	*/
	public static function getaddress($user_id){
		$map = array(
			'user_id' => $user_id,
			'status' => 'normal',
		);
		return Db::name('address')
				->where($map)
				->select();
		/* 获取地址分类*/
		/* $_map = array(
			'type' => 'address',
			'status' => 'normal',
		);
		$category = Db::name('category')->field('id,pid,name')->where($_map)->select();
		foreach($category as $val){
			$category[$val['id']] = $val;
		}
		foreach($address as $key=>$ls){
			$address[$key]['type'] = $category[$ls['type']]['name'];
			$address[$key]['city'] = $category[$ls['city']]['name'];
		}
		return $address;*/
	}
	/*获取会员指定地址*/
	public static function getinfoaddress($id){
		$map = array(
			'id' => $id,
			'status' => 'normal',
		);
		$info = Db::name('address')->where($map)->find();
		$_map = array(
			'type' => 'address',
			'status' => 'normal',
		);
		$_address = Db::name('category')->field('id,pid,name')->where($_map)->select();
		$address_list = array();
		foreach($_address as $ls){
			$address_list[$ls['id']] = $ls;
		}
		$info['type'] = $address_list[$info['type']]['name'];
		$info['city'] = $address_list[$info['city']]['name'];
		return $info;
	}
	/**
	* 获取会员微信账户
	*/
	public static function getUserWx($user_id){
		$map = array(
			'user_id' => $user_id,
			'is_prove' => array('in',array('1','2')),
			'status' => 'normal',
		);
		// print_r($map);exit;
		return Db::name('user_wx')
				->field('*')
				->where($map)
				->select();
	}
	/**
	* 获取会员微信人工账户
	*/
	public static function getUserRg($user_id){
		$map = array(
			'user_id' => $user_id,
			'status' => 'normal',
		);
		// print_r($map);exit;
		return Db::name('user_artificial')
				->field('*')
				->where($map)
				->select();
	}
	/**
	* 获取会员支付宝账户
	*/
	public static function getUserAli($user_id){
		$map = array(
			'user_id' => $user_id,
			'is_prove' => array('in',array('1','2')),
			'status' => 'normal',
		);
		return Db::name('user_alipay')
				->field('*')
				->where($map)
				->select();
	}
	/**
	* 获取会员支付宝账户
	*/
	public static function getUserRgong($user_id){
		$map = array(
			'user_id' => $user_id,
			'status' => 'normal',
		);
		return Db::name('user_artificial')
				->field('*')
				->where($map)
				->select();
	}
	/**
	* 获取单一储值账户
	*/
	public static function getUserOne($table,$id){
		$map = array(
			'id' => $id,
			'status' => 'normal',
		);
		return Db::name($table)
				->field('*')
				->where($map)
				->find();
	}
	/**
	* 获取平台银行分类
	*/
	public static function getBankCategory(){
		$map = array(
			'type' => 'bankcatygory',
			'status' => 'normal',
		);
		return Db::name('category')
				->field('id,pid,name,image')
				->where($map)
				->select();
	}
	/**
	* 获取订单列表
	*/
	public static function getOrderList($map,$num,$table='df_order'){
		$map['status'] = 'normal';
		return Db::name($table)
			->field('*')
			->order('createtime','desc')
			->where($map)
			->paginate($num);
	}
	/**
	* 获取订单详情
	*/
	public static function getOrder($id,$table='df_order'){
		if(!$table){
			$table='df_order';
		}
		$map = array(
			'id' => $id,
			'status' => 'normal',
		);
		/* 获取订单消息 */
		$find = Db::name($table)
				->field('*')
				->where($map)
				->find();
		/* 银行卡支付获取用户银行卡 */
		$list = array();
		$sys_bank = array();
		$bank = array();
		if($find['pay_status'] == '1' || $find['pay_status'] == '3'){
			$bank = explode(',',$find['bank_id']);
			$map = array(
				'user_bank.id' => array('in',$bank),
				'user_bank.status' => 'normal',
			);
			$list = Db::view('user_bank','*')
					->view('category',['name'],'category.id = user_bank.category_id')
					->where($map)
					->select();
			/* 获取平台收款账户 */
			if($list){
				$_map['id'] = $list[0]['relation_id'];
				$_map['status'] = 'normal';
				$sys_bank = Db::name('sys_bank')
					->field('*')
					->where($_map)
					->find();
				if(!$sys_bank){
					$sys_bank = Db::name('sys_bank')
						->field('*')
						->where('is_relation','1')
						->find();
				}
			}
		}
		/* 获取发票信息 */
		$invoice_map = array(
			'id' => $find['invoice_id'],
			'status' => 'normal',
		);
		$invoice = Db::name('invoice')
				->field('*')
				->where($invoice_map)
				->find();
		return $data = array(
			'order' => $find,
			'bank' => $list,
			'sys_bank' => $sys_bank,
			'invoice' => $invoice,
			'order_status' => Config::get('site.order_status'),
			'pay_type' => Config::get('site.pay_type'),
		);
	}
	/**
	* 获取订单详情
	*/
	public static function getliveOrder($id,$table){
		$map = array(
			'id' => $id,
			'status' => 'normal',
		);
		/* 获取订单消息 */
		$find = Db::name($table)
				->field('*')
				->where($map)
				->find();
		/* 银行卡支付获取用户银行卡 */
		$list = array();
		$sys_bank = array();
		$bank = array();
		if($find['pay_status'] == '1' || $find['pay_status'] == '3'){
			$bank = explode(',',$find['bank_id']);
			$map = array(
				'user_bank.id' => array('in',$bank),
				'user_bank.status' => 'normal',
			);
			$list = Db::view('user_bank','*')
					->view('category',['name'],'category.id = user_bank.category_id')
					->where($map)
					->select();
			/* 获取平台收款账户 */
			if($list){
				$_map['id'] = $list[0]['relation_id'];
				$_map['status'] = 'normal';
				$sys_bank = Db::name('sys_bank')
					->field('*')
					->where($_map)
					->find();
				if(!$sys_bank){
					$sys_bank = Db::name('sys_bank')
						->field('*')
						->where('is_relation','1')
						->find();
				}
			}
		}
		return $data = array(
			'order' => $find,
			'bank' => $list,
			'sys_bank' => $sys_bank,
			'order_status' => Config::get('site.order_status'),
			'pay_type' => Config::get('site.pay_type'),
		);
	}
	/* 获取虚拟订单详情 */
	public static function getVirtualOrder($id){
		$map = array(
			'id' => $id,
			'status' => 'normal',
		);
		/* 获取订单消息 */
		$find = Db::name('virtual_order')
				->field('*')
				->where($map)
				->find();
		/* 银行卡支付获取用户银行卡 */
		$list = array();
		$sys_bank = array();
		$bank = array();
		if($find['pay_status'] == '1' || $find['pay_status'] == '3'){
			$bank = explode(',',$find['bank_id']);
			$map = array(
				'user_bank.id' => array('in',$bank),
				'user_bank.status' => 'normal',
			);
			$list = Db::view('user_bank','*')
					->view('category',['name'],'category.id = user_bank.category_id')
					->where($map)
					->select();
			/* 获取平台收款账户 */
			if($list){
				$_map['id'] = $list[0]['relation_id'];
				$_map['status'] = 'normal';
				$sys_bank = Db::name('sys_bank')
					->field('*')
					->where($_map)
					->find();
			}
		}
		/* 获取发票信息 */
		$invoice_map = array(
			'id' => $find['invoice_id'],
			'status' => 'normal',
		);
		$invoice = Db::name('invoice')
				->field('*')
				->where($invoice_map)
				->find();
		return $data = array(
			'order' => $find,
			'bank' => $list,
			'sys_bank' => $sys_bank,
			'invoice' => $invoice,
			'usdt_type' => '1',
			'order_status' => Config::get('site.virtual_status'),
		);
	}
	/* 获取淘币明细 */
	public static function getTaobiLog($id,$type=""){
		$map = array(
			'user_id' => $id,
			'status' => 'normal',
		);
		if($type){
			$map['type'] = $type;
		}
		return Db::name('taobi_log')
				->field('*')
				->where($map)
				->order('createtime','desc')
				->paginate();
	}
	/* 获取购物金明细 */
	public static function getGoldLog($id){
		$map = array(
			'user_id' => $id,
			'status' => 'normal',
		);
		return Db::name('gold_log')
				->field('*')
				->where($map)
				->order('createtime','desc')
				->paginate();
	}
	/* 获取用户下一等级 */
	public static function getNextLevel ($id){
		$map = array(
			'id' => $id,
			'status' => 'normal',
		);
		/* 获取用户信息 */
		$info = Db::name('user')
				->field('*')
				->where($map)
				->find();
		/* 获取用户下一等级 */
		$level = Config::get('site.user_level');
		$next_level = empty($level[$info['level']+1])?$info['level']:$info['level']+1;
		$bill = $level[$info['level']]/$level[$next_level]*100;
		return $bill?$bill:'1';
	}
	/*获取用户分组*/
	public static function getUserGroup($group_id){
		$group = Db::name('group_pay')->where('id',$group_id)->find();
		if($group){
			/*获取支付宝*/
			$alipay_open_array = Config::get('site.alipay_open_array');
			return $group['collection'];
		}
	}
	/*配置数组*/
	public static function getsys_array(){
		return array(
			'8' => array(
				'name' => '系統增加',
				'image' => '__CDN__/assets/img/wap/pay8.png',
			),
			'9' => array(
				'name' => '系統減少',
				'image' => '__CDN__/assets/img/wap/pay9.png',
			),
			'10' => array(
				'name' => '推廣任務獎勵',
				'image' => '__CDN__/assets/img/wap/pay10.png',
			),
			'11' => array(
				'name' => '取消訂單退還',
				'image' => '__CDN__/assets/img/wap/pay11.png',
			),
			'12' => array(
				'name' => '用戶取消退款',
				'image' => '__CDN__/assets/img/wap/pay12.png',
			),
			'13' => array(
				'name' => '返利提現',
				'image' => '__CDN__/assets/img/wap/pay13.png',
			),
			'14' => array(
				'name' => '发票返利',
				'image' => '__CDN__/assets/img/wap/pay14.png',
			),
		);
	}
	public static function getShowTip($type,$user_id){
		$map =array(
			'user_id' => $user_id,
			'type' => $type,
			'status' => 'normal',
		);
		return Db::name('show_tip')->field('tip_type')->where($map)->find();
	}
	public static function superQuotien($user_id){
		return Db::name('super_quotient')
				->where('user_id', $user_id)
				->order('id','desc')
				->find();
	}
}
