<?php

namespace app\common\model;

use think\Model;
use think\Session;
use fast\Random;
use think\Db;

/**
 * 文章模型
 */
class NewsModel extends Model
{

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    // 追加属性
    protected $append = [
        'url',
    ];

    /**
     * 获取通知
     * @return array
     */
    public static function getTongzhi($num)
    {
        return self::getNews($num,'0');
    }
	 /**
     * 获取公告
     * @return array
     */
    public static function getGonggao($num)
    {
        return self::getNews($num,'1');
    }
	 /**
     * 获取文章
     * @return array
     */
    public static function getNews($num,$type)
    {
		$map = array(
			'type' => $type,
			'status' => 'normal',
		);
        $list = Db::name('notice')
				->field('*')
				->order('hot', 'asc')
				->order('weigh', 'desc')
				->where($map)
				->paginate($num,false,['query'=>array('type' => $type)]);
		return $list;
    }
	/**
	* 获取文章详情
	*/
	public static function noticeDetail($id){
		 $find = Db::name('notice')
				->field('*')
				->where('id', $id)
				->find();
		return $find;
	}
	/**
	* 获取申诉
	*/
	public static function appealList($type,$user_id,$num){
		$map = array(
			'user_id' => $user_id,
			'status' => 'normal',
		);
		if($type){
			$map['type'] = $type;
		}
		return Db::name('appeal')
				->field('*')
				->where($map)
				->paginate($num);
	}
	/**
	* 获取申诉回复
	*/
	public static function replyList($ids){
		$map = array(
			'appeal_id' => array('in',$ids),
			'status' => 'normal',
		);
		return Db::name('appeal_reply')
					->field('*')
					->where($map)
					->select();
	}
}
