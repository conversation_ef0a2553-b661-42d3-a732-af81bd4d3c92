<?php

namespace app\common\model;

use think\Model;
use think\Db;
use think\Validate;
use think\Config;
/**
 * 会员模型
 */
class User extends Model
{

    // 开启自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    // 追加属性
    protected $append = [
        'url',
    ];

    /**
     * 获取个人URL
     * @param   string $value
     * @param   array  $data
     * @return string
     */
    public function getUrlAttr($value, $data)
    {
        return "/u/" . $data['id'];
    }

    /**
     * 获取头像
     * @param   string $value
     * @param   array  $data
     * @return string
     */
    public function getAvatarAttr($value, $data)
    {
        if (!$value) {
            //如果不需要启用首字母头像，请使用
            //$value = '/assets/img/avatar.png';
            $value = letter_avatar($data['nickname']);
        }
        return $value;
    }

    /**
     * 获取会员的组别
     */
    public function getGroupAttr($value, $data)
    {
        return UserGroup::get($data['group_id']);
    }

    /**
     * 获取验证字段数组值
     * @param   string $value
     * @param   array  $data
     * @return  object
     */
    public function getVerificationAttr($value, $data)
    {
        $value = array_filter((array)json_decode($value, true));
        $value = array_merge(['email' => 0, 'mobile' => 0], $value);
        return (object)$value;
    }

    /**
     * 设置验证字段
     * @param mixed $value
     * @return string
     */
    public function setVerificationAttr($value)
    {
        $value = is_object($value) || is_array($value) ? json_encode($value) : $value;
        return $value;
    }

    /**
     * 变更会员余额
     * @param int    $money   余额
     * @param int    $user_id 会员ID
     * @param string $memo    备注
     */
    public static function money($money, $user_id, $memo)
    {
        $user = self::get($user_id);
        if ($user && $money != 0) {
            $before = $user->money;
            $after = $user->money + $money;
            //更新会员信息
            $user->save(['money' => $after]);
            //写入日志
            MoneyLog::create(['user_id' => $user_id, 'money' => $money, 'before' => $before, 'after' => $after, 'memo' => $memo]);
        }
    }

    /**
     * 变更会员积分
     * @param int    $score   积分
     * @param int    $user_id 会员ID
     * @param string $memo    备注
     */
    public static function score($score, $user_id, $memo)
    {
        $user = self::get($user_id);
        if ($user && $score != 0) {
            $before = $user->score;
            $after = $user->score + $score;
            $level = self::nextlevel($after);
            //更新会员信息
            $user->save(['score' => $after, 'level' => $level]);
            //写入日志
            ScoreLog::create(['user_id' => $user_id, 'score' => $score, 'before' => $before, 'after' => $after, 'memo' => $memo]);
        }
    }

    /**
     * 根据积分获取等级
     * @param int $score 积分
     * @return int
     */
    public static function nextlevel($score = 0)
    {
        $lv = Config::get('site.user_level');
		$level = 1;
        foreach ($lv as $key => $value) {
            if ($score >= $value) {
                $level = $key;
            }
        }
        return $level;
    }
	/* 获取推荐人信息-code */
	public static function recommender($invitationcCode){
		return Db::name('user')
				->field('*')
				->where('invitation_code', trim($invitationcCode))
				->find();
	}
	/* 获取推荐人信息-id */
	public static function recommender_ids($ids){
		return Db::name('user')
				->field('*')
				->where('id', $ids)
				->find();
	}
	/**
	* $info 推荐人信息 数组
	* $id 赠送购物金规则id
	* $user_id 当前用户id
	*/
	public static function give_gold($info,$id,$user_id,$reg=false){
		$gold_rules = Db::name('gold_rules')
					->field('*')
					->where('id',$id)
					->find();
		$price = $info['buy_gold'] + $gold_rules['money'];
		$surplus_price = $info['buy_gold'];
		$data = array(
			'type' => '10',
			'order_id' => $user_id,
			'user_id' => $info['id'],
			'price' => $gold_rules['money'],
			'surplus_price' => $info['buy_gold'],
			'msg' => $gold_rules['title'],
			'createtime' => time(),
			'updatetime' => time(),
		);
		Db::name('gold_log')->insert($data);
        $_data = array(
            'buy_gold' => $price,
        );
		if($reg==true){
            $_data['spread_num'] = $info['spread_num']+1;
            $_data['spread_profit'] = $info['spread_profit']+$gold_rules['money'];
        }
		Db::name('user')->where('id', $info['id'])->update($_data);
	}
}
