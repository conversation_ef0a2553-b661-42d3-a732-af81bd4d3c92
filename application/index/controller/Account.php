<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\MemberModel;
use app\index\controller\Line;
use think\Db;
use think\Config;
use think\Cookie;
use think\Hook;
use think\Session;
use think\Validate;

/**
 * 账号中心
 */
class Account extends Frontend
{
    protected $layout = 'user';
    protected $noNeedLogin = ['login', 'register', 'third'];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
        $auth = $this->auth;

        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'));
        }
        //监听注册登录注销的事件
        Hook::add('user_login_successed', function ($user) use ($auth) {
            $expire = input('post.keeplogin') ? 30 * 86400 : 0;
            Cookie::set('uid', $user->id, $expire);
            Cookie::set('token', $auth->getToken(), $expire);
        });
        Hook::add('user_register_successed', function ($user) use ($auth) {
            Cookie::set('uid', $user->id);
            Cookie::set('token', $auth->getToken());
        });
        Hook::add('user_delete_successed', function ($user) use ($auth) {
            Cookie::delete('uid');
            Cookie::delete('token');
        });
        Hook::add('user_logout_successed', function ($user) use ($auth) {
            Cookie::delete('uid');
            Cookie::delete('token');
        });
    }

    /**
     * 空的请求
     * @param $name
     * @return mixed
     */
    public function _empty($name)
    {
        $data = Hook::listen("user_request_empty", $name);
        foreach ($data as $index => $datum) {
            $this->view->assign($datum);
        }
        return $this->view->fetch('user/' . $name);
    }

    /**
     * 账号中心
     */
    public function index()
    {
		$userbank = MemberModel::getUserBank($this->auth->id);
		$userwx = MemberModel::getUserWx($this->auth->id);
		$userali = MemberModel::getUserAli($this->auth->id);
		$userrg = MemberModel::getUserRgong($this->auth->id);
		$userqiyebank = MemberModel::getUserQiyebank($this->auth->id);
		$line_robot = $this->line_robot();
        $this->view->assign('userbank',$userbank);
        $this->view->assign('userwx',$userwx);
        $this->view->assign('userali',$userali);
        $this->view->assign('userrg',$userrg);
        $this->view->assign('userqiyebank',$userqiyebank);
        $this->view->assign('line_robot',$line_robot);
        $this->view->assign('title', __('Account'));
        return $this->view->fetch('/account/index');
    }
	
	// 地址管理
	public function address()
	{	
		$userbank = MemberModel::getUserBank($this->auth->id);
		$userwx = MemberModel::getUserWx($this->auth->id);
		$userali = MemberModel::getUserAli($this->auth->id);
		$userrg = MemberModel::getUserRgong($this->auth->id);
		/*获取用户地址*/
		$map = array(
			'type' => 'address',
			'status' => 'normal',
		);
		$_address = Db::name('category')->field('id,pid,name')->where($map)->select();
		$address_list = array();
		foreach($_address as $ls){
			$address_list[$ls['id']] = $ls;
		}
		$address = Db::name('address')->where('user_id',$this->auth->id)->select();
		$this->view->assign('address', $address);
		$this->view->assign('_address', $address_list);
		$this->view->assign('userbank',$userbank);
		$this->view->assign('userwx',$userwx);
		$this->view->assign('userali',$userali);
		$this->view->assign('userrg',$userrg);
		$this->view->assign('title', __('Account'));
	    return $this->view->fetch('/account/address');
	}
	
	// 地址编辑
	public function add_address(){	
		if ($this->request->isPost()) {
			$id = $this->request->Post('id');
			$address_name = $this->request->Post('name');
			$address_mobile = $this->request->Post('mobile');
			$type = $this->request->Post('province');
			$city = $this->request->Post('city');
			$address = $this->request->Post('detail');
			$data = array(
				'type' => $type,
				'city' => $city,
				'address' => $address,
				'address_name' => $address_name,
				'address_mobile' => $address_mobile,
				'user_id' => $this->auth->id,
				'createtime' => time(),
				'updatetime' => time(),
			);
			$count = Db::name('address')->where('user_id',$this->auth->id)->count();
			if($count >= '5'){
				$this->error('地址最多只能添加5條');
			}else{
				if($id){
					$result = Db::name('address')->where('id',$id)->update($data);
				}else{
					$result = Db::name('address')->insert($data);
				}
				if($result){
					$this->success('操作成功');
				}else{
					$this->error('添加失敗');
				}
			}
		}else{
			$id = $this->request->Get('id');
			$address = "";
			if($id){
				$address = Db::name('address')->where('id',$id)->find();
			}
			/* 获取地址分类*/
			$map = array(
				'type' => 'address',
				'status' => 'normal',
			);
			$_address = Db::name('category')->field('id,pid,name')->where($map)->select();
			$this->view->assign('address', json_encode($this->list_to_tree($_address)));
			$this->view->assign('info',$address);
			$this->view->engine->layout('layout/empty');
			return $this->view->fetch('account/add_address');
		}
		
	}
	
	// pc地址弹窗编辑
	public function win_add_address()
	{	
		/*获取用户地址*/
		$map = array(
			'type' => 'address',
			'status' => 'normal',
		);
		$_address = Db::name('category')->field('id,pid,name')->where($map)->select();
		$this->view->assign('address', json_encode($this->list_to_tree($_address)));
		$this->view->engine->layout('layout/empty');
		return $this->view->fetch('account/m_add_address');
	}
	/*
	*地址删除
	*/
	public function address_del(){
		$id = $this->request->Get('id');
		if(Db::name('address')->where('id',$id)->delete()){
			$this->success('操作成功');
		}else{
			$this->error('添加失敗');
		}
	}
    public function bank()
    {
		$userbank = MemberModel::getUserBank($this->auth->id);
		$this->view->assign('userbank',$userbank);
        $this->view->assign('title', __('Account'));
        return $this->view->fetch('/account/bank');
    }
    public function wechat()
    {
		$userwx = MemberModel::getUserWx($this->auth->id);
		$this->view->assign('userwx',$userwx);
        $this->view->assign('title', __('Account'));
        return $this->view->fetch('/account/wechat');
    }
	public function rengong()
    {
		$userrg = MemberModel::getUserRg($this->auth->id);
		$this->view->assign('userrg',$userrg);
        $this->view->assign('title', __('Account'));
        return $this->view->fetch('/account/rengong');
    }
    public function alipay()
    {
		$userali = MemberModel::getUserAli($this->auth->id);
		$this->view->assign('userali',$userali);
        $this->view->assign('title', __('Account'));
        return $this->view->fetch('/account/alipay');
    }
	/* 银行卡添加 */
	public function addBank(){
		if ($this->request->isPost()) {
            $user_id = $this->auth->id;
			 $type = $this->request->Post('types');
            /**验证验证码是否正确*/
           // $code = $this->request->Post('code');
            //$return = $this->ruleCode($this->auth->mobile,$code,4);
            //if($return == false)
            //{
            //    $this->error(__($this->tips(0)));
            //}else{
				$img = $this->request->Post('img');
				$img2 = $this->request->Post('img2');
				$return = $this->getImg($img);
				$return2 = $this->getImg($img2);
				if($return['code'] == '1'){
					$this->error(__($return['msg']));
				}
				if($return2['code'] == '1'){
					$this->error(__($return['msg']));
				}
				$map = array(
					'user_id' => $user_id,
					'is_state' => '1',
					'status' => 'normal',
				);
                $bank_count = Db::name('user_bank')->where($map)->count();
                if( $bank_count < 3 )
                {
                    $bank_id = $this->request->Post('bank');
                    $bank_name = $this->request->Post('name');
                    $bank_account = $this->request->Post('lastsix');
					$_map = array(
						'user_id' => $this->auth->id,
						'account_six' => $bank_account,
					);
					$_info = Db::name('user_bank')->where($_map)->order('id', 'desc')->find();
					if(!empty($_info)){
						//if($_info['is_state'] == '3'){
						//	$this->error('稽核中，請勿重複提交');
						//}
						if($_info['is_state'] == '1'){
							$this->error('銀行卡已經存在');
						}
					}
                    $rule = [
                        'category_id'  => 'require',
                        'account_name'     => 'require|length:1,20',
                        'account_six'     => 'number|length:6',
                    ];
                    $msg = [
                        'category_id.require' => 'Bank required',
                        'account_name.require' => 'Bank card name required',
                        'account_name.length'  => 'Account name cannot exceed 20 characters',
                        'account_six.length'  => 'The last six digits of the bank card must be six',
                    ];
                    $data = [
                        'category_id'  => $bank_id,
                        'account_name'     => $bank_name,
                        'account_six'    => $bank_account,
                        'user_id' => $user_id,
                        'card_img' => $return['data'],
                        'card_img_img' => $return2['data'],
                        'is_lock' => '1',
                        'is_state' => '1',
                        'createtime' => time(),
                        'updatetime' => time(),
                    ];
                    $validate = new Validate($rule, $msg);
                    $result = $validate->check($data);
                    if (!$result) {
                        $this->error(__($validate->getError()));
                    }else{
                        Db::startTrans();
                        $res = Db::name('user_bank')->insertGetId($data);
                        if($res)
                        {
                            Db::commit();
							if($type){
								$this->success(__('Submit successfully'), url('daifu/select_bank'));
							}else{
								$this->success(__('Submit successfully'), url('account/index'));
							}
							$_content = $this->auth->username .'('. $this->auth->mobile .')'.'银行卡提交需要审核，请至后台进行审核';
							$dinghorn = new \addons\dinghorn\Dinghorn();
							$res      = $dinghorn->msgNotice('notice', ['content' => $_content], [
								'dynamic_variable' => '111'
							]);
                        }else{
                            Db::rollback();
                            $this->error($this->auth->getError());
                        }
                    }
                }else{
                    $this->error(__("Bank card is online"));
                }
            //}
		}
		$map = array(
			'user_id' => $this->auth->id,
			'is_state' => '1',
			'status' => 'normal',
		);
		$bank_count = Db::name('user_bank')->where($map)->count();
		if( $bank_count >= 3 ){
			$this->view->assign('num','1');
		}
		/* 获取银行分类 */
		$bankcategory = MemberModel::getBankCategory();
		$bank = $this->list_to_tree($bankcategory);
		$type = $this->request->param("type");
		// print_r($bank);exit;
		$this->view->assign('bank',$bank);
		$this->view->assign('type',$type);
		$this->view->assign('bank_count',$bank_count);
		$this->view->assign('is_card',$this->auth->is_card_img);
		if(is_mobile()){
			return $this->view->fetch();
		}else{
			$this->view->engine->layout('layout/empty');
			return $this->view->fetch('account/add_bank');
		}
	}
	
	/* 微信添加 */
	public function addWx(){
		$count = Db::name('user_wx')->where('user_id', $this->auth->id)->count();
		$_count = Db::name('user_wx')->where('user_id', $this->auth->id)->whereTime('createtime','>',date('Y-m-d',time()))->count();
		if($_count >= '3'){
			$this->error(__('Only three accounts can be added every day'));
		}
		$this->view->assign('count',$count);
		$this->view->engine->layout('layout/empty');
		return $this->view->fetch('account/add_wx');
	}
	public function getWxCount(){
		$count = Db::name('user_wx')->where('user_id', $this->auth->id)->count();
		if($count > $this->request->param("count")){
			$this->success(__("Submit successfully"));
		}else{
			$this->error(__("Failure to submit"));
		}
	}

	/* 支付宝添加 */
	public function add_alipay(){
		if ($this->request->isPost()) {
			/* 添加逻辑 */
            $account = $this->request->Post('account');
            $name = $this->request->Post('name');
			$user_id = $this->auth->id;
			/*验证支付宝是否被拉黑*/
			$info = Db::name('blacklist_alipay')->where('ali_account',$account)->find();
			if($info){
				Db::name('user')->where('id',$info['user_id'])->update(['is_lock'=>'1','remarks'=>'支付宝被禁，自动锁定用户']);
				$this->error(__("Alipay account is abnormal, please contact customer service"), url('index/login/logout'));
			}
			$map = array(
				'user_id' => $user_id,
				'add_time' => date('Ymd',time()),
			);
            $count = Db::name('user_alipay')->where($map)->count();
			if($count > 2){
				$this->error(__("Only three accounts can be added every day"), url('account/index'));
			}
			Db::startTrans();
			$ali_account = Db::name('user_alipay')->where(['ali_account'=>$account,'name'=>$name])->find();
			$transfer = '0';
			if($ali_account){
				/* 不是本人添加，直接增加 */
				if($ali_account['user_id'] != $user_id && ($ali_account['is_prove'] == '1' || $ali_account['is_prove'] == '2')){
					$transfer = '1';
				}else{
					$this->error(__("This account has been added, please do not add again"));
				}
			}else{
				/*支付宝实名认证*/
				/**验证支付宝账号是否正确*/
				$result = $this->alipayWithDraw('', $user_id, '0.1', $account, $name, 1);
				if($result['status'] == '1'){
					$transfer = '1';
				}else{
					$transfer = '3';
					$this->error($result['msg']);
				}
			}
			$data = array(
				'user_id' => $user_id,
				'ali_account' => $account,
				'name' => $name,
				'add_time' => date('Ymd',time()),
				'is_prove' => $transfer,
				'createtime' => time(),
				'updatetime' => time(),
			);
			if(Db::name('user_alipay')->insert($data)){
				Db::commit();
				$this->success(__("Submit successfully"));
			}else{
				Db::rollback();
				$this->error(__("Failure to submit"));
			}
		}
		$this->view->engine->layout('layout/empty');
		return $this->view->fetch('account/add_alipay');
	}
	/* 添加人工微信 */
	public function add_rengong(){
		if ($this->request->isPost()) {
			$account = $this->request->Post('account');
            $name = $this->request->Post('name');
			$ali_account = Db::name('user_artificial')->where(['name'=>$name])->find();
			if($ali_account['user_id'] == $this->auth->id){
				$this->error(__("Please do not add again"));
			}
			$data = array(
				'ali_account' => $account,
				'name' => $name,
				'user_id' => $this->auth->id,
				'createtime' => time(),
				'updatetime' => time(),
			);
			if(Db::name('user_artificial')->insert($data)){
				$this->success(__("Submit successfully"));
			}else{
				$this->error(__("Failure to submit"));
			}
		}
		$this->view->engine->layout('layout/empty');
		return $this->view->fetch('account/add_rengong');
	}
	
	/* 企业账号*/
	public function company(){
		$userqiyebank = MemberModel::getUserQiyebank($this->auth->id);
		$this->view->assign('userqiyebank',$userqiyebank);
		return $this->view->fetch('account/company');
	}
	/* 添加企业账号*/
	public function add_company(){
		if ($this->request->isPost()) {
			$head_bank = $this->request->Post('head_bank');
			$bank_name = $this->request->Post('bank_name');
			$bank_username = $this->request->Post('bank_username');
			$bank_account = $this->request->Post('bank_account');
			$bank_count = Db::name('qiye_bank')->where('user_id',$this->auth->id)->count();
			if( $bank_count >= 5 ){
				$this->error('銀行帳號最多只能添加5個');
			}
			$map = array(
				'bank_account' => $bank_account,
				'user_id' => $this->auth->id,
			);
			
			$ali_account = Db::name('qiye_bank')->where($map)->find();
			if($ali_account){
				$this->error('銀行帳號已被添加');
			}
			$data = array(
				'head_bank' => $head_bank,
				'bank_name' => $bank_name,
				'bank_username' => $bank_username,
				'bank_account' => $bank_account,
				'user_id' => $this->auth->id,
				'createtime' => time(),
				'updatetime' => time(),
			);
			if(Db::name('qiye_bank')->insert($data)){
				$this->success(__("Submit successfully"));
			}else{
				$this->error(__("Failure to submit"));
			}
		}
		
		$this->view->engine->layout('layout/empty');
		return $this->view->fetch('account/add_company');
	}
	
    // 添加银行卡
    public function share()
    {
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch('user/share/index');
    }
	/* 删除支付宝 */
	public function dele_alipay(){
		if($this->request->isPost()){
			$id = $this->request->post('id');
			if(Db::name('user_alipay')->where('id', $id)->delete()){
				$this->success(__("Submit successfully"));
			}else{
				$this->error(__("Failure to submit"));
			}
		}
		$this->error(__("Network connection failed, please refresh and try again"));
	}
	/* 删除微信 */
	public function dele_wx(){
		if($this->request->isPost()){
			$id = $this->request->post('id');
			if(Db::name('user_wx')->where('id', $id)->delete()){
				$this->success(__("Submit successfully"));
			}else{
				$this->error(__("Failure to submit"));
			}
		}
		$this->error(__("Network connection failed, please refresh and try again"));
	}
}
