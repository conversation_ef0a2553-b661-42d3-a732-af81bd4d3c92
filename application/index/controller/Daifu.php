<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\DaifuModel;
use app\common\model\MemberModel;
use think\Session;
use think\Db;
use think\Exception;
use think\Config;
use fast\Random;
use fast\Qrcpucom;



class Daifu extends Frontend
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];
    protected $layout = 'user';
    public function _initialize()
    {
        parent::_initialize();
        $auth = $this->auth;
        if(!$this->auth->id){
            $this->success(__('Account not logged in, please log in first'),url('/index/login/login'));
        }
        $this->view->assign('nav_url',$this->auth->getRequestUri());
        /*关键key获取*/
        $set_type = request()->param('set_type');
        $this->set_type = $set_type;
        /*获取菜单*/
        $this->set_nav($set_type);
        /*获取公用配置信息*/
        $this->common_configuration($set_type);
        /*关键key*/
        $this->view->assign('set_type',$set_type);
    }

    // 实体代购
    public function realgoods(){
        $this->untreate($this->set_type);
        /* 获取用户地址 */
        $address = MemberModel::getaddress($this->auth->id);
        $this->view->assign('address',$address);
        $this->view->assign('user_id',$this->auth->id);
        return $this->view->fetch('daifu/realgoods');
    }
    //银行采购
    public function bank(){
        $this->untreate($this->set_type);
        /* 获取用户企业账号 */
        $qiyebank = MemberModel::getUserQiyebank($this->auth->id);
        $this->view->assign('qiyebank',$qiyebank);
        $this->view->assign('user_id',$this->auth->id);
        return $this->view->fetch('daifu/bank');
    }
    // 直播点数
    public function new_df(){
        $this->untreate($this->set_type);
        return $this->view->fetch('daifu/new_df');
    }
    // 直播点数
    public function live(){
        $this->untreate($this->set_type);
        return $this->view->fetch('daifu/live');
    }
    /*wap 端 新通用提交-中转*/
    public function new_confirm_index()
    {
        $data = $this->request->post();
        if(empty($data['money'])){
            $data['money'] = $data['num'];
        }else{
            $data['money'] = $data['money'];
        }
        $exchange = DaifuModel::getExchange($this->auth->id,$data['key_id'],$data['money'],'1');
        $data['tmoney'] = $exchange['money'];
        $data['num'] = $data['money'];
        Session::set('new_field',$data);
        $this->success(__('Submitted successfully page skipping'),url('daifu/new_confirm_indexs'));
    }

    /**/
    public function new_confirm_indexs(){
        $data = Session::get('new_field');
        /*获取当前业务信息*/
        $special = Db::name('special')->where('key_id',$data['key_id'])->find();
        $this->view->assign('df_type',$data['key_id']);
        $this->view->assign('new_field',$data);
        $this->view->assign('special',$special);
        return $this->view->fetch('daifu/m_new_confirm_index');
    }
    /*新通用提交*/
    public function new_recharge(){
        $link = $this->request->post('link');
        $user_remarks = $this->request->post('name');
        $address = $this->request->post('address');
        $key_id = $this->request->post('key_id');
        $url = $this->request->post('url');
        $live_id = $this->request->post('id');
        $live_account = $this->request->post('account');
        $live_pwd = $this->request->post('password');
        $live_game = $this->request->post('game');
        $live_type = $this->request->post('num_type');
        $live_money = intval($this->request->post('num'));
        $tmoney = $this->request->post('tmoney');
        $money = $this->request->post('money');
        $tabobi = $this->request->post('tabobi');
        $pay_status = $this->request->post('pay_type');
        $bank_id = $this->request->post('bank_id');
        $type = $this->request->post('type');
        $company = $this->request->post('company');
        $rmb_money = $money = $this->request->post('money');
        $live_url = $link?$link:$url;

        /*获取当前业务信息*/
        $special = Db::name('special')->where('key_id',$key_id)->find();
        if(!$special){
            $this->error('業務類型錯誤，請聯系客服');
        }
        if($key_id == 16){
            $rmb_money = $money = $this->request->post('money');
            /*获取图片*/
            $file = $_POST['imgs'];
            $img = [];
            foreach($file as $ls){
                $return = $this->getImg($ls);
                if($return['code'] == '1'){
                    $this->error(__($return['msg']));
                }
                $img[] = $return['data'];
            }
            $img = json_encode($img);
            $_company = json_encode(MemberModel::getinfoUserQiyebank($company));
        }
        /*获取地址信息存json*/
        if($address){
            //$rmb_money = $money = $this->request->post('money');
            /* 获取用户地址 */
            $address = json_encode(MemberModel::getinfoaddress($address));
        }
        if($key_id == 31){
            $rmb_money = $money = $this->request->post('money');
        }
        /*获取台币和淘币*/
        if($type == '1'){
            /*通过类型和面值获取金额*/
            $set_two = Db::name('set_special_two')->where('key_id',$key_id)->find();
            $set_id = json_decode($set_two['set_id'],true);
            foreach($set_id as $ls){
                if($ls['title'] == $live_type){
                    foreach($ls['child'] as $val){
                        if($val['money'] == $live_money){
                            $money = $val['money'];
                        }
                    }
                }
            }
            if(!$money){
                $this->error('金額提交錯誤，請聯系客服1');
            }
            $rmb_money = $money;
        }
        if($type == '2'){
            if(preg_match("/^[1-9][0-9]*$/" ,$live_money)){
                $set_special_two = Db::name('set_special_two')->where('key_id',$key_id)->find();
                $set_special = json_decode($set_special_two['set_id'],true);
                foreach($set_special as $ls){
                    if($ls['title'] == $live_type){
                        $discount = $ls['discount'];
                    }
                }
                //$rmb_money = ceil($money = $live_money * $discount);
                $rmb_money = $money;
            }else{
                $this->error('自定義數值只能輸入整數');
            }

        }
        if($type == '3'){
            $rmb_money = $money = $live_money * $special['discount'];
        }
        /*判断用户淘币是否足够使用*/
        $service = '0';
        $pay_type = '0';
        $order_status = '0';
        if($tabobi){
            /* 获取用户淘币 */
            $userinfo = $this->auth->getUserinfo();
            if($userinfo['money'] >= $money){
                /* 用户填写淘币大于当前商品总价-自动转换至淘币支付 */
                $pay_status = '0';
                $pay_type = '2';
                $service = '0';
                $tabobi = $money;
                $order_status = '2';
            }
            $money = $money - $tabobi;
        }
        /*根据当前汇率计算台币*/
        $exchange = DaifuModel::getExchange($this->auth->id,$key_id,$rmb_money,'1');
        $actual_money = ceil(bcmul($money,$exchange['exchange'],'2'));
        $_actual_money = ceil(bcmul($money,$exchange['site_exchange'],'2'));
        /* 绑定银行 */
        $bank = "";
        if($bank_id){
            $bank = DaifuModel::bondingBank($bank_id,$this->auth->is_bank);
            $service = '0';
        }
        if($pay_status == '2'){
            $service = '30';
            if($actual_money < '70'){
                $this->error(__('The payment amount of the super merchant shall not be less than 70 (excluding the handling fee)'));
            }
            if($actual_money > '5970'){
                $this->error(__('The maximum amount of super business payment shall not exceed 6000 (including handling fee)'));
            }
        }
        if($pay_status == '4'){
            $service = ceil($actual_money*0.03);
        }
        /* 开启事务 */
        Db::startTrans();
        try {
            $_live_money = empty($live_money)?'0':$live_money;
            $data = array(
                'type' => $key_id,
                'order_no' => DaifuModel::orderNo($special['table_name']),
                'tb_money' => $exchange['money'],
                'rmb_money' => $rmb_money,
                'balance_money' => $tabobi,
                'actual_money' => $actual_money + $service,
                'service' => $service,
                'exchange' => $exchange['exchange'],
                'site_exchange' => $exchange['site_exchange'],
                'profit_money' => $actual_money-$_actual_money,
                'live_url' => empty($live_url)?'':$live_url,
                'live_id' => empty($live_id)?'':$live_id,
                'live_account' => empty($live_account)?'':$live_account,
                'live_pwd' => empty($live_pwd)?'':$live_pwd,
                'live_value' => empty($live_value)?'':$live_value,
                'live_game' => empty($live_game)?'':$live_game,
                'live_type' => empty($live_type)?'':$live_type,
                'live_money' => $_live_money * 10,
                'user_remarks' => empty($user_remarks)?'':$user_remarks,
                'address' => empty($address)?'':$address,
                'live_img' => empty($img)?'':$img,
                'qiyebank' => empty($_company)?'':$_company,
                'pay_status' => $pay_status,
                'pay_type' => $pay_type,
                'bank_id' => $bank_id,
                'bank_num' => $bank,
                'order_status' => $order_status,
                'user_id' => $this->auth->id,
                'createtime' => time(),
                'updatetime' => time(),
                'day_time' => date('Ymd'),
                // 'source'   => $userinfo['source']
            );
            /* 拦截重复提交 */
            $this->untreate($key_id,$special['table_name']);
            if($pay_type == '2'){
                $data['completetime'] = time();
            }
            $log_title = "";
            /* 存储订单 */
            $result = Db::name($special['table_name'])->strict(false)->insertGetId($data);
            $this->orderLog('创建订单'.$log_title,$result,$special['table_name']);
            if(Config::get('site.virtual_open') && $pay_status == '1'){
                $virtual_code = $this->generate_code($result,$data['user_id'],$data['actual_money'],'',$special['table_name']);
            }
            /* 插入淘币日志 */
            if($tabobi){
                $this->taobiLog($key_id,$tabobi,$result,'','',$special['table_name']);
                $this->orderLog('使用淘币'.$tabobi,$result,$special['table_name']);
            }
            if($bank_id){
                /*使用银行卡付款，关联信息，方便匹配流水*/
                DaifuModel::order_bank_matching($bank_id,$special['table_name'],$result,$this->auth->id,$bank);
            }
            Db::commit();
            if(is_mobile()){
                $url = url('index/user/order_detail');
            }else{
                $url = url('daifu/liveSuccess');
            }

            if($pay_status == '2'){
                DaifuModel::cs_pay($data);
            }
            if($pay_status == '4'){
                $card_url = DaifuModel::card_pay($data,$result,$url);
                $this->success(__('Just a moment, please. Credit card payment is jumping'),$card_url);
            }
            $this->success(__('Order Creation Success'),$url.'?id='.$result.'&set_type='.$key_id);
        }catch (Exception $e) {
            $this->error(__('Network exception, please refresh and try again'));
            Db::rollback();
        }
    }
    /*获取价格*/
    public function new_price($price='',$key_id='',$type='',$num_type=''){
        $price = $this->request->post('price');
        $key_id = $this->request->post('key_id');
        $type = $this->request->post('type');
        $num_type = $this->request->post('num_type');
        $info = Db::name('special')->where('key_id',$key_id)->find();
        $user = Db::name('user')->where('id',$this->auth->id)->find();
        /*type 1=类型 2类型自定义 3面额 4虚拟*/
        if($type == '1'){
            $money = $price;
        }
        if($type == '2'){
            $set_special_two = Db::name('set_special_two')->where('key_id',$key_id)->find();
            $set_special = json_decode($set_special_two['set_id'],true);
            foreach($set_special as $ls){
                if($ls['title'] == $num_type){
                    $discount = $ls['discount'];
                }
            }
            //$money = $price * $discount;
            $money = $price;
        }
        if($type == '3'){
            $money = $price * $info['discount'];
        }
        if($type == '4'){
            $money = $price;
        }
        //$user_money  = '';
        if($user['money'] >= $money){
            $user_money = $money;
        }else{
            $user_money = $user['money'];
        }
        /*根据当前汇率计算台币*/
        $exchange = DaifuModel::getExchange($this->auth->id,$key_id,$money,'1');
        $return = array(
            'money'=>$exchange['money'],
            'user_money'=>$user_money,
        );
        return $return;
    }
    //提交完成页面
    public function liveSuccess(){
        $id = $this->request->get('id');
        $key_id = $this->request->get('set_type');
        $info = Db::name('special')->where('key_id',$key_id)->find();
        /* 获取订单详情 */
        $order = MemberModel::getliveOrder($id,$info['table_name']);
        $this->view->assign('order',$order);

        return $this->view->fetch('daifu/live_success');
    }
    //手机卡充值-预留
    public function mobile(){
        $this->untreate($this->set_type);
        return $this->view->fetch('daifu/live');
    }
    // 游戏储值---新-预留
    public function gamenew(){
        $this->untreate($this->set_type);
        return $this->view->fetch('daifu/live');
    }
    //其他代购--新-预留
    public function othernew(){
        $this->untreate($this->set_type);
        return $this->view->fetch('daifu/live');
    }

    /******************************业务分割线👆新业务👆👇老业务👇********************************************/
    // 阿里巴巴代付
    public function index(){
        $this->untreate($this->set_type);
        $account_type = request()->param('account_type');
        /* 获取平台支付宝账号 */
        $sysalipay = DaifuModel::getSysAlipay($account_type);
        /*检测是否是用户中心跳转过来的*/
        $type = request()->param('type');
        $product['price'] = '0';
        if($type){
            /* 获取用户绑定的支付宝账号 */
            $_map = array(
                'user_id' => $this->auth->id,
                'status' => 'normal',
            );
            $list = Db::name('bonding_alipay')
                ->field('*')
                ->where($_map)
                ->select();
            $alipay = [];
            foreach($list as $ls){
                $alipay[] = $ls['other_account'];
            }
            $product = [];
            if($alipay){
                $time = time()-10800;
                $start_time = date('Y-m-d H:i:s',$time);
                $end_time = date('Y-m-d H:i:s',time());
                $map['other_account'] = array('in',$alipay);
                $map['status'] = '0';
                $product = Db::view('t_alipay_order_goods','*')
                    ->view('t_alipay_order',['peerpay_link','sys_alipay_id','trade_no','type','other_name','other_account','status','update_time'],'t_alipay_order.id=t_alipay_order_goods.alipay_order_id')
                    ->where($map)
                    ->whereTime('update_time','between',[$start_time,$end_time])
                    ->find();
            }
        }
        /*老业务配置信息*/
        $this->old_common_configuration($this->set_type);
        $this->view->assign('product',$product['price']);
        $this->view->assign('type',$type);
        $this->view->assign('account_type',$account_type);
        $this->view->assign('sysalipay',$sysalipay);
        return $this->view->fetch();
    }
    // 淘宝天猫
    public function taobao()
    {
        $this->untreate($this->set_type);
        $account_type = request()->param('account_type');
        /* 获取平台支付宝账号 */
        $sysalipay = DaifuModel::getSysAlipay($account_type);
        $type = request()->param('type');
        /*老业务配置信息*/
        $this->old_common_configuration($this->set_type);
        $this->view->assign('type',$type);
        $this->view->assign('account_type',$account_type);
        $this->view->assign('sysalipay',$sysalipay);
        return $this->view->fetch('daifu/taobao');
    }
    // 抖音代购
    public function douyin(){
        $this->untreate($this->set_type);
        $account_type = request()->param('account_type');
        /* 获取平台支付宝账号 */
        $sysalipay = DaifuModel::getSysAlipay($account_type);
        $type = request()->param('set_type');
        /*老业务配置信息*/
        $this->old_common_configuration($this->set_type);
        $this->view->assign('type',$type);
        $this->view->assign('account_type',$account_type);
        $this->view->assign('sysalipay',$sysalipay);
        return $this->view->fetch('daifu/douyin');
    }
    // 吱口令
    public function zhikouling()
    {
        $this->untreate($this->set_type);
        /* 获取平台支付宝账号 */
        $sysalipay = DaifuModel::getSysAlipay();
        /*老业务配置信息*/
        $this->old_common_configuration($this->set_type);
        $this->view->assign('zhi','1');
        $this->view->assign('sysalipay',$sysalipay);
        return $this->view->fetch('daifu/zhi');
    }

    // 吱口令
    public function zhikouling_test()
    {
        $this->untreate($this->set_type);
        /* 获取平台支付宝账号 */
        $sysalipay = DaifuModel::getSysAlipay();
        /*老业务配置信息*/
        $this->old_common_configuration($this->set_type);
        $this->view->assign('zhi','1');
        $this->view->assign('sysalipay',$sysalipay);
        return $this->view->fetch('daifu/zhi');
    }
    // 支付宝储值
    public function alipay()
    {
        $this->untreate($this->set_type);
        /*获取用户分组*/
        $user_group = Db::name('group_pay')->where('id',$this->auth->group_id)->find();
        if($user_group['is_maintain']){
            $this->error($user_group['maintain_text']);
        }
        /* 获取用户支付宝账号 */
        $userali = MemberModel::getUserAli($this->auth->id);
        /*老业务配置信息*/
        $this->old_common_configuration($this->set_type);
        $this->view->assign('userali',$userali);
        $this->view->assign('user_id',$this->auth->id);
        return $this->view->fetch('daifu/alipay');
    }
    // 微信储值
    public function wechatpay()
    {
        $this->untreate($this->set_type);
        /* 获取用户支付宝账号 */
        $userali = MemberModel::getUserWx($this->auth->id);
        /*老业务配置信息*/
        $this->old_common_configuration($this->set_type);
        $this->view->assign('userali',$userali);
        $this->view->assign('user_id',$this->auth->id);
        return $this->view->fetch('daifu/wechatpay');
    }
    // 游戏储值
    public function game()
    {
        $this->untreate($this->set_type);
        //$this->error("8-28日起未綁定大陸銀行卡的微信已經無法透過電腦版微信來收取轉賬了。現在微信的錢只能出，不能進了。因為大陸政策關係，微信何時會在開啟信用卡綁定實名，還無法確定。
        //付唄已關閉《微信人工》業務，《微信儲值》業務當前不受影響，需要您綁定大陸銀行卡，並在會員中心掃碼新增您的微信，請參考《微信儲值幫助教學》

        // 付唄其他業務均正常，支付寶儲值 微信儲值 淘寶代付 阿里巴巴代付（需要代付遊戲可以在淘寶逛逛，淘寶賣家均有提供服務）");
        /* 获取用户支付宝账号 */
        $userali = MemberModel::getUserRgong($this->auth->id);
        $super = MemberModel::superQuotien($this->auth->id);

        /*老业务配置信息*/
        $this->old_common_configuration($this->set_type);
        $this->view->assign('userali',$userali);
        $this->view->assign('type','0');
        $this->view->assign('super',$super);
        $this->view->assign('user_id',$this->auth->id);

        return $this->view->fetch('daifu/rengong');
    }
    // 其他代付
    public function other()
    {
        $this->untreate($this->set_type);
        $super = MemberModel::superQuotien($this->auth->id);
        /*老业务配置信息*/
        $this->old_common_configuration($this->set_type);
        $this->view->assign('super',$super);
        $this->view->assign('user_id',$this->auth->id);
        return $this->view->fetch('daifu/other');
    }
    // 淘币储值
    public function charge()
    {
        $this->untreate($this->set_type);
        /*老业务配置信息*/
        $this->old_common_configuration($this->set_type);
        $this->view->assign('type','0');
        $this->view->assign('user_id',$this->auth->id);
        return $this->view->fetch('daifu/charge');
    }
    /* 抖音查询商品第一步解析二维码，存储代付链接*/
    public function confirmAccount_douyin(){
        if ($this->request->isPost()) {
            //$code_img = $this->request->post('code_img');
            /*$type = $this->request->post('type');
            $money = $this->request->post('money');
            $url = $this->request->post('url');
            $account = $this->request->post('account');

            $return = $this->getImg($img);
            if($return['code'] == '1'){
                $this->error(__($return['msg']));
            }*/
            /**开始**/		/*解析二维码------后续会更换*/
            $img = $this->request->post('code_img');
            $config = array(
                'appcode'=> '2b97000e494741f8bd504be071b1f09c',
                'cpu_id'=>'cpu24124',
                'cpu_key'=>'XwhHRTvYmzzkys',
            );
            $qrcpu = new Qrcpucom($config);
            $imgurl = '';//远程图片
            $imgpath = $img;//'./qrcode.jpg';//本地图片
            $qrdata = $qrcpu->qrdecode($imgurl,$imgpath);
            if($qrdata['status'] != '1'){
                $this->error('請重新上傳QR碼');
            }
            $return = $this->getImg($img);
            if($return['code'] == '1'){
                $this->error(__($return['msg']));
            }
            /**结束**/
            //$url = "https://qr.alipay.com/_d?_b=peerpay&enableWK=YES&biz_no=2024050704200356561025456854_5a6b25f63e532b2f73ee16e1d33c9fe8&app_name=mc&sc=qr_code&v=20240514&sign=822ff0&__webview_options__=pd%3dNO&channel=qr_code";
            $url = $qrdata['data'];
            $url_array = parse_url(html_entity_decode($url));
            parse_str($url_array['query'],$_url_array);
            $url_id = explode('_',$_url_array['biz_no'])['0'];
            $sign = $_url_array['sign'];
            $map = [
                'is_dou' => '1',
                'url_id' => $url_id,
                //'trade_no' => $url_id,
                'order_id' => '0',
            ];
            $alipay_order = Db::name('alipay_order')->where($map)->find();
            if($alipay_order){
                if(!$alipay_order['other_name'] && $alipay_order['price'] == '0.00'){
                    $this->error('正在獲取商品資訊');
                }
                if($alipay_order['is_daifu']){
                    $userAlipay[] = $alipay_order;
                    $this->view->assign('userAlipay',$userAlipay);
                    $this->view->engine->layout('layout/empty');
                    $html = $this->view->fetch('daifu/confirm_account');
                    $this->success('', '',$html);
                }
                $this->error('正在獲取商品資訊');
            }else{
                $alipay_order_data = [
                    'is_dou' => '1',
                    'is_daifu' => '0',
                    'img_path' => $return['data'],
                    'peerpay_link' => $url,
                    'url_id' => $url_id,
                    'trade_no' => $url_id,
                    'sign' => $sign,
                    'user_id' => $this->auth->id,
                    'verson' => '1',
                    'create_time' => date('Y-m-d H:i:s',time()),
                ];
                Db::name('alipay_order')->insert($alipay_order_data);
                $this->error('正在査詢中');
            }
        }else{
            $this->error(__('Network exception, please refresh and try again'));
        }
    }
    /* 查询商品第一步，获取用户支付宝账号 */
    public function confirmAccount(){
        if ($this->request->isPost()) {
            $type = $this->request->post('type');
            $money = $this->request->post('money');
            $url = $this->request->post('url');
            $account = $this->request->post('account');
            /*检测url是否正常*/
            $url_array = parse_url($url);
            if(empty($url_array['scheme']) || empty($url_array['host']) || empty($url_array['path']) || empty($url_array['query'])){
                $this->error('請輸入正確的代付鏈接');
            }else{
                $url_array = parse_url(html_entity_decode($url));
                parse_str($url_array['query'],$_url_array);
                if($url_array['scheme'] != 'https'){
                    $this->error('請輸入正確的代付鏈接code:5');
                }
                if(strlen($_url_array['id']) != '28' && strlen($_url_array['id']) != '19'){
                    $this->error('請輸入正確的代付鏈接code:28');
                }
                if(strlen($_url_array['sign']) != '8'){
                    $this->error('請輸入正確的代付鏈接code:8');
                }
            }
            $sys_alipay = Db::name('sys_alipay')->where('sys_account',$account)->find();
            if($type == '1'){
                /* 吱口令 */
                $zhi = DaifuModel::getZhikouling($money,$this->auth->id);
                switch($zhi['status']){
                    case 1:
                        $this->error(__('Please input the correct password'));
                        break;
                    case 2:
                        $this->error(__('You have more than five unprocessed passwords. Please process them before applying'));
                        break;
                    case 3:
                        // print_r($zhi['goods']);exit;
                        $this->view->assign('goods',$zhi['goods']);
                        $this->view->engine->layout('layout/empty');
                        $html = $this->view->fetch('daifu/confirm_zkl_goods');
                        $this->success('', '',$html);
                        break;
                }
            }
            $userAlipay = DaifuModel::getDaifuProduct($money,$this->auth->id,$url,$sys_alipay['id']);
            if($userAlipay == '1'){
                $this->error('正在査詢中');
            }
            if($userAlipay == '2'){
                $this->error(__('The goods have not been inquired yet. Please try again later'));
            }
            if($userAlipay){
                $this->view->assign('userAlipay',$userAlipay);
                $this->view->engine->layout('layout/empty');
                $html = $this->view->fetch('daifu/confirm_account');
                $this->success('', '',$html);
            }else{
                $this->error(__('The goods have not been inquired yet. Please try again later'));
            }
        }else{
            $this->error(__('Network exception, please refresh and try again'));
        }
    }
    /* 查询第二步绑定支付宝账号，并返回商品信息 */
    public function confirmGoods(){
        if ($this->request->isPost()) {
            $id = $this->request->post('id');
            $product = DaifuModel::getProduct($id,$this->auth->id,'1');
            if(!empty($product)){
                $this->view->assign('product',$product);
                $this->view->engine->layout('layout/empty');
                $html = $this->view->fetch('daifu/confirm_goods');
                $this->success('', '',$html);
            }else{
                $this->error(__('The goods have not been inquired yet. Please try again later'));
            }
        }else{
            $this->error(__('Network exception, please refresh and try again'));
        }
    }
    /* 查询第三步确定商品信息 */
    public function confirmGoods_ids(){
        if ($this->request->isPost()) {
            $id = $this->request->post();
            $map['alipay_order_id'] = array('in',$id['ids']);
            $product = DaifuModel::getDfProduct($map,'1');
            if($product['status'] == '1'){
                $this->view->assign('product',$product);
                $this->view->engine->layout('layout/empty');
                if(is_mobile()){
                    $html = $this->view->fetch('daifu/m_confirm_product');
                }else{
                    $html = $this->view->fetch('daifu/confirm_product');
                }
                $this->success('', '',$html);
            } else if($product['status'] == '2'){
                $this->error(__('Goods amount change, unable to submit, please re apply for goods'));
            }else{
                $this->error(__('The goods have not been inquired yet. Please try again later'));
            }
        }else{
            $this->error(__('Network exception, please refresh and try again'));
        }
    }
    /*
    * 阿里巴巴查询商品
    */
    public function select_product(){
        $url = $this->request->post('url');
        // https://shenghuo.alipay.com:443/peerpaycore/confirmPeerPay.htm?id=2024061104200217001034714197&peerpayType=NEW_PPAY&sign=f0e4f7ee
        // $url = 'https://qr.alipay.com/_d?_b=peerpay&enableWK=YES&biz_no=2024060404200356561032570609_7d9576734d67323b0d4cc44d24232936&app_name=tb&sc=qr_code&v=20240611&sign=cd512f&__webview_options__=pd%3dNO&channel=qr_code';
        $https = substr_count($url,'https');
        if($https > '1'){
            $this->error('請輸入正確的代付鏈接 錯誤碼:004');
        }
        $url_array = parse_url(html_entity_decode($url));
        parse_str($url_array['query'],$_url_array);
        if($url_array['scheme'] != 'https'){
            $this->error('請輸入正確的代付鏈接 錯誤碼:005');
        }
        if(strlen($_url_array['id']) != '28' && strlen($_url_array['id']) != '19'){
            $this->error('請輸入正確的代付鏈接 錯誤碼:028');
        }
        if(strlen($_url_array['sign']) != '8'){
            $this->error('請輸入正確的代付鏈接 錯誤碼:008');
        }
        // print_r($_url_array);exit;
        // $url_id = explode('_',$_url_array['biz_no'])['0'];
        $map = [
            'url_id' => $_url_array['id'],
            'status' => '0',
            // 'is_daifu' => '1',
        ];
        $info = Db::name('alipay_order')->where($map)->find();
        if($info){
            Db::name('debugging')->insert(['title' => $info['id'], 'msg' => 'url: ' . $url,'add_time'=>time()]);


            $other_name = $info['other_name'];
            $map_product['alipay_order_id'] = $info['id'];
            $map_product['status'] ='0';
            $product = Db::name('alipay_order_goods')->where($map_product)->select();
            $data = [];
            $price = '0';
            foreach($product as $key=>$val){
                $data[$key]['other_name'] = $other_name;
                $data[$key]['id'] = $val['alipay_order_id'];
                $data[$key]['title'] = $val['title'];
                $data[$key]['price'] = $val['price'];
            }
            if (strlen($url) === 0 || empty($url)){
                return $this->error('代付鏈接讀取異常，請刷新頁面');
            }
            $res['list']=$data;
            $res['code']='1';
            $data = [
                'peerpay_link' => $url,
                'detail_link' => $url,
                'user_id' => $this->auth->id,
            ];

            Db::startTrans();
            try{
                //Db::name('alipay_order')->where('id',$info['id'])->update($data);
                // $ao_row = Db::name('alipay_order')->where('id',$info['id'])->find();
                // if($ao_row['peerpay_link']=="" && strlen($url) > 0 ){
                //     if( Db::name('alipay_order')->where('id',$info['id'])->update($data) ){
                //         Db::commit();
                //         return $res;
                //     }
                // }

                if( Db::name('alipay_order')->where('id',$info['id'])->update($data) ){
                    Db::commit();

                    $test_row = Db::name('alipay_order')->where('id', $info['id'])->find();
                    Db::name('debugging')->insert(['title' => $info['id'], 'msg' => json_encode($test_row) . ',data=' . json_encode($data),'add_time'=>time()]);
                    
                    return $res;
                }
                Db::rollback();
            }catch(Exception $e){
                Db::rollback();
                Db::name('debugging')->insert(['title' => $info['id'], 'msg' => $e->getMessage(),'add_time'=>time()]);
                Db::name('debugging')->insert(['title' => $info['id'], 'msg' => 'data=' . json_encode($data),'add_time'=>time()]);
                return $this->error('數據獲取異常，請刷新頁面後再操作');
            }
        }else{
            $this->error('正在査詢中');
        }
    }

    /**
     * 手机查不了的情况下替换为web查询
     * @return array<array[]|int|string>
     */
    // public function select_product_web(){
    //     $url = $this->request->post('url');
    //     // https://shenghuo.alipay.com:443/peerpaycore/confirmPeerPay.htm?id=2024061104200217001034714197&peerpayType=NEW_PPAY&sign=f0e4f7ee
    //     // $url = 'https://qr.alipay.com/_d?_b=peerpay&enableWK=YES&biz_no=2024060404200356561032570609_7d9576734d67323b0d4cc44d24232936&app_name=tb&sc=qr_code&v=20240611&sign=cd512f&__webview_options__=pd%3dNO&channel=qr_code';
    //     $https = substr_count($url,'https');
    //     if($https > '1'){
    //         $this->error('請輸入正確的代付鏈接 錯誤碼:004');
    //     }
    //     $url_array = parse_url(html_entity_decode($url));
    //     parse_str($url_array['query'],$_url_array);
    //     if($url_array['scheme'] != 'https'){
    //         $this->error('請輸入正確的代付鏈接 錯誤碼:005');
    //     }
    //     if(strlen($_url_array['id']) != '28' && strlen($_url_array['id']) != '19'){
    //         $this->error('請輸入正確的代付鏈接 錯誤碼:028');
    //     }
    //     if(strlen($_url_array['sign']) != '8'){
    //         $this->error('請輸入正確的代付鏈接 錯誤碼:008');
    //     }

    //     $map = [
    //         'url_id' => $_url_array['id'],
    //         'status' => '0',
    //     ];
    //     $info = Db::name('alipay_order')->where($map)->find();
    //     if($info){
    //         Db::name('debugging')->insert(['title' => $info['id'], 'msg' => 'url: ' . $url,'add_time'=>time()]);


    //         $other_name = $info['other_name'];
    //         $map_product['alipay_order_id'] = $info['id'];
    //         $map_product['status'] ='0';
    //         $product = Db::name('alipay_order_goods')->where($map_product)->select();
    //         $data = [];
    //         $price = '0';
    //         foreach($product as $key=>$val){
    //             $data[$key]['other_name'] = $other_name;
    //             $data[$key]['id'] = $val['alipay_order_id'];
    //             $data[$key]['title'] = $val['title'];
    //             $data[$key]['price'] = $val['price'];
    //         }
    //         if (strlen($url) === 0 || empty($url)){
    //             return $this->error('代付鏈接讀取異常，請刷新頁面');
    //         }
    //         $res['list']=$data;
    //         $res['code']='1';
    //         $data = [
    //             'peerpay_link' => $url,
    //             'detail_link' => $url,
    //             'user_id' => $this->auth->id,
    //         ];

    //         Db::startTrans();
    //         try{
    //             //Db::name('alipay_order')->where('id',$info['id'])->update($data);
    //             // $ao_row = Db::name('alipay_order')->where('id',$info['id'])->find();
    //             // if($ao_row['peerpay_link']=="" && strlen($url) > 0 ){
    //             //     if( Db::name('alipay_order')->where('id',$info['id'])->update($data) ){
    //             //         Db::commit();
    //             //         return $res;
    //             //     }
    //             // }

    //             if( Db::name('alipay_order')->where('id',$info['id'])->update($data) ){
    //                 Db::commit();

    //                 $test_row = Db::name('alipay_order')->where('id', $info['id'])->find();
    //                 Db::name('debugging')->insert(['title' => $info['id'], 'msg' => json_encode($test_row) . ',data=' . json_encode($data),'add_time'=>time()]);
                    
    //                 return $res;
    //             }
    //             Db::rollback();
    //         }catch(Exception $e){
    //             Db::rollback();
    //             Db::name('debugging')->insert(['title' => $info['id'], 'msg' => $e->getMessage(),'add_time'=>time()]);
    //             Db::name('debugging')->insert(['title' => $info['id'], 'msg' => 'data=' . json_encode($data),'add_time'=>time()]);
    //             return $this->error('數據獲取異常，請刷新頁面後再操作');
    //         }
    //     }else{
    //         $this->error('正在査詢中');
    //     }
    // }







    /* 吱口令确认商品 */
    public function confirmGoods_zkl(){
        if ($this->request->isPost()) {
            $money = $this->request->post('money');
            /* 吱口令 */
            $zhi = DaifuModel::getZhikouling($money,$this->auth->id,$this->auth->mobile);
            switch($zhi['status']){
                case 1:
                    $this->error('1');
                    break;
                case 2:
                    $this->error('2');
                    break;
                case 3:
                    $id = [];
                    foreach($zhi['goods'] as $ls){
                        $id[] = $ls['zkl_id'];
                    }
                    $map['zkl_id'] = array('in',$id);
                    $product = DaifuModel::getzklProduct($map);
                    $this->view->assign('goods',$product);
                    $this->view->assign('find',$zhi['find']);
                    $this->view->engine->layout('layout/empty');
                    $html = $this->view->fetch('daifu/confirm_zkl_product');
                    $this->success('', '',$html);
                    break;
            }

        }else{
            $this->error(__('Network exception, please refresh and try again'));
        }
    }
    /* 会员中心跳转返回商品信息 */
    public function userGoods(){
        $_map = array(
            'user_id' => $this->auth->id,
            'status' => 'normal',
        );
        $list = Db::name('bonding_alipay')
            ->field('*')
            ->where($_map)
            ->select();
        foreach($list as $ls){
            $alipay[] = $ls['other_account'];
        }
        $map['other_account'] = array('in',$alipay);
        $map['status'] = '0';
        $product = DaifuModel::getDfProduct($map);
        $this->view->assign('product',$product);
        $this->view->engine->layout('layout/empty');
        $html = $this->view->fetch('daifu/confirm_goods');
        $this->success('', '',$html);
    }
    /* 阿里巴巴代付提交成功 */
    public function indexSuccess()
    {
        if($this->request->isPost()){
            $this->dfPiblic('0',url('daifu/indexSuccess'));
        }else{
            $id = $this->request->get('id');
            /* 获取订单详情 */
            $order = MemberModel::getOrder($id);
            // print_r($order);exit;
            $this->view->assign('order',$order);
            return $this->view->fetch('daifu/index_success');
        }
    }
    /*  淘宝天猫提交成功 */
    public function taobaoSuccess()
    {
        if($this->request->isPost()){
            $this->dfPiblic('1',url('daifu/taobaoSuccess'));
        }else{
            $id = $this->request->get('id');
            /* 获取订单详情 */
            $order = MemberModel::getOrder($id);
            // print_r($order);exit;
            $this->view->assign('order',$order);
            return $this->view->fetch('daifu/index_success');
        }
    }
    /* 抖音代付提交成功 */
    public function douyin_success()
    {
        if($this->request->isPost()){
            $this->dfPiblic('17',url('daifu/douyin_success'));
        }else{
            $id = $this->request->get('id');
            /* 获取订单详情 */
            $order = MemberModel::getOrder($id);
            // print_r($order);exit;
            $this->view->assign('order',$order);
            return $this->view->fetch('daifu/douyin_success');
        }
    }
    /* 代付共用处理 */
    public function dfPiblic($type,$url){
        session('fbi', null);
        if($this->auth->is_card == '0'){
            $this->error('身份證一次認證未開通');
        }
        /* if($this->auth->is_old == '0'){
            if($this->auth->is_card_img == '0'){
                $this->error('身份證二次認證未開通');
            }
        } */
        $account = $this->request->post('account')?$this->request->post('account'):Session::get('good_array.account');
        $money = $this->request->post('money');
        $goods_ids = $this->request->post('goods_ids')?$this->request->post('goods_ids'):Session::get('good_array.goods_ids');
        $tabobi = $this->request->post('tabobi');
        $pay_status = $this->request->post('pay_type');
        $bank_id = $this->request->post('bank_id');
        $fp_type = $this->request->post('fp_type');
        $number = $this->request->post('number');
        $fp_title = $this->request->post('fp_title');
        $fp_email = $this->request->post('fp_email');
        /* 获取需要代付的商品 */
        if($type == '2'){
            /* 吱口令 */
            $alipay_order_id = array_unique(explode(',',$goods_ids));
            $map['id'] = array('in',$alipay_order_id);
            $product = DaifuModel::getzklProduct($map);
            $price = '0';
            foreach($product as $ls){
                $zkl_id = $ls['zkl_id'];
                $price = $ls['price']+$price;
                if($ls['status'] != '0'){
                    $this->error('商品狀態發生變化，請聯系客服');
                }
            }
            /*新改-********，软件无法在商品表插入价格*/
            $info = Db::name('zkl')->where('id',$product[0]['zkl_id'])->find();
            $price = $info['price'];
            $receipts_price = $all_price = sprintf("%.2f",$price);
        }else{
            $alipay_order_id = array_unique(explode(',',$goods_ids));
            $map['alipay_order_id'] = array('in',$alipay_order_id);
            $product = DaifuModel::getDfProduct($map);
            foreach($product['alipay'] as $val){
                if($val['status'] != '0'){
                    $this->error('商品狀態發生變化，請聯系客服');
                }
            }
            $receipts_price = $all_price = sprintf("%.2f",$product['all_price']);
        }

        /* 计算用户淘币数量是否足够支付 */
        $pay_type = '0';
        $service = '0';
        $userinfo = $this->auth->getUserinfo();
        if($tabobi){
            /* 获取用户淘币 */
            if($userinfo['money'] >= $tabobi){
                if($tabobi >= $all_price){
                    /* 用户填写淘币大于当前商品总价-自动转换至淘币支付 */
                    $pay_status = '0';
                    $pay_type = '2';
                    $service = '0';
                    $tabobi = $all_price;
                }
            }else{
                /* 用户填写淘币小于当前商品总价-判断淘币是否足够 */
                if($tabobi <= $userinfo['money']){
                    $user_taobi = $userinfo['money'] - $tabobi;
                    $pay_type = '1';
                }else{
                    $this->error(__('Current shortage of currency clearing balance'));
                }
            }
            $receipts_price = $receipts_price - $tabobi;
        }
        /* 开启事务 */
        Db::startTrans();
        try {
            /* 按照当前用户等级汇率把人民币转换成台币 */
            $exchange = DaifuModel::getExchange($this->auth->id,$type,$all_price,'1');
            $tb_money = $exchange['money'];
            $actual_money = ceil(bcmul($receipts_price,$exchange['exchange'],'2'));
            $_actual_money = ceil(bcmul($receipts_price,$exchange['site_exchange'],'2'));
            /* 绑定银行 */
            $bank = "";
            if($bank_id){
                $bank = DaifuModel::bondingBank($bank_id,$this->auth->is_bank);
                $service = '0';
            }
            if($pay_status == '2'){
                $service = '30';
                if($actual_money < '70'){
                    $this->error(__('The payment amount of the super merchant shall not be less than 70 (excluding the handling fee)'));
                }
                if($actual_money > '5970'){
                    $this->error(__('The maximum amount of super business payment shall not exceed 6000 (including handling fee)'));
                }
            }
            if($pay_status == '4'){
                $service = ceil($actual_money*0.03);
            }

            /* 存储订单数据 */
            $data = array(
                'type' => $type,
                'order_no' => DaifuModel::orderNo($type),
                'tb_money' => $tb_money,
                'rmb_money' => $all_price,
                'balance_money' => $tabobi,
                'actual_money' => $actual_money+$service,
                'service' => $service,
                'exchange' => $exchange['exchange'],
                'site_exchange' => $exchange['site_exchange'],
                'profit_money' => $actual_money-$_actual_money,
                'pay_status' => $pay_status,
                'pay_type' => $pay_type,
                'bank_id' => $bank_id,
                'bank_num' => $bank,
                'product_json' => json_encode($product),
                'user_id' => $this->auth->id,
                'invoice_id' => &$invoice,
                'createtime' => time(),
                'updatetime' => time(),
                'day_time' => date('Ymd'),
                'source'    => $userinfo['source'],
            );
            /* 存储发票信息 */
            if($actual_money){
                if(Config::get('site.virtual_open') && $pay_status == '1'){
                    $virtual = '3';
                }else{
                    $virtual = $pay_type;
                }
                $invoice = DaifuModel::invoice($fp_type,$number,$fp_title,$fp_email,$actual_money,$this->auth->id,$data['order_no'],$virtual);
            }else{
                $invoice = '0';
            }
            /* 拦截重复提交 */
            $this->untreate($type);
            if($pay_type == '2'){
                $data['completetime'] = time();
            }
            $log_title = "";
            /* if($this->auth->is_order == '0'){
                $data['order_status'] = '2';
                $log_title = "用户首单自动转人工";
            } */
            if($type == '2'){
                if($all_price > Config::get('site.zkl_artificial')){
                    $data['order_status'] = '2';
                    $log_title = "金額超過 請客服檢查";
                }
            }else{
                if($all_price > Config::get('site.df_artificial')){
                    $data['order_status'] = '2';
                    $log_title = "金額超過 請客服檢查";
                }
            }
            /* 存储订单 */
            $result = Db::name('df_order')->insertGetId($data);
            Db::name('debugging')->insert(['title' => $result, 'msg' => json_encode($data),'add_time'=>time()]);
            $this->orderLog('创建订单'.$log_title,$result);
            if(Config::get('site.virtual_open') && $pay_status == '1'){
                $virtual_code = $this->generate_code($result,$data['user_id'],$data['actual_money'],'','df_order');
            }
            /* 插入淘币日志 */
            if($tabobi){
                $this->taobiLog($type,$tabobi,$result);
                $this->orderLog('使用淘币'.$tabobi,$result);
            }
            if($bank_id){
                /*使用银行卡付款，关联信息，方便匹配流水*/
                DaifuModel::order_bank_matching($bank_id,'df_order',$result,$this->auth->id,$bank);
            }
            /* 更改代付商品状态 */
            if($type == '2'){
                /* 吱口令 */
                DaifuModel::zklStatus($map,$zkl_id,$result);
            }else{
                $map['order_id'] = $result;
                DaifuModel::productStatus($map);
            }
            if($pay_type == '2'){
                if($type == '2'){
                    Db::name('zkl')->where('order_id',$result)->update(['status'=>'1']);
                }else{
                    $sys_alipay = Db::name('alipay_order')->where('order_id',$result)->group('sys_alipay_id')->select();
                    if($type != '17' && $type != '1'){
                        foreach($sys_alipay as $ls){
                            $uuid = Random::uuid();
                            $order_pay = array(
                                'guid' => $uuid,
                                'order_id' => $result,
                                'sys_alipay_id' => $ls['sys_alipay_id'],
                                'is_dou' => '0',
                            );
                            Db::name('df_order_pay')->insert($order_pay);
                        }
                    }
                }
            }
            Db::commit();
            if($pay_status == '2'){
                DaifuModel::cs_pay($data);
            }
            if($pay_status == '4'){
                $card_url = DaifuModel::card_pay($data,$result,$url);
                $this->success(__('Just a moment, please. Credit card payment is jumping'),$card_url);
            }
            $this->success(__('Order Creation Success'),$url.'?id='.$result.'&set_type='.$type);
        }catch (Exception $e) {
            $this->error(__('Network exception, please refresh and try again'));
            Db::rollback();
        }
    }
    // 支付宝储值提交成功
    public function alipay_success()
    {
        if($this->request->isPost()){
            $money = $this->request->post('money');
            if($money < Config::get('site.alipay_small')){
                $this->error('代儲值金額不能少於'.Config::get('site.alipay_small').'元人民幣');
            }
            if($money > Config::get('site.alipay_max')){
                $this->error('代儲值單筆金額不能超過'.Config::get('site.alipay_max').'元人民幣');
            }
            $this->storedPiblic('3',url('daifu/alipay_success'));
        }
        $id = $this->request->get('id');
        /* 获取订单详情 */
        $order = MemberModel::getOrder($id);
        $this->view->assign('order',$order);
        return $this->view->fetch('daifu/alipay_success');
    }
    // 微信储值提交成功
    public function wechatpay_success()
    {
        if($this->request->isPost()){
            $money = $this->request->post('money');
            if($money < Config::get('site.wx_small')){
                $this->error('代儲值金額不能少於'.Config::get('site.wx_small').'元人民幣');
            }
            if($money > Config::get('site.wx_max')){
                $this->error('代儲值單筆金額不能超過'.Config::get('site.wx_max').'元人民幣');
            }
            $this->storedPiblic('4',url('daifu/wechatpay_success'));
        }
        $id = $this->request->get('id');
        /* 获取订单详情 */
        $order = MemberModel::getOrder($id);
        $this->view->assign('order',$order);
        return $this->view->fetch('daifu/wechatpay_success');
    }
    /* 储值共用处理 */
    public function storedPiblic($type,$url){
        if($this->auth->is_card == '0'){
            $this->error('身份證一次認證未開通');
        }
        /* if($this->auth->is_card_img == '0'){
            $this->error('身份證二次認證未開通');
        } */
        // print_r($this->request->post());exit;
        $account = $this->request->post('account');
        $money = $this->request->post('money');
        $tabobi = $this->request->post('tabobi');
        $buy_gold = $this->request->post('buy_gold');
        $pay_status = $this->request->post('pay_type');
        $bank_id = $this->request->post('bank_id');
        $url_path = $this->request->post('url_path');
        $fp_type = $this->request->post('fp_type');
        $number = $this->request->post('number');
        $fp_title = $this->request->post('fp_title');
        $fp_email = $this->request->post('fp_email');
        /* if($money <= '10'){
            $this->error(__('The stored value shall not be less than 10 yuan'));
        } */
        /* 获取用户购物金 */
        $userinfo = $this->auth->getUserinfo();
        /* 计算用户购物金数量是否足够-预留 */
        $gold_buy = DaifuModel::goldBuy($money,$this->auth->id);
        $buy_gold = $gold_buy;
        /* 获取用户储值账号 */
        $group = "";
        if(empty($tabobi)){
            $tabobi = '0';
        }
        $pay_type = '0';
        $order_status = '0';
        if($tabobi){
            /* 获取用户淘币 */
            if($userinfo['money'] >= $tabobi){
                if($tabobi >= $money){
                    /* 用户填写淘币大于当前商品总价-自动转换至淘币支付 */
                    $pay_status = '0';
                    $pay_type = '2';
                    $service = '0';
                    $order_status = '2';
                    $receipts_price = $money - $tabobi;
                }else{
                    $pay_type = '1';
                    $receipts_price = $money - $tabobi;
                }
            }else{
                $this->error(__('Current shortage of currency clearing balance'));
            }
        }
        if($type == '5'){
            $buy_gold = '0';
        }
        if($type == '3'){
            /* if($money < '10'){
                $this->error(__('The stored value cannot be less than 1'));
            } */
            $alipay = MemberModel::getUserOne('user_alipay',$account);
            if($alipay){
                $stored_account = $alipay['ali_account'];
                $wechat_openid = $alipay['name'];
            }else{
                $this->error('支付寶帳號异常，請聯系客服');
            }
            /*获取用户分组，根据用户分组配置收款账号*/
            $group = MemberModel::getUserGroup($this->auth->group_id);
        }
        $headimage = '';
        if($type == '4'){
            /* if($money < '10'){
                $this->error(__('The stored value shall not be less than 10 yuan'));
            } */
            $wxpay = MemberModel::getUserOne('user_wx',$account);
            $stored_account = $wxpay['wx_account'];
            $wechat_openid = $wxpay['openid'];
            $headimage = $wxpay['headimage'];
        }
        if($type == '5'){
            /* if($money < '10'){
                $this->error(__('The stored value shall not be less than 10 yuan'));
            } */
            $wxpay = MemberModel::getUserOne('user_artificial',$account);
            $stored_account = $wxpay['ali_account'];
            $wechat_openid = $wxpay['name'];
        }
        $service = '0';
        /* 开启事务 */
        //Db::startTrans();
        //try {
        if(!$money){
            $this->error('金額錯誤，請重繪後重試');
        }
        /* 按照当前用户等级汇率把人民币转换成台币 */
        $exchange = DaifuModel::getExchange($this->auth->id,$type,$money,'1');
        $tb_money = $exchange['money'];
        if($tabobi){
            $actual_money = ceil(bcmul($receipts_price,$exchange['exchange'],'2'));
            $_actual_money = ceil(bcmul($receipts_price,$exchange['site_exchange'],'2'));
        }else{
            $actual_money = $tb_money-$buy_gold;
            $_actual_money = ceil(bcmul($money,$exchange['site_exchange'],'2'));
        }
        /* 绑定银行 */
        $bank = "";
        if($bank_id){
            $bank = DaifuModel::bondingBank($bank_id,$this->auth->is_bank);
            $service = '0';
        }
        if($pay_status == '4'){
            $service = ceil($actual_money*0.03);
        }
        if($pay_status == '2'){
            $service = '30';
            if($actual_money < '70'){
                $this->error(__('The payment amount of the super merchant shall not be less than 70 (excluding the handling fee)'));
            }
            if($actual_money > '5970'){
                $this->error(__('The maximum amount of super business payment shall not exceed 6000 (including handling fee)'));
            }
        }
        /* 存储订单数据 */
        $data = array(
            'type' => $type,
            'order_no' => DaifuModel::orderNo($type),
            'tb_money' => $tb_money,
            'rmb_money' => $money,
            'gold_money' => $buy_gold,
            'balance_money' => $tabobi,
            'actual_money' => $actual_money+$service,
            'service' => $service,
            'exchange' => $exchange['exchange'],
            'site_exchange' => $exchange['site_exchange'],
            'profit_money' => $actual_money-$_actual_money,
            'pay_status' => $pay_status,
            'bank_id' => $bank_id,
            'bank_num' => $bank,
            'user_id' => $this->auth->id,
            'invoice_id' => &$invoice,
            'alipay_account' => empty($stored_account)?'':$stored_account,
            'wechat_account' => empty($wechat_openid)?'':$wechat_openid,
            'account' => empty($account)?'':$account,
            'url_path' => empty($url_path)?$headimage:$url_path,
            'createtime' => time(),
            'updatetime' => time(),
            'pay_type' => $pay_type,
            'order_status' => $order_status,
            'group_id' => $this->auth->group_id,
            'day_time' => date('Ymd'),
            'source'   => $userinfo['source']
        );
        /* 拦截重复提交 */
        $this->untreate($type);
        /* 存储发票信息 */
        $invoice = DaifuModel::invoice($fp_type,$number,$fp_title,$fp_email,$actual_money,$this->auth->id,$data['order_no'],$pay_status);
        /* 存储订单 */
        $result = Db::name('df_order')->insertGetId($data);
        $this->orderLog('创建订单',$result);
        if(Config::get('site.cz_virtual_open') && $pay_status == '1'){
            $virtual_code = $this->generate_code($result,$data['user_id'],ceil($data['actual_money']),$group,'df_order');
        }
        if($bank_id){
            /*使用银行卡付款，关联信息，方便匹配流水*/
            DaifuModel::order_bank_matching($bank_id,'df_order',$result,$this->auth->id,$bank);
        }
        /* 插入购物金日志 */
        if($buy_gold){
            $this->goldLog($type,$buy_gold,$result);
        }
        /* 插入淘币日志 */
        if($tabobi){
            $this->taobiLog($type,$tabobi,$result);
            $this->orderLog('使用淘币'.$tabobi,$result);
        }
        Db::commit();
        if($pay_status == '2'){
            DaifuModel::cs_pay($data);
        }
        if($pay_status == '4'){
            $card_url = DaifuModel::card_pay($data,$result,$url);
            $this->success(__('Just a moment, please. Credit card payment is jumping'),$card_url);
        }
        $this->success(__('Order Creation Success'),$url.'?id='.$result.'&set_type='.$type);
        //}catch (Exception $e) {
        //	$this->error(__('Network exception, please refresh and try again'));
        //	Db::rollback();
        //}
    }
    // 游戏储值提交成功
    public function game_success()
    {

        if($this->request->isPost()){
            $money = $this->request->post('money');
            if($money > '100000'){
                $this->error(__('The maximum amount of stored value cannot exceed 100000'));
            }
            $this->storedPiblic('5',url('daifu/game_success'));
        }
        $id = $this->request->get('id');
        /* 获取订单详情 */
        $order = MemberModel::getOrder($id);
        $this->view->assign('order',$order);
        return $this->view->fetch('daifu/rengong_success');
    }
    // 其他代付提交成功
    public function other_success()
    {

        if($this->request->isPost()){
            $money = $this->request->post('money');
            if($money < Config::get('site.othe_small')){
                $this->error('代儲值金額不能少於'.Config::get('site.othe_small').'元人民幣');
            }
            if($money > Config::get('site.othe_max')){
                $this->error('代儲值單筆金額不能超過'.Config::get('site.othe_max').'元人民幣');
            }
            $this->storedPiblic('6',url('daifu/other_success'));
        }
        $id = $this->request->get('id');
        /* 获取订单详情 */
        $order = MemberModel::getOrder($id);
        $this->view->assign('order',$order);
        return $this->view->fetch('daifu/other_success');
    }
    // 淘币提交成功
    public function charge_success()
    {

        if($this->request->isPost()){
            $money = $this->request->post('money');
            if($money < Config::get('site.f_samll')){
                $this->error('代儲值金額不能少於'.Config::get('site.f_samll').'元人民幣');
            }
            if($money > Config::get('site.f_max')){
                $this->error('代儲值單筆金額不能超過'.Config::get('site.f_max').'元人民幣');
            }
            $this->storedPiblic('7',url('daifu/charge_success'));
        }
        $id = $this->request->get('id');
        /* 获取订单详情 */
        $order = MemberModel::getOrder($id);
        $this->view->assign('order',$order);
        return $this->view->fetch('daifu/charge_success');
    }
    // usdt-我要买
    public function usdt()
    {
        if(Config::get('site.usdt_maintain')){
            $this->error(Config::get('site.usdt_text'));
        }
        /* 获取用户银行账号 */
        $userbank = MemberModel::getUserBank($this->auth->id);
        $invoice = json_decode($this->auth->invoice_json,true);
        $super = MemberModel::superQuotien($this->auth->id);
        $this->view->assign('bi_type',Config::get('site.bi_type'));
        $this->view->assign('invoice_json',$invoice);
        $this->view->assign('userbank',$userbank);
        $this->view->assign('user_id',$this->auth->id);
        $this->view->assign('super',$super);
        return $this->view->fetch('daifu/usdt');
    }
    // usdtout-我要卖
    public function usdtout()
    {
        $userali = MemberModel::getUserAli($this->auth->id);
        $userwx = MemberModel::getUserRgong($this->auth->id);
        /* 获取用户银行账号 */
        $userbank = MemberModel::getUserBank($this->auth->id);
        $invoice = json_decode($this->auth->invoice_json,true);
        $this->view->assign('invoice_json',$invoice);
        $this->view->assign('bi_type',Config::get('site.bi_type'));
        $this->view->assign('userbank',$userbank);
        $this->view->assign('userali',$userali);
        $this->view->assign('userwx',$userwx);
        $this->view->assign('user_id',$this->auth->id);
        return $this->view->fetch('daifu/usdtout');
    }
    // usdt-提交公用
    public function usdtPiblic($type,$url)
    {
        if($this->request->isPost()){
            $money = $this->request->post('money');/*usdt数量*/
            $status = $this->request->post('address_type');/*收币类型*/
            $address = $this->request->post('address');/*收币地址*/
            $pay_type = $this->request->post('pay_type');/*付款方式*/
            $bank_id = $this->request->post('bank_id');/*银行卡id*/
            $fp_type = $this->request->post('fp_type');
            $bank_number = $this->request->post('bank_number');
            $number = $this->request->post('number');
            $fp_title = $this->request->post('fp_title');
            $fp_email = $this->request->post('fp_email');
            $ali_account = $this->request->post('ali_account');
            $wx_account = $this->request->post('wx_account');
            /* 开启事务 */
            Db::startTrans();
            try {
                $site_address = "";
                if($type == '0'){
                    $usdt = Config::get('site.usdt');
                }else{
                    $usdt = Config::get('site.usdt_out');
                    $money_address = Config::get('site.money_address');
                    $site_address = $money_address[$status];
                    /* if($status == '1'){
                        $site_address = Config::get('site.address');
                    }else{
                        $site_address = Config::get('site.usdt_erc20');
                    } */
                }
                $site_usdt = Config::get('site.site_usdt');
                /* 计算盈利*/
                $tb_money = $money * $usdt;
                $site_money = $money * $site_usdt;
                $profit_money = $tb_money - $site_money;
                $service = Config::get('site.charge');
                /* 绑定银行 */
                $bank = "";
                if($bank_id){
                    $bank = DaifuModel::bondingBank($bank_id,$this->auth->is_bank,'1');
                }
                /* 台币总额	*/
                if($type){
                    $actual_money = $tb_money - $service;
                }else{
                    $actual_money = $tb_money + $service;
                }
                $rmb_money = '0';
                if($type){
                    $rmb_money = $actual_money/Config::get('site.virtual_rate');
                }
                /* 我要卖，钱到微信*/
                if($wx_account && $type && $pay_type == '3'){
                    $wxpay = MemberModel::getUserOne('user_artificial',$wx_account);
                    $stored_account = $wxpay['name'];
                    $wechat_openid = $wxpay['ali_account'];
                }
                /* 我要卖，钱到支付宝*/
                if($ali_account && $type && $pay_type == '2'){
                    $wxpay = MemberModel::getUserOne('user_alipay',$ali_account);
                    $stored_account = $wxpay['ali_account'];
                    $wechat_openid = $wxpay['name'];
                }
                /* 存储订单数据 */
                $data = array(
                    'type' => $type,
                    'currency_type' => $status,
                    'order_no' => DaifuModel::orderNo('11'),
                    'tb_money' => $tb_money,
                    'rmb_money' => $rmb_money,
                    'number' => $money,
                    'actual_money' => $actual_money,
                    'service' => $service,
                    'exchange' => $usdt,
                    'site_exchange' => Config::get('site.site_usdt'),
                    'site_address' => $site_address,
                    'profit_money' => $profit_money,
                    'pay_status' => $pay_type,
                    'bank_id' => $bank_id,
                    'address' => $address,
                    'bank_num' => $bank,
                    'user_id' => $this->auth->id,
                    'alipay_account' => empty($stored_account)?'':$stored_account,
                    'wechat_account' => empty($wechat_openid)?'':$wechat_openid,
                    'account' => empty($account)?'':$account,
                    'invoice_id' => &$invoice,
                    'createtime' => time(),
                    'updatetime' => time(),
                );
                $invoice = DaifuModel::invoice($fp_type,$number,$fp_title,$fp_email,$data['service'],$this->auth->id,$data['order_no'],$pay_type);
                /* 存储订单 */
                $result = Db::name('virtual_order')->insertGetId($data);
                $this->virtualLog('创建订单',$result);
                Db::commit();
                if($pay_type == '2' && $type == '0'){
                    DaifuModel::cs_virtual_pay($data);
                }
                if($pay_type == '4' && $type == '0'){
                    $card_url = DaifuModel::usdt_card_pay($data,$result);
                    $this->success(__('Just a moment, please. Credit card payment is jumping'),$card_url);
                }
                $this->success(__('Order Creation Success'),$url.'?id='.$result);
            }catch (Exception $e) {
                $this->error(__('Network exception, please refresh and try again'));
                Db::rollback();
            }
        }else{
            $this->error(__('Network exception, please refresh and try again'));
        }
    }
    /*
    *	usdt 我要买提交成功
    */
    public function usdt_success(){
        if($this->request->isPost()){
            $map = array(
                'user_id' => $this->auth->id,
                'pay_type' => '0',
                'order_status' => '0',
            );
            $info = Db::name('virtual_order')->where($map)->find();
            if($info){
                $this->error('您有一筆未支付訂單，請先支付或取消後在下單');
            }
            $money = $this->request->post('money');
            if($money < Config::get('site.buy_number')){
                $this->error(__('The minimum purchase quantity is'.Config::get('site.buy_number')));
            }
            $this->usdtPiblic('0',url('daifu/usdt_success'));
        }
        $id = $this->request->get('id');
        /* 获取订单详情 */
        $order = MemberModel::getVirtualOrder($id);
        $this->view->assign('bi_type',Config::get('site.bi_type'));
        $this->view->assign('order',$order);
        return $this->view->fetch('daifu/usdt_success');
    }
    /*
    *	usdt 我要卖提交成功
    */
    public function usdtout_success(){
        if($this->request->isPost()){
            $money = $this->request->post('money');
            if($money < Config::get('site.sell_number')){
                $this->error(__('The minimum quantity for sale is'.Config::get('site.sell_number')));
            }
            $this->usdtPiblic('1',url('daifu/usdt_success'));
        }
        $id = $this->request->get('id');
        /* 获取订单详情 */
        $order = MemberModel::getVirtualOrder($id);
        $this->view->assign('order',$order);
        $this->view->assign('bi_type',Config::get('site.bi_type'));
        return $this->view->fetch('daifu/usdt_success');
    }
    /*
    *	提交交易id
    */
    public function sub_id(){
        $id = $this->request->post('id');
        $transaction_id = $this->request->post('transaction_id');
        if(Db::name('virtual_order')->where('id',$id)->update(['transaction_id'=>$transaction_id,'order_status'=>'1'])){
            $this->success(__('Submit successfully'));
        }else{
            $this->error(__('Failure to submit'));
        }
    }
    // 申请超商
    public function apply_shop()
    {
        if($this->request->isPost()){
            $content = $this->request->post('content');
            $data = array(
                'user_id' => $this->auth->id,
                'apply_text' => $content,
                'createtime' => time(),
                'updatetime' => time(),
            );
            $info = Db::name('super_quotient')->where('type','0')->find();
            if($info){
                $this->error(__('Please do not submit again during the audit'));
            }
            if(Db::name('super_quotient')->insert($data)){
                $this->success(__('Submit successfully'));
            }else{
                $this->error(__('Failure to submit'));
            }
        }
        $find = Db::name('super_quotient')
            ->field('*')
            ->where('user_id', $this->auth->id)
            ->find();
        $this->view->assign('find',$find);
        if(is_mobile()){
            return $this->view->fetch('daifu/m_apply_shop');
        }else{
            $this->view->engine->layout('layout/empty');
            return $this->view->fetch('daifu/apply_shop');
        }

    }
    // 手机验证
    public function checkMobile()
    {
        if ($this->request->isPost()) {
            Session::set('fbi','1');
            $mobile = $this->request->post('mobile');
            $code = $this->request->post('code');
            /* 验证验证码是否正确 */
            $return = $this->ruleCode($mobile,$code,2);
            if($return == false){
                $this->error(__($this->tips(2)));
            }else{
                $this->success(__('Verify success'));
            }
        }
        $userinfo = $this->auth->getUserinfo();
        $this->view->assign('userinfo',$userinfo);
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch('daifu/check_mobile');
    }
    // wap 端 确认支付
    // 所有确认合在一起了，区别请看注释
    public function confirm_index()
    {
        if($this->request->isPost()){
            $map = $this->request->post();
            $canuse_gwj = DaifuModel::goldBuy($map['money'],$this->auth->id);
            Session::set('good_array',$map);
            Session::set('canuse_gwj',$canuse_gwj);
            if(Session::get('df_type') <= '2' || Session::get('df_type') == '17'){
                $this->success(__('Submitted successfully page skipping'),url('daifu/confirm_indexs'));
            }else{
                $this->success(__('Submitted successfully page skipping'),url('daifu/cz_confirm_indexs'));
            }
        }
        $this->error(__('Network connection failed, please refresh and try again'));
    }
    /* 手机代付共用跳转 */
    public function confirm_indexs()
    {
        if($this->request->isPost()){
            $this->dfPiblic(Session::get('df_type'),url('index/user/order_detail'));
        }
        $good_array = Session::get('good_array');
        /* 获取需要代付的商品 */
        if(Session::get('df_type') == '2'){
            /* 吱口令 */
            $alipay_order_id = array_unique(explode(',',$good_array['goods_ids']));
            $map['id'] = array('in',$alipay_order_id);
            $product = DaifuModel::getzklProduct($map);
            /*新改-********，软件无法在商品表插入价格*/
            $info = Db::name('zkl')->where('id',$product[0]['zkl_id'])->find();
            $all_price = $info['price'];
            /*$price = '0';
            foreach($product as $ls){
                $price = $ls['price']+$price;
            }
            $all_price = $price;*/
        }else{
            /* 代付商品 */
            $alipay_order_id = array_unique(explode(',',$good_array['goods_ids']));
            $map['alipay_order_id'] = array('in',$alipay_order_id);
            $product = DaifuModel::getDfProduct($map);
            $all_price = $product['all_price'];
        }
        $exchange = DaifuModel::getExchange($this->auth->id,Session::get('df_type'),$all_price,'1');
        $this->view->assign('money',$exchange['money']);
        $this->view->assign('bank_type','3');
        $this->view->assign('all_price',$all_price);
        $this->view->assign('fbi',Session::get('fbi'));
        $this->view->assign('df_type',Session::get('df_type'));
        $this->view->assign('currency',$good_array);
        return $this->view->fetch('daifu/m_confirm_index');
    }
    /* 手机储值共用跳转 */
    public function cz_confirm_indexs()
    {
        if($this->request->isPost()){
            $this->storedPiblic(Session::get('df_type'),url('index/user/order_detail'));
        }
        $good_array = Session::get('good_array');
        switch(Session::get('df_type')){
            case 3:
                $small_money = Config::get('site.alipay_small');
                $max_money = Config::get('site.alipay_max');
                $_type = '1';
                break;
            case 4:
                $small_money = Config::get('site.wx_small');
                $max_money = Config::get('site.wx_max');
                $_type = '1';
                break;
            case 6:
                $small_money = Config::get('site.othe_small');
                $max_money = Config::get('site.othe_max');
                $_type = '1';
                break;
            case 7:
                $small_money = Config::get('site.f_samll');
                $max_money = Config::get('site.f_max');
                $_type = '1';
                break;
            default:
                $_type = '0';
                ;
        }
        if($_type){
            if($good_array['money'] < $small_money){
                $this->error('代儲值金額不能少於'.$small_money.'元人民幣');
            }
            if($good_array['money'] > $max_money){
                $this->error('代儲值單筆金額不能超過'.$max_money.'元人民幣');
            }
        }
        /* 获取需要代付的商品 */
        $exchange = DaifuModel::getExchange($this->auth->id,Session::get('df_type'),$good_array['money'],'1');
        $this->view->assign('currency',$good_array);
        $this->view->assign('canuse_gwj',Session::get('canuse_gwj'));
        $this->view->assign('money',$exchange['money']);
        $this->view->assign('bank_type','0');
        $this->view->assign('fbi','0');
        $this->view->assign('df_type',Session::get('df_type'));
        return $this->view->fetch('daifu/m_confirm_index');
    }
    // wap 选择支付宝账号

    public function select_alipay()
    {
        /* 获取用户支付宝账号 */
        $userali = MemberModel::getUserAli($this->auth->id);
        $this->view->assign('userali',$userali);
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch('daifu/m_select_alipay');
    }
    // wap 选择微信账号

    public function select_wechat()
    {
        /* 获取用户支付宝账号 */
        $userali = MemberModel::getUserWx($this->auth->id);
        $this->view->assign('userali',$userali);
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch('daifu/m_select_wechat');
    }
    // wap 选择微信账号

    public function select_artificial()
    {
        /* 获取用户支付宝账号 */
        $userali = MemberModel::getUserRgong($this->auth->id);
        $this->view->assign('userali',$userali);
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch('daifu/m_select_artificial');
    }
    // wap 选择银行

    public function select_bank()
    {
        $type = request()->param('type');
        if($type){
            $url = url('daifu/new_confirm_indexs');
        }else{
            $url = url('daifu/cz_confirm_indexs');
        }
        /* 获取用户银行账号 */
        $userbank = MemberModel::getUserBank($this->auth->id);
        $this->view->assign('userbank',$userbank);
        $this->view->assign('form_url',$url);
        return $this->view->fetch('daifu/select_bank');
    }
    /* 取消代码 */
    public function cancelDf(){
        if($this->request->isPost()){
            $id =$this->request->post('id');
            $set_type =$this->request->param('set_type');
            if($set_type){
                $set_info = Db::name('special')->where('key_id',$set_type)->find();
                $table = $set_info['table_name'];
            }else{
                $table = "df_order";
            }
            $order = MemberModel::getOrder($id,$table);
            /* 未支付 */
            if($order['order']['pay_type'] == '0' && $order['order']['order_status'] == '0'){
                Db::startTrans();
                try {
                    /* 购物金退还 */
                    if(!empty($order['order']['gold_money'])){
                        $this->goldLog('12',$order['order']['gold_money'],$order['order']['id']);
                    }
                    /* 淘币退还 */
                    if($order['order']['balance_money']){
                        $this->taobiLog('12',$order['order']['balance_money'],$order['order']['id'],'','',$table);
                    }
                    /* 更改代付商品状态 */
                    if($order['order']['type'] == '0' || $order['order']['type'] == '1'){
                        $_result = Db::name('alipay_order')->where('order_id', $order['order']['id'])->update(['status' => "0"]);
                    }else{
                        $_result = '1';
                    }
                    /* 更改代付吱口令商品状态 */
                    if($order['order']['type'] == '2'){
                        $z_result = Db::name('zkl')->where('order_id', $order['order']['id'])->update(['type' => "2"]);
                    }else{
                        $z_result = '1';
                    }
                    $result = Db::name($table)->where('id', $order['order']['id'])->update(['order_status' => "6"]);
                    $order_bank_matching = Db::name('order_bank_matching')->where('id',$order['order']['id'])->delete();
                    $this->orderLog('用户取消',$order['order']['id'],$table);
                    if($_result && $result && $z_result && $order_bank_matching){
                        Db::commit();
                        $this->success(__('Submit successfully'),url('user/index'));
                    }else{
                        Db::rollback();
                        $this->error(__('Failure to submit'));
                    }
                }catch (Exception $e) {
                    Db::rollback();
                    $this->error(__('Failure to submit'));
                }
            }else{
                $this->error(__('Order is locked. Please contact customer service if you need to cancel'));
            }
        }
        $this->error(__('Network connection failed, please refresh and try again'));
    }
    /* 关闭新手引导 */
    public function closeTip(){
        $type = $this->request->post('type');
        $data =array(
            'user_id' => $this->auth->id,
            'type' => $type,
            'tip_type' => '1',
            'createtime' => time(),
            'updatetime' => time(),
        );
        Db::name('show_tip')->insert($data);
    }
    public function user_set($type){
        $set = Db::name('user_set')->where('user_id',$this->auth->id)->find();
        if($set){
            switch($type){
                case 0:
                    if(!$set['ali']){
                        $this->error('當前功能不可使用，如需使用請聯系客服');
                    }
                    break;
                case 1:
                    if(!$set['taobao']){
                        $this->error('當前功能不可使用，如需使用請聯系客服');
                    }
                    break;
                case 2:
                    if(!$set['zhikouling']){
                        $this->error('當前功能不可使用，如需使用請聯系客服');
                    }
                    break;
                case 3:
                    if(!$set['alipay_maintain']){
                        $this->error('當前功能不可使用，如需使用請聯系客服');
                    }
                    break;
                case 4:
                    if(!$set['wechatpay']){
                        $this->error('當前功能不可使用，如需使用請聯系客服');
                    }
                    break;
                case 6:
                    if(!$set['other']){
                        $this->error('當前功能不可使用，如需使用請聯系客服');
                    }
                    break;
                case 7:
                    if(!$set['charge_maintain']){
                        $this->error('當前功能不可使用，如需使用請聯系客服');
                    }
                    break;
            }
        }
    }
    /* 有未处理订单拦截 */
    public function untreate($type = '',$table='df_order'){
        $this->view->assign('set_type',$type);
        $map = array(
            'user_id' => $this->auth->id,
            'order_status' => array('in',array('0','2','3','5')),
        );
        $_special = Db::name('special')->where('key_id',$type)->find();
        $df_order = Db::name('df_order')->field('*')->where($map)->find();
        $order_game = Db::name('order_game')->field('*')->where($map)->find();
        $order_live = Db::name('order_live')->field('*')->where($map)->find();
        $order_new_df = Db::name('order_new_df')->field('*')->where($map)->find();
        $order_other = Db::name('order_other')->field('*')->where($map)->find();
        if($df_order){
            $special = Db::name('special')->where('key_id',$df_order['type'])->find();
        }
        if($order_game){
            $special = Db::name('special')->where('key_id',$order_game['type'])->find();
        }
        if($order_live){
            $special = Db::name('special')->where('key_id',$order_live['type'])->find();
        }
        if($order_new_df){
            $special = Db::name('special')->where('key_id',$order_new_df['type'])->find();
        }
        if($order_other){
            $special = Db::name('special')->where('key_id',$order_other['type'])->find();
        }
        if($df_order || $order_game || $order_live || $order_new_df || $order_other){
            if(is_mobile()){
                $url = url('user/order_detail');
            }else{
                switch($special['key_id']){
                    case 0:
                        $url = url('daifu/indexSuccess');
                        break;
                    case 1:
                        $url = url('daifu/taobaoSuccess');
                        break;
                    case 2:
                        $url = url('daifu/indexSuccess');
                        break;
                    case 3:
                        $url = url('daifu/alipay_success');
                        break;
                    case 4:
                        $url = url('daifu/wechatpay_success');
                        break;
                    case 5:
                        $url = url('daifu/game_success');
                        break;
                    case 6:
                        $url = url('daifu/other_success');
                        break;
                    case 7:
                        $url = url('daifu/charge_success');
                        break;
                    case 17:
                        $url = url('daifu/douyin_success');
                        break;
                    default:
                        $url = url('daifu/liveSuccess');
                }
            }
            if($df_order){
                $this->error('您有一筆等待處理的'.$special['name'].'訂單，請先處理',$url.'?id='.$df_order['id'].'&set_type='.$df_order['type']);
            }
            if($order_game){
                $this->error('您有一筆等待處理的'.$special['name'].'訂單，請先處理',$url.'?id='.$order_game['id'].'&set_type='.$order_game['type']);
            }
            if($order_live){
                $this->error('您有一筆等待處理的'.$special['name'].'訂單，請先處理',$url.'?id='.$order_live['id'].'&set_type='.$order_live['type']);
            }
            if($order_new_df){
                $this->error('您有一筆等待處理的'.$special['name'].'訂單，請先處理',$url.'?id='.$order_new_df['id'].'&set_type='.$order_new_df['type']);
            }
            if($order_other){
                $this->error('您有一筆等待處理的'.$special['name'].'訂單，請先處理',$url.'?id='.$order_other['id'].'&set_type='.$order_other['type']);
            }
        }
    }
    /*菜单配置信息*/
    public function set_nav($set_type){
        /*获取导航*/
        $nav = Db::name('special')->where('status','normal')->select();
        $one_nav = $two_nav = [];
        foreach($nav as $key=>$ls){
            if($ls['pid'] == '0'){
                $one_nav[$key] = $ls;
            }else{
                $two_nav[$ls['pid']][$key] = $ls;
            }
            //if($ls['key_id'] == '7'){
            //if($this->auth->level < '3'){
            //	unset($two_nav[$ls['pid']][$key]);
            //}
            //}

        }
        /*获取菜单配置信息*/
        $set_info = Db::name('special')->where('key_id',$set_type)->find();
        /*判断是否开启维护*/
        if($set_info['is_maintain']){
            $this->error($set_info['maintain_text']);
        }
        $set_one = Db::name('set_special_one')->where('key_id',$set_type)->find();
        $set_two = Db::name('set_special_two')->where('key_id',$set_type)->find();
        $this->view->assign('set_one',$set_one);
        $this->view->assign('set_two',$set_two);
        /*菜单信息*/
        $this->view->assign('set_info',$set_info);
        /*一级菜单*/
        $this->view->assign('one_nav',array_reverse($one_nav));
        /*二级菜单*/
        $this->view->assign('two_nav',$two_nav);
        $this->view->assign('set_type',$set_type);
    }
    /*公用配置信息*/
    public function common_configuration($set_type){
        /* 获取用户银行账号 */
        $userbank = MemberModel::getUserBank($this->auth->id);
        /* 获取初始汇率 */
        $exchange = DaifuModel::getExchange($this->auth->id,$set_type,'0','1');
        /*发票配置信息*/
        $invoice = json_decode($this->auth->invoice_json,true);
        /*获取超商信息*/
        $super = MemberModel::superQuotien($this->auth->id);
        $this->view->assign('super',$super);
        $this->view->assign('exchange',$exchange);
        $this->view->assign('userbank',$userbank);
        $this->view->assign('invoice_json',$invoice);
    }
    /*老业务配置信息*/
    public function old_common_configuration($set_type){
        /*有未处理订单拦截*/
        $this->untreate($set_type);
        /*检测用户是否关闭该功能*/
        $this->user_set($set_type);
        Session::set('df_type',$set_type);

        /*新人提示*/
        $show_tip = MemberModel::getShowTip($set_type,$this->auth->id);
        $this->view->assign('set_type',$set_type);
        $this->view->assign('df_type',$set_type);
        $this->view->assign('show_tip',$show_tip);
    }
    // 手机导航
    public function nav(){
        return $this->view->fetch('daifu/nav');
    }
    // 吱口令
    //public function zhi()
    //{
    //	/* 获取平台支付宝账号 */
    //	$sysalipay = DaifuModel::getSysAlipay();
    //	/* 获取用户银行账号 */
    //	$userbank = MemberModel::getUserBank($this->auth->id);
    //	$show_tip = MemberModel::getShowTip('0',$this->auth->id);
    //	$invoice = json_decode($this->auth->invoice_json,true);
    //	$type = request()->param('type');
    //	$this->view->assign('invoice_json',$invoice);
    //	$this->view->assign('show_tip',$show_tip);
    //	$this->view->assign('userbank',$userbank);
    //	$this->view->assign('type',$type);
    //	$this->view->assign('sysalipay',$sysalipay);
    //    return $this->view->fetch('daifu/zhi');
    //}
}
