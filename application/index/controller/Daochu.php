<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use think\Config;
use think\Db;

class Daochu extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = 'default';

	

	public function test(){
		$map = array(
			'status' => '5',
			'alipay_status' => '1',
		);
		$result = Db::name('alipay_order')->where($map)->whereTime('update_time', 'between', ['2022-4-1', '2022-4-10'])->select();
		$_result = [];
		foreach($result as $ls){
			$_result[] = array(
				'id' => $ls['id'],
				'peerpay_link' => $ls['peerpay_link'],
				'other_name' => $ls['other_name'],
				'other_account' => $ls['other_account'],
				'trade_no' => $ls['trade_no'],
				'price' => $ls['price'],
				'title' => $ls['title'],
				'update_time' => $ls['update_time'],
			);
		}
		$data['result'] = $_result;
		/*填充表名*/
		$data['sheetName'] = '代付商品';
		$this->export_data($this->excel_config(), $data);
	}
	public function excel_config(){
		$config = array(
			array('title' => 'id', 'field' => 'id','width'=>'10'),
			array('title' => '代付链接', 'field' => 'peerpay_link','width'=>'20'),
			array('title' => '申请人昵称', 'field' => 'other_name','width'=>'10'),
			array('title' => '支付宝账号', 'field' => 'other_account','width'=>'10'),
			array('title' => '交易号', 'field' => 'trade_no','width'=>'20'),
			array('title' => '价格', 'field' => 'price','width'=>'10'),
			array('title' => '商品名称', 'field' => 'title','width'=>'10'),
			array('title' => '代付时间', 'field' => 'update_time','width'=>'10'),
		);
		return $config;
	}

	/**
	 * 实例化PHPExcel对象，并配置文件基本信息
	 * @return PHPExcel对象
	 */
	public function excel_obj_config(){
		vendor("PHPExcel.PHPExcel");
		$objPHPExcel=new \PHPExcel();
		$objPHPExcel->getProperties()
								->setCreator("sunrun")
								->setLastModifiedBy("sunrun")
								->setKeywords("sunrun")
								->setCategory("zstyle");
		return $objPHPExcel;
	}
	/**
	 * 输出Excel表格
	 * @param  [type] $objPHPExcel PHPExcel对象，即数据集
	 * @param  [type] $fileName    导出的文件名
	 * @return [type]
	 */
	public function excel_out($objPHPExcel,$fileName){
		$_type='.xlsx';
		switch ($_type) {
			case '5':
				$_type=".xls";
				$xlsWriter=new \PHPExcel_Writer_Excel5 ($objPHPExcel);
				break;
			case '2007':
				$_type=".xlsx";
				$xlsWriter=new \PHPExcel_Writer_Excel2007 ($objPHPExcel);
				break;
			default:
				$_type=".xls";
				$xlsWriter=new \PHPExcel_Writer_Excel5 ($objPHPExcel);
		}
		$outputFileName = $fileName.$_type;
		header("Content-Type: application/force-download");
		header("Content-Type: application/octet-stream");
		header("Content-Type: application/download");
		header('Content-Disposition:inline;filename="'.$outputFileName.'"');
		header('Cache-Control: max-age=0');
		header("Pragma: no-cache");
		$xlsWriter->save("php://output");
	}
	/**
	 * 导出$data的信息
	 * @param   $[config] [格式：$config = array(
	 *                                array(
	 *                                        'title' => '昵称',
	 *                                        'field' => 'nickname',
	 *                                        'width' => '15', //单元格宽度
	 *                                    ),
	 *                                array(
	 *                                        'title' => '联系方式',
	 *                                        'field' => 'mobile',
	 *                                        'width' => '20' //单元格宽度
	 *                                    ),
	 *
	 *                             )]
	 * @time 2016-9-18
	 * <AUTHOR>
	 */
	public function export_data($config,$data){
		/**总页数集合*/
		$cellName = array('A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z','AA','AB','AC','AD','AE','AF','AG','AH','AI','AJ','AK','AL','AM','AN','AO','AP','AQ','AR','AS','AT','AU','AV','AW','AX','AY','AZ');
		/**导出Excel*/
		$objPHPExcel=$this->excel_obj_config();
		/**设置操作的工作表*/
		//$objPHPExcel->setActiveSheetIndex('0');
		$sheet = $objPHPExcel->getActiveSheet();
		/**设置工作表的名字*/
		$sheet->setTitle($data['sheetName']);

		/**设置工作表的第一栏标题及列宽*/
		for($i = 0; $i< count($config);$i++){
			$sheet->setCellValue($cellName[$i] . '1',$config[$i]['title']);
			$sheet->getColumnDimension($cellName[$i])->setWidth($config[$i]['width']);
		}
		$i = 2;
		foreach($data['result'] as $key => $item){
			/**设置单个元的值*/
			for($j = 0 ; $j< count($config);$j++){
				$sheet->setCellValue($cellName[$j] . $i, $item[$config[$j]['field']]);
			}
			$i++;
		}
		//$objPHPExcel->createSheet();
		$this->excel_out($objPHPExcel,$data['sheetName']);
	}
	/**
	 * 导入excel文件
	 * @param  string $file excel文件路径
	 * @return array        excel文件内容数组
	 */
	public function import_excel($file){
		/*判断文件是什么格式*/
		$type = pathinfo($file);
		$type = strtolower($type["extension"]);
		$type=$type==='csv' ? $type : 'Excel5';
		ini_set('max_execution_time', '0');
		Vendor('PHPExcel.PHPExcel');
		/*判断使用哪种格式*/
		$objReader = PHPExcel_IOFactory::createReader($type);
		$objPHPExcel = $objReader->load($file);
		$sheet = $objPHPExcel->getSheet(0);
		/*取得总行数*/
		$highestRow = $sheet->getHighestRow();
		/*取得总列数*/
		$highestColumn = $sheet->getHighestColumn();
		/*循环读取excel文件,读取一条,插入一条*/
		$data=array();
		/*从第一行开始读取数据*/
		for($j=1;$j<=$highestRow;$j++){
				/*从A列读取数据*/
				for($k='A';$k<=$highestColumn;$k++){
						/*读取单元格*/
						$data[$j][]=$objPHPExcel->getActiveSheet()->getCell("$k$j")->getValue();
				}
		}
		return $data;
	}

}
