<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\CouponModel;

class Goods extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = 'user';

    public function index()
    {
		$keyword = request()->param('keyword')?request()->param('keyword'):'';
		$p = request()->param('p')?request()->param('p'):'1';
		if($keyword){
			$this->view->assign('type', '1');
			$list = CouponModel::seach_product($keyword,$p);
			$tpl = 'sindex';
		}else{
			$this->view->assign('type', '0');
			$list = CouponModel::taobao_api(20,$p);
			$tpl = 'index';
		}
		$this->view->assign('list', $list);
		$this->view->assign('keyword', $keyword);
		$this->view->assign('p', $p);
        return $this->view->fetch($tpl);
    }
	
	public function detail()
	{
		$tpl = 'detail';
		$layout = 'user';
	    return $this->view->fetch($tpl);
	}
	public function buy()
	{
		$this->view->engine->layout('layout/empty');
		return $this->view->fetch('goods/buy');
	}
	
	
    public function lists()
    {
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch();
    }

    
}
