<?php

namespace app\index\controller;
use think\Config;
use think\Db;
use app\common\controller\Frontend;

class Helps extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = 'user';

    public function index()
    {
		$this->get_list();
        return $this->view->fetch();
    }
    public function list()
    {
		$this->get_list();
        return $this->view->fetch();
    }
    public function lists()
    {
		$this->get_list();
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch();
    }
	public function get_list(){
		$set_type = request()->param('set_type')?request()->param('set_type'):'0';
		$type = request()->param('type')?request()->param('type'):'0';
		$set_type = $set_type?$set_type:$type;
		/*获取导航*/
		$nav = Db::name('special')->where('status','normal')->select();
		$one_nav = $two_nav = [];
		foreach($nav as $ls){
			if($ls['pid'] == '0'){
				$one_nav[] = $ls;
			}else{
				$two_nav[$ls['pid']][] = $ls;
			}
			if($ls['key_id'] == $set_type){
				$set_nav = $ls;
			}
		}
		$map = array(
			'type' => $set_type,
			'status' => 'normal',
		);
		$list = Db::name('help')->where($map)->order('createtime desc')->paginate('20');
		/*一级菜单*/
		$this->view->assign('one_nav',$one_nav);
		/*二级菜单*/
		$this->view->assign('two_nav',$two_nav);
		$this->view->assign('list',$list);
		//$this->view->assign('type_title',$type_title);
		$this->view->assign('set_type',$set_type);
		$this->view->assign('set_nav',$set_nav);
	}
    public function detail()
    {
		$set_type = request()->param('set_type')?request()->param('set_type'):'0';
		$id = request()->param('id');
		/*获取导航*/
		$nav = Db::name('special')->where('status','normal')->select();
		$one_nav = $two_nav = [];
		foreach($nav as $ls){
			if($ls['pid'] == '0'){
				$one_nav[] = $ls;
			}else{
				$two_nav[$ls['pid']][] = $ls;
			}
		}
		$map = array(
			'id' => $id,
			'type' => $set_type,
			'status' => 'normal',
		);
		$list = Db::name('help')->where($map)->paginate('20');
		/*一级菜单*/
		$this->view->assign('one_nav',$one_nav);
		/*二级菜单*/
		$this->view->assign('two_nav',$two_nav);
		$info = Db::name('help')->where($map)->find();
		Db::name('help')->where($map)->setInc('view',1);
		$this->view->assign('info',$info);
		$this->view->assign('set_type',$set_type);
        return $this->view->fetch();
    }
	public function help_center(){
		$map = array(
			'status' => 'normal',
		);
		$list = Db::name('site_map')->field('id,type,title')->where($map)->select();
		foreach($list as $ls){
			$data[$ls['type']][] = $ls;
		}
		$sitemap = Config::get('site.sitemap');
		$this->view->assign('sitemap',$sitemap);
		$this->view->assign('list',$data);
		return $this->view->fetch();
	}
	public function help_content(){
		$id = request()->param('id');
		$map = array(
			'id' => $id,
			'status' => 'normal',
		);
		$info = Db::name('site_map')->field('title,maincontent')->where($map)->find();
		/*上一篇*/
		$up_info = Db::name('site_map')->field('id,type,title')->where('id','<',$id)->limit(1)->find();
		/*下一篇*/
		$down_info = Db::name('site_map')->field('id,type,title')->where('id','>',$id)->limit(1)->find();
		$this->view->assign('info',$info);
		$this->view->assign('up_info',$up_info);
		$this->view->assign('down_info',$down_info);
        return $this->view->fetch();
	}
}
