<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\CouponModel;
use think\Config;
use think\Db;
use think\Exception;
use think\Session;
use fast\Random;
use Zxing\QrReader;
use app\common\model\DaifuModel;


class Index extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = 'default';
	
	
	function calculateMatches($numTeams, $x) {
		if ($numTeams < $x) {
			return -1;
		}
		return $numTeams - $x;
	}
	/*99乘法表*/
	public function niceTable(){
		for ($i = 1; $i <= 9; $i++) {
            for ($j = 1; $j <= $i; $j++) {
                echo "$j × $i = " . $i * $j . "&nbsp;&nbsp;&nbsp;";
            }
            echo "<br>";
        }
        die;
	}
	// 已知笼子里有鸡和兔共35只，脚共94只，求鸡兔各多少只？
	public function coinsProblem($heads = '35', $legs = '94') {
		$rabbits = ($legs - 2 * $heads) / 2;
		$chickens = $heads - $rabbits;
		
		if ($rabbits >= 0 && $chickens >= 0 && $rabbits == (int)$rabbits) {
			echo "鸡有".$chickens, "兔有".$rabbits;
		} else {
			echo "无解";
		}
	}
	// 某次考试，班级平均分85分，男生平均分82，女生平均分88，求男女比例
	public function studentRatio($classAvg = '85', $maleAvg = '82', $femaleAvg = '88', $totalStudents = '40') {
		$femaleRatio = ($classAvg - $maleAvg) / ($femaleAvg - $maleAvg);
		$maleRatio = 1 - $femaleRatio;
		echo "男生人数".round($maleRatio * $totalStudents), "女生人数".round($femaleRatio * $totalStudents);
	}
	// 一个水池有A、B两个水管，A管单独注满需6小时，B管单独注满需9小时
	// 两管同时工作，但B管在注水1小时后关闭，求总注满时间
	public function waterPipeProblem($pipeATime = '6', $pipeBTime = '9', $pipeBWorkingHours = '1') {
		$aRate = 1 / $pipeATime;
		$bRate = 1 / $pipeBTime;
		$stage1 = ($aRate + $bRate) * $pipeBWorkingHours;
		$remaining = 1 - $stage1;
		$stage2 = $remaining / $aRate;
		$time  = $pipeBWorkingHours + $stage2;
		echo  "总注满时间: " . $time . " 小时";
	}
	// 父亲今年35岁，儿子7岁，几年后父亲的年龄是儿子的3倍？
	public function ageProblem($fatherAge = '35', $sonAge = '7', $times = '3') {
		$years = ($fatherAge - $times * $sonAge) / ($times - 1);
		if ($years >= 0 && $years == (int)$years) {
			echo "需要 " . $years . " 年后";
		} else {
			echo "无解";
		}
	}
    public function index()
    {
		////$list = CouponModel::taobao_api(4,1); //多余
        $type = is_mobile()?2:1;        //1是PC  2是手机
        $banner = Db::name('user_banner')->where('type',$type)->where('status', 'normal')->select();
		$map = array(
			'status' => 'normal',
			'type' => '0',
		);
		$video = Db::name('video')->where($map)->select();
		$this->view->assign('video',$video);
		$list = array();
		$this->view->assign('list', $list);
        $this->view->assign('banner', $banner);
        return $this->view->fetch();
    }

    /*首页-推荐给好友*/
    public function rmfriends()
    {
        if(is_mobile()){
            $this->view->engine->layout('layout/m_empty');
            return $this->view->fetch('index/m_rmfriends');
        }else{
            $this->view->engine->layout('layout/empty');
            return $this->view->fetch('index/rmfriends');
        }
    }

    /*首页-注册账号送礼*/
    public function reggift()
    {
        $map = [
            'rule_type' => 0,
        ];
        $use_rule = Db::name('use_rule')->where($map)->find();
        $res = Db::name("coupon")->where("id", "in", $use_rule['coupon_id'])->order('reduce_amount', 'asc')->select();
        $this->view->assign('list', $res);
        if(is_mobile()){
            $this->view->engine->layout('layout/m_empty');
            return $this->view->fetch('index/m_reggift');
        }else{
            $this->view->engine->layout('layout/empty');
            return $this->view->fetch('index/reggift');
        }
    }


    /* 消耗购物金获取抽奖次数*/
    public function get_draw_cout(){
        if ($this->request->isPost()) {
            if(!$this->auth->id){
                return json_encode(array("rs"=>false,"msg"=>'你還沒有登錄'));
            }
            // post 多久购物金来替换一次抽奖机会
            $dr = $this->request->param("dr");
            $sub = $this->request->param("sub");
            Db::startTrans();
            try {
                $user_info = Db::name('user')->where("id", $this->auth->id)->find();
                if(($user_info['buy_gold']-$sub) < 0){
                    return json_encode(array("rs"=>false,"msg"=>'你的購物金不足'));
                }
                // 购物金日志 15抽奖消费
                $this->goldLog(15, $sub, 0, "消耗购物金" . $sub . "抽奖");
                // 表draw_permit 插入记录
                Db::name('draw_permit')->insert(['user_id'=>$this->auth->id,'use_type'=>1, 'amount'=>$sub, 'draw_rule_id'=>$dr,'createtime'=>time()]);
                $us_info = Db::name('user')->where("id", $this->auth->id)->find();
                Db::commit();
                return json_encode(array("rs"=>true,"msg"=>'恭喜你獲取了壹次抽獎機會', 'gw'=>$us_info['buy_gold']));
            }catch (Exception $e) {
                Db::rollback();
                return json_encode(array("rs"=>false,"msg"=>'網絡异常'));
            }
        }
    }

    /*首页-抽奖*/
    public function jdraw()
    {
        if ($this->request->isPost()) {
            if(!$this->auth->id){
                return json_encode(array("rs"=>0,"msg"=>'你還沒有登錄'));
            }
            $dr = $this->request->param("dr");
            if(!isset($dr)){
                return json_encode(array("rs"=>0,"msg"=>'錯誤的數據'));
            }
            $isfree = true;
            $dr_rule = Db::name('draw_rule')->where("id", $dr)->find();
            if(time() < $dr_rule['begtime']){
                return json_encode(array("rs"=>0,"msg"=>"活动尚未开始，敬请期待"));
            }else if(time() >= $dr_rule['endtime']){
                return json_encode(array("rs"=>0,"msg"=>"活動已經結束，請關注其他精彩內容"));
            }

            Db::startTrans();
            try {
                $user_info = Db::name('user')->where('id', $this->auth->id)->find();
                // 判断用户每日抽奖权限
                $lu_data = Db::name('luck_user')->field("luck_type,COUNT(*) as count")->where("user_id", $this->auth->id)
                    ->whereTime('createtime', 'today')->group("luck_type")->select();

                // 获取user_coupon中系统每日白送的记录
                $uc_column = array_column($lu_data, "luck_type");
                $mr_index = array_search(1, $uc_column);    //1 是免费送类型  3 购物金

                $dp_data = Db::name('draw_permit')->field("use_type, COUNT(*) as count")->where("user_id", $this->auth->id)
                    ->where("draw_rule_id", $dr)->whereTime('createtime', 'today')->group("use_type")->select();
                // 获取购物金得来的抽奖次数,use_type:1, 目前只有1个购物金购买可以获取抽奖次数
                $useb = $dr_rule['useb'];
                $dp_column = array_column($dp_data, "use_type"); // 获取所有key值
                $dp_index = array_search($useb, $dp_column); // 查找使用什么类型获取到的记录

                $rd_count = 0;
                foreach ($lu_data as $row) {
                    $rd_count += $row['count'];
                }
                $freec = $dr_rule['freec'];
                $pm_count = $dr_rule['egc'];
                // 不免费抽奖所需要的金额
                $sub = $dr_rule['money'];
                if($freec <= 0){
                    if($dp_index === false){
                        return json_encode(array("rs"=>2, "gw"=>$user_info['buy_gold'],'sub'=>$sub,"msg"=>"你今日的抽獎次數已經用完，還可以使用購物金獲取" . $pm_count . "次。"));
                    }
                    // 获取购物金已经抽奖的记录
                    $gw_count = 0;
                    $uc_column = array_column($lu_data, "luck_type");
                    $gw_index = array_search(3, $uc_column);        // 3 购物金抽取
                    if($gw_index !== false){
                        $gw_count = $lu_data[$gw_index]['count'];
                    }
                    if( $dp_data[$dp_index]['count'] === $gw_count ){
                        $num = $pm_count - $dp_data[$dp_index]['count'];
                        if($num <= 0){
                            return json_encode(array("rs"=>0,"msg"=>"  你今日的抽獎次數已經用完，請明日再來"));
                        }else{
                            return json_encode(array("rs"=>2,"gw"=>$user_info['buy_gold'],'sub'=>$sub,"msg"=>"你今日的抽獎次數已經用完，還可以使用購物金獲取" . $num . "次。"));
                        }
                    }
                }else{
                    if($mr_index !== false){
                        if($rd_count >= $freec){
                            if($dp_index === false){
                                return json_encode(array("rs"=>2, "gw"=>$user_info['buy_gold'],'sub'=>$sub,"msg"=>"你今日的抽獎次數已經用完，還可以使用購物金獲取" . $pm_count . "次。"));
                            }
                            // 获取购物金已经抽奖的记录
                            $gw_count = 0;
                            $uc_column = array_column($lu_data, "luck_type");
                            $gw_index = array_search(3, $uc_column);    // 3为购物金抽奖
                            if($gw_index !== false){
                                $gw_count = $lu_data[$gw_index]['count'];
                            }
                            if( $dp_data[$dp_index]['count'] === $gw_count ){
                                $num = $pm_count - $dp_data[$dp_index]['count'];
                                if($num <= 0){
                                    return json_encode(array("rs"=>0,"msg"=>"  你今日的抽獎次數已經用完，請明日再來"));
                                }else{
                                    return json_encode(array("rs"=>2,"gw"=>$user_info['buy_gold'],'sub'=>$sub,"msg"=>"你今日的抽獎次數已經用完，還可以使用購物金獲取" . $num . "次。"));
                                }
                            }
                        }
                    }
                }
                if( $rd_count >= $freec){
                    $isfree = false;
                }

                $_proArr =  Db::name('luck_draw')->where("id", "in", explode(",", $dr_rule['draw_id']))->order('id asc')->select();
                /*id当下标*/
                foreach($_proArr as $ls){
                    $proArr[$ls['id']] = $ls;
                }
                /*
                 * 每次前端页面的请求，PHP循环奖项设置数组，
                 * 通过概率计算函数get_rand获取抽中的奖项id。
                 * 将中奖奖品保存在数组$res['yes']中，
                 * 而剩下的未中奖的信息保存在$res['no']中，
                 * 最后输出json个数数据给前端页面。
                 */
                $arr = [];
                $arr_max = array("probability"=>0,"data" =>array());
                $arr_min = array("probability"=>0,"data" =>array());
                foreach ($proArr as $key => $val) {
                    $arr[$val['id']] = $val['probability'];
                    if($val['probability'] >= $arr_max["probability"]){
                        $arr_max["data"] = $val;
                        $arr_max["probability"] = $val['probability'];
                    }
                    if($val['probability'] <= $arr_min["probability"]){
                        $arr_min["data"] = $val;
                        $arr_min["probability"] = $val['probability'];
                    }
                }
                $rid = $this->get_rand_num_ex($arr); //根据概率获取奖项id
                $prize = $proArr[$rid];
                //如果抽到概率最小的，也换成概率大的
                if($prize["id"] == $arr_min["data"]["id"]){
                    $prize = $arr_max["data"];
                }
                $freight_status = '1';
                if($prize['type'] == '0'){
                    $freight_status = '0';
                }
                $data = array(
                    'order_no' => 'CJ'.Random::numeric(16),
                    'user_id' => $this->auth->id,
                    'username' => $this->auth->username,
                    'mobile' => $this->auth->mobile,
                    'title' => $prize['title'],
                    'type' => $prize['type'],
                    'money' => $prize['money'],
                    'freight_status' => $freight_status,
                    'luck_type' => $isfree?1:3,
                    'dr_id' => $dr,
                    'createtime' => time(),
                    'updatetime' => time(),
                );
                //如果type  1加淘币，3加优惠券
                if($prize['type'] == 1){
                    $this->taobiLog('8',$prize['money'],'','抽獎獲得');
                }else if($prize['type'] == 2){
                    $this->goldLog(8, $prize['money'], "", '抽獎獲得');
                }else if($prize['type'] == 3){
                    $coupon = Db::name('coupon')->where('id',$prize['coupon_id'])->find();
                    $use_coupon = [
                        'coupon_id' => $coupon['id'],
                        'title' => $coupon['title']	,
                        'reaching_amount' => $coupon['reaching_amount'],
                        'reduce_amount' => $coupon['reduce_amount'],
                        'start_time' => time(),
                        'end_time' => strtotime("+{$coupon['use_num']} days"),
                        'is_use' => '0',
                        'createtime' => time(),
                        'updatetime' => time(),
                        'user_id' => $this->auth->id,
                    ];
                    Db::name('coupon')->where('id', $coupon['id'])->setInc('issued_num');
                    Db::name('user_coupon')->insert($use_coupon);
                }
                $luck = Db::name('luck_user')->insertGetId($data);
                //  这句代码有什么哦用   ？？？？？？？？？？？？？？？？？？？？？？？？
                Session::set('luck', $luck);
                Db::commit();
                return json_encode(array("rs"=>1,"id"=>$prize['id']));
                //Db::name('debugging')->insert(['title' => '点击测试', 'msg' => date("Y-m-d H:i:s") . '9', 'add_time'=>time()]);
            }catch (Exception $e) {
                Db::rollback();
                return json_encode(array("rs"=>0,"msg"=>'網絡异常'));
            }
        }
        // ID是0的规则抽奖活动
        $draw_rule_index = 1;
        $dr_data = Db::name('draw_rule')->where("id", $draw_rule_index)->find();
        $goods_list =  Db::name("luck_draw")->where("id", "in", explode(",", $dr_data['draw_id']))->order('id asc')->select();
        $rs_data = Db::name('luck_user')->field('username,title,money')->whereTime('createtime', 'today')->where('dr_id', $draw_rule_index)->order('id desc')->limit(20)->select();
        if(count($rs_data) <= 5){
            $rs_data = Db::name('luck_user')->field('username,title,money')->where('dr_id', $draw_rule_index)->order('id desc')->limit(20)->select();
        }
        $this->view->assign('dr', $dr_data);
        $this->view->assign('list', $goods_list);
        $this->view->assign('ulist', $rs_data);
        if(is_mobile()){
            $this->view->engine->layout('layout/m_empty');
            return $this->view->fetch('index/m_jdraw');
        }else{
            $this->view->engine->layout('layout/empty');
            return $this->view->fetch('index/jdraw');
        }
    }

    /*首页-提示支付减免*/
    public function ptip()
    {
        if(is_mobile()){
            $this->view->engine->layout('layout/m_empty');
            return $this->view->fetch('index/m_ptip');
        }else{
            $this->view->engine->layout('layout/empty');
            return $this->view->fetch('index/ptip');
        }
    }



    public function news()
    {
        $newslist = [];
        return jsonp(['newslist' => $newslist, 'new' => count($newslist), 'url' => 'https://www.baidu.com']);
    }
	/*抽奖*/
	public function luck(){
		if(!Config::get('site.luck_open')){
			$this->error('抽獎的人太多了，我們馬上把工程師小哥哥殺了祭天');
		}
		if(!$this->auth->id){
			$this->error('登入過期，請重新登入');
		}
		$user_info = Db::name('user')->where('id',$this->auth->id)->find();
		Db::startTrans();
		try {
			$map = array(
				'user_id' => $this->auth->id,
				'day_time' => date('Ymd',time()),
			);
			$info = Db::name('luck_num')->where($map)->find();
			/*获取用户完成订单待使用次数*/
			$surplus_num = $info['complete_num'] - $info['use_num'];
			if($info['use_num'] >= Config::get('site.luck_open')){
				$this->error('今日抽獎已達上限，請明日再來');
			}
			/*判断用户是否拥有系统赠送的次数*/
			$luck_type = '0';
			if($info['sys_num']){
				/*扣除赠送次数*/
				$sys_num = $info['sys_num'] - 1;
				Db::name('luck_num')->where($map)->update(['sys_num'=>$sys_num]);
				$luck_type = '1';
			}else{
				if($surplus_num){
					/*扣除订单次数*/
					$use_num = $info['use_num'] + 1;
					Db::name('luck_num')->where($map)->update(['use_num'=>$use_num]);
					$luck_type = '2';
				}else{
					if($info['gold_num'] < Config::get('site.luck_open')){
						/*订单待使用次数已达上限使用抵扣金*/
						/*判断用户抵扣金是否足够*/
						if($user_info['buy_gold'] >= '100'){
							/*扣除抵扣金并抽奖*/
							$this->goldLog('9','100','','抽獎扣除',$user_info);
							/*扣除订单次数*/
							$gold_num = $info['gold_num'] + 1;
							if($info){
								Db::name('luck_num')->where($map)->update(['gold_num'=>$gold_num]);
							}else{
								$luck_data = array(
									'user_id' => $this->auth->id,
									'gold_num' => $gold_num,
									'day_time' => date('Ymd',time()),
									'createtime' => time(),
									'updatetime' => time()
								);
								Db::name('luck_num')->insert($luck_data);
							}
							$luck_type = '3';
						}else{
							$this->error('您的抵扣金餘額不足');
						}
					}else{
						$this->error('今日抽獎已達上限，請明日再來');
					}
				}
			}
			$return = $this->luck_draw($user_info,$luck_type);
			Db::commit();
			return $return;
		}catch (Exception $e) {
			Db::rollback();
			$this->error('網絡异常');
		}
	}
	/*抽奖*/
	public function luck_draw($info,$luck_type){
		$_proArr = Db::name('luck_draw')->order('id desc')->select();
		/*id当下标*/
		foreach($_proArr as $ls){
			$proArr[$ls['id']] = $ls;
		}
		/*
		 * 每次前端页面的请求，PHP循环奖项设置数组，
		 * 通过概率计算函数get_rand获取抽中的奖项id。
		 * 将中奖奖品保存在数组$res['yes']中，
		 * 而剩下的未中奖的信息保存在$res['no']中，
		 * 最后输出json个数数据给前端页面。
		 */
		$arr = [];
        foreach ($proArr as $key => $val) {
            $arr[$val['id']] = $val['probability'];
        }
		$rid = $this->get_rand_num($arr); //根据概率获取奖项id 
		$prize = $proArr[$rid];
		$res = array(
			'id' => $prize['id'],//中奖项 
			'status' => '1',
			'type' => $prize['type']?'1':'2',
		);
		$freight_status = '1';
		if($prize['type'] == '0'){
			$freight_status = '0';
		}
		$data = array(
			'order_no' => 'CJ'.Random::numeric(16),
			'user_id' => $this->auth->id,
			'username' => $this->auth->username,
			'mobile' => $this->auth->mobile,
			'title' => $prize['title'],
			'type' => $prize['type'],
			'money' => $prize['money'],
			'freight_status' => $freight_status,
			'luck_type' => $luck_type,
			'createtime' => time(),
			'updatetime' => time(),
		);
		$luck = Db::name('luck_user')->insertGetId($data);
		Session::set('luck',$luck);
		if($prize['type'] == '2'){
			$this->goldLog('8',$prize['money'],'','抽獎獲得',$info);
		}
		if($prize['type'] == '1'){
			$this->taobiLog('8',$prize['money'],'','抽獎獲得',$info);
		}
		return $res;
	}

    public function get_rand_num_ex($proArr = array()) {
        if(empty($proArr)) die;
        $rid = '';
        // 概率数组的总权重
        $proSum = array_sum($proArr);
        // 从 1 到概率总数中任意取值
//        $randNum = mt_rand(1, 1000);
        $randNum = mt_rand(1, $proSum);
        $currentProbability = 0;
        foreach ($proArr as $k => $proCur) {
            $currentProbability += $proCur;
            // 判断随机数是否在概率权重中
            if ($randNum <= $currentProbability) {
                // 取出奖品 id
                $rid = $k;
                break;
            }
        }
        unset($proArr);
        return $rid;
    }


    public function get_rand_num($proArr = array()) {
        if(empty($proArr)) die;
        $rid = '';
        // 概率数组的总权重
        $proSum = array_sum($proArr);
        // 概率数组循环
        foreach ($proArr as $k => $proCur) {
            // 从 1 到概率总数中任意取值
            $randNum = mt_rand(1, 1000);
            // 判断随机数是否在概率权重中
            if ($randNum <= $proCur) {
                // 取出奖品 id
                $rid = $k;
                break;
            } else {
                // 如果随机数不在概率权限中，则不断缩小总权重，直到从奖品数组中取出一个奖品
                $proSum -= $proCur;
            }
        }
        unset($proArr);
        return $rid;
    }

	/*收货地址*/
	public function sele_address()
    {
		/* 获取地址*/
			$map = array(
				'type' => 'address',
				'status' => 'normal',
			);
			$_address = Db::name('category')->field('id,pid,name')->where($map)->select();
			$address_list = array();
			foreach($_address as $ls){
				$address_list[$ls['id']] = $ls;
			}
		if ($this->request->isPost()) {
			$luck_id = Session::get('luck');
			$id = $this->request->Post('id');
			/*获取地址*/
			$address_info = Db::name('address')->where('id',$id)->find();
			$address_info['type'] = $address_list[$address_info['type']]['name'];
			$address_info['city'] = $address_list[$address_info['city']]['name'];
			if(Db('luck_user')->where('id',$luck_id)->update(['address'=>json_encode($address_info)])){
				$this->success('操作成功');
			}else{
				$this->error('操作失敗');
			}
		}else{
			/*获取用户地址*/
			$address = Db::name('address')->where('user_id',$this->auth->id)->select();
			$this->view->assign('address', $address);
			$this->view->assign('_address', $address_list);
			if(is_mobile()){
				$this->view->engine->layout('layout/m_empty');
				return $this->view->fetch('index/m_sele_address');
			}else{
				$this->view->engine->layout('layout/empty');
				return $this->view->fetch('index/sele_address');
			}
		}
    }
    public function add_address()
    {
		if ($this->request->isPost()) {
			$address_name = $this->request->Post('name');
			$address_mobile = $this->request->Post('mobile');
			$type = $this->request->Post('address');
			$city = $this->request->Post('city');
			$address = $this->request->Post('detail');
			$data = array(
				'type' => $type,
				'city' => $city,
				'address' => $address,
				'address_name' => $address_name,
				'address_mobile' => $address_mobile,
				'user_id' => $this->auth->id,
				'createtime' => time(),
				'updatetime' => time(),
			);
			$count = Db::name('address')->where('user_id',$this->auth->id)->count();
			if($count >= '5'){
				$this->error('地址最多只能添加5條');
			}else{
				if(Db::name('address')->insert($data)){
					$this->success('添加成功');
				}else{
					$this->error('添加失敗');
				}
			}
		}else{
			/* 获取地址*/
			$map = array(
				'type' => 'address',
				'status' => 'normal',
			);
			$_address = Db::name('category')->field('id,pid,name')->where($map)->select();
			$this->view->assign('address', json_encode($this->list_to_tree($_address)));
			if(is_mobile()){
				$this->view->engine->layout('layout/m_empty');
				return $this->view->fetch('index/m_add_address');
			}else{
				$this->view->engine->layout('layout/empty');
				return $this->view->fetch('index/add_address');
			}
		}
    }
	/*分类数据获取*/
	public function business(){
		$type = request()->param('type');
		$status = request()->param('status');
		$map = array(
			'pid' => $type,
			'status' => 'normal',
		);
		$result = Db::name('special')->where($map)->field('id,key_id,name,nickname,image')->select();
		$_result = [];
		$nickname = '';
		foreach($result as $key => $ls){
			if($status){
				if($key <= '6'){
					$_result[$key] = $ls;
					$_result[$key]['nickname'] = $ls['nickname'].'/set_type/'.$ls['key_id'];
					if(is_mobile()){
						/*手机版删除淘宝代付*/
						if($ls['key_id'] == '1'){
							unset($_result[$key]);
							$nickname = '/index/daifu/nav';
						}
					}else{
						/*pc版删除吱口令*/
						if($ls['key_id'] == '2'){
							unset($_result[$key]);
							$nickname = '/index/daifu/index';
						}
						if($ls['key_id'] == '7'){
							unset($_result[$key]);
							$nickname = '/index/daifu/index';
						}
					}
				}
			}else{
				$_result[$key] = $ls;
				$_result[$key]['nickname'] = $ls['nickname'].'/set_type/'.$ls['key_id'];
				if(is_mobile()){
					/*手机版删除淘宝代付*/
					if($ls['key_id'] == '1'){
						unset($_result[$key]);
						$nickname = '/index/daifu/nav';
					}
				}else{
					/*pc版删除吱口令*/
					if($ls['key_id'] == '2'){
						unset($_result[$key]);
						$nickname = '/index/daifu/index';
					}
					if($ls['key_id'] == '7'){
						unset($_result[$key]);
						$nickname = '/index/daifu/index';
					}
				}
			}
		}
		if($status){
			$_result[] = array(
				'id' => '500',
				'key_id' => '0',
				'name' => '更多',
				'nickname' => $nickname,
				'image' => '/uploads/20220317/ab130d2ce3630a0af9d13ad208570727.png',
			);
		}
		
		sort($_result);
		if($_result){
			return json_encode(array(
				'status' => '1',
				'list' => $_result,
				'title' => '獲取成功',
			));
		}else{
			return json_encode(array(
				'status' => '0',
				'title' => '系統异常，請重繪後重試',
			));
		} 
	}
	public function perfect(){
		$this->success('功能研發中,敬請期待',url('/index/login/login'));
	}
}
