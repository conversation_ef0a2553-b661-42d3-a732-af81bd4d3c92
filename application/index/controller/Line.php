<?php
/**
 * Created by PhpStorm.
 * User: WYY
 * Date: 2020/10/28
 * Time: 21:39
 */

namespace app\index\controller;

use app\common\controller\Frontend;
use fast\Random;
use think\Config;
use think\Validate;
use think\Db;
use think\Session;
use think\Cookie;

class Line extends Frontend
{
	
	const REDIRECT_URI = 'https://www.paybei.com.tw/index/Line/line_callback';
	const APPID = '2005604312';
	const SECRET = '075d470dbd0fe4d679723e6c620cde1f';
	const CHANNEL_ACCESS_TOKEN = 's7LE2RGQDR1yODexmJVBXHEeEGpPQvIZuxxQLSD4isd/MhuldNprZxh1BtjhYP0aOY9QsXsoSb/TdqlrBStAuz1DOAPKdR/O85P+kYOveEybr3HI/gN/L7jD0v8JhSQTB/p+D5l9iDq/ON/mCxK4RAdB04t89/1O/w1cDnyilFU=';
	protected $noNeedLogin = ['line_login','line_callback','line_binding','send_line','send_line_notify'];
    protected $noNeedRight = '*';
	
	/* line登录回调 */
	public function line_callback(){
		$type = $this->request->param('type');
		$Tokencurl = 'https://api.line.me/oauth2/v2.1/token'; // 获取 access_token 的地址 以 post方式请求
		$TokenParameter['grant_type'] = 'authorization_code';
		$TokenParameter['code'] = $_GET['code']; // line 登录成功后返回的 code
		$TokenParameter['client_id'] = self::APPID;// 你的  Channel ID
		if($type){
			$TokenParameter['redirect_uri'] = self::REDIRECT_URI.'?type='.$type;
		}else{
			$TokenParameter['redirect_uri'] = self::REDIRECT_URI;
		}
		/* $TokenParameter['redirect_uri'] = self::REDIRECT_URI;// 回调地址 即平台配置的回调地址 保持一致 */
		$TokenParameter['client_secret'] = self::SECRET;// 你的 channel Secret
		$header = array('Content-type:application/x-www-form-urlencoded');
		//$tr = $this->GetToken($Tokencurl,$TokenParameter);// post 请求 获取必要的 access_token
		$TokenData = $this->sendrequest($Tokencurl,$TokenParameter,'POST','',$header);// post 请求 获取必要的 access_token
		// Session::set('TokenData',$TokenData);
		// Session::set('type',$type);
		// 下面则根据拿到的 access_token 去获取用户的详细信息 最终的信息 如图最下方post获取的数据格式
		$verifyUrl = 'https://api.line.me/oauth2/v2.1/verify';// 地址 GET获取
		$verif_data = [
			'id_token' => $TokenData['id_token'],
			'client_id' => self::APPID,
		];
		$verif_return = $this->sendrequest($verifyUrl,$verif_data);
		$info = Db::name('user')->where('lineid',$verif_return['sub'])->find();
		if(!empty($verif_return['sub'])){
			if($type){
				if($info){
					$this->error('Line帳號已被綁定，請更換Line');
				}else{
					/* 绑定 */
					$data = [
						'lineid' => $verif_return['sub'],
						'line_name' => $verif_return['name'],
					];
					if(Db::name('user')->where('id',$type)->update($data)){
						$url = url('index/user/index');
						$this->success('綁定成功',$url);
					}else{
						$this->error('綁定失敗');
					}
				}
			}else{
				/* 登录 */
				if($info){
					$this->auth->direct($info['id']);
					Cookie::set('token', $this->auth->getToken());
					$url = url('index/user/index');
					$this->success('登錄成功',$url);
				}else{
					$this->error('請先綁定line');
				}
			}
		}
	}
	/* line登录 */
	public function line_login(){
		$redirect_uri = self::REDIRECT_URI;
		$appid = self::APPID;
		$state = Random::alnum('10');
		$url = "https://access.line.me/oauth2/v2.1/authorize?response_type=code&client_id=".$appid."&redirect_uri=".$redirect_uri."&state=".$state."&scope=profile%20openid&nonce=09876xyz&bot_prompt=normal";
		$this->success('獲取成功',$url);
	}
	/* line绑定 */
	public function line_binding(){
		$redirect_uri = self::REDIRECT_URI.'?type='.$this->auth->id;
		$appid = self::APPID;
		$state = Random::alnum('10');
		$url = "https://access.line.me/oauth2/v2.1/authorize?response_type=code&client_id=".$appid."&redirect_uri=".$redirect_uri."&state=".$state."&scope=profile%20openid&nonce=09876xyz&bot_prompt=normal";
		$this->success('獲取成功',$url);
	}
	public function send_line_notify(){
		$data = array(
			'to' => '1c6563eda289558c4eebdd3c324ea3fe', // Replace with your destination group ID
			'messages' => array(
				array(
					'type' => 'text',
					'text' => '测试消息' // Replace with your desired message text
				)
			)
		);
		$headers = array(
			'Content-Type: application/json',
			'Authorization: Bearer ' . self::CHANNEL_ACCESS_TOKEN
		);
		$url = "https://api.line.me/v2/bot/message/push";
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($curl, CURLOPT_POST, true);
		curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
		$response = curl_exec($curl);
 		curl_close($curl);
	}
	/*wwebhook*/
	public function line_webhook(){
		$httpClient = new CurlHTTPClient(self::CHANNEL_ACCESS_TOKEN);
		$bot = new LINEBot($httpClient, ['channelSecret' => self::CHANNEL_SECRET]);
        \think\Log::record('-----------------line-webhook----------------------');
 		//$signature = $_SERVER['HTTP_' . LINEBot\Constant\HTTPHeader::LINE_SIGNATURE];
		$signature = $_SERVER['HTTP_X_LINE_SIGNATURE'];
		$events = $bot->parseEventRequest(file_get_contents('php://input'), $signature);
		$request_body = file_get_contents('php://input');
		$data = json_decode($request_body, true);
        $events = $data['events'];
		print_r($events);exit;
	}

}