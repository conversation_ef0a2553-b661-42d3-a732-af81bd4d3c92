<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use think\Config;
use think\Cookie;
use think\Db;
use think\Hook;
use think\Session;
use think\Validate;

/**
 * 会员中心
 */
class Login extends Frontend
{
    protected $layout = 'login';
    protected $noNeedLogin = ['login', 'register', 'third'];
    protected $noNeedRight = ['*'];
	
	public function _initialize()
    {
        parent::_initialize();
        $auth = $this->auth;

        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'));
        }
        //监听注册登录注销的事件
        Hook::add('user_login_successed', function ($user) use ($auth) {
            $expire = input('post.keeplogin') ? 12 * 86400 : 0;
            Cookie::set('uid', $user->id, $expire);
            Cookie::set('token', $auth->getToken(), $expire);
        });
        Hook::add('user_register_successed', function ($user) use ($auth) {
            Cookie::set('uid', $user->id);
            Cookie::set('token', $auth->getToken());
        });
        Hook::add('user_delete_successed', function ($user) use ($auth) {
            Cookie::delete('uid');
            Cookie::delete('token');
        });
        Hook::add('user_logout_successed', function ($user) use ($auth) {
            Cookie::delete('uid');
            Cookie::delete('token');
        });
    }

    /**
     * 空的请求
     * @param $name
     * @return mixed
     */
    public function _empty($name)
    {
        $data = Hook::listen("user_request_empty", $name);
        foreach ($data as $index => $datum) {
            $this->view->assign($datum);
        }
        return $this->view->fetch('user/' . $name);
    }

    /**
     * 会员中心
     */
    public function index()
    {
        $this->view->assign('title', __('User center'));
        return $this->view->fetch();
    }

    /**
     * 注册会员
     */
    public function register()
    {
		//$this->error('因新會員審核功能維護~升級新的審核流程，故註冊功能暫時維護！已註冊用戶可以使用正在運營的業務功能！');
        if ($this->auth->id) {
            $this->success(__('You\'ve logged in, do not login again'), url('user/index'));
        }
        if ($this->request->isPost()) {
            $mobile = $this->request->post('mobile');
			/*检测用户是否被拉黑*/
			$blacklist_user = Config::get('site.blacklist_user');
			$user_array = explode(',',$blacklist_user);
			if(in_array($mobile,$user_array)){
				$this->error('系統錯誤，請聯絡客服');
			}
            $password = $this->request->post('password');
            $code = $this->request->post('code');
            $username = $this->request->post('realname');
            $invitationcCode = $this->request->post('invitationCode');
			/* 验证验证码是否正确 */
			/* $return = $this->ruleCode($mobile,$code,0);
			if($return == false){
				$this->error(__($this->tips(0)));
			} */
			/* 验证规则 */
            $rule = [
                'mobile'  => 'require|unique:user|regex:/^09\d{8}$/',
                'password'  => 'require',
                'username'     => 'require|length:1,20',
            ];
			/* 验证提示 语言包目录app/index/lang/zh-cn/login.php */
            $msg = [
                'mobile.require' => 'Mobile phone number cannot be empty',
                'mobile.regex'  => 'Incorrect format of mobile phone number',
                'mobile.unique'  => 'Mobile number already exists',
                'password.require' => 'Password cannot be empty',
                'username.require'  => 'Real name cannot be empty',
                'username.length'  => 'Real names are between 1 and 20 digits in length',
            ];
            $c_mask = Cookie::get('mask');
            $mask = request()->param('mask');
            if($c_mask){
                $mask = $c_mask;
            }else{
                $mask = $mask;
            }
            if($mask){
                /*获取运营数据*/
                $info = Db::name('operation')->where('suffix',$mask)->find();
                $source = $info['id'];
            }else{
                $info = Db::name('operation')->where('suffix', 'organic')->find();
                $source = $info['id'];
            }
			/* 入库数据 */
			/* 掩码-密码加密用 */
			// $salt = get_rand_char(6);
			// $information = new Requests();
            $data = [
                'mobile'  => $mobile,
                'password'  => $password,
                'username'     => $username,
                'jointime'    => time(),
                'createtime'    => time(),
            ];
            $validate = new Validate($rule, $msg);
            $result = $validate->check($data);
            if (!$result) {
                $this->error(__($validate->getError()));
            }
			if ($this->auth->register($mobile, $password, $username,$invitationcCode,['source'=>$source])) {
                //$this->success(__('login was successful'), url('user/index'));
                $this->success(__('login was successful'), url('register_success'));
            } else {
                $this->error($this->auth->getError(), null, ['token' => $this->request->token()]);
            }
        }
        $this->view->assign('title', __('Register'));
        return $this->view->fetch();
    }


    public function register_success(){
//         $this->success(__('login was successful'), url('user/index'), '', 6);
        return $this->view->fetch('');
    }

    public function m_register_success(){
        return $this->view->fetch();
    }






    /**
     * 会员登录
     */
    public function login()
    {
        if ($this->auth->id) {
            $this->success(__('You\'ve logged in, do not login again'), url('user/index'));
        }
        if ($this->request->isPost()) {
			
			$code = $this->request->post('captcha');
			//$captcha = new \think\captcha\Captcha();
			//if (!$captcha->check($code)) {
			//	$this->error('验证码错误');
			//}
			$account = $this->request->post('mobile');
			/*检测用户是否被拉黑*/
			$blacklist_user = Config::get('site.blacklist_user');
			$user_array = explode(',',$blacklist_user);
			if(in_array($account,$user_array)){
				$this->error('系統錯誤，請聯絡客服');
			}
			$password = $this->request->post('password');
			if ($this->auth->login($account, $password)) {
                $this->login_activity();
                $this->success(__('Successful login'), url('user/index'));
            } else {
                $this->error($this->auth->getError(), null, ['token' => $this->request->token()]);
            }
        }
        $this->view->assign('title', __('Login'));
        return $this->view->fetch();
    }


    private function login_activity(){
        /* 赠送优惠卷 */
        if(Config::get('site.coupon_open')){
            /*获取当前活动规则      3*/
            $rule = 3;
            $use_rule = Db::name('use_rule')->where('rule_type', $rule)->where('status', 'normal')->find();
            // $use_rule = Db::name('use_rule')->where('id', $id)->where('status', 'normal')->find();
            if( $use_rule && (time() >= $use_rule['begtime'] && time() < $use_rule['endtime']) ){
                /* 检查是否已经领取 */                 
                $row = Db::name('use_rule_log')->where('user_id', $this->auth->id)->where('use_rule_id', $use_rule['id'])->find();
                if( !$row ){
                    Frontend::give_coupon_limit($this->auth->id,$use_rule['coupon_id'], $use_rule['begtime'], $use_rule['endtime']);
                    Db::name('use_rule_log')->insert(array('user_id'=>$this->auth->id, 'use_rule_id'=>$use_rule['id'], 'createtime'=>time()));
                }
            }
        }
    }

    /**
     * 忘记密码
     */
    public function forget()
    {
		if ($this->request->isPost()) {
			$mobile = $this->request->post('mobile');
			$code = $this->request->post('code');
			$password = $this->request->post('password');
			/* 验证验证码是否正确 */
			$return = $this->ruleCode($mobile,$code,1);
			if($return == false){
				$this->error(__($this->tips(0)));
			}
			/* 验证规则 */
            $rule = [
                'password'  => 'require',
            ];
			/* 验证提示 语言包目录app/index/lang/zh-cn/login.php */
            $msg = [
                'password.require' => 'Password cannot be empty',
            ];
			/* 入库数据 */
			/* 掩码-密码加密用 */
			$salt = get_rand_char(6);
			// $information = new Requests();
            $data = [
                'salt'  => $salt,
                'password'  => md5(md5($password).$salt),
            ];
			$validate = new Validate($rule, $msg);
            $result = $validate->check($data);
            if (!$result) {
                $this->error(__($validate->getError()));
            }
			$_result = Db::name('user')
				->where('mobile', $mobile)
				->update($data);
			if(is_numeric($_result)){
				$this->success(__('uccessful password modification'), url('login/login'));
			}else{
				$this->error(__('Password modification failed'));
			}
		}
        return $this->view->fetch();
    }
    /**
     * 服务协议
     */
    public function agreement()
    {   
		$register_txt = Config::get('site.register');
		$this->view->assign('register_txt',$register_txt);
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch();
    }
	 /**
     * 资金问题
     */
    public function capital()
    {   
		$register_txt = Config::get('site.capital');
		$this->view->assign('register_txt',$register_txt);
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch('agreement');
    }
    /**
     * 结汇授权书
     */
    public function settlement()
    {   
		$register_txt = Config::get('site.settlement ');
		$this->view->assign('register_txt',$register_txt);
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch('agreement');
    }
    /**
     * 注销登录
     */
    public function logout()
    {
        //注销本站
        $this->auth->logout();
        $this->success(__('Logout successful'), url('index/index'));
    }

    /**
     * 个人信息
     */
    public function profile()
    {
        $this->view->assign('title', __('Profile'));
        return $this->view->fetch();
    }
	/* 注册获取验证码 */
	public function getCode(){
		$type = $this->auth->getTypecode()?$this->auth->getTypecode():'0';
		$this->sendCode($type);
	}
}
