<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\CouponModel;
use think\Config;
use think\Db;
use think\Exception;
use think\Session;
use fast\Random;
use Zxing\QrReader;
use app\common\model\DaifuModel;
use app\common\model\MemberModel;


class Test extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = 'default';
	/*获取户政部验证码*/
	public function get_card_num(){
		$url = 'https://uat-onlinepay.jkopay.app/platform/entry';
		$data = [
			'platform_order_id' => 'A3616907345129278',	
			'store_id' => '0b4ba78e-e8e1-11ef-94d5-005056b665e9',	
			'currency' => 'TWD',	
			'total_price' => '888',	
			'final_price' => '888',	
			'unredeem' => '888',	
			'result_url' => 'http://www.fubei.com/Api/Test/llll',	
		];
			$curl = $this->post_data($url,'',$data);
			print_r($curl);exit;
	}
	//模拟登录 
	public static function post_data($url, $cookie, $post) { 
	  $curl = curl_init();//初始化curl模块 
	  curl_setopt($curl, CURLOPT_URL, $url);//登录提交的地址 
	  curl_setopt($curl, CURLOPT_HEADER, 0);//是否显示头信息 
	  // curl_setopt($ch, CURLOPT_TIMEOUT,60);
	  // curl_setopt($ch, CURLOPT_PROXY, "127.0.0.1"); //代理服务器地址  
	  // curl_setopt($ch, CURLOPT_PROXYPORT, 1080); //代理服务器端口
	  curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);//是否自动显示返回的信息 
	  curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);//绕过ssl验证
	  curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
	  curl_setopt($curl, CURLOPT_COOKIEFILE, $cookie); //设置Cookie信息保存在指定的文件中 
	  curl_setopt($curl, CURLOPT_POST, 1);//post方式提交 
	  curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($post));//要提交的信息 
	  $rs = curl_exec($curl);//执行cURL 
	  print_r($rs);exit;
	  curl_close($curl);//关闭cURL资源，并且释放系统资源 
	  return $rs; 
	}
}
