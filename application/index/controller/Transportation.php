<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\DaifuModel;
use app\common\model\MemberModel;
use think\Config;
use think\Db;
use think\Session;
use think\Cookie;
use think\Hook;
use think\Validate;

class Transportation extends Frontend
{

    protected $layout = 'user';
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];
	public function _initialize()
    {
        parent::_initialize();
        if(!$this->auth->id){
			$this->error('帳戶未登錄，請先登錄',url('index/login/login'));
		}
    }
	/*统计角标*/
	public function jiaobiao(){
		$count = Db::name('express')->where('user_id',$this->auth->id)->field('count(id) as num,type')->group('type')->select();
		$_count = [];
		foreach($count as $ls){
			$_count[$ls['type']] = $ls;
		}
		/*订单角标-代付款*/
		$pay_order_count = Db::name('jy_order')->where(array('user_id'=>$this->auth->id,'pay_type'=>'0'))->count('id');
		/*订单角标-代发货*/
		$_order_count = Db::name('jy_order')->where(array('user_id'=>$this->auth->id))->field('count(id) as num,order_status')->group('order_status')->select();
		$order_count = array();
		foreach($_order_count as $ls){
			$order_count[$ls['order_status']] = $ls['num'];
		}
		$this->view->assign('order_count',$order_count);
		$this->view->assign('jiaobiao',$_count);
		$this->view->assign('pay_order_count',$pay_order_count);
	}

    public function index()
    {
		if($this->request->isPost()){
			$waybill = $this->request->post('number');
			$waybill_name = $this->request->post('express');
			$good_name = $this->request->post('name');
			$good_url = $this->request->post('weburl');
			$remarks = $this->request->post('remark');
			$is_sale = $this->request->post('is_te');
			$is_type = $this->request->post('is_wai');
			$is_transport = $this->request->post('is_transport');
			if(!$waybill){
				$this->error('請輸入您包裹的運單號碼');
			}
			if(!$waybill_name){
				$this->error('請選擇快遞名稱');
			}
			if(!$good_name){
				$this->error('請輸入商品名稱');
			}
			if(!Db::name('express')->where('waybill',$waybill)->find()){
				$data = array(
					'user_id' => $this->auth->id,
					'entrust_no' =>DaifuModel::orderNo('12'),
					'waybill' => $waybill,
					'waybill_name' => $waybill_name,
					'good_name' => $good_name,
					'good_url' => $good_url,
					'is_sale' => $is_sale,
					'is_type' => $is_type,
					'is_transport' => $is_transport,
					'remarks' => $remarks,
					'createtime' => time(),
					'updatetime' => time(),
				);
				if(Db::name('express')->insert($data)){
					$this->success('提交成功',url('index/transportation/no_warehouse'));
				}else{
					$this->error('提交失敗');
				}
			}else{
				$this->error('運單號碼重複');
			}
		}
		$map = array(
			'type' => 'kuaidi',
			'status' => 'normal',
		);
		$kuaidi = Db::name('category')->field('id,nickname,name')->where($map)->select();
		$this->jiaobiao();
		$this->view->assign('kuaidi',$kuaidi);
		$this->view->assign('user_id',$this->auth->id);
		
        return $this->view->fetch();
    }

    public function rule()
    {
    	$this->view->engine->layout('layout/empty');
        return $this->view->fetch();
    }

    // 未到仓库
    public function no_warehouse()
    {
		$keyword = request()->param('keyword');
		$pages = request()->param('pages')?request()->param('pages'):'10';
		$map = array(
			'user_id' => $this->auth->id,
			'type' => array('in',array('0','5')),
		);
		if(strlen($keyword)){
			$map['waybill'] = array('like','%'.$keyword.'%');
		}
		$list = Db::name('express')->where($map)->paginate($pages);
		$this->jiaobiao();
		$jiyun = Config::get('site.jiyun');
		$this->view->assign('list',$list);
		$this->view->assign('jiyun',$jiyun);
		$this->view->assign('keyword',$keyword);
		$this->view->assign('type','1');
        return $this->view->fetch();
    }
	// 已到仓库
    public function warehouse()
    {
		$keyword = request()->param('keyword');
		$pages = request()->param('pages')?request()->param('pages'):'10';
		$map = array(
			'user_id' => $this->auth->id,
			'type' => array('in',array('1','5','7','9')),
		);
		if(strlen($keyword)){
			$map['waybill'] = array('like','%'.$keyword.'%');
		}
		$list = Db::name('express')->where($map)->order('id','desc')->paginate($pages);
		$jiyun = Config::get('site.jiyun');
		$_list = array();
		foreach($list as $key=>$ls){
			$is_transport = $ls['is_transport']?'海':'空';
			$entrust_no = $ls['entrust_no'].'-'.$is_transport;
			$_list[$key]['id'] = $ls['id'];
			$_list[$key]['order_no'] = $entrust_no;
			$_list[$key]['express_no'] = $ls['waybill'];
			$_list[$key]['weight'] = $ls['scale'].'Kg';
			$_list[$key]['beyond'] = $ls['beyond'];
			if($ls['is_transport']){
				$_list[$key]['money'] = $ls['is_sale']?Config::get('site.haisale'):Config::get('site.haiordinary');
			}else{
				$_list[$key]['money'] = $ls['is_sale']?Config::get('site.sale'):Config::get('site.ordinary');
			}
			$_list[$key]['dispatch'] = $ls['dispatch'];
			$_list[$key]['sale_text'] = $ls['is_sale']?'是':'否';
			$_list[$key]['status'] = $ls['type'];
			$_list[$key]['waybill'] = $ls['waybill'];
			$_list[$key]['waybill_name'] = $ls['waybill_name'];
			$_list[$key]['status_text'] = $jiyun[$ls['type']];
			$_list[$key]['add_time'] = date('Y-m-d H:i:s',$ls['arrivetime']);
		}
		
		$this->jiaobiao();
		$this->view->assign('list',$list);
		$this->view->assign('_list',$_list);
		$this->view->assign('keyword',$keyword);
		$this->view->assign('type','2');
		
		if(is_mobile()){
            return $this->view->fetch('transportation/m_no_warehouse');
        }else{
             return $this->view->fetch();
        }
    }
	/*同捆出货*/
	public function tong(){
		$ids = $this->request->post('id');
		if(!$ids){
			$this->error('請選擇需要出貨的訂單');
		}
		/*获取合并的集运*/
		$ids_array = explode(',',$ids);
		$jiyun = DaifuModel::jiyun($ids_array,$this->auth->id);

		if($jiyun == 1){
			$this->error('訂單中含有空運和海快訂單請分別提交');
		}
		if($jiyun == 2){
			$this->error('訂單中含有特貨和普貨訂單請分別提交');
		}
		Session::set('ids',$ids);
		$this->success('跳轉支付，請稍等~~~',url('transportation/pay'));
	}
	/*出货*/
	public function onetong(){
		$ids = $this->request->param('id');
		if(!$ids){
			$this->error('請選擇需要出貨的訂單');
		}
		Session::set('ids',$ids);
		$this->success('跳轉支付，請稍等~~~',url('transportation/pay'));
	}
	/*支付*/
    public function pay()
    {
		$id = request()->param('id');
		if($id){
			$info = Db::name('jy_order')->where('id',$id)->find();
			$ids = $info['ids'];
		}else{
			$ids = Session::get('ids');
		}
		/*获取集运地址*/
		$address = Db::name('address')->where('user_id',$this->auth->id)->select();
		$map = array(
			'type' => 'address',
			'status' => 'normal',
		);
		/*地址分类循环*/
		$_address = Db::name('category')->field('id,pid,name')->where($map)->select();
		$category = array();
		foreach($_address as $val){
			$category[$val['id']] = $val;
		}
		/*获取合并的集运*/
		$ids_array = explode(',',$ids);
		$jiyun = DaifuModel::jiyun($ids_array,$this->auth->id);
		if(!$jiyun){
			$this->success('請勿重複提交',url('index/user/index'));
		}
		/* 获取用户银行账号 */
		$userbank = MemberModel::getUserBank($this->auth->id);
		$this->jiaobiao();
		$this->view->assign('address',$address);
		$this->view->assign('category',$category);
		$this->view->assign('jiyun',$jiyun);
		$this->view->assign('userbank',$userbank);
        return $this->view->fetch();
    }
	// 优惠券列表
	public function coupon()
    {
		$ids = Session::get('ids');
		/*获取合并的集运*/
		$ids_array = explode(',',$ids);
		$jiyun = DaifuModel::jiyun($ids_array,$this->auth->id);
		/*获取优惠卷*/
		$map = array(
			'user_id' => $this->auth->id,
			'is_use' => 0,
			'reaching_amount' => array('lt',$jiyun['all_money']),
			'end_time' => array('gt',time()),
		);
		$coupon = Db::name('user_coupon')->where($map)->select();
		//$this->view->assign('jiyun',$jiyun);
		$this->view->assign('coupon',$coupon);
    	$this->view->engine->layout('layout/empty');
        return $this->view->fetch();
    }
	/*订单提交*/
	public function warehousing(){
		/*获取参数*/
		$address = $this->request->post('address');
		//$buy_gold = $this->request->post('buy_gold');
		$pay_status = $this->request->post('type');
		$bank_id = $this->request->post('bank_ids');
		$coupon_ids = $this->request->post('coupon_ids');
		/*获取地址*/
		$address_info = Db::name('address')->where('id',$address)->find();
		/*获取合并的集运*/
		$ids = Session::get('ids');
		$ids_array = explode(',',$ids);
		$jiyun = DaifuModel::jiyun($ids_array,$this->auth->id);
		$pay_type = '0';
		$buy_gold = $jiyun['goldBuy'];
		
		if($buy_gold){
			//获取用户抵扣金
			$userinfo = $this->auth->getUserinfo();
			if($userinfo['buy_gold'] >= $buy_gold){
				if($buy_gold >= $jiyun['all_money']){
					//用户填写抵扣金大于当前商品总价-自动转换至抵扣金支付
					$pay_status = '0';
					$pay_type = '1';
					$buy_gold = $jiyun['all_money'];
				}
			}else{
				$this->error('當前抵扣金餘額不足');
			}
		}
		/*获取是否使用优惠卷*/
		$coupon_reduce_amount = '0';
		if($coupon_ids){
			$coupon = Db::name('user_coupon')->where('id',$coupon_ids)->find();
			if($coupon['end_time'] < time()){
				$this->error('此優惠券已失效');
			}
			if($coupon['reaching_amount'] > $jiyun['all_money']){
				$this->error('此優惠券不適用於當前訂單');
			}
			if($coupon['is_use']){
				$this->error('此優惠券已被使用');
			}
			$coupon_reduce_amount = $coupon['reduce_amount'];
		}
		/*开启事务 */
		Db::startTrans();
		try {
			/* 按照当前用户等级汇率把人民币转换成台币 */
			//$exchange = DaifuModel::getExchange($this->auth->id,'8',$all_price,'1');
			//$tb_money = $exchange['money'];
			//$actual_money = ceil(bcmul($all_price,$exchange['exchange'],'1'));
			//$_actual_money = ceil(bcmul($all_price,$exchange['site_exchange'],'1'));
			/* 绑定银行 */
			$bank = "";
			if($bank_id){
				$bank = DaifuModel::bondingBank($bank_id,$this->auth->is_bank);
				$service = '0';
			}else{
				$bank_id = '0';
			}
			if($pay_status == '2'){
				$service = '30';
				if($jiyun['all_money'] < '70'){
					$this->error('超商付款最小不能低于100（含手續費）');
				}
				if($jiyun['all_money'] > '5970'){
					$this->error('超商付款最大不能超過6000（含手續費）');
				}
			}
			/* 存储订单数据 */
			$data = array(
				'type' => '0',
				'ids' => $ids,
				'order_no' => DaifuModel::orderNo('12'),
				'tb_money' => $jiyun['all_money'],
				'balance_money' => $buy_gold,
				'actual_money' => $jiyun['all_money'] - $buy_gold + $service -$coupon_reduce_amount,
				'service' => $service,
				'unit_price' => $jiyun['unit_price'],
				'peisongfei' => $jiyun['peisongfei'],
				'beyond' => $jiyun['all_beyond'],
				'pay_status' => $pay_status,
				'is_transport' => $jiyun['haiyun'],
				'pay_type' => $pay_type,
				'bank_id' => $bank_id,
				'bank_num' => $bank,
				'address_json' => json_encode($address_info),
				'user_id' => $this->auth->id,
				'order_status' => '2',
				'scale' => $jiyun['all_scale'],
				'coupon_reduce_amount' => $coupon_reduce_amount,
				'createtime' => time(),
				'updatetime' => time(),
				'num_jiyun' => count($ids_array),
			);
			if($pay_type == '2'){
				$data['completetime'] = time();
			}
			/* 存储订单 */
			$result = Db::name('jy_order')->insertGetId($data);
			/**更改称重订单日志*/
			$this->update_orderLog($result,$ids_array);
			$this->orderLog('创建订单',$result,'jy_order');
			
			if(Config::get('site.virtual_open') && $pay_status == '1'){
				$virtual_code = $this->generate_code($result,$data['user_id'],$data['actual_money'],'','jy_order');
			}
			/* 插入抵扣金日志 */
			if($buy_gold){
				$this->goldLog('40',$buy_gold,$result);
				$this->orderLog('使用抵扣金'.$buy_gold,$result,'jy_order');
			}
			foreach($ids_array as $ll){
				Db::name('express')->where('id',$ll)->update(['type'=>'10']);
			}
			if($coupon_ids){
				$this->orderLog('使用優惠券'.$coupon_ids.'-'.$coupon_reduce_amount,$result,'jy_order');
				Db::name('user_coupon')->where('id',$coupon_ids)->update(['is_use'=>1]);
			}
			Db::commit();
			if($pay_status == '2'){
				DaifuModel::cs_pay_jy($data);
			}
			session('ids', null);
			$this->success('訂單創建成功',url('index/transportation/details').'?id='.$result);
		}catch (Exception $e) {
			$this->error('網絡异常，請重繪後重試');
			Db::rollback();
		}
	}
	/*查看物流-大陆*/
    public function logistics()
    {
		$id = request()->param('id');
		$info = Db::name('express')->where('id',$id)->find();
		$kuaidi = $this->get_kuaidi($info);
		$this->view->assign('info',$info);
		$this->view->assign('kuaidi',$kuaidi);
    	$this->view->engine->layout('layout/empty');
        if(is_mobile()){
            return $this->view->fetch('transportation/m_logistics');
        }else{
            return $this->view->fetch('transportation/m_logistics');
        }
    }
	/*查看物流-集运*/
	public function logistics_jiyun(){
		$id = request()->param('id');
		$info = Db::name('jy_order')->where('id',$id)->find();
		$this->view->assign('info',$info);
    	$this->view->engine->layout('layout/empty');
        if(is_mobile()){
            return $this->view->fetch('transportation/m_logistics_jiyun');
        }else{
            return $this->view->fetch('transportation/m_logistics_jiyun');
        }
	}
	/*订单详情*/
	public function details(){
		$id = request()->param('id');
		$info = Db::name('jy_order')->where('id',$id)->find();
		$ids = $info['ids'];
		/*获取集运地址*/
		$address = json_decode($info['address_json'],true);
		$map = array(
			'type' => 'address',
			'status' => 'normal',
		);
		/*地址分类循环*/
		$_address = Db::name('category')->field('id,pid,name')->where($map)->select();
		$category = array();
		foreach($_address as $val){
			$category[$val['id']] = $val;
		}
		/* 银行卡支付获取用户银行卡 */
		$list = array();
		$sys_bank = array();
		$bank = array();
		if($info['pay_status'] == '1' || $info['pay_status'] == '3'){
			$bank = explode(',',$info['bank_id']);
			$map = array(
				'user_bank.id' => array('in',$bank),
				'user_bank.status' => 'normal',
			);
			$list = Db::view('user_bank','*')
					->view('category',['name'],'category.id = user_bank.category_id')
					->where($map)
					->select();
			/* 获取平台收款账户 */
			if($list){
				$_map['id'] = $list[0]['relation_id'];
				$_map['status'] = 'normal';
				$sys_bank = Db::name('sys_bank')
					->field('*')
					->where($_map)
					->find();
				if(!$sys_bank){
					$sys_bank = Db::name('sys_bank')
						->field('*')
						->where('is_relation','1')
						->find();
				}
			}
		}
		/*获取合并的集运*/
		$ids_array = explode(',',$ids);
		$jiyun = DaifuModel::jiyun($ids_array,$this->auth->id);
		$this->jiaobiao();
		$this->view->assign('address',$address);
		$this->view->assign('category',$category);
		$this->view->assign('jiyun',$jiyun);
		$this->view->assign('info',$info);
		$this->view->assign('sys_bank',$sys_bank);
        return $this->view->fetch();
	}
    // 待付款
    public function no_pay()
    {
		$map = array(
			'order_status' => array('in',array('2','5','7','9')),
		);
		$this->jiyun_common($map);
		$this->view->assign('type','3');
		if(is_mobile()){
            return $this->view->fetch('transportation/m_no_warehouse');
        }else{
             return $this->view->fetch();
        }
    }
	// 待发货
    public function no_goods()
    {
		$map = array(
			'order_status' => array('in',array('3','5','7','9')),
			'pay_type' => array('in',array('2','3')),
		);
		$this->jiyun_common($map);
		$this->view->assign('type','4');
		if(is_mobile()){
            return $this->view->fetch('transportation/m_no_warehouse');
        }else{
             return $this->view->fetch('no_pay');
        }
    }
	// 已发货
    public function yes_goods()
    {
		$map = array(
			'order_status' => array('in',array('4','5','7','9')),
			'pay_type' => array('in',array('2','3')),
		);
		$this->jiyun_common($map);
		$this->view->assign('type','5');
		if(is_mobile()){
            return $this->view->fetch('transportation/m_no_warehouse');
        }else{
             return $this->view->fetch('no_pay');
        }
    }
	public function jiyun_common($map){
		$keyword = request()->param('keyword');
		$pages = request()->param('pages')?request()->param('pages'):'10';
		$map['user_id'] = $this->auth->id;
		if(strlen($keyword)){
			$map['order_no'] = array('like','%'.$keyword.'%');
		}
		$list = Db::name('jy_order')->where($map)->paginate($pages);
		$jiyun = Config::get('site.jiyun');
		$this->view->assign('list',$list);
		$this->view->assign('jiyun',$jiyun);
		$this->view->assign('keyword',$keyword);
		$this->jiaobiao();
	}
	/*取消*/
	public function cancel(){
		$id = request()->param('id');
		$info = Db::name('jy_order')->where('id',$id)->find();
		$ids = explode(',',$info['ids']);
		/* 开启事务 */
		Db::startTrans();
		/*删除订单*/
		$del = Db::name('jy_order')->where('id',$id)->delete();
		/*回复集运状态至已到仓库*/
		$map = [
			'id' => ['in',$ids],
		];
		$update = Db::name('express')->where($map)->update(['type'=>'1']);
		if($del && $update){
			Db::commit();
			$this->success('操作成功');
		}else{
			Db::rollback();
			$this->error('操作失敗');
		}
	}
    // 地址
    public function address()
    {
		if($this->request->isPost()){
			$count = Db::name('address')->where('user_id',$this->auth->id)->count();
			if($count >= '15'){
				$this->error('地址最多只能添加15條');
			}
			$type = $this->request->post('province');
			$city = $this->request->post('city');
			$address_name = $this->request->post('name');
			$address = $this->request->post('address');
			$address_mobile = $this->request->post('mobile');
			$data = array(
				'type' => $type,
				'city' => $city,
				'address_name' => $address_name,
				'address' => $address,
				'address_mobile' => $address_mobile,
				'user_id' => $this->auth->id,
				'createtime' => time(),
				'updatetime' => time(),
			);
			if(Db::name('address')->insert($data)){
				$this->success('提交成功');
			}else{
				$this->error('提交失敗');
			}
		}
		$map = array(
			'type' => 'address',
			'status' => 'normal',
		);
		$address = Db::name('category')->field('id,pid,name')->where($map)->select();
		/*狗前端用json写列表，害的我要在这里多一个循环*/
		$category = array();
		foreach($address as $val){
			$category[$val['id']] = $val;
		}
		$_address = $this->list_to_tree($address);
		$list = Db::name('address')->where('user_id',$this->auth->id)->select();
		/*狗前端用json写列表，害的我要在这里多两个循环*/
		$_list = array();
		foreach($list as $ls){
			$_list[] = array(
				'id' => $ls['id'],
				'province' => $category[$ls['type']]['name'],
				'province_id' => $ls['type'],
				'city' => $category[$ls['city']]['name'],
				'city_id' => $ls['city'],
				'name' => $ls['address_name'],
				'address' => $ls['address'],
				'mobile' => $ls['address_mobile'],
			);
		}
		$this->jiaobiao();
		$this->view->assign('address',$_address);
		$this->view->assign('list',$_list);
        return $this->view->fetch();
    }
	 // 编辑地址
    
    public function address_add()
    {
		if($this->request->isPost()){
			$id = $this->request->post('id');
			if(!$id){
				$count = Db::name('address')->where('user_id',$this->auth->id)->count();
				if($count >= '15'){
					$this->error('地址最多只能添加15條');
				}
			}
			$type = $this->request->post('province');
			$city = $this->request->post('city');
			$address_name = $this->request->post('name');
			$address = $this->request->post('address');
			$address_mobile = $this->request->post('mobile');
			$data = array(
				'type' => $type,
				'city' => $city,
				'address_name' => $address_name,
				'address' => $address,
				'address_mobile' => $address_mobile,
				'user_id' => $this->auth->id,
				'createtime' => time(),
				'updatetime' => time(),
			);
			if($id){
				$map = array(
					'id' => $id,
				);
				if(Db::name('address')->where($map)->update($data)){
					$this->success('修改成功');
				}else{
					$this->error('修改失敗');
				}
			}else{
				if(Db::name('address')->insert($data)){
					$this->success('提交成功');
				}else{
					$this->error('提交失敗');
				}
			}
		}
		$id = $this->request->param('id');
		$info = Db::name('address')->where('id',$id)->find();
		$map = array(
			'type' => 'address',
			'status' => 'normal',
		);
		$address = Db::name('category')->field('id,pid,name')->where($map)->select();
		$_address = $this->list_to_tree($address);
		$this->view->assign('address',$_address);
		$this->view->assign('info',$info);

		$this->view->engine->layout('layout/empty');
		if(is_mobile()){
            return $this->view->fetch('transportation/m_address_add');
        }else{
            return $this->view->fetch('transportation/address_add');
        }
        
        return $this->view->fetch();
    }
	/*删除地址*/
	public function address_dele(){
		$id = $this->request->param('id');
		if(Db::name('address')->where('id',$id)->delete()){
			$this->success('删除成功');
		}else{
			$this->error('删除失敗');
		}
	}
   

    // 注意事项
    public function article()
    {
		$this->jiaobiao();
        return $this->view->fetch();
    }
    // 手机端列表加载更多
    public function list()
    {
		$keyword = request()->param('keyword');
		$pages = request()->param('pages')?request()->param('pages'):'10';
		$map = array(
			'user_id' => $this->auth->id,
			'type' => array('in',array('1','5','7','9')),
		);
		if(strlen($keyword)){
			$map['waybill'] = array('like','%'.$keyword.'%');
		}
		$list = Db::name('express')->where($map)->paginate($pages);
		$jiyun = Config::get('site.jiyun');
		$_list = array();
		foreach($list as $key=>$ls){
			$_list[$key]['id'] = $ls['id'];
			$_list[$key]['order_no'] = $ls['entrust_no'];
			$_list[$key]['express_no'] = $ls['waybill'];
			$_list[$key]['weight'] = $ls['scale'].'Kg';
			$_list[$key]['beyond'] = $ls['beyond'];
			$_list[$key]['money'] = $ls['is_sale']?Config::get('site.sale'):Config::get('site.ordinary');
			$_list[$key]['dispatch'] = $ls['dispatch'];
			$_list[$key]['sale_text'] = $ls['is_sale']?'是':'否';
			$_list[$key]['status'] = $ls['type'];
			$_list[$key]['status_text'] = $jiyun[$ls['type']];
			$_list[$key]['add_time'] = date('Y-m-d H:i:s',$ls['arrivetime']);
		}
		$this->jiaobiao();
		$this->view->engine->layout('layout/empty');
		$this->view->assign('list',$list);
		$this->view->assign('_list',$_list);
		$this->view->assign('keyword',$keyword);
		$this->view->assign('type','2');
        return $this->view->fetch('m_list');
    }
	/*虚拟码核验入库*/
	public function generate_code($order_id,$member_id,$price,$group="",$table){
		$virtual_code = $this->generate_get_code($order_id,$member_id,$price,$group="");
		$info = Db::name('virtual_code')->where(array('num'=>$virtual_code,'type'=>'0'))->find();
		if($info){
			$virtual_code = $this->generate_get_code($order_id,$member_id,$price,$group="");
		}
		Db::name($table)->where('id',$order_id)->update(['cs_code' => $virtual_code,'pay_status'=>'3']);
		$data = array(
			'order_id'=>$order_id,
			'type'=>'0',
			'table_name'=>$table,
			'num'=>$virtual_code,
			'createtime'=>time(),
			'updatetime'=>time(),
		);
		Db::name('virtual_code')->insert($data);
		$this->orderLog('生成虚拟码'.$virtual_code,$order_id,$table);
	}
	public function generate_get_code($order_id,$member_id,$price,$group=""){
		if($group){
			$a = $group;
		}else{
			if(Config::get('site.virtual_open') == '1'){
				$a = '43869';
			}
			if(Config::get('site.virtual_open') == '2'){
				$a = '92113';
			}
			if(Config::get('site.virtual_open') == '3'){
				$a = '92245';
			}
			if(Config::get('site.virtual_open') == '4'){
				$a = '92487';
			}
		}
		$bb = date("z")+4;/*缴款截至期限*/
		$b = str_pad($bb,3,0,STR_PAD_LEFT );
		$year = date('Y',time());
		$c = $year%10;/*缴款截至年份*/
		$member_id = rand('1111','9999');
		$qiye = $a.$c.$b.$member_id;
		$_price = str_pad($price,10,0,STR_PAD_LEFT );
		$a_value = $this->virtual_code($qiye);/*A值6*/
		$b_value = $this->virtual_code($_price);/*B值3*/
		$ab_value = $a_value + $b_value;
		$c_value = $ab_value%10;/*C值*/
		$_check_code = 10 - $c_value;
		$check_code = $_check_code%10;/*检查码*/
		return $qiye.$check_code;
	}
    
}
