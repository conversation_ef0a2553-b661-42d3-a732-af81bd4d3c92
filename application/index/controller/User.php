<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\NewsModel;
use app\common\model\MemberModel;
use app\common\model\DaifuModel;
use app\common\model\User as Users;
use think\Config;
use think\Cookie;
use think\Hook;
use think\Session;
use think\Db;

/**
 * 会员中心
 */
class User extends Frontend
{
    protected $layout = 'user';
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

   public function _initialize()
    {
        parent::_initialize();
        $auth = $this->auth;
		$actionname = strtolower($this->request->action());
		if(!$this->auth->id && $actionname != 'customer'){
			$this->error(__('Account not logged in, please log in first'),url('index/login/login')); 
		}
        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'));
        }
        //监听注册登录注销的事件
        Hook::add('user_login_successed', function ($user) use ($auth) {
            $expire = input('post.keeplogin') ? 30 * 86400 : 0;
            Cookie::set('uid', $user->id, $expire);
            Cookie::set('token', $auth->getToken(), $expire);
        });
        Hook::add('user_register_successed', function ($user) use ($auth) {
            Cookie::set('uid', $user->id);
            Cookie::set('token', $auth->getToken());
        });
        Hook::add('user_delete_successed', function ($user) use ($auth) {
            Cookie::delete('uid');
            Cookie::delete('token');
        });
        Hook::add('user_logout_successed', function ($user) use ($auth) {
            Cookie::delete('uid');
            Cookie::delete('token');
        });
		$this->view->assign('nav_url',$this->auth->getRequestUri());
    }

	 /**
     * 优惠券
     */
    public function coupon()
    {
		$order_status = request()->param('order_status');
		/*获取优惠卷*/
		$map = array(
			'user_id' => $this->auth->id,
			'end_time' => array('gt',time()),
		);
		if($order_status){
			$map['is_use'] = $order_status;
		}
		$coupon_num = Db::name('user_coupon')->where('user_id',$this->auth->id)->count();
		$coupon = Db::name('user_coupon')->where($map)->select();
		//$this->view->assign('jiyun',$jiyun);
		$this->view->assign('coupon',$coupon);
		$this->view->assign('coupon_num',$coupon_num);
		$this->view->assign('order_status',$order_status);
        if(is_mobile()){
            return $this->view->fetch('user/coupon/m_index');
        }else{
            return $this->view->fetch('user/coupon/index');
        }
    }
    /**
     * 我的奖品
     */
    public function prize()
    {
		$list = Db::name('luck_user')->where('user_id',$this->auth->id)->order('id','desc')->select();
		$this->view->assign('list',$list);
		if(is_mobile()){
			return $this->view->fetch('user/prize/m_index');
		}else{
			return $this->view->fetch('user/prize/index');
		}
    }
    

    public function prize_lists()
    {
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch('user/prize/lists');
    }

    /**
     * 空的请求
     * @param $name
     * @return mixed
     */
    public function _empty($name)
    {
        $data = Hook::listen("user_request_empty", $name);
        foreach ($data as $index => $datum) {
            $this->view->assign($datum);
        }
        return $this->view->fetch('user/' . $name);
    }



    /* 发票 上传 */
	public function invoice(){
		$c = request()->param('c');
		/*获取公司*/
		$list = Db::name('invoice_company')->field('id,title')->select();
		$this->view->assign('list',$list);
		$this->view->assign('c',$c);
		if(is_mobile()){
            return $this->view->fetch('user/invoice/m_index');
        }else{
            return $this->view->fetch('user/invoice/index');
        }
	}
	/*发票提交*/
	public function invoice_submit(){
		if ($this->request->isPost()) {
			$data = $this->request->post();
			/*查询订单是否存在*/
			$info = Db::name('df_order')->where('order_no',$data['order_no'])->find();
			if(!$info){
				$this->error('不存在的訂單號，請您核實後再次提交');
			}
			if($data['money'] < '500'){
				$this->error('發票金額不得低於500');
			}
			/*比对金额*/
			if($info['rmb_money'] == $data['money']){
				$return = $this->getImg($data['img']);
				if($return['code'] == '1'){
					$this->error(__($return['msg']));
				}
				$_data = array(
					'order_id' => $this->auth->id,
					'order_no' => $data['order_no'],
					'company_id' => $data['company'],
					'money' => $data['money'],
					'type' => $data['type'],
					'invoice_image' => $return['data'],
					'invoice_status' => '1',
					'createtime' =>time(),
					'updatetime' => time(),
				);
				$result = Db::name('recovery_invoice')->insert($_data);
				if($result){
					$this->success(__('Submit successfully'),url('user/invoice_my'));
				}else{
					$this->error(__('Failure to submit'));
				}
			}else{
				$this->error('檢測到您的發票金額跟訂單金額不一致');
			}
		}
		$this->error('提交錯誤');
	}
	/* 发票 回收 */
	public function invoice_recovery(){
		/*获取公司*/
		$list = Db::name('invoice_company')->select();
		$this->view->assign('list',$list);
		if(is_mobile()){
            return $this->view->fetch('user/invoice/m_recovery');
        }else{
            return $this->view->fetch('user/invoice/recovery');
        }
	}

	/* 发票 回收---加载更多 */
	public function invoice_lists()
    {
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch('user/invoice/lists');
    }

	/* 我的發票 */
	public function invoice_my(){
		$list = Db::name('recovery_invoice')->where('order_id',$this->auth->id)->order('id','desc')->select();
		$invoice_status = Config::get('site.invoice_status');
		$this->view->assign('list',$list);
		$this->view->assign('invoice_status',$invoice_status);
		if(is_mobile()){
            return $this->view->fetch('user/invoice/m_my');
        }else{
            return $this->view->fetch('user/invoice/my');
        }
	}

	/* 我的發票---加载更多 */
	public function myinvoice_lists()
    {
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch('user/invoice/mylists');
    }


	/* 發票教程 */
	public function invoice_tutorial(){
		if(is_mobile()){
            return $this->view->fetch('user/invoice/m_tutorial');
        }else{
            return $this->view->fetch('user/invoice/tutorial');
        }
	}


    /* 客服 */
	public function customer(){
		/* $count = Db::name('user_wx')->where('user_id', $this->auth->id)->count();
		$this->view->assign('count',$count); */
		$this->view->engine->layout('layout/empty');
		return $this->view->fetch('user/info/customer');
	}
	/* 机器人 */
	public function robot(){
		/* $count = Db::name('user_wx')->where('user_id', $this->auth->id)->count();
		$this->view->assign('count',$count); */
		$this->view->engine->layout('layout/empty');
		return $this->view->fetch('user/info/robot');
	}
	/* 消息通知配送 */
	public function set_msg_notify(){
		$data = $this->request->post();
		$info = Db::name('robot_open')->where('user_id',$this->auth->id)->find();
		if($info){
			unset($data['id']);
			$return = Db::name('robot_open')->where('user_id',$this->auth->id)->update($data);
		}else{
			$data['user_id'] = $this->auth->id;
			$return = Db::name('robot_open')->where('user_id',$this->auth->id)->insert($data);
		}
		if($return){
			return true;
		}else{
			return false;
		}
	}

    /**
     * 会员中心
     */
    public function index()
    {
		$bill = MemberModel::getNextLevel($this->auth->id);
		$tongzhi = NewsModel::getTongzhi(3);
		$gonggao = NewsModel::getGonggao(5);
		// $this->userReport();
		/* 获取用户绑定的支付宝账号 */
		$_map = array(
			'user_id' => $this->auth->id,
			'status' => 'normal',
		);
		$list = Db::name('bonding_alipay')
				->field('*')
				->where($_map)
				->select();
		$alipay = [];
		foreach($list as $ls){
			$alipay[] = $ls['other_account'];
		}
		$product = [];
		if($alipay){
			$time = time()-86400;
			$start_time = date('Y-m-d H:i:s',$time);
			$end_time = date('Y-m-d H:i:s',time());
			$map['other_account'] = array('in',$alipay);
			$map['status'] = '0';
			$map['alipay_status'] = '0';
			$product = Db::view('t_alipay_order_goods','*')
					->view('t_alipay_order',['peerpay_link','sys_alipay_id','trade_no','type','other_name','other_account','status','update_time'],'t_alipay_order.id=t_alipay_order_goods.alipay_order_id')
					->where($map)
					->whereTime('update_time','between',[$start_time,$end_time])
					->find();
		}
		$banner = Db::name('user_banner')->where('status','normal')->select();
		$order_map = array(
			'pay_type' => '0',
			'order_status' => '0',
			'user_id' => $this->auth->id,
		);
		/*未支付*/
		$df_order = Db::name('df_order')->where($order_map)->count();
		$order_game = Db::name('order_game')->where($order_map)->count();
		$order_live = Db::name('order_live')->where($order_map)->count();
		$order_new_df = Db::name('order_new_df')->where($order_map)->count();
		$order_other = Db::name('order_other')->where($order_map)->count();
		$unpaid = $df_order + $order_game + $order_live + $order_new_df + $order_other;
		/*获取最大的值 拼凑数组*/
		$array = array(
			'1' =>$df_order,
			'2' =>$order_live,
			'3' =>$order_game,
			'4' =>$order_new_df,
			'5' =>$order_other,
		);
		$max_num = array_search(max($array),$array);
		$robot = Db::name('robot_open')->where('user_id',$this->auth->id)->find();
		$map = array(
			'status' => 'normal',
			'type' => '1',
		);
		$video = Db::name('video')->where($map)->select();
		$coupon = Db::name('user_coupon')->where('user_id',$this->auth->id)->count();
		$this->view->assign('tongzhi',$tongzhi);
		$this->view->assign('gonggao',$gonggao);
		$this->view->assign('bill',$bill);
		$this->view->assign('max_num',$max_num);
		$this->view->assign('product',$product);
		$this->view->assign('banner',$banner);
		$this->view->assign('unpaid',$unpaid);
		$this->view->assign('robot',$robot);
		$this->view->assign('video',$video);
		$this->view->assign('coupon',$coupon);
		$this->view->assign('lineid',$this->auth->lineid);
        $this->view->assign('title', __('User center'));
        return $this->view->fetch();
    }

   public function userReport(){
	   $type = $this->request->param("type");
	   switch($type){
		   case 1:
				$start = date('Ymd',strtotime('-7 day'));
		   break;
		   case 2:
				$start = date('Ymd',strtotime('-14 day'));
		   break;
		   case 3:
				$start = date('Ymd',strtotime('-30 day'));
		   break;
		   default:
				$start = date('Ymd',strtotime('-7 day'));
	   }
	   
	   $map['daytime'] = array('gt',$start);
	   $map['user_id'] = $this->auth->id;
	   // print_r($map);exit;
	   $list = Db::name('count_order')->field('*')->where($map)->select();
	   // print_r($list);exit;
	   $all_order = $refund_money = $tb_deal_money = $rmb_deal_money = '0';
	   $refund = $order_num = $deal_money = $day_time = array();
	   foreach($list as $key=>$ls){
		   $rmb_deal_money += $ls['all_rmb_money'];
		   $tb_deal_money += $ls['all_tb_money'];
		   $refund_money += $ls['refund_money'];
		   $all_order += $ls['all_order'];
		   $day_time[$key] = date('m-d',$ls['createtime']);
		   $deal_money[$key] = $ls['all_rmb_money'];
		   $order_num[$key] = (string)$ls['all_order'];
		   $refund[$key] = $ls['refund_money'];
	   }
		$data = array(
			'rmb_deal_money' => $rmb_deal_money,
			'tb_deal_money' => $tb_deal_money,
			'refund_money' => $refund_money,
			'all_order' => $all_order,
			// 'day_time' => json_encode($day_time),
			'day_time' => $day_time,
			// 'deal_money' => json_encode($deal_money),
			'deal_money' => $deal_money,
			// 'order_num' => json_encode($order_num),
			'order_num' => $order_num,
			// 'refund' => json_encode($refund),
			'refund' => $refund,
		);
		$this->success($data);
		// print_r($data);exit;
	   // return $this->view->assign('count',$data);
   }

    /**
     * 注销登录
     */
    public function logout()
    {
        //注销本站
        $this->auth->logout();
        $this->success(__('Logout successful'), url('user/index'));
    }

    /**
     * 个人信息
     */
    public function profile()
    {
        $this->view->assign('title', __('Profile'));
        return $this->view->fetch();
    }

    /**
     * 修改密码
     */
    public function password()
    {
        if ($this->request->isPost()) {
            $mobile = $this->auth->mobile;
            $code = $this->request->post("code");
            $password = $this->request->post("password");
			/* 验证验证码是否正确 */
			$return = $this->ruleCode($mobile,$code,3);
			if($return == false){
				$this->error(__($this->tips(0)));
			}
            $ret = $this->auth->changepwd($password);
            if ($ret) {
				$this->user_log('修改密码');
                $this->success(__('Reset password successful'), url('user/login'));
            } else {
                $this->error($this->auth->getError(), null, ['token' => $this->request->token()]);
            }
        }
        $this->view->assign('title', __('Change password'));
		
		if(!is_mobile()){
            $this->view->engine->layout('layout/empty');
        }
        return $this->view->fetch('user/password/index');
    }
    /**
     * 订单
     */
    public function order()
    {
		$status = $this->request->get('order_status');
		$pay_status = $this->request->get('pay_status');
		$pay_type = $this->request->get('pay_type');
		$type = $this->request->get('type');
		$_type = $this->request->param("_type");
		$set_type = $this->request->param("set_type");
		/*初始值*/
		$map = array(
			'user_id' => $this->auth->id,
		);
		/* 订单状态 */
		if(strlen($status)){
			$map['order_status'] = $status;
		}
		/* 支付方式 */
		if(strlen($pay_status)){
			$map['pay_status'] = $pay_status;
		}
		/* 支付状态 */
		if(strlen($pay_type)){
			$map['pay_type'] = $pay_type;
		}
		/*订单状态*/
		if(strlen($_type)){
			switch($_type){
				case 1:
					$map['pay_type'] = '0';
					$map['order_status'] = '0';
					/*计算未支付*/
					$df_order = MemberModel::getOrderList($map,20,'df_order');
					$order_live = MemberModel::getOrderList($map,20,'order_live');
					$order_game = MemberModel::getOrderList($map,20,'order_game');
					$order_new_df = MemberModel::getOrderList($map,20,'order_new_df');
					$order_other = MemberModel::getOrderList($map,20,'order_other');
					/*获取数量*/
					$df_order_count = count($df_order);
					$order_live_count = count($order_live);
					$order_game_count = count($order_game);
					$order_new_df_count = count($order_new_df);
					$order_other_count = count($order_other);
					$this->view->assign('df_order_count', $df_order_count);
					$this->view->assign('order_live_count', $order_live_count);
					$this->view->assign('order_game_count', $order_game_count);
					$this->view->assign('order_new_df_count', $order_new_df_count);
					$this->view->assign('order_other_count', $order_other_count);
				break;
				case 2:
					$map['pay_type'] = array('EGT','1');
				break;
				case 3:
					$map['order_status'] = '1';
				break;
				case 4:
					$map['order_status'] = '8';
				break;
			}
		}
		/*根据业务类型切换不同的表*/
		$table = 'df_order';
		/* 订单类型 */
		if(strlen($type)){
			$map['type'] = $type;
			$info_special = Db::name('special')->where('key_id',$type)->find();
			$table = $info_special['table_name'];
		}
		if(strlen($set_type) && !strlen($type)){
			switch($set_type){
				case 1:
					$table = 'df_order';
				break;
				case 2:
					$table = 'order_live';
				break;
				case 3:
					$table = 'order_game';
				break;
				case 4:
					$table = 'order_new_df';
				break;
				case 5:
					$table = 'order_other';
				break;
				default:
					$table = 'df_order';
				;
			}
			unset($_list);
		}
		$special = Db::name('special')->where('status','normal')->select();
		foreach($special as $val){
			$special[$val['key_id']] = $val;
		}
		$list = MemberModel::getOrderList($map,20,$table);
        $this->view->assign('special', $special);
        $this->view->assign('list', $list);
        $this->view->assign('_type', $_type);
        $this->view->assign('set_type', $set_type);
		if(is_mobile()){
			return $this->view->fetch('user/order/m_index');
		}else{
			return $this->view->fetch('user/order/index');
		}
    }
    public function order_detail()
    {
		$id = $this->request->param('id');
		$set_type = $this->request->param('set_type');
		$special = Db::name('special')->where('key_id',$set_type)->find();
		$order = MemberModel::getOrder($id,$special['table_name']);
        $this->view->assign('order', $order);
        $this->view->assign('special', $special);
        if(is_mobile($special['pid'] = '1')){
            return $this->view->fetch('user/order/detail');
        }else{
            $this->view->engine->layout('layout/empty');
            return $this->view->fetch('user/order/detail');
        }
    }

    public function order_lists()
    {
		$status = $this->request->get('order_status');
		$pay_status = $this->request->get('pay_status');
		$pay_type = $this->request->get('pay_type');
		$type = $this->request->get('type');
		$_type = $this->request->param("_type");
		$set_type = $this->request->param("set_type");
		/*初始值*/
		$map = array(
			'user_id' => $this->auth->id,
		);
		/* 订单状态 */
		if(strlen($status)){
			$map['order_status'] = $status;
		}
		/* 支付方式 */
		if(strlen($pay_status)){
			$map['pay_status'] = $pay_status;
		}
		/* 支付状态 */
		if(strlen($pay_type)){
			$map['pay_type'] = $pay_type;
		}
		/*订单状态*/
		if(strlen($_type)){
			switch($_type){
				case 1:
					$map['pay_type'] = '0';
					$map['order_status'] = '0';
					/*计算未支付*/
					$df_order = MemberModel::getOrderList($map,20,'df_order');
					$order_live = MemberModel::getOrderList($map,20,'order_live');
					$order_game = MemberModel::getOrderList($map,20,'order_game');
					$order_new_df = MemberModel::getOrderList($map,20,'order_new_df');
					$order_other = MemberModel::getOrderList($map,20,'order_other');
					/*获取数量*/
					$df_order_count = count($df_order);
					$order_live_count = count($order_live);
					$order_game_count = count($order_game);
					$order_new_df_count = count($order_new_df);
					$order_other_count = count($order_other);
					$this->view->assign('df_order_count', $df_order_count);
					$this->view->assign('order_live_count', $order_live_count);
					$this->view->assign('order_game_count', $order_game_count);
					$this->view->assign('order_new_df_count', $order_new_df_count);
					$this->view->assign('order_other_count', $order_other_count);
				break;
				case 2:
					$map['pay_type'] = array('EGT','1');
				break;
				case 3:
					$map['order_status'] = '1';
				break;
				case 4:
					$map['order_status'] = '8';
				break;
			}
		}
		/*根据业务类型切换不同的表*/
		$table = 'df_order';
		/* 订单类型 */
		if(strlen($type)){
			$map['type'] = $type;
			$info_special = Db::name('special')->where('key_id',$type)->find();
			$table = $info_special['table_name'];
		}
		if(strlen($set_type) && !strlen($type)){
			switch($set_type){
				case 1:
					$table = 'df_order';
				break;
				case 2:
					$table = 'order_live';
				break;
				case 3:
					$table = 'order_game';
				break;
				case 4:
					$table = 'order_new_df';
				break;
				case 5:
					$table = 'order_other';
				break;
				default:
					$table = 'df_order';
				;
			}
			unset($_list);
		}
		$special = Db::name('special')->where('status','normal')->select();
		foreach($special as $val){
			$special[$val['key_id']] = $val;
		}
		$list = MemberModel::getOrderList($map,20,$table);
        $this->view->assign('special', $special);
        $this->view->assign('list', $list);
        $this->view->assign('_type', $_type);
        $this->view->assign('set_type', $set_type);
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch('user/order/lists');
    }

    



    /**
     * 通知公告
     */
    public function notice()
    {
		$type = $this->request->get('type');
		if($type){
			$list = NewsModel::getGonggao(15);
		}else{
			$list = NewsModel::getTongzhi(15);
		}
        $this->view->assign('list', $list);
        $this->view->assign('type', $type);
        return $this->view->fetch('user/notice/index');
    }
    public function notice_detail()
    {
		$id = $this->request->get('id');
		$news = NewsModel::noticeDetail($id);
		$type = $this->request->get('type');
		Db::name('notice')->where('id',$id)->setInc('view',1);
        $this->view->assign('news', $news);
        $this->view->assign('type', $type);
        // $this->view->engine->layout('layout/empty');
        return $this->view->fetch('user/notice/detail');
    }
	public function site_map()
    {
        // $this->view->engine->layout('layout/empty');
		$list = Db::name('seo')->where('status','normal')->paginate('15');
		$this->view->assign('list', $list);
        return $this->view->fetch('user/notice/sitemap');
    }
	public function lists()
    {
		$type = $this->request->get('type');
		if($type){
			$list = NewsModel::getGonggao(15);
		}else{
			$list = NewsModel::getTongzhi(15);
		}
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch();
    }
	public function get_list(){
		$type = request()->param('type')?request()->param('type'):'0';
		$type_title = Config::get('site.order_type');
		$type_title['8'] = '其他页面';
		$map = array(
			'type' => $type,
			'status' => 'normal',
		);
		$list = Db::name('notice')->where($map)->paginate('20');
		$this->view->assign('list',$list);
		$this->view->assign('type_title',$type_title);
		$this->view->assign('type',$type);
	}
    // 申诉
    public function appeal()
    {
		if(is_mobile()){
			$type = '0';
		}else{
			$type = request()->param('type')?request()->param('type'):'1';
		}
		$list = NewsModel::appealList($type,$this->auth->id,'20');
		$reply = array();
		foreach($list as $ls){
			$reply[]  = $ls['id'];
		}
		$_list = NewsModel::replyList($reply);
		$reply_result = array();
		foreach($_list as $ls){
			$reply_result[$ls['appeal_id']][]  = $ls;
		}
		// $admin = Db::name('admin')
					// ->select();
		// foreach($admin as $ls){
			// $_admin[$ls['id']] = $ls;
		// }
		// $admin_name = NewsModel::get_admin($reply);
        $this->view->assign('list', $list);
        $this->view->assign('type', $type);
        $this->view->assign('reply_result', $reply_result);
		// $this->view->assign("admin_name", $_admin);
        $this->view->assign('appeal_title', Config::get('site.appeal'));
        $this->view->assign('title', __('Profile'));
        return $this->view->fetch('user/appeal/index');
    }
    public function appeal_lists()
    {
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch('user/appeal/lists');
    }
    public function appeal_submit()
    {
		if($this->request->isPost()){
			$type = $this->request->post('type');
			$content = $this->request->post('content');
			$data = array(
				'user_id' => $this->auth->id,
				'type' => $type,
				'content' => $content,
				'is_read' => '1',
				'createtime' => time(),
				'updatetime' => time(),
			);
			if(Db::name('appeal')->insert($data)){
				$_content = $this->auth->username .'('. $this->auth->mobile .')'.'发起了申诉,申诉内容为：'.$content;
				$dinghorn = new \addons\dinghorn\Dinghorn();
				$res      = $dinghorn->msgNotice('notice', ['content' => $_content], [
					'dynamic_variable' => '111'
				]);
				$this->success(__('Submit successfully'),url('user/appeal'));
			}else{
				$this->error(__('Failure to submit'));
			}
		}
        $this->view->assign('title', __('Profile'));
        return $this->view->fetch('user/appeal/submit');
    }
	public function appeal_submit_content(){
		if($this->request->isPost()){
			$id = $this->request->post('id');
			$content = $this->request->post('content');
			$data = array(
				'appeal_id' => $id,
				'reply_content' => $content,
				'createtime' => time(),
				'updatetime' => time(),
			);
			if(Db::name('appeal_reply')->insert($data)){
				Db::name('appeal')->where('id', $id)->update(['is_read' => '1']);
				$this->success(__('Submit successfully'));
			}else{
				$this->error(__('Failure to submit'));
			}
		}
		$this->error(__('Network connection failed, please refresh and try again'));
	}
	/* 删除申诉 */
	public function appeal_dele(){
		$id = $this->request->post('id');
		if(Db::name('appeal')->where('id', $id)->delete()){
			Db::name('appeal_reply')->where('appeal_id', $id)->delete();
			$this->user_log('删除申诉'.$id);
			$this->success(__('Submit successfully'));
		}else{
			$this->error(__('Failure to submit'));
		}
	}

    // 头像修改
    public function edit_avatar()
    {
		if($this->request->isPost()){
			$img = $this->request->post('avatar_img');
			$return = $this->getImg($img);
			if($return['code'] == '1'){
				$this->error(__($return['msg']));
			}
			if(Db::name('user')->where('id', $this->auth->id)->update(['avatar' => $return['data']])){
				$this->success(__('Submit successfully'),url('index/user/index'));
			}else{
				$this->error(__('Failure to submit'));
			}
		}
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch('user/info/avatar');
    }
    // 手机号修改
    public function editMobile()
    {
		if($this->request->isPost()){
			$mobile = $this->request->post('mobile');
			$info = Db::name('user')->where('mobile',$mobile)->find();
			if($info){
				$this->error('手機號已經存在');
			}
			$result = Db::name('user')
				->where('id', $this->auth->id)
				->update(['mobile' => $mobile]);
			if($result){
				$this->user_log('修改手机号'.$this->auth->mobile .'>>>'.$mobile);
				$this->success(__('Submit successfully'), url('user/index'));
			}else{
				$this->error(__('Failure to submit'));
			}
		}
        if(is_mobile()){
            return $this->view->fetch('user/info/mobile');
        }else{
            $this->view->engine->layout('layout/empty');
            return $this->view->fetch('user/info/mobile');
        }
    }
    // 身份认证
    public function editIdcard()
    {
		if($this->request->isPost()){
			$cardid = $this->request->Post('cardid');
			$year = $this->request->Post('year');
			$month = $this->request->Post('month');
			$day = $this->request->Post('day');
			$type = $this->request->Post('type');
			$code = $this->request->Post('code');
			$card_cookie = $this->request->Post('card_cookie');
			$captchaKey = $this->request->Post('captchaKey');
			$time1 = $this->request->Post('time1');
			$data =array(
				'cardid' => $cardid,
				'year' => $year,
				'month' => $month,
				'day' => $day,
				'type' => $type,
				'user_id' => $this->auth->id,
				'createtime' => time(),
				'updatetime' => time(),
				'is_state' => '4',
				//'code' => $code,
			);
			$_map = array(
				'cardid' => $cardid,
				'cardid' => $cardid,
			);
			$info_card = Db::name('user_card')->where($_map)->find();
			//if($info_card){
			//	$this->error('身份證號已存在');
			//}
			if($info_card['is_state'] == '1'){
				$this->error('身份證號已被認證');
			}
			if($info_card['is_state'] == '4'){
				$this->error('稽核中，請耐心等待');
			}
			if(Db::name('user_card')->insert($data)){
				$_content = $this->auth->username .'('. $this->auth->mobile .')'.'有新的身份证审核需要认证';
				$dinghorn = new \addons\dinghorn\Dinghorn();
				$res      = $dinghorn->msgNotice('notice', ['content' => $_content], [
					'dynamic_variable' => '111'
				]);
				Db::name('user')->where('id',$this->auth->id)->update(['is_card'=>'1']);
				$this->success('提交成功，請等待稽核',url('user/index'));
			}else{
				$this->error('提交失敗');
			}
			/************************分割线************************/
			/* 开启事务 */
			//Db::startTrans();
			////sleep(3);
			//try {
			//	$_map = array(
			//		'cardid' => $cardid,
			//	);
			//	$info_card = Db::name('user_card')->where($_map)->find();
			//	if($info_card['is_state'] == '1'){
			//		$this->error('身份證號已被認證');
			//	}else{
			//		/*户政部审核*/
			//		$return = MemberModel::putCard($data,$card_cookie,$captchaKey,$time1);
			//		if($return['status'] == '1'){
			//			MemberModel::userStatus($this->auth->id,'is_card');
			//			$pid = Users::recommender_ids($this->auth->pid);
			//			if($pid){
			//				Users::give_gold($pid,Config::get('site.give3'),$this->auth->id);
			//			}
			//			$info = Db::name('user')->where('id',$this->auth->id)->find();
			//			Users::give_gold($info,Config::get('site.give1'),$this->auth->id);
			//		}else{
			//			Db::commit();
			//			$_content = $this->auth->username .'('. $this->auth->mobile .')'.'身份证审核未通过，原因：' .$return['result_str'];
			//			$dinghorn = new \addons\dinghorn\Dinghorn();
			//			$res      = $dinghorn->msgNotice('notice', ['content' => $_content], [
			//				'dynamic_variable' => '111'
			//			]);
			//			$this->error($return['result_str']);
			//		}
			//	}
			//	if($info_card['is_state'] == '1'){
			//		$this->error('身份證號已被認證');
			//	}
			//	$cardid = Db::name('user_card')->where('user_id',$this->auth->id)->order('id desc')->find();
			//	if($cardid['is_state'] == '3' || $cardid['is_state'] == '4'){
			//		$this->error('請勿重複提交');
			//	}
			//	Db::commit();
			//	$this->success(__('Submit successfully'),url('user/index'));
			//}catch (Exception $e) {
			//	$this->error(__('Failure to submit'),url('user/index'));
			//	Db::rollback();
			//}
		}
		$map = array(
			'user_id' => $this->auth->id,
		);
		$info = Db::name('user_card')->where($map)->order('createtime','desc')->find();
		$type = $this->request->param("type");
		//$card_num = $this->get_card_num();
		$this->view->assign('type',$type);
		//$this->view->assign('card_num',json_decode($card_num,true));
		$this->view->assign('info',$info);
        if(is_mobile()){
        	if($type){
        		$this->view->engine->layout('layout/m_empty');
				return $this->view->fetch('user/info/idcard');
        	}else{
        		return $this->view->fetch('user/info/idcard');
        	}
        }else{
            $this->view->engine->layout('layout/empty');
            return $this->view->fetch('user/info/idcard');
        }
    }
	/*获取户政部验证码*/
	public function get_card_num(){
		$maintain = Config::get('site.Maintain');
		if($maintain){
			$data = [
				'code' => '0',
				'sms' => '戶政司網站維護',
			];
			return json_encode($data);
		}else{
			$url = dirname(dirname(dirname(dirname(__FILE__))));
			$time1 = time().$this->auth->id;
			$cookie = $url.'/public/uploads/data/aaavcookie_'.$time1.'.txt';
			$return = MemberModel::put_card_num($cookie,$time1);
			$data = [
				'code' => '1',
				'cookie' => $cookie,
				'img_url' => $return['captchaImage'],
				'captchaKey' => $return['captchaKey'],
				'time1' => $time1,
			];
			return json_encode($data);
		}
	}
    // 身份二次认证
    public function editIdcardTwo()
    {
		if($this->request->isPost()){
			$img = $this->request->Post('img');
			$img2 = $this->request->Post('img2');
			$return = $this->getImg($img);
			$return2 = $this->getImg($img2);
			if($return['code'] == '1'){
				$this->error(__($return['msg']));
			}
			if($return2['code'] == '1'){
				$this->error(__($return['msg']));
			}
			sleep(3);
			if(Db::name('user_card_two')->field('*')->where(array('user_id'=>$this->auth->id,'is_state'=>'1'))->find()){
				$this->error('請勿重複提交');
			}
			if(Db::name('user_card_two')->field('*')->where(array('user_id'=>$this->auth->id,'is_state'=>'3'))->find()){
				$this->error('稽核中，請勿重複提交');
			}
			$_return = MemberModel::putCardTwo($this->auth->id,$return['data'],$return2['data']);
			if(is_numeric($_return)){
				$_content = $this->auth->username .'('. $this->auth->mobile .')'.'身份证二次提交需要审核，请至后台进行审核';
				$dinghorn = new \addons\dinghorn\Dinghorn();
				$res      = $dinghorn->msgNotice('notice', ['content' => $_content], [
					'dynamic_variable' => '111'
				]);
				$this->success(__('Submit successfully'));
			}else{
				$this->error(__('Failure to submit'));
			}
		}
		$find = Db::name('user_card_two')
				->field('*')
				->order('id','desc')
				->where('user_id', $this->auth->id)
				->find();
		$type = $this->request->param("type");
		$this->view->assign('type',$type);
		$this->view->assign('card', $find);
		$this->view->assign('is_card', $this->auth->is_card);
        if(is_mobile()){
        	if($type){
        		$this->view->engine->layout('layout/m_empty');
				return $this->view->fetch('user/info/idcard_two');
        	}else{
        		return $this->view->fetch('user/info/idcard_two');
        	}
        }else{
            $this->view->engine->layout('layout/empty');
            return $this->view->fetch('user/info/idcard_two');
        }
    }

    // 汇率
    public function rate_fun()
    {
		if($this->request->isPost()){
			/* 按照当前用户等级汇率把人民币转换成台币 */
			$money = $this->request->post('money');
			$type = $this->request->post('type');
			$category = $this->request->post('category');
			$huilv = DaifuModel::getExchange($this->auth->id,$category,$money,$type);
			$huilv['canuse_gwj'] = DaifuModel::goldBuy($money,$this->auth->id);
			return $huilv;
		}
		$exchange = Db::name('exchange')->field('*')->where('status','normal')->select();
		$this->view->assign('exchange',$exchange);
		$this->view->assign('level',$this->auth->level);
        if(is_mobile()){
            return $this->view->fetch('user/rate/index');
        }else{
            $this->view->engine->layout('layout/empty');
            return $this->view->fetch('user/rate/index');
        }
        
    }

    // 分享
    public function share()
    {
		$map = array(
			'gold_log.user_id' => $this->auth->id,
			'gold_log.type' => '10',
			'gold_log.status' => 'normal',
		);
		$info = Db::name('gold_log')->where(array('order_id'=>$this->auth->id,'type'=>'10','status'=>'normal'));
		$list = Db::view('gold_log','*')
		->view('user',['username'],'gold_log.order_id = user.id')
		->where($map)
		->select();
		$_count = Db::name('user')
			->where('pid', $this->auth->id)
			->count();
		$this->view->assign('list',$list);
		$this->view->assign('_count',$_count);
        if(is_mobile()){
            return $this->view->fetch('user/share/m_index');
        }else{
            $this->view->engine->layout('layout/empty');
            return $this->view->fetch('user/share/index');
        }
        
    }
    public function share_lists()
    {
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch('user/share/lists');
    }

    // 充值
    public function charge()
    {
		/* 获取用户银行账号 */
		$userbank = MemberModel::getUserBank($this->auth->id);
		$this->view->assign('userbank',$userbank);
        return $this->view->fetch('user/charge/index');
    }
    // 资金管理
    public function capital()
    {
		
		/* 获取淘币明细 */
		$type = $this->request->param('type');
		$list = MemberModel::getTaobiLog($this->auth->id);
		/*获取导航*/
		$nav = Db::name('special')->field('id,key_id,name,image')->where('status','normal')->select();
		
		$nav_array =  [];
		foreach($nav as $ls){
			$nav_array[$ls['key_id']] = $ls;
		}
		$sys_array = MemberModel::getsys_array($this->auth->id);
		$nav_array = $nav_array + $sys_array;
		$this->view->assign('nav_array', $nav_array);
		$this->view->assign('list', $list);
		$this->view->assign('type', $type);
        if(is_mobile()){
            return $this->view->fetch('user/capital/m_index');
        }else{
            return $this->view->fetch('user/capital/index');
        }
    }
    public function capital_lists()
    {
		
		$list = MemberModel::getTaobiLog($this->auth->id);
		/*获取导航*/
		$nav = Db::name('special')->where('status','normal')->select();
		$nav_array =  [];
		foreach($nav as $ls){
			$nav_array[$ls['key_id']] = $ls;
		}
		$sys_array = MemberModel::getsys_array($this->auth->id);
		$this->view->assign('nav_array',  array_merge($nav_array,$sys_array));
		
		$this->view->assign('list', $list);
        $this->view->engine->layout('layout/empty');
        return $this->view->fetch('user/capital/lists');
    }


    
    // 资金管理--购物金明细
    public function capital_gold()
    {
		/* 获取淘币明细 */
		$type = $this->request->param('type');
		$list = MemberModel::getGoldLog($this->auth->id,$type);
		/*获取导航*/
		$nav = Db::name('special')->where('status','normal')->select();
		$nav_array =  [];
		foreach($nav as $ls){
			$nav_array[$ls['key_id']] = $ls;
		}
		$sys_array = MemberModel::getsys_array($this->auth->id);
		$this->view->assign('nav_array',  array_merge($nav_array,$sys_array));
		$this->view->assign('list', $list);
		$this->view->assign('type', $type);
        if(is_mobile()){
            return $this->view->fetch('user/capital/m_index_gold');
        }else{
            return $this->view->fetch('user/capital/index');
        }
    }
    // 资金管理--返利明细
    public function capital_back()
    {
        if(is_mobile()){
            return $this->view->fetch('user/capital/m_index_back');
        }else{
            return $this->view->fetch('user/capital/capital_back');
        }
    }


    // 提现
    
    public function withdrawal()
    {
		if($this->request->isPost()){
			$money = $this->request->post('money');
			$info = Db::name('user')->where('id',$this->auth->id)->find();
			if($info['withdrawal'] < $money){
				$this->error(__('Insufficient withdrawable balance'));
			}
			$data = array(
				'user_id' => $this->auth->id,
				'money' => $money,
				'is_adopt' => '0',
				'createtime' => time(),
				'updatetime' => time(),
			);
			$withdrawal = $info['withdrawal'] - $money;
			/* 开启事务 */
			Db::startTrans();
			try {
				$result = Db::name('user_withdrawal')->insert($data);
				$_result = Db::name('user')->where('id',$this->auth->id)->update(['withdrawal'=>$withdrawal]);
				if($result && $_result){
					Db::commit();
					$this->success(__('Submit successfully'),url('user/capital_back'));
				}else{
					Db::rollback();
					$this->error(__('Failure to submit'));
				}
			}catch (Exception $e) {
				$this->error(__('Network exception, please refresh and try again'));
				Db::rollback();
			}
		}
        return $this->view->fetch('user/withdrawal/index');
    }
    public function capitalReport(){
		$type = $this->request->param("type");
		$day = $this->request->param("day");
		switch($day){
		   case 7:
				$start = date('Y-m-d',strtotime('-7 day'));
		   break;
		   case 14:
				$start = date('Y-m-d',strtotime('-14 day'));
		   break;
		   case 30:
				$start = date('Y-m-d',strtotime('-30 day'));
		   break;
		   default:
				$start = date('Y-m-d',strtotime('-7 day'));
	   };
		switch($type){
		   case 1:
				$table = 'taobi_count';
		   break;
		   case 2:
				$table = 'gold_count';
		   break;
		   case 3:
				$table = 'taobi_count';
		   break;
		   default:
				$table = 'taobi_count';
	   }
	   $map['day_time'] = array('gt',$start);
	   $map['user_id'] = $this->auth->id;
	   // print_r($map);exit;
	   $list = Db::name($table)->field('*')->where($map)->select();
	   $all_order = $refund_money = $tb_deal_money = $rmb_deal_money = '0';
	   $refund = $order_num = $deal_money = $day_time = array();
	   foreach($list as $key=>$ls){
		   // $rmb_deal_money += $ls['all_rmb_money'];
		   // $tb_deal_money += $ls['all_tb_money'];
		   // $refund_money += $ls['refund_money'];
		   // $all_order += $ls['all_order'];
		   $day_time[$key] = date('m-d',$ls['createtime']);
		   $deal_money[$key] = $ls['money'];
	   }
		$data = array(
			// 'rmb_deal_money' => $rmb_deal_money,
			// 'tb_deal_money' => $tb_deal_money,
			// 'refund_money' => $refund_money,
			// 'all_order' => $all_order,
			// 'day_time' => json_encode($day_time),
			'day_time' => $day_time,
			// 'deal_money' => json_encode($deal_money),
			'money' => $deal_money,
			'list' => $list,
			// 'order_num' => json_encode($order_num),
			// 'order_num' => $order_num,
			// 'refund' => json_encode($refund),
			// 'refund' => $refund,
		);
		$this->success($data);
	}
    // wap 图表
    public function capitalChart()
    {
        return $this->view->fetch('user/capital/m_chart');
    }
    
}
