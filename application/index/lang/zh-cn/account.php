<?php

return [
		'Bank drawee'   => '付款人認證',
		'Bank required'   => '所屬銀行必填',
		'Bank card name required'   => '銀行卡姓名必填',
		'Account name cannot exceed 20 characters'   => '賬戶姓名不能超過20字符',
		'Bank card already exists'   => '銀行卡已經存在',
		'The last six digits of the bank card must be six'   => '銀行卡後6位必須是6位',
		'Bank card is online'   => '銀行卡已達到上線',
		'WeChat artificial'   => '微信人工',
		'WeChat account'   => '微信昵稱',
		'Wechat number'   => '微信號',
		'Please enter WeChat account'   => '請輸入微信昵稱',
		'Please enter Wechat number'   => '請輸入微信號',
		'Add Wechat account'   => '添加微信帳號',
		'Please do not add again'   => '請不要重複添加',
		'Alipay account is abnormal, please contact customer service'   => '支付寶帳號异常，請聯系客服',
];
