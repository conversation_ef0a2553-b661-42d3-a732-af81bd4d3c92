<style type="text/css">
	.address_form .tt{font-weight: bold; line-height: 30px; margin-left: 17px;}
	.address_form{border: 1px dashed #EF436D;}
	.address_form .input_box .input_tt{width: 80px;}
	.address_form .input_box .input_tt span{display: inline-block;  width: 7px; height: 10px;}
	.address_form .cancel{color: #999; margin-left: 30px;}
</style>
<div class="address_form margin_b10 pad_b30">
	<form action="{:url('index/Account/add_address')}" autocomplete="off" class="small_input edit_info_form" style="padding: 10px 24px 0 24px">
		<!-- 编辑时必传 id -->
		<input type="hidden" name="id" value="<?=empty($info['id'])?'':$info['id']?>">
		<div class="clearfix">
			<div class="input_box flex_ac">
			    <div class="input_tt">所在地區:</div>
			    <div class="input_rr flex1 flex">
					<!-- 如果是修改，data-val 给值，否则给空 -->
			        <select name="province" data-val="<?=empty($info['type'])?'0':$info['type']?>" is_required="true" class="flex_s js_province" style="width: 100px; margin-right: 10px;" empty_tip="請選擇所在地區">
					</select>
					
					<!-- 如果是修改，data-val 给值 -->
					<select name="city"  data-val="<?=empty($info['city'])?'0':$info['city']?>"  is_required="true" class="flex1 js_city" empty_tip="請選擇所在地區">
					</select>
			    </div>
			</div>
			<div class="input_box flex_ac">
			    <div class="input_tt">詳細地址:</div>
			    <div class="input_rr flex1">
			        <input type="text" style="height: 40px" placeholder="請輸入" name="detail" value="<?=empty($info['address'])?' ':$info['address']?>" maxlength="50" is_required="true" empty_tip="請輸入詳細地址">
			    </div>
			</div>
			<div class="input_box flex_ac">
			    <div class="input_tt">收<span></span>貨<span></span>人:</div>
			    <div class="input_rr flex1">
			        <input type="text" style="height: 40px; width: 200px;" placeholder="請輸入" name="name" value="<?=empty($info['address_name'])?' ':$info['address_name']?>" maxlength="30" is_required="true" empty_tip="請輸入收貨人">
			    </div>
			</div>
			<div class="input_box flex_ac">
			    <div class="input_tt">聯繫方式:</div>
			    <div class="input_rr flex1">
			        <input type="text" style="height: 40px; width: 200px;" placeholder="請輸入" name="mobile" value="<?=empty($info['address_mobile'])?' ':$info['address_mobile']?>" maxlength="20" is_required="true" empty_tip="請輸入聯繫方式">
			    </div>
			</div>
			<div class="input_box flex_ac">
			    <div class="input_tt" style="height: 14px;"></div>
			    <div class="input_rr flex1 flex_ac">
			        <a href="javascript:;" class="sub_btn small js_form_sub" data-text="保存" data-loadding="保存中···" data-type="new_location">保存</a>
					<a href="javascript:;" class="js_cancel cancel">取消</a>
			    </div>
			</div>
		</div>
	</form>
</div>
<script>
$('.js_cancel').click(function(){
	$(this).parents('.address_form').remove()
})
var address_data=<?=$address?>;
function init_pro(val){
	var html='<option value="">請選擇</option>';
	var is_val=false;
	for(var i=0;i<address_data.length;i++){
		is_val=address_data[i].id==val?true:false;
		html+='<option '+(is_val?'selected':'')+' value="'+address_data[i].id+'">'+address_data[i].name+'</option>'
	};
	$('.js_province').html(html);
	init_city(val,$('.js_city').attr('data-val'))
}
init_pro($('.js_province').attr('data-val'));
function init_city(val1,val2){
	var html='<option value="">請選擇</option>';
	for(var i=0;i<address_data.length;i++){
		if(address_data[i].id==val1){
			var list=address_data[i].list;
			for(var k=0;k<list.length;k++){
				html+='<option '+(val2==list[k].id?'selected':'')+' value="'+list[k].id+'">'+list[k].name+'</option>'
			};
			break;
		}
	};
	$('.js_city').html(html);
}
$('.js_province').change(function(){
	var val=$('.js_province').val()
	init_city(val,'')
})
</script>