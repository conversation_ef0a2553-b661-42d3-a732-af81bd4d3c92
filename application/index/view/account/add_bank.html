<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<?php
	if(!empty($num)){
?>
	<style type="text/css">
		.tip_wind{position: absolute;left: 0; top: 0; z-index: 300; background: rgba(0,0,0,0.8); color: #fff; width: 100%; height: 100%; }
		.tip_wind .d{ width: 300px; height: 60px; line-height: 30px; position: absolute; left: 50%; margin-left: -125px; top: 50%; margin-top:-60px; font-size: 18px; }
	</style>
	<div class="tip_wind pad_lr20 pad_b40">
		<div class="text-center pad_tb30 f16 d">
			{:__('Bank card is online')}
		</div>
	</div>
<?php
	}
?>
<?php
	if(!$is_card && !$bank_count){
?>
	<style type="text/css">
		.tip_wind{position: absolute;left: 0; top: 0; z-index: 300; background: rgba(0,0,0,0.8); color: #fff; width: 100%; height: 100%; }
		.tip_wind .d{ width: 300px; height: 60px; line-height: 30px; position: absolute; left: 50%; margin-left: -125px; top: 50%; margin-top:-60px; font-size: 18px; }
	</style>
	<div class="tip_wind pad_lr20 pad_b40">
		<div class="text-center pad_tb30 f16 d">
			金融審核認證通過後才可以添加銀行卡
		</div>
	</div>
<?php
	}
?>
<div class="my_close_win pad_lr20 pad_b40">
    <div class="color_red pad_t10 my_close_win_title">{:__('Bank drawee')}</div>
	<!--身份证 start-->
	<!--<div class="color_666 pad_t10 f12" style="line-height: 24px;"><font size="3" color="red"><b>付唄極力配合165防詐騙中心要求，為了杜絕網絡詐騙，請先添加付款人信息！這裡需要上傳的是匯款人的身份證+銀行卡+手寫簽字（為杜絕詐騙，請您盡量使用本人或者家人的賬戶匯款）</b></font></div>-->
	<form action="{:url('account/addBank')}" autocomplete="off" class="small_input edit_info_form" style="padding: 10px 0px 0 0px">
		<!--<div class="id_card_img clearfix">
            <div class="item_s pull-left text-center">
                <div class="img">
                    <div class="js_up_load_btn">
						<input type="hidden" name="img" value=""  is_required="true" empty_tip="{:__('Please upload photos')}">
						<img class="up_icon" src="{$site.Front_card}">
					</div>
					<div class="img_src js_img_src hide" style="background-image: url(__CDN__/assets/img/pc/upload.png);">
						<a href="javascript:;"><img src="__CDN__/assets/img/dele.png"></a>
					</div>

                    <div class="hide">
                        <input type="file" class="js_up_img_input_btn" data-type="img" accept="image/*" multiple data-maxMum="1" data-fileSizeMax="2" onchange="filecountup($(this),this.files)" data-filename="img" />
                    </div>
                </div>
                <div class="tt">{:__('Upload photos')}(正面)&nbsp;&nbsp;
				<a target="_black" href="{$site.Front_card}">{:__('Example diagram')}(正面)</a>
				</div>
            </div>
            <div class="item_s pull-left text-center">
                <div class="img">
                    <div class="js_up_load_btn2">
						<input type="hidden" name="img2" value=""  is_required="true" empty_tip="{:__('Please upload photos')}"> 
						<img class="up_icon" src="{$site.Reverse_card}">
					</div>
					<div class="img_src js_img_src2 hide" style="background-image: url(__CDN__/assets/img/pc/upload.png);">
						<a href="javascript:;"><img src="__CDN__/assets/img/dele.png"></a>
					</div>
                    <div class="hide">
                        <input type="file" class="js_up_img_input_btn2" data-type="img" accept="image/*" multiple data-maxMum="1" data-fileSizeMax="2" onchange="filecountup2($(this),this.files)" data-filename="img" />
                    </div>
                </div>
                <div class="tt">{:__('Upload photos')}(反面)
				<a target="_black"href="{$site.Reverse_card}">{:__('Example diagram')}(反面)
				</div>
            </div>
        </div>-->
	<!--身份证-->
    <!-- <div class="color_666 pad_t10 f12" style="line-height: 24px;">{:__('Payment bank account can not')}<br>{:__('Please fill in the last 6 bits of')}</div> -->
    <div class="color_red f12" style="line-height: 24px;">{:__('* There are three new caps for payment')}</div>
    <img src="__CDN__/assets/img/pc/add_card_tip.jpg" class="margin_t10 js_add_card_tip" style="width: 100%">
    <!--<form action="{:url('account/addBank')}" autocomplete="off" class="small_input edit_info_form" style="padding: 10px 0px 0 0px">-->
    	<div class="clearfix appeal_form_div">
    		<input type="text" class="js_ss_type" name="type" is_required="true" empty_tip="{:__('Please select the type of bank card')}" style="width: 0; height: 0; opacity: 0;">
			<?php
				foreach($bank as $key=>$ls){
			?>
				<a href="javascript:;" class="moni_radio js_ss_radio margin_r30" data-id="<?=$key?>">
					<div class="radio_r"></div>
					<?=$ls['name']?>
				</a>
			<?php
				}
			?>
    	</div>

    	<div class="clearfix pad_t10">
    		<div class="item_col_3 pad_r10">
    			<div class="input_box full_width">
		            <div class="input_tt js_type_tt">{:__('Affiliated bank')}</div>
		            <div class="input_rr" style="overflow: visible; ">
		                <div class="moni_select js_type_val" style="width: 100% ">
		                    <input type="text" name="bank" value="" is_required="true" empty_tip="{:__('Please select the bank')}">
		                    <div class="moni_selected clearfix">
		                        <img class="arr" src="__CDN__/assets/img/arr.png">
		                        <span>{:__('Please select')}</span>
		                    </div>
		                    <div class="moni_s_down">
		                    </div>
		                </div>
		            </div>
		        </div>
    		</div>
    		<div class="item_col_3 pad_r10">
    			<div class="input_box full_width">
		            <div class="input_tt">{:__('Account name')}</div>
		            <div class="input_rr">
		                <input type="text" style="height: 40px" placeholder="{:__('Please enter the name of the account')}" name="name" maxlength="20" is_required="true" empty_tip="{:__('Please enter the name of the account')}">
		            </div>
		        </div>
    		</div>	
    		<div class="item_col_3 pad_r10">
    			<div class="input_box full_width">
		            <div class="input_tt">{:__('The last six')}</div>
		            <div class="input_rr">
		                <input type="text" style="height: 40px" placeholder="{:__('Please enter The last six')}" data-type="number" name="lastsix" maxlength="6" is_required="true" empty_tip="{:__('Please enter The last six')}">
		            </div>
		        </div>
    		</div>	
    	</div>

    	<!--<div class="clearfix pad_t10">
    		<div class="item_col_6 pad_r10">
    			<div class="input_box full_width">
		            <div class="input_tt">{:__('Mobile verification code')}</div>
		            <div class="input_rr">
		                <a href="javascript:;" style="height: 40px; line-height: 40px" class="get_code" data-url="{:url('/index/login/getCode',array('type'=>'4'))}">{:__('Get code')}</a>
                        <input type="text" style="height: 40px" name="code" data-type="number" maxlength="6" is_required="true" empty_tip="{:__('Please enter mobile verification code')}">
		            </div>
		        </div>
    		</div>	
    		<div class="item_col_3 pad_r10">
    			<div class="input_box full_width">
		            <div class="input_tt" style="height: 30px"></div>
		            <div class="input_rr color_999 f14" style="line-height: 40px">
		            	{:__('Phone number')}：<?=substr_replace($user['mobile'],'****',2,4)?>
		            	<input type="hidden" class="js_code_input" value="<?=$user['mobile']?>">
		            </div>
		        </div>
    		</div>	
        </div>-->
        <div class="text-center pad_t30">
            <a href="javascript:;" class="sub_btn small js_form_sub" data-text="{:__('Add')}" data-loadding="{:__('Submitting')}" data-type="refresh">{:__('Add')}</a>
        </div>
    </form>
</div>
<div class="js_hide_img" style="width: 0; height: 0; overflow: hidden;">
<div id="img_prev" style="width: 1000px"><img style="width: 1000px;" src="{$site.Front_card}"></div>
</div>



<div class="js_hide_img2" style="width: 0; height: 0; overflow: hidden;">
<div id="img_prev2" style="width: 1000px"><img style="width: 1000px;" src="{$site.Reverse_card}"></div>
</div>
<script type="text/javascript">

	var bank_list=<?=json_encode($bank)?>;
	// var bank_list=[
	// 	{id:1,name:'银行',image:'__CDN__/assets/img/pc/add_card_tip.jpg',list:[{id:'11',name:'中国银行'},{id:'12',name:'农业银行'}]},
	// 	{id:4,name:'邮局',image:'__CDN__/assets/img/pc/id_card_sl.jpg',list:[{id:'41',name:'中国邮局'},{id:'42',name:'农业邮局'}]},
	// 	{id:4,name:'信用合作社',image:'__CDN__/assets/img/pc/index_banner.jpg',list:[{id:'51',name:'中国信用合作社'},{id:'52',name:'农业信用合作社'}]},
	// 	{id:4,name:'晟會',image:'__CDN__/assets/img/pc/index_coupon_banner.jpg',list:[{id:'61',name:'中国晟會'},{id:'62',name:'农业晟會'}]},
	// 	{id:4,name:'漁會',image:'__CDN__/assets/img/pc/index_step_img02.png',list:[{id:'71',name:'中国漁會'},{id:'72',name:'农业漁會'}]}
	//  ];
	$('.js_ss_radio').click(function(){
		if(!$(this).hasClass('acitve')){
			var _index=$(this).attr('data-id');
			$(this).addClass('active').siblings('.js_ss_radio').removeClass('active');
			$('.js_ss_type').val(bank_list[_index].id);
			$('.js_type_tt').html(bank_list[_index].name);
			$('.js_type_val input').val('');
			$('.js_type_val .moni_selected span').html("{:__('Please select')}"+bank_list[_index].name);

			$('.js_add_card_tip').attr('src',bank_list[_index].image)
			var _html='';
			for(var i=0;i<bank_list[_index].list.length;i++){
				var _item=bank_list[_index].list[i];
				_html+='<a href="javascript:;" value="'+_item.id+'">'+_item.name+'</a>';
			}
			$('.js_type_val .moni_s_down').html(_html);

		}
	})
</script>
<script type="text/javascript">
    $('.js_id_card_img').click(function(){
        layer.open({
          type: 1,
          title: false,
          closeBtn: 1,
          area: ['1000px',$('#img_prev img').height()+'px'],
          skin: 'layui-layer-nobg', //没有背景色
          shadeClose: true,
          content: $('.js_hide_img').html()
        });
    })

    $('.js_up_load_btn').click(function(){
        $('.js_up_img_input_btn').click()
    })



    function getImgData(img,dir,max_w,next){
     var image=new Image();
     image.onload=function(){
      var degree=0,drawWidth,drawHeight,width,height;
      drawWidth=max_w>this.naturalWidth?this.naturalWidth:max_w;
      drawHeight=this.naturalHeight*drawWidth/this.naturalWidth;
      
      var canvas=document.createElement('canvas');
      canvas.width=width=drawWidth;
      canvas.height=height=drawHeight; 
      var context=canvas.getContext('2d');
     
      context.drawImage(this,0,0,drawWidth,drawHeight);
      //返回校正图片
      next(canvas.toDataURL("image/jpeg",.7));
     }
     image.src=img;
    }


    //图片上传预览 2018.7.11
    function filecountup(_this,files,count){
        // 文件大小限制
        // 
        console.log(files)
        if(_this.attr('data-fileSizeMax')!=''){
            var max=parseFloat(_this.attr('data-fileSizeMax'))*1024*1024;
            if(files[0].size > max){
                tip_show("文件不能大于"+parseFloat(_this.attr('data-fileSizeMax'))+"M","2");
                return false;
            } 
        }


        var file = files[0];
        var reader = new FileReader();
        reader.onloadend = function () {
            // console.log(reader.result);
            getImgData(reader.result,'',1000,function(data){
                console.log(data)
                $('.js_up_load_btn').addClass('hide').find('input').val(data);
                $('.js_img_src').removeClass('hide').css('background-image','url('+data+')')
            })

        }
        if (file) {
            reader.readAsDataURL(file);
        }
    };


    $('.js_img_src a').click(function(){
        var a=layer.confirm("{:__('Are you sure you want to delete it?')}", {
          title:false,
          area:['500px', 'auto'],
          skin: 'my_confirm',
          btn: ["{:__('Confirm')}","{:__('Cancel')}"], //按钮
          btnAlign:'c'
        }, function(){
            layer.close(a);
            $('.js_up_load_btn').removeClass('hide').find('input').val('');
            $('.js_img_src').addClass('hide')
        });
    })





    $('.js_id_card_img2').click(function(){
        layer.open({
          type: 1,
          title: false,
          closeBtn: 1,
          area: ['1000px',$('#img_prev2 img').height()+'px'],
          skin: 'layui-layer-nobg', //没有背景色
          shadeClose: true,
          content: $('.js_hide_img2').html()
        });
    })

    $('.js_up_load_btn2').click(function(){
        $('.js_up_img_input_btn2').click()
    })

    //图片上传预览 2018.7.11
    function filecountup2(_this,files,count){
        // 文件大小限制
        // 
        console.log(files)
        if(_this.attr('data-fileSizeMax')!=''){
            var max=parseFloat(_this.attr('data-fileSizeMax'))*1024*1024;
            if(files[0].size > max){
                tip_show("文件不能大于"+parseFloat(_this.attr('data-fileSizeMax'))+"M","2");
                return false;
            } 
        }


        var file = files[0];
        var reader = new FileReader();
        reader.onloadend = function () {
            // console.log(reader.result);
            // $('.js_up_load_btn2').addClass('hide').find('input').val(reader.result);
            // $('.js_img_src2').removeClass('hide').css('background-image','url('+reader.result+')')
            getImgData(reader.result,'',1000,function(data){
                console.log(data)
                $('.js_up_load_btn2').addClass('hide').find('input').val(data);
                $('.js_img_src2').removeClass('hide').css('background-image','url('+data+')')
            })
        }
        if (file) {
            reader.readAsDataURL(file);
        }
    };


    $('.js_img_src2 a').click(function(){
        var a=layer.confirm("{:__('Are you sure you want to delete it?')}", {
          title:false,
          area:['500px', 'auto'],
          skin: 'my_confirm',
          btn: ["{:__('Confirm')}","{:__('Cancel')}"], //按钮
          btnAlign:'c'
        }, function(){
            layer.close(a);
            $('.js_up_load_btn2').removeClass('hide').find('input').val('');
            $('.js_img_src2').addClass('hide')
        });
    })

    

</script>