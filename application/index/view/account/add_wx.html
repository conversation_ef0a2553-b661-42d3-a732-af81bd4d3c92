<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 pad_b40">
    <div class="color_red pad_t10 my_close_win_title">{:__('Add Wechat Account')}</div>
    
    <div class="small_input " style="padding: 80px 0px 0 0px">
    	<div class="clearfix add_wx_div text-center">
    		<div class="img margin_b10" id="code"></div>
    		<div class="color_999">{:__('Scraperadd_Wechat')}</div>
    	</div>
    </div>
</div>

<script type="text/javascript" src="__CDN__/assets/js/pc/qrcode.min.js"></script>
<script type="text/javascript">
    //先清空
    $("#code").empty();
    //中文格式转换

    //生成二维码
    $("#code").qrcode({
        render: "canvas", // 渲染方式 table,  canvas.推荐使用 table， 兼容性高。。如果要保存二维码图片，使用canvas
        width: 120,
        height: 120,
        text: "http://www.bozhen.ltd/api/wx/bindingwx/user_id/<?=$user['id']?>",
        background:"#fff", //背景颜色。
        foreground:"#000" //码块颜色。
    });
    var timer = setInterval(function(){
        $.get("<?=url('getWxCount',array('count'=>$count))?>",function(res){
			console.log(res);
            if(res.code == 1){
                // <!-- tip_show('新增微信賬戶成功','1'); -->
                setTimeout(function(){
                    window.location.reload();
                },1500)
            }
        })
    },3000);
    setTimeout(function(){
        clearInterval(timer)
    },60000)
</script>