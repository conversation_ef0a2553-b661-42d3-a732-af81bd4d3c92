<style type="text/css">
	body .add_address_btn{padding-top: 25px;}
	body .add_address_btn a{color: #666666;}
	body .add_address_btn img{height: 16px; position: relative; top: -1px; margin-right: 5px;}
	body .add_address_btn a:hover{color: #EF436D;}
</style>
<div class="container">
	<div class="account_mana margin_tb40 clearfix">
		<div class="pull-right account_mana_r margin_l20">
			<div class="border_box pad_lr20 pad_tb10">
				<div class="title pad_b10">{:__('Real name authentication')}</div>
				<div class="rz_item clearfix">
					<div class="icon"><img src="__CDN__/assets/img/pc/auth1a.png"></div> {:__('Mobile is bound')}：<?=substr_replace($user['mobile'],'****',2,4)?>
				</div>
				<?php
					if($user['is_card']){
				?>
					<div class="rz_item clearfix">
						<div class="icon"><img src="__CDN__/assets/img/pc/auth2a.png"></div> {:__('Identity authentication')}（{:__('Certified')}）
					</div>
				<?php
					}else{
				?>
					<div class="rz_item clearfix color_999">
						<div class="icon"><img src="__CDN__/assets/img/pc/auth2a.png"></div> {:__('Identity authentication')} <a href="{:url('/index/user/editIdcard')}" class="js_ajax_win" data-width="600" class="color_red">（{:__('Click authentication')}）</a>
					</div>
				<?php
					}
				?>
				
				
				<?php
					if($user['is_card_img']){
				?>
					<div class="rz_item clearfix ">
						<div class="icon"><img src="__CDN__/assets/img/pc/auth3a.png"></div> {:__('Second authentication')}（{:__('Certified')}）</a>
					</div>
				<?php
					}else{
				?>
					<div class="rz_item clearfix color_999">
						<div class="icon"><img src="__CDN__/assets/img/pc/auth3a.png"></div> {:__('Second authentication')} 
					</div>
				<?php
					}
				?>
			</div>

			<div class="border_box pad_lr20 pad_tb10 margin_t20">
				<div class="title">{:__('Security settings')}</div>
				<div class="pad_t10 clearfix">
					<a href="{:url('user/password')}" class="links ellipsis js_ajax_win"  data-width="600"><img src="__CDN__/assets/img/pc/r.png"> {:__('Modify login password')}</a>
					<a href="{:url('/index/user/editMobile')}" data-width="600" class="links ellipsis js_ajax_win"><img src="__CDN__/assets/img/pc/r.png"> {:__('Modify mobile')}</a>
				</div>
			</div>
		</div>
		<div class="over_hide">
			<div class="border_box pad_lr20 pad_t20 pad_b30">
				<div class="clearfix user_info pad_b10">
					<div class="pull-right add_address_btn">
						<a href="{:url('/index/account/address')}"><img src="__CDN__/assets/img/pc/icon_dizhi.png">地址管理</a>
					</div>
		        	<div class="avatar_div pull-left js_ajax_win" data-width="930" data-height="650" href="{:url('/index/user/edit_avatar')}">
		        		<img src="<?=$user['avatar']?>" class="avatar">
		        		<div class="vip">
		        			<img src="__CDN__/assets/img/pc/vip.png">
		        			<span><?=$user['level']?></span>
		        		</div>
		        	</div>
		        	<div class="over_hide">
		        		<div class="nickname ellipsis"><?=substr_replace($user['mobile'],'****',2,4)?>   <span class="color_999 pad_l20 f14"><?=substr_replace($user['username'],'*',0,3)?></span></div>
		        	</div>
	        	</div>
	        	<div class="flex_ac pad_tb20 address_t">
					地址管理
					<a href="javascript:history.go(-1);" class="margin_a flex_ac"><img src="__CDN__/assets/img/pc/back.png">返回</a>
				</div>
				<div class="address_list pad_b20">
					<?php
						foreach($address as $ls){
					?>
						<div class="box">
							<div class="tt"><?=$ls['address_name']?></div>
							<div class="pp flex_ac">
								<div class="t">收<span></span>貨<span></span>人：</div>
								<div class="flex1"><?=$ls['address_name']?></div>
							</div>
							<div class="pp flex_ac">
								<div class="t">聯繫方式：</div>
								<div class="flex1"><?=substr_replace($ls['address_mobile'],'*',0,4)?></div>
							</div>
							<div class="pp flex_ac">
								<div class="t">所在地區：</div>
								<div class="flex1"><?=$_address[$ls['city']]['name']?></div>
							</div>
							<div class="pp flex_ac">
								<div class="t">詳細地址：</div>
								<div class="flex1"><?=$ls['address']?></div>
							</div>
							<a href="{:url('/index/account/address_del')}?id=<?=$ls['id']?>" class="js_ajax_confirm del" tips="您確認删除該地址嗎？"><img src="__CDN__/assets/img/pc/close2.png"></a>
							<a href="{:url('/index/account/add_address')}?id=<?=$ls['id']?>" class="edit_btn js_load_temp">編輯</a>
						</div>
					<?php
						}
					?>
				</div>
				<?php
					if(count($address) < '5'){
				?>
					<div class="flex_ac address_op_btn">
						<a href="{:url('/index/account/add_address')}?id=" class="js_load_temp">新增收貨地址</a> <span class="color_999 margin_l10">已創建<span class="red"> <?=count($address)?> </span>個收貨地址</span>
					</div>
				<?php
					}
				?>
			</div>
		</div>
	</div>
</div>

<script type="text/javascript">
	$(function(){
		$(document).on('click','.js_load_temp',function(){
			var _this=$(this);
			$.get(_this.attr('href'),function(res){
				var is_add=_this.parent().hasClass('box')?true:false;
				$('.address_form').remove()
				if(!is_add){
					$('.address_list').append(res)
				}else{
					_this.parent().after(res)
				}
			})
			return false;
		})
	})
</script>