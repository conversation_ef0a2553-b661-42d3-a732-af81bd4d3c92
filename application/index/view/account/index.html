<style type="text/css">
	body .add_address_btn{padding-top: 25px;}
	body .add_address_btn a{color: #666666;}
	body .add_address_btn img{height: 16px; position: relative; top: -1px; margin-right: 5px;}
	body .add_address_btn a:hover{color: #EF436D;}
	.new_set_div{line-height: 1;}
	.set_title{font-size: 16px; padding: 20px 0 10px 0; color: #333;}
	.set_items{padding: 5px 0; color: #666; font-size: 13px;}
	.set_items .icon{width: 16px; height: 16px; margin-right: 5px;}
	.set_items .line_add_btn{padding: 0 5px; color: #EF436D; line-height: 22px; text-decoration: none; border:1px solid #EF436D; border-radius: 3px; margin-left: 10px;}
	.switch_new{display: inline-block; margin-left: 10px; width: 36px; height: 18px; position: relative; background: #CCCCCC; border-radius: 18px;transition: 0.3s;}
	.switch_new:after{content:''; background: #fff; display: block; width: 14px; height: 14px; border-radius: 50%; position: absolute; left: 2px; top: 2px; transition: 0.3s;}
	.switch_new.active{background: #3C3D9C;}
	.switch_new.active:after{left: 20px;}
</style>
<div class="container">
	<div class="account_mana margin_tb40 clearfix">
		<div class="pull-right account_mana_r margin_l20">
			<div class="border_box pad_lr20 pad_tb10">
				<div class="title pad_b10">{:__('Real name authentication')}</div>
				<div class="rz_item clearfix">
					<div class="icon"><img src="__CDN__/assets/img/pc/auth1a.png"></div> {:__('Mobile is bound')}：<?=substr_replace($user['mobile'],'****',2,4)?>
				</div>
				<?php
					if($user['is_card']){
				?>
					<div class="rz_item clearfix">
						<div class="icon"><img src="__CDN__/assets/img/pc/auth2a.png"></div> {:__('Identity authentication')}（{:__('Certified')}）
					</div>
				<?php
					}else{
				?>
					<div class="rz_item clearfix color_999">
						<div class="icon"><img src="__CDN__/assets/img/pc/auth2a.png"></div> {:__('Identity authentication')} <a href="{:url('/index/user/editIdcard')}" class="js_ajax_win" data-width="600" class="color_red">（{:__('Click authentication')}）</a>
					</div>
				<?php
					}
				?>
				
				
				<?php
					if($user['is_card_img']){
				?>
					<div class="rz_item clearfix ">
						<div class="icon"><img src="__CDN__/assets/img/pc/auth3a.png"></div> {:__('Second authentication')}（{:__('Certified')}）
					</div>
				<?php
					}else{
				?>
					<div class="rz_item clearfix color_999">
						<div class="icon"><img src="__CDN__/assets/img/pc/auth3a.png"></div> {:__('Second authentication')} <a href="{:url('/index/user/editIdcardTwo/type/1')}" class="js_ajax_win" data-width="600" class="color_red">（{:__('Click authentication')}）</a>
					</div>
				<?php
					}
					if(!$user['lineid']){
				?>
					<div class="rz_item clearfix color_999">
						<div class="icon"><img src="__CDN__/assets/img/pc/line.png"></div> 綁定LINE <a href="javascript:;"  data-url="{:url('/index/Line/line_binding')}"  class="color_red js_get_line">（點擊綁定）</a>
					</div>
				<?php
					}else{
				?>
					<div class="rz_item clearfix color_999">
						<div class="icon"><img src="__CDN__/assets/img/pc/line.png"></div> 已綁定 <a href="javascript:;"  class="color_red js_get_line">（<?=$user['line_name']?>）</a>
					</div>
				<?php
					}
				?>
			</div>

			<div class="border_box pad_lr20 pad_tb10 margin_t20">
				<div class="title">{:__('Security settings')}</div>
				<div class="pad_t10 clearfix">
					<a href="{:url('user/password')}" class="links ellipsis js_ajax_win"  data-width="600"><img src="__CDN__/assets/img/pc/r.png"> {:__('Modify login password')}</a>
					<a href="{:url('/index/user/editMobile')}" data-width="600" class="links ellipsis js_ajax_win"><img src="__CDN__/assets/img/pc/r.png"> {:__('Modify mobile')}</a>
				</div>
			</div>
			<?php
				if($user['lineid']){
			?>
				<div class="border_box pad_lr20 pad_tb10 margin_t20">
					<div class="set_title">機器人是否為好友</div>
					<div class="new_set_div">
						<?php
							if($line_robot){
						?>
							<div class="set_items flex_ac" style="padding-top:0">
								<img src="__CDN__/assets/img/pc/success_icon.png" class="icon" alt="">
								已是好友
							</div>
							<div class="set_title">訂單通知設定</div>
							<div class="set_items flex_ac">
								開啟訂單成立通知
								<a href="javascript:;" class="switch_new <?=empty($robot['is_order_found'])?'active':' ';?>" data-url="{:url('/index/user/set_msg_notify')}" data-key="is_order_found"></a>
							</div>
							
							<div class="set_title">集運消息通知設定</div>
							<div class="set_items flex_ac">
								開啟集運稱重通知
								<a href="javascript:;" class="switch_new <?=empty($robot['is_jiyun_weighing'])?'active':' ';?>" data-url="{:url('/index/user/set_msg_notify')}" data-key="is_jiyun_weighing"></a>
							</div>
							<div class="set_items flex_ac">
								開啟集運發貨通知
								<a href="javascript:;" class="switch_new <?=empty($robot['is_jiyun_send'])?'active':' ';?>" data-url="{:url('/index/user/set_msg_notify')}" data-key="is_jiyun_send"></a>
							</div>
						<?php
							}else{
						?>
							<div class="set_items flex_ac" style="padding-top:0">
								<img src="__CDN__/assets/img/pc/alert_icon.png" class="icon" alt="">
								不是好友
								<a href="{:url('index/user/robot')}" data-width="450" class="line_add_btn js_ajax_win">立即新增</a>
							</div>
						<?php
							}
						?>
						
					</div>
				</div>
			<?php
				}
			?>
			
		</div>
		<div class="over_hide">
			<div class="border_box pad_lr20 pad_t20">
				<div class="clearfix user_info pad_b10">
					<div class="pull-right add_address_btn">
						<a href="{:url('/index/account/address')}"><img src="__CDN__/assets/img/pc/icon_dizhi.png">地址管理</a>
					</div>
		        	<div class="avatar_div pull-left js_ajax_win" data-width="930" data-height="650" href="{:url('/index/user/edit_avatar')}">
		        		<img src="<?=$user['avatar']?>" class="avatar">
		        		<div class="vip">
		        			<img src="__CDN__/assets/img/pc/vip.png">
		        			<span><?=$user['level']?></span>
		        		</div>
		        	</div>
		        	<div class="over_hide">
		        		<div class="nickname ellipsis"><?=substr_replace($user['mobile'],'****',2,4)?>   <span class="color_999 pad_l20 f14"><?=substr_replace($user['username'],'*',0,3)?></span></div>
		        	</div>
	        	</div>
	        	<div class="pad_t20 clearfix">
	        		<div class="account_box">
	        			<div class="title pad_lr20"><img src="__CDN__/assets/img/pc/icon_account01_bank.png">{:__('Payment bank account')}</div>
	        			<div class="pad_lr20 pad_b20">
							<?php
								foreach($userbank as $ls){
							?>
								<div class="bank_b clearfix">
									<div class="icon pull-left"><?=$ls['name']?></div>
									<div class="pull-left name"><?=$ls['account_name']?></div>
									<div class="over_hide"><?=$ls['account_six']?></div>
								</div>
							<?php
								}
							?>
		        			<a href="{:url('/index/account/addBank')}" class="add_btn js_ajax_win" data-width="600" data-height="880" ><img src="__CDN__/assets/img/pc/add_btn.png"></a>
	        			</div>
	        		</div>
	        		<div class="account_box margin_l20">
	        			<div class="title pad_lr20"><img src="__CDN__/assets/img/pc/icon_account02_wechat.png">{:__('Receivables')} WeChat ID</div>
	        			<div class="we_account pad_b10 clearfix">
	        				<?php
								foreach($userwx as $ls){
							?>
								<div class="we_b">
									<img src="<?=$ls['headimage']?>">
									<div class="ellipsis pad_lr10"><?=$ls['wx_account']?></div>
								</div>
							<?php
								}
							?>
	        				<div class="we_b">
	        					<a href="{:url('/index/account/addWx')}" class="add_btn js_ajax_win" data-width="610" data-height="370"><img src="__CDN__/assets/img/pc/add_btn.png"></a>
	        					<div class="ellipsis pad_lr10"></div>
	        				</div>

	        			</div>
	        		</div>
	        	</div>
	        	<div class="pad_t20 clearfix">
	        		<div class="account_box">
	        			<div class="title pad_lr20"><img src="__CDN__/assets/img/pc/icon_account03_zhifubao.png">{:__('Receivable Alipay account')}</div>
	        			<div class="pad_lr20 pad_b20">
							<?php
								foreach($userali as $ls){
							?>
								<div class="bank_b ali clearfix">
									<div class="pull-left name"><?=$ls['name']?></div>
									<div class="over_hide"><?=$ls['ali_account']?></div>
								</div>
							<?php
								}
							?>
		        			<a href="{:url('/index/account/add_alipay')}" class="add_btn js_ajax_win" data-width="610" data-height="370"><img src="__CDN__/assets/img/pc/add_btn.png"></a>
	        			</div>
	        		</div>
					<div class="account_box margin_l20">
	        			<div class="title pad_lr20"><img src="__CDN__/assets/img/pc/icon_account02_wechat.png">{:__('Receivables')} {:__('WeChat artificial')}</div>
	        			<div class="pad_lr20 pad_b20">
							<?php
								foreach($userrg as $ls){
							?>
								<div class="bank_b ali clearfix">
									<div class="pull-left name"><?=$ls['ali_account']?></div>
									<div class="over_hide"><?=$ls['name']?></div>
								</div>
							<?php
								}
							?>
		        			<a href="{:url('/index/account/add_rengong')}" class="add_btn js_ajax_win" data-width="610" data-height="370"><img src="__CDN__/assets/img/pc/add_btn.png"></a>
	        			</div>
	        		</div>
	        	</div>
				
				
				<div class="pad_t20 clearfix">
					<div class="account_box">
						<div class="title pad_lr20"><img style="height: 16px;" src="__CDN__/assets/img/pc/icon_account04.png">企業收款帳號</div>
						<div class="pad_lr20 pad_b20">
							<?php
								foreach($userqiyebank as $ls){
							?>
								<div class="bank_b ali company clearfix">
									<img src="__CDN__/assets/img/pc/bank_icon.png" class="bank_icon pull-left" alt="">
									<div class="over_hide">
										<div class="clearfix">
											<div class="pull-left name2"><?=$ls['head_bank']?>(<?=$ls['bank_name']?>)</div>
										</div>
										<div class="clearfix">
											<div class="pull-left name"><?=$ls['bank_username']?></div>
											<div class="over_hide"><?=$ls['bank_account']?></div>
										</div>
									</div>
								</div>
							<?php
								}
								if(count($userqiyebank) < 5){
							?>
								<a href="{:url('/index/account/add_company')}" class="add_btn js_ajax_win" data-width="610" data-height="370"><img src="__CDN__/assets/img/pc/add_btn.png"></a>
							<?php
								}
							?>
						</div>
					</div>
				</div>
				
			</div>
		</div>
	</div>
</div>

<script type="text/javascript">
	$(function(){
		let is_ajax_get=false;
		$('.js_get_line').click(function(){
			if(is_ajax_get){return false;}
			is_ajax_get=true;
			$.post($(this).attr('data-url'),{},function(res){
				if(res.code==1){
					//window.open(res.url)
					location.href=res.url
					is_ajax_get=false;
				}else{
					is_ajax_get=false;
				}
			})
		})
		
		let is_ajax_set=false;
		$('.switch_new').click(function(){
			if(is_ajax_set){return false;}
			if($(this).hasClass('active')){
				$(this).removeClass('active')
			}else{
				$(this).addClass('active')
			}
			is_ajax_set=true;
			let obj={}
			for(let i=0;i<$('.switch_new').length;i++){
				obj[$('.switch_new').eq(i).attr('data-key')]=$('.switch_new').eq(i).hasClass('active')?1:0
			}
			
			$.post($(this).attr('data-url'),obj,function(res){
				is_ajax_set=false;
				if(res.code==1){
					
				}else{
					
				}
			})
		})
		
	})
</script>