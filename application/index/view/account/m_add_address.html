
<style type="text/css">
.my_close_win .input_box .input_tt{width: 90px;}
.my_close_win .input_box .input_rr .js_province{width: 30%; margin-right: 10px;}
.my_close_win .input_box .input_rr .js_city{width: 70%;}
.my_close_win .input_box  textarea{padding: 10px 10px;}
@media (max-width: 480px){
	.my_close_win .input_box{margin-bottom: 0.1rem;}
	.my_close_win .address_form .input_box .input_tt{width: 70px;font-size: 0.24rem;}
	.address_form .input_box .input_tt span{display: inline-block;  width: 7px; height: 10px;}
	.address_form .cancel{color: #999; margin-left: 30px;}
	.input_box input, .input_box select, .input_box textarea{font-size: 0.24rem;};
}
</style>
<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 pad_b40">
    <div class="color_red pad_t10 my_close_win_title">新增/編輯地址</div>
    <div class="address_form margin_b10">
    	<form action="{:url('index/Account/add_address')}" autocomplete="off" class="small_input edit_info_form" style="padding: 10px 10px 0 0px">
    		<!-- 编辑时必传 id -->
    		<input type="hidden" name="id" value="<?=empty($info['id'])?'':$info['id']?>">
    		<div class="clearfix">
				<div class="input_box flex_ac">
				    <div class="input_tt">收<span></span>貨<span></span>人:</div>
				    <div class="input_rr flex1">
				        <input type="text" style="height: 40px; width:100%;" placeholder="請輸入" name="name" value="<?=empty($info['address_name'])?' ':$info['address_name']?>" maxlength="30" is_required="true" empty_tip="請輸入收貨人">
				    </div>
				</div>
				<div class="input_box flex_ac">
				    <div class="input_tt">聯繫方式:</div>
				    <div class="input_rr flex1">
				        <input type="text" style="height: 40px; width:100%;" placeholder="請輸入" name="mobile" value="<?=empty($info['address_mobile'])?' ':$info['address_mobile']?>" maxlength="20" is_required="true" empty_tip="請輸入聯繫方式">
				    </div>
				</div>
				
    			<div class="input_box flex">
    			    <div class="input_tt">所在地區:</div>
    			    <div class="input_rr flex1 flex">
    					<!-- 如果是修改，data-val 给值，否则给空 -->
    			        <select name="province" data-val="<?=empty($info['type'])?'0':$info['type']?>" is_required="true" class="flex_s js_province" empty_tip="請選擇所在地區">
    					</select>
    					<!-- 如果是修改，data-val 给值 -->
    					<select name="city"  data-val="<?=empty($info['city'])?'0':$info['city']?>"  is_required="true" class="flex1 js_city"  empty_tip="請選擇所在地區">
    					</select>
    			    </div>
    			</div>
    			<div class="input_box flex">
    			    <div class="input_tt">詳細地址:</div>
    			    <div class="input_rr flex1">
    			        <textarea type="text" style="height: 80px" placeholder="請輸入" name="detail" maxlength="50" is_required="true" empty_tip="請輸入詳細地址"><?=empty($info['address'])?' ':$info['address']?></textarea>
    			    </div>
    			</div>
    		</div>
			<div class="flex_ac_jc pad_t20"><a href="javascript:;" class="sub_btn small js_form_sub" data-text="保存" data-loadding="保存中···" data-type="new_location">保存</a></div>
    	</form>
    </div>
</div>



<script>

var address_data=<?=$address?>;
function init_pro(val){
	var html='<option value="">請選擇</option>';
	var is_val=false;
	for(var i=0;i<address_data.length;i++){
		is_val=address_data[i].id==val?true:false;
		html+='<option '+(is_val?'selected':'')+' value="'+address_data[i].id+'">'+address_data[i].name+'</option>'
	};
	$('.js_province').html(html);
	init_city(val,$('.js_city').attr('data-val'))
}
init_pro($('.js_province').attr('data-val'));
function init_city(val1,val2){
	var html='<option value="">請選擇</option>';
	for(var i=0;i<address_data.length;i++){
		if(address_data[i].id==val1){
			var list=address_data[i].list;
			for(var k=0;k<list.length;k++){
				html+='<option '+(val2==list[k].id?'selected':'')+' value="'+list[k].id+'">'+list[k].name+'</option>'
			};
			break;
		}
	};
	$('.js_city').html(html);
}
$('.js_province').change(function(){
	var val=$('.js_province').val()
	init_city(val,'')
})
</script>