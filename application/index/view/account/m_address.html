<style type="text/css">
	body{padding-bottom: 1.2rem;}
    .footer_new{ display: none }
	.ad_title{height: 0.88rem;}
	.ad_title img{height: 0.28rem; margin-right: 0.2rem; }
	.ad_title a{color: #EF436D;}
	.address_list .box{background: #fff; padding-top:0.2rem ; position: relative; margin-top: 0.2rem;}
	.address_list .box .pp{line-height: 0.4rem;margin-top:0.1rem;}
	.address_list .box .pp .t{color: #666;}
	.address_list .box .pp .t span{display: inline-block;  width: 0.14rem; height: 10px;}
	.op_btns{display: none;}
	.address_list .box.active .op_btns{display: block;}
	.op_btns a{width: 50%; text-align: center; line-height: 0.88rem;}
	.op_btns a.blue{color: #00A4FF;}
	.op_btns a.red{color:#FF5050;}
	.op_btns .line{width: 1px; height: 0.88rem; background: #eee;  transform: scale(0.5,1);}
	.foot_b{width: 100%; height: 1.2rem; position: fixed; left: 0; bottom: 0; z-index: 20;}
	.foot_b .sub_btn{width: 100%; border-radius: 0; line-height: 0.88rem;}
</style>
<div class="margin_t20 pad_lr30 bg_fff ad_title flex_ac">
	<img src="__CDN__/assets/img/wap/icon_title_dizhi.png" alt="">
	地址管理
	<a href="javascript:;" class="margin_a js_load_temp">編輯</a>
</div>
<div class="address_list pad_b20">
	<div class="box">
	<?php
		foreach($address as $ls){
	?>
		<div class="pad_lr20 pad_b20">
			<div class="pp flex_ac">
				<div class="t">收<span></span>貨<span></span>人：</div>
				<div class="flex1"><?=$ls['address_name']?></div>
			</div>
			<div class="pp flex_ac">
				<div class="t">聯繫方式：</div>
				<div class="flex1"><?=substr_replace($ls['address_mobile'],'*',0,4)?></div>
			</div>
			<div class="pp flex_ac">
				<div class="t">所在地區：</div>
				<div class="flex1"><?=$_address[$ls['city']]['name']?></div>
			</div>
			<div class="pp flex">
				<div class="t">詳細地址：</div>
				<div class="flex1"><?=$ls['address']?></div>
			</div>
		</div>
		<div class="op_btns">
			<div class="common_border"></div>
			<div class="flex_ac">
				<a href="{:url('/index/account/add_address')}?id=<?=$ls['id']?>" class="js_ajax_win blue">編輯</a>
				<div class="line flex_s"></div>
				<a href="{:url('/index/account/address_del')}?id=<?=$ls['id']?>" class="js_ajax_confirm red" data-width="300" tips="您確認删除該地址嗎？">删除</a>
			</div>
		</div>
	<?php
		}
	?>
	</div>
</div>
<?php
	if(count($address) < '5'){
?>
	<div class="foot_b flex_ac_jc bg_fff pad_lr20">
		<a href="{:url('/index/account/add_address')}?id=" class="sub_btn js_ajax_win">新增地址</a>
	</div>
<?php
	}
?>
<script>
$(function(){
	$('.js_load_temp').click(function(){
		if($(this).hasClass('active')){
			$(this).removeClass('active').html('編輯');
			$('.address_list .box').removeClass('active')
		}else{
			$(this).addClass('active').html('完成');
			$('.address_list .box').addClass('active')
		}
	})
})
</script>