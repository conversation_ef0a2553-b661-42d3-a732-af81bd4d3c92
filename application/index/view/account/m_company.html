<style type="text/css">
    .footer_new{ display: none }
	.bank_l .box{line-height: 0.6rem; font-size: 0.28rem;}
	.titl{color: #fff;}
	.titl img{height: 0.28rem; margin-right: 0.2rem;}
</style>
<div class="bank_l pad_lr20 pad_t20 clearfix">
	<?php
		foreach($userqiyebank as $ls){
	?>
		<a href="javascript:;" class="box b">
			<div class="flex_ac titl">
				<img src="__CDN__/assets/img/wap/bank_icon.png" ><?=$ls['head_bank']?>(<?=$ls['bank_name']?>)
			</div>
			<div class="clearfix flex_ac" style="height: 1.2rem;">
				<div class="text-left item ellipsis"><?=$ls['bank_username']?></div>
				<div class="text-right item flex1 ellipsis"><?=$ls['bank_account']?></div>
			</div>
		</a>
	<?php
		}
		if(count($userqiyebank) < 5){
	?>
		<a href="{:url('/index/account/add_company')}" class="box add js_ajax_win">
			<div class="clearfix">
				<img src="__CDN__/assets/img/wap/add_bank.png">
				<div>添加企業收款帳號</div>
			</div>
		</a>
	<?php
		}
	?>
</div>
