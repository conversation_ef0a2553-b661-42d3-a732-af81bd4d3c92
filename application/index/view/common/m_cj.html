


<style type="text/css">
    .cj_div_mark,.cj_div_mark2{width: 100%;height: 100%; display: none; position: fixed; left: 0; top: 0; background: rgba(0,0,0,0.7); z-index: 100}
    .cj_div_mark.active,.cj_div_mark2.active{display: block;}
    .cj_div{width: 385px; height: 740px; opacity: 0; display: none; position: fixed; left: 50%; margin-left: -192px;top: 50%; margin-top: -400px; background: url(__CDN__/assets/img/pc/cj/bg.png); background-size: 100% 100%;transform: scale(1.5); z-index: 110; transition:all  0.3s cubic-bezier(.17,.67,.48,1.36) 0s;}
    .cj_div.active{opacity: 1; transform: scale(0.7);}
    .cj_div2{height: 640px;top: 50%; margin-top: -370px;background: url(__CDN__/assets/img/pc/cj/zj_bg.png); background-size: 100% 100%; }
    .cj_div .close{width: 40px; height: 40px; position: absolute; right: -40px; top: 125px; opacity: 0.5;transition: 0.1s}
    .cj_div .close:hover{ opacity: 0.7 }
    .cj_div .title{font-size:34px; text-align: center; color: #fff; padding-top: 168px; font-weight: bold;}
    /*.cj_div2 .title{padding-top: 142px}*/
    .grid_d{width: 334px; margin: 30px auto 0 auto;padding-left: 7px; }
    .grid_d .box{width: 108px; height: 110px; float: left; text-align: center; background: url(__CDN__/assets/img/pc/cj/btn.png); background-size: 100% 100%;}
    .cj_div2 .grid_d{padding-top: 73px}
    .cj_div2 .grid_d .box{ float: none; margin: 0 auto; padding-top: 1px }
    .grid_d .box.cj{background: url(__CDN__/assets/img/pc/cj/cj_btn.png); background-size: 100% 100%; cursor: pointer;}
    .grid_d .box.active{background: url(__CDN__/assets/img/pc/cj/btna.png); background-size: 100% 100%;}
    .grid_d .box .img{width: 54px; height: 54px; margin: 0 auto; text-align: center; line-height: 54px; margin-top: 10px}
    .grid_d .box .img img{max-width: 50px; max-height: 50px}
    .grid_d .box .tt{font-size: 14px; color: #333;padding-top: 3px}
    .grid_d .box.active .tt{color: #FF7D00}
    .gz_tt{color: #FF7D00; font-weight: bold; text-align: center; padding-top: 15px;}
    .gz_content{height: 80px; overflow: auto; color: #333333; font-size: 12px; line-height: 20px; margin-top: 5px; padding: 0 27px;}
    .zjjl{text-align: center; line-height: 40px}
    .zjjl img{height: 13px}
    .zjjl a{ color: #FF7D00; font-size: 12px; text-decoration: underline; padding: 0 16px }
    .jz_tt{color:  #FF7D00; text-align: center; font-size: 18px; margin-top: 10px}
    .other_zj{line-height: 50px; padding: 0 26px; position: absolute; left: 0; bottom: 12px; width: 100%; color: #333333}
    .other_zj span{color: #EF426D}
    .other_zj span.time{color: #999999}
    .address_btn a{ color: #fff; display: inline-block; width: 120px; height: 32px; line-height: 32px; background: #FF7D00; border-radius: 4px; text-align: center; margin-top: 30px }
    .dm{width: 100%; height:130px; position: fixed; left: 0; bottom: 0; z-index: 102; overflow: hidden; display: none }
    .dm.active{display: block;}
    .dm .box{ position: absolute;word-break: keep-all; background: #fff; white-space: nowrap; border-radius: 20px; width: 230px; line-height: 20px; padding: 0 10px; font-size: 10px  }
    .dm .box span{color: #EF426D}
    .dm .box span.time{color: #999999; margin-left: 0px}

    .cjbtn img{width: 120px;
    position: fixed;
    left: 0px;
    bottom: 80px;
    z-index: 30;}
</style>
<div class="cj_div_mark"></div>
<div class="cj_div js_cj_div">
    <a href="javascript:;" class="js_close_cj"><img src="__CDN__/assets/img/pc/cj/close.png" class="close"></a>
    <div class="title">幸運大抽獎</div>
    <div class="grid_d clearfix">
	<?php
		foreach($luck_list as $key=>$ls){
	?>
		<div class="box" data-id="<?=$ls['id']?>">
            <div class="img"><img src="<?=$ls['avatar']?>"></div>
            <div class="tt ellipsis"><?=$ls['title']?></div>
        </div>
	<?php
		if($key == '3'){
	?>
		<div class="box cj js_start_cj">
        </div>
	<?php
			}
		}
	?>
    </div>
    <div class="gz_tt">抽獎規則</div>
    <div class="gz_content">
        一.抽奖规则抽奖规则抽奖规则抽奖规则抽奖规则抽奖规则. <br>
        一.抽奖规则抽奖规则抽奖规则抽奖规则抽奖规则抽奖规则.<br>
        一.抽奖规则抽奖规则抽奖规则抽奖规则抽奖规则抽奖规则.<br>
        一.抽奖规则抽奖规则抽奖规则抽奖规则抽奖规则抽奖规则.
    </div>
    <div class="zjjl">
        <img src="__CDN__/assets/img/pc/cj/tt_bg.png">
        <a href="<?=url('user/prize')?>">我的中獎記錄</a>
        <img src="__CDN__/assets/img/pc/cj/tt_bg2.png">
    </div>
</div>
<div class="dm js_dm">
    <!-- <div class="box">恭喜<span>用户123456</span>抽中了iMAC 电脑<span class="time">1分鐘前</span></div> -->
</div>


<div class="cj_div_mark2"></div>
<div class="cj_div cj_div2 js_cj_div2">
    <a href="javascript:;" class="js_close_cj2"><img src="__CDN__/assets/img/pc/cj/close.png" class="close"></a>
    <div class="title">幸運大抽獎</div>
    <div class="grid_d js_grid_d clearfix">
        <div class="box">
            <div class="img"><img src=""></div>
            <div class="tt ellipsis"></div>
        </div>
    </div>
    <div class="jz_tt">恭喜您抽中了 <span class="js_jz_tt"></span>！</div>
    <div class="text-center address_btn js_address_btn"><a href="<?=url('index/sele_address')?>" class="js_ajax_win">選擇收貨地址</a></div>
    <!-- <div class="other_zj clearfix"><span class="pull-right time">1分鐘前</span>恭喜<span>用户123456</span>抽中了iMAC 电脑</div> -->
</div>
<input type="hidden" class="js_tip_val" value="<?=$luck_tips?>">
<?php
	if($luck_open && $user['mobile'] == '0906211888'){
?>
<a href="javascript:;" class="cjbtn"><img src="__CDN__/assets/img/pc/cj/cjbtn.png"></a>
<?php
	}
?>
<script type="text/javascript">
	var jp_id='';
	$(function(){
		var time_set=null;
	    var time_index=0;
	    var jp_index=null;
	    
	    //抽奖
	    function show_cj(){
	        $('.js_cj_div,.js_dm').css('display','block');
	        $('.cj_div_mark').addClass('active');
	        setTimeout(function(){
	            $('.js_cj_div').addClass('active');
	        },30)
	    }
	    $('.cjbtn').click(function(){
	        show_cj()
	    })
	    function hide_cj(){
	        $('.js_cj_div').removeClass('active');
	        setTimeout(function(){
	            $('.cj_div_mark').removeClass('active');
	            setTimeout(function(){
	                $('.js_cj_div').css('display','none')
	            },30)
	        },300)
	    }

	    function show_cj2(){
	        $('.js_cj_div2').css('display','block')
	        $('.cj_div_mark2').addClass('active');
	        setTimeout(function(){
	            $('.js_cj_div2').addClass('active');
	        },30)
	    }

	    $('.js_close_cj').click(function(){
	        $('.js_dm').css('display','none')
	        hide_cj()
	    })
	    $('.js_close_cj2').click(function(){
	        $('.js_cj_div2').removeClass('active');
	        $('.js_dm').css('display','none')
	        setTimeout(function(){
	            $('.cj_div_mark2').removeClass('active');
	            setTimeout(function(){
	                $('.js_cj_div2').css('display','none')
	            },30)
	        },300)
	    })

	    var indexs=[0,1,2,5,8,7,6,3];
	    $('.js_start_cj').click(function(){
	        if(time_index!=0){
	            return false;
	        }
	    	layer.confirm($('.js_tip_val').val(), {
	    	  title:false,
	    	  area:['500px', 'auto'],
	    	  skin: 'my_confirm',
	    	  btn: ['確認','取消'], //按钮
	    	  btnAlign:'c'
	    	}, function(){
				layer.closeAll();
	    	    start_run()   
	    	});
	    })

	    function start_run(){
	        time_index=0;
	        jp_index=null;
	        if(time_set){
	            clearInterval(time_set)
	        }
	        time_set=setInterval(function(){
	            time_index++;
	            $('.js_cj_div .box').eq(indexs[time_index%indexs.length]).addClass('active').siblings('.box').removeClass('active');
	            if(jp_index==indexs[time_index%indexs.length]){
	                clearInterval(time_set);
	                
	                $('.js_grid_d .tt,.js_jz_tt').html($('.js_cj_div .box').eq(jp_index).find('.tt').html());
	                $('.js_grid_d .img img').attr('src',$('.js_cj_div .box').eq(jp_index).find('.img img').attr('src'))
	                setTimeout(function(){
	                    time_index=0;
	                    hide_cj()
	                    show_cj2()
	                },2000)
	            }
	        },100)



	        
	         $.post('<?=url('index/luck')?>',{},function(res){
	             // 未中奖怎么处理等对接时候再讨论
	             if(res.status==1){
	                 var id=res.id; //奖品id 。。一定要给，对应上面宫格里的 data-id
	                 jp_id=id;
	                 var type=res.type;  //奖品类型 1 虚拟 2 实物
	                 var item=$('.cj_div .box[data-id="'+id+'"]');
	                 var index=$('.cj_div .box').index(item);
	                 setTimeout(function(){
	                     jp_index=index;
	                     if(type==1){
	                         $('.js_address_btn a').css('display','none')
	                     }else{
	                         $('.js_address_btn a').css('display','inline-block')
	                     }
	                 },4000)
	             }else{
	                 // 其他错误
	                 tip_show(res.msg,'2');
	                 clearInterval(time_set);
	                 time_index=0;
	                 $('.cj_div .box').removeClass('active')
	             }
	          
	         })

	        // 模拟中奖，开发删除
	       //var id='7';
	       //jp_id=id;
	       //var type=2;
	       //var item=$('.js_cj_div .box[data-id="'+id+'"]');
	       //var index=$('.js_cj_div .box').index(item);
	       //setTimeout(function(){
	       //    jp_index=index;
	       //    if(type==1){
	       //        $('.js_address_btn a').css('display','none')
	       //    }else{
	       //        $('.js_address_btn a').css('display','inline-block')
	       //    }
	       //},3000)
	    }



	    // 中奖记录列表 （不宜太多。。推荐取最近的 200条）
	    var zj_list=<?=$luck_user?>;
	    var top=[10,40,70,100];

	    var dm_index=0;
	    var old_top=null;
	    function get_top(){
	        var t=parseInt(Math.random()*top.length);
	        if(t==old_top){
	            return get_top()
	        }else{
	            old_top=t;
	            return old_top
	        }
	    }
	    function add_dm(){
	        var item=zj_list[dm_index];
	        var top_val=get_top();
	        $('.js_dm').append('<div style="left:'+$(window).width()+'px;top:'+top[top_val]+'px;" class="box"><span class="time pull-right">'+item.time+'</span>恭喜<span>'+item.name+'</span>抽中了'+item.goods+'</div>');
	        setTimeout(function(){
	            dm_index++;
	            if(dm_index>=zj_list.length){
	                dm_index=0
	            }
	            add_dm()
	        },3500+Math.random()*2000)
	    }
	    add_dm();
	    setInterval(function(){
	        var items=$('.js_dm .box');
	        for(var i=0;i<items.length;i++){
	            var left=parseInt(items.eq(i).css('left'));
	            items.eq(i).css('left',(left-1));
	            if(left-1<-250){
	                items.eq(i).remove();
	            }
	        }
	    },30)
    })
</script>