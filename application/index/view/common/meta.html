<meta charset="utf-8">
<title>{$seo.title}</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
<meta name="renderer" content="webkit">
<meta name="keywords" content="{$seo.keywords}">
<meta name="description" content="{$seo.description}">

<link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico" />
<link href="__CDN__/assets/css/elementui/common.css?v={$Think.config.site.version}123" rel="stylesheet">
<link href="__CDN__/assets/css/pc/boot.css?v={$Think.config.site.version}" rel="stylesheet">
<link href="__CDN__/assets/css/pc/common.css?v={$Think.config.site.version}456" rel="stylesheet">
<link href="__CDN__/assets/css/certify.css?v={$Think.config.site.version}" rel="stylesheet">
<link href="__CDN__/assets/css/swiper-bundle.min.css?v={$Think.config.site.version}" rel="stylesheet">
<link href="__CDN__/assets/css/pc/img_smooth_check.css?v={$Think.config.site.version}" rel="stylesheet">
<!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
<!--[if lt IE 9]>
  <script src="__CDN__/assets/js/html5shiv.js"></script>
  <script src="__CDN__/assets/js/respond.min.js"></script>
<![endif]-->
<script type="text/javascript">
    var require = {
        config: {$config|json_encode}
    };
    // 文件上传接口
    var _FILE_UPLOAD_="{:url('uploadPictureList')}";
    // 临时文件删除接口（即上传了，表单没有提交的临时文件）
	var _FILE_REMOVE_="{:url('temp_file_remove')}";
	// 文件删除接口（修改时，已有文件的删除）
	var _FILE_REMOVE_UPLOAD_="{:url('upload_file_remove')}";
</script>
<!-- Google 标签 (gtag.js) --> <script async src=" https://www.googletagmanager.com/gtag/js?id=AW-689144482 "></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'AW-689144482'); </script>
<!-- Google tag (gtag.js) --> <script async src="https://www.googletagmanager.com/gtag/js?id=G-E8L2CV5XBK"></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'G-E8L2CV5XBK'); </script>
