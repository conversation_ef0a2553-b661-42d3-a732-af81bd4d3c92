
var _FILE_UPLOAD_='<?=U('uploadPictureList')?>';
var _FILE_REMOVE_='<?=U('temp_file_remove')?>';
var _FILE_REMOVE_UPLOAD_='<?=U('upload_file_remove')?>';


//图片上传预览 2018.7.11
function filecount(_this,files,count){
    // 文件大小限制
    if(_this.attr('data-fileSizeMax')!=''){
        var max=parseFloat(_this.attr('data-fileSizeMax'))*1024*1024;
        for(i=0;i<files.length;i++){
            if(files[i].size > max){
                tips("文件不能大于"+parseFloat(_this.attr('data-fileSizeMax'))+"M",1500,"error");
                return false;
                break;
            }  
        }
    }

    // 文件数量限制
    // if(_this.attr('data-maxMum')!=''){
    //  var max=parseInt(_this.attr('data-maxMum'));
       //  if(files.length>max){
       //   tips("文件不能大于"+parseFloat(_this.attr('data-fileSizeMax'))+"M",1500,"error");
       //      return false;
       //  }
    // }
    
        var _parents=_this.parents('.up_load_image');
        if(_this.attr('data-type')=="video"){
            if($('.img_s_pre').find('div.l_tips').length<=0){
                var _html='<div class="l_tips hide"></div>';
                $('.img_s_pre').append(_html)
            }
            
        }
        // 新建一个form 上传图片

        var formData = new FormData();
        for (var i = 0; i < files.length; i++) {
          formData.append(_this.attr('data-filename'),files[i]);
        }

        $.ajax({
        url: _FILE_UPLOAD_,
        type: 'POST',
        data:formData,
        processData: false, //用来回避jquery对formdata的默认序列化，XMLHttpRequest会对其进行正确处理  
        contentType: false, //设为false才会获得正确的conten-Type  
        xhr: function() { //用以显示上传进度  
            var xhr = $.ajaxSettings.xhr();
            if (xhr.upload) {
                xhr.upload.addEventListener('progress', function(event) {
                    var percent = Math.floor(event.loaded / event.total * 100);
                    $('.img_s_pre').find('.l_tips').removeClass('hide').html('已上传'+percent + "%");
                    console.log("进度"+percent + "%");
                     // $('.js_uping').html('已上传'+percent + "%");
                    
                    // document.querySelector("#progress .progress-item").style.width = percent + "%";
                }, false);
            }
            return xhr
        },
        success: function(res) {
            if (res.status == "1") {
                tips(res.msg,1500, 'success');
                var _info=res.info;
                var max_num=parseInt(_this.attr('data-maxMum'));
                for(i=0;i<_info.length;i++){
                    var o_num=_parents.find('.js_up_img_s').length;
                    if(o_num<=max_num-1){
                        if(_this.attr('data-type')=="img"){
                            var _add_html='<div class="img_s js_up_img_s" style="background:url('+_ROOT_+'/'+_info[i].save_path+') center center; background-size: cover">';
                                _add_html+='<a href="javascript:;" class="js_dele_this_img" data-id="'+_info[i].id+'"><img src="'+_STATIC_+'/img/dele.png" alt=""></a><input type="hidden" name="'+_this.attr('data-name')+'[]" value="'+_info[i].id+'" /><div class="set_cover">设为封面</div></div>';
                                _parents.find('.img_s_pre').before(_add_html);
                            if(o_num==max_num-1){
                                _parents.find('.img_s_pre').addClass('hide');
                            }
                        }

                        if(_this.attr('data-type')=="file"){

                            var _add_html='<div class="img_s file_img_s js_up_img_s"><div class="file_name ellipsis">文件名称：'+_info[i].file_name+'</div>';
                                _add_html+='<a href="javascript:;" class="js_dele_this_img" data-id="'+_info[i].id+'"><img src="'+_STATIC_+'/img/dele.png" alt=""></a><input type="hidden" name="'+_this.attr('data-name')+'[]" value="'+_info[i].id+'" /></div>';
                                _parents.find('.img_s_pre').before(_add_html);
                            if(o_num==max_num-1){
                                _parents.find('.img_s_pre').addClass('hide');
                            }
                        }

                        if(_this.attr('data-type')=="video"){
                            var _add_html='<div class="img_s img_s_video js_up_img_s">';
                                _add_html+='<a href="javascript:;" class="js_dele_this_img" data-id="'+_info[i].id+'"><img src="'+_STATIC_+'/img/dele.png" alt=""></a><video src="'+_ROOT_+'/'+_info[i].save_path+'" preload controls></video><input type="hidden" name="'+_this.attr('data-name')+'" value="'+_info[i].id+'" /></div>';
                                _parents.find('.img_s_pre').before(_add_html);
                            if(o_num==max_num-1){
                                _parents.find('.img_s_pre').addClass('hide');
                            }
                        }
                    }
                }

                _parents.find('.img_s_pre').find('.l_tips').addClass('hide').html('');
                
                // 成功以后
            } else {
                tips(res.msg,1500, 'error');
            }

            $('#up_img_form').remove();
            _this.val('')
        }
    })


};


// 删除2018.7.11

$(document).on('click','.js_dele_this_img',function(){
    var _this=$(this);
    var ico="ask";
    var _id=_this.attr('data-id');
    var _info_id = _this.attr('data-info_id');
    var _parents=_this.parents('.up_load_image');
    var _url=_FILE_REMOVE_;
    var save_path = "";
    var table = "";
    if(_info_id!=undefined) {
        var _url=_FILE_REMOVE_UPLOAD_;
        var save_path = _this.attr('data-save_path');
        var table = _this.attr('data-table');
    }
    var d=dialog({
        fixed: true,
        title:"系统提示",
        content:'<div class="font_ico '+ico+'">确认删除吗？</div>',
        ok:function(){
            $.post(_url,{file_id:_id,save_path:save_path,table:table},function(data){
                if(data.status=="1"){
                    tips(data.msg,1500,'success');
                    _parents.find('.js_dele_this_img[data-id="'+_id+'"]').parents('.js_up_img_s').remove();
                    _parents.find('.img_s_pre').removeClass('hide');
                    if(_id==_parents.find('.js_cover').val()){
                        _parents.find('.js_cover').val('')
                    }
                }else{
                    tips(data.msg,1500,'error')
                }
            })
        },
        cancel:function(){
            d.close().remove();
            return false;
        },
        lock:true
    });
    d.showModal();
})

// 图片编辑时候是否显示添加按钮 2018.7.11

function check_show_up(){
    var up_load_image=$('.up_load_image');

    for(i=0;i<up_load_image.length;i++){
        var max_num= parseInt(up_load_image.eq(i).find('input[type="file"]').attr('data-maxMum'));
        var o_num= up_load_image.eq(i).find('.js_up_img_s').length;
        if(o_num>=max_num){
            up_load_image.eq(i).find('.img_s_pre').addClass('hide')
        }
    }
}

$(function(){
    check_show_up()
})

// 图片上传 设为封面
$(document).on('click','.set_cover',function(){
    var _this=$(this);
    var _id=_this.siblings('a').attr('data-id');
    _this.parents('.up_load_image').find('input.js_cover').val(_id);
    _this.html('封面').parents('.js_up_img_s').siblings('.js_up_img_s').find('.set_cover').html('设为封面')
})




<!-- 切记 父级不要用lable 应该用div，，<div class="form-group"> -->
<div class="clearfix">
    <div class="up_load_image clearfix">
        <!-- 如果已上传 如果已上传，使用已上传图片 -->

        <!--修改时 在这里循环已经 存在的图片
            data-table： 该文件存的表， 用于删除
            data-save_path： 该文件存的  字段， 用于删除
            data-info_id： 前端用的，不用管
            data-id： 文件存储的id
         -->
        <div class="img_s js_up_img_s" style="background:url(__CDN__/assets/img/pc/index_banner.jpg) center center; background-size: cover">
            <a href="javascript:;" data-table="goods_img" data-save_path = "imgs" data-info_id = "1" class="js_dele_this_img" data-id="2"><img src="__CDN__/assets/img/dele.png" alt=""></a>
        </div>
        <!--修改时 在这里循环已经 存在的图片 -->
        <a href="javascript:;" class="img_s img_s_pre">
            <img class="img" src="__CDN__/assets/img/up_icon.png" alt="">
            <!-- input 配置  
                文件大小限制 单位 M  ,无限制留空 data-fileSizeMax=""
                data-maxMum 最大上传数量
                data-filename 文件预上传 的name
                data-name 表单提交的name
            -->
            <input type="file" data-type="img" accept="image/*" multiple data-maxMum="3" data-fileSizeMax="1" onchange="filecount($(this),this.files)" data-filename="imgs[]" data-name="save_path" />
        </a>
    </div>
</div>


// <!-- 通用上传视频  切记 父级不要用lable 应该用div，，<div class="form-group">-->
// <div class="clearfix">
//     <div class="up_load_image up_load_video clearfix">
//         <?php
//             if(isset($info['url']) && !empty($info['url'])){
//         ?>
//         <div class="img_s img_s_video js_up_img_s">
//             <a href="javascript:;" data-table="goods" data-save_path = "url" data-info_id = "<?=$info['id']?>" class="js_dele_this_img" data-id="<?=$info['id']?>"><img src="__STATIC__/img/dele.png" alt=""></a>
//             <video src="__ROOT__/<?=$info['url']?>" preload controls></video>
//         </div>
//         <?php
//             }
//         ?>
//         <!--
//         <div class="img_s img_s_video js_up_img_s">
//             <a href="javascript:;" data-table="SysEmp" data-save_path = "headimg" data-info_id = "<?=$info['owner_id']?>" class="js_dele_this_img" data-id="<?=$info['id']?>"><img src="__STATIC__/img/dele.png" alt=""></a>
//             <video src="__IMG__/cache/video.mp4" preload controls></video>
//         </div>
//         -->
//         <a href="javascript:;" class="img_s img_s_pre">
//             <img class="img" src="__STATIC__/img/up_icon.png" alt="">
//             <!-- input 配置  
//                 文件大小限制 M ,无限制留空 data-fileSizeMax=""
//                 data-maxMum 最大上传数量
//                 data-filename 文件预上传 的name
//                 data-name 表单提交的name
//             -->
//             <input type="file" data-type="video" accept="video/*" multiple data-maxMum="1" data-fileSizeMax="10" onchange="filecount($(this),this.files)" data-filename="cover[]" data-name="url" />
//         </a>
//     </div>
// </div>




// <!-- 通用上传文件  切记 父级不要用lable 应该用div，，<div class="form-group">-->
// <div class="clearfix">
//     <div class="up_load_image up_load_file clearfix">
//         <!-- 如果已上传 如果已上传，使用已上传文件 -->
//         <?php
//             if(isset($info['file_path']) && !empty($info['file_path'])){
//                 $file_path = end(explode('/', $info['file_path']));
//         ?>
//         <div class="img_s file_img_s js_up_img_s">
//             <div class="file_name ellipsis">文件名称：<?=$file_path?></div>
//             <a href="javascript:;" data-table="BillPrintTemplate" data-save_path = "file_path" data-info_id = "<?=$info['id']?>" class="js_dele_this_img" data-id="<?=$info['id']?>"><img src="__STATIC__/img/dele.png" alt=""></a>
//         </div>
//         <?php
//             }
//         ?>
//         <a href="javascript:;" class="img_s img_s_pre">
//             <div class="file_tt btn btn-danger">选择文件</div>
//             <!-- input 配置  
//                 文件大小限制 M ,无限制留空 data-fileSizeMax=""
//                 data-num 最大上传数量
//             -->
//             <input type="file" data-type="file" multiple data-maxMum="1" data-fileSizeMax="5" onchange="filecount($(this),this.files)" data-filename="temp_file[]" data-name="file_path" />
//         </a>
//     </div>
// </div>
// <!-- 通用上传文件 -->


/*图片上传 2018.7.10*/

// .up_load_image .img_s{ display: block; width: 120px; height: 120px; border: 1px solid #ddd; float: left; margin-right: 10px; margin-bottom: 10px; overflow: hidden; position: relative; }
// .up_load_image .img_s .img{width: 100%; float: left;}
// .up_load_image .img_s a{ display: block; width: 30px; height: 30px; z-index: 5; transition: 0.1s; -webkit-transition: 0.1s; position: absolute; right: 10px; top: 10px; }
// .up_load_image .img_s a:hover{opacity: 0.8 }
// .up_load_image .img_s a img{ width: 100%; float: left; }
// .up_load_image .img_s input[type="file"]{ display: none }
// .up_load_image .img_s_pre input[type="file"]{ display: block; width: 100%; height: 100%; position: absolute; left: 0; top: 0; cursor: pointer; opacity:0; filter:alpha(opacity=0) }

// .up_load_image .img_s .set_cover{ position: absolute; width: 100%; left: 0; bottom: 0; z-index: 10; background: #000; background: rgba(0,0,0,0.5); color: #fff; line-height: 30px; text-align: center;  cursor: pointer;}
// .up_load_video .img_s_pre .l_tips{ width: 100%; height: 100%; position: absolute; left: 0; top: 0; z-index: 20; background: rgba(0,0,0,0.7); color: #fff; line-height: 118px; text-align: center; }
// body .up_load_image.up_load_file .file_img_s{ width: 300px; line-height: 32px; height: 32px; padding-left: 10px; background: #f7f7f7 }
// body .up_load_image.up_load_file .img_s_pre{ width: 300px; height: 36px; line-height: 36px; color: #333 }
// body .up_load_image.up_load_file .img_s_pre .file_btn{ cursor: pointer;  }
// body .up_load_image.up_load_video .img_s_video{ width:auto; height: 220px; }
// body .up_load_image.up_load_video .img_s_video  video{ height: 220px; }
// .up_load_video .img_s_pre .l_tips{ width: 100%; height: 100%; position: absolute; left: 0; top: 0; z-index: 20; background: rgba(0,0,0,0.7); color: #fff; line-height: 118px; text-align: center; }
