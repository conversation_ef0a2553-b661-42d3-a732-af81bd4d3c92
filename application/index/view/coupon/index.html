<div class="index_banner js_index_banner js_banner_head" style="background: url(__CDN__/assets/img/pc/index_coupon_banner.jpg) center center; background-size: cover;">
    <div class="index_banner_mark text-center">
        <div class="title js_need_active bottom_top_trans">來付唄  領取淘寶優惠券   代付還返佣  省錢又便利</div>
        <div class="slogo_items js_need_active bottom_top_trans">
            <div>
                <img src="__CDN__/assets/img/pc/icon_banner01.png">
                {$site.Savings}
            </div>
            <div>
                <img src="__CDN__/assets/img/pc/icon_banner02.png">
                {$site.Delivery}
            </div>
            <div>
                <img src="__CDN__/assets/img/pc/icon_banner03.png">
                {$site.Simple}
            </div>
        </div>
        <div class="text-center coupon_search_banner start_enter js_need_active bottom_top_trans">
            <form method="get" action="">
				<input type="text" class="js_index_search_input" name="keyword" maxlength="20" placeholder="{:__('Please enter you want to search for')}">
				<a href="javascript:;">
					<img src="__CDN__/assets/img/pc/icon_search_white.png">
					</a>
            </form>
        </div>
    </div>
</div>
<div class="index_quanlist text-center pad_b30" style="margin-top: 10px;">
    <div class="container">
        <div class="clearfix quan_list pad_t20">
            <?php
			if($list){
				foreach($list as $ls){
			?>
				<div  class="box clearfix">
					<a href="<?=$ls['coupon_share_url']?>" target="_black" class="img img_no pull-left">
						<div class="img_src" style="background-image: url(<?=$ls['pict_url']?>);"></div>
                        <div class="s_num">销量：<?=$ls['volume']?></div>
					</a>
					<a href="<?=$ls['coupon_share_url']?>" target="_black" class="text over_hide">
						<div class="tt ellipsis-3">
							<?=$ls['title']?>
						</div>
						<?php
							if(!empty($ls['coupon_end_time'])){
						?>
							<div class="time_d js_time_down" data-time="<?=substr($ls['coupon_end_time'],'0','-4')-substr($ls['coupon_start_time'],'0','-4')?>">
								倒计时：<span class="js_d">00</span>天<span class="js_h">00</span>時<span class="js_m">00</span>分<span class="js_s">00</span>秒
							</div>
						<?php
							}
						?>
						<div class="price color_red">
							<div><span>¥</span><?=$ls['zk_final_price']-$ls['coupon_amount']?></div>
                            <div class="o_price">￥<?=$ls['zk_final_price']?></div>
						</div>
					</a>
					<a class="info" href="<?=$ls['coupon_share_url']?>" target="_black">
						{:__('Reduction of securities')}<?=$ls['coupon_amount']?>{:__('element')}
					</a>
				</div>
			<?php
				}
			}
			?>
        </div>
		<?php
			if($list){
				if($p == '1'){
					$up = '1';
					$down = $p + 1;
				}else{
					$up = $p - 1;
					$down = $p + 1;
				}
		?>
			<div class="new_pages text-center">
				<a href="<?=url('coupon/index',array('p'=>$up))?>">{:__('Prev')}</a>
				<a href="<?=url('coupon/index',array('p'=>$down))?>">{:__('Nexts')}</a>
			</div>
		<?php
			}else{
		?>
			{include file="common/nodata" /}
		<?php
			}
		?>
    </div>
</div>
<script type="text/javascript">
    $(function(){

    	// 倒计时
            // 补0
          function c_data(str) {
              if (str < 0) {
                  str = "00"
              } else {
                  str = String(str);
                  if (str.length == 1) {
                      str = "0" + str;
                  }
              }
              return str;
          }
          setInterval(function() {
              render_time()
          }, 1000);

          function render_time(){
            var js_time_down=$('.js_time_down');
            for(var i=0;i<js_time_down.length;i++){
              var obj=js_time_down.eq(i);
              var now_time=obj.attr('data-now')?parseInt(obj.attr('data-now')):1;
              obj.attr('data-now',now_time+1);
              var end_time_s=parseInt(obj.attr('data-time'));
              var time_d = end_time_s - now_time; //更新时间差
                  var hour = parseInt(time_d / 3600)%24; // 小时
                  var minute = parseInt(time_d % 3600 / 60); //分
                  var second = parseInt(time_d % 3600 % 60); //秒
                  var day=parseInt(time_d/3600/24);
                  obj.find('.js_d').html(day);
                  obj.find('.js_h').html(c_data(hour));
                  obj.find('.js_m').html(c_data(minute));
                  obj.find('.js_s').html(c_data(second));
            }
          }
          render_time();

        $('.js_index_search_input').focus(function(){
            $(this).attr('data-pla',$(this).attr('placeholder'));
            $(this).attr('placeholder','')
        })
        $('.js_index_search_input').blur(function(){
            $(this).attr('placeholder',$(this).attr('data-pla'));
        })
        $('.js_index_search_input').siblings('a').click(function(){
            $(this).parents('form').submit()
        })
    })
</script>