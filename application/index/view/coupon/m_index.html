<style type="text/css">
    .body{ padding-bottom: 1rem }
    .index_search{ padding: 0; padding-bottom: 0.3rem }
    .index_search .s_input{ margin: 0 0.2rem; width: auto }
</style>
<div class="pad_t30">
    <div class="index_search">
      <div class="s_input ">
          <form action="<?=url('index/coupon/index')?>" method="get">
              <input type="input" class="js_index_search_input" name="keyword" value="<?=$keyword?>" placeholder="{:__('Please enter the coupon information you want to search for')}" maxlength="30">
              <a href="javascript:;"><img src="__CDN__/assets/img/pc/icon_search_red.png"></a>
          </form>
      </div>
   </div>
    <div class="clearfix quan_list pad_t20 js_scroll_more_list" data-url="{:url('index/coupon/lists')}">
        {include file="coupon/m_lists" /}
    </div>
    <!-- 上拉加载的 无数据我自己判断 -->
    <div class="loadding_d hide">
        <img src='__CDN__/assets/img/loading.png'>
        <span>{:__('Pull-up Load More')}</span>
    </div>
    <div class="js_no_data pad_tb30 hide">
        {include file="common/nodata" /}
    </div>
    <!-- 上拉加载的 无数据我自己判断 -->
</div>

<script type="text/javascript">
    $(function(){
        // 倒计时
            // 补0
          function c_data(str) {
              if (str < 0) {
                  str = "00"
              } else {
                  str = String(str);
                  if (str.length == 1) {
                      str = "0" + str;
                  }
              }
              return str;
          }
          setInterval(function() {
              render_time()
          }, 1000);

          function render_time(){
            var js_time_down=$('.js_time_down');
            for(var i=0;i<js_time_down.length;i++){
              var obj=js_time_down.eq(i);
              var now_time=obj.attr('data-now')?parseInt(obj.attr('data-now')):1;
              obj.attr('data-now',now_time+1);
              var end_time_s=parseInt(obj.attr('data-time'));
              var time_d = end_time_s - now_time; //更新时间差
                  var hour = parseInt(time_d / 3600)%24; // 小时
                  var minute = parseInt(time_d % 3600 / 60); //分
                  var second = parseInt(time_d % 3600 % 60); //秒
                  var day=parseInt(time_d/3600/24); 
                  obj.find('.js_d').html(day);
                  obj.find('.js_h').html(c_data(hour));
                  obj.find('.js_m').html(c_data(minute));
                  obj.find('.js_s').html(c_data(second));
            }
          }
          render_time();


          $('.js_index_search_input').focus(function(){
              $(this).attr('data-pla',$(this).attr('placeholder'));
              $(this).attr('placeholder','')
          })
          $('.js_index_search_input').blur(function(){
              $(this).attr('placeholder',$(this).attr('data-pla'));
          })
          $('.js_index_search_input').siblings('a').click(function(){
              $(this).parents('form').submit()
          })
    })
</script>