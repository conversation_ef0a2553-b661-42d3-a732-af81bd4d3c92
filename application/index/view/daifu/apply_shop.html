<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 pad_b40">
    <div class="color_red pad_t10 my_close_win_title">{:__('Apply for super quotient')}</div>
    
    <form action="<?=url('daifu/apply_shop')?>" autocomplete="off" class="small_input edit_info_form" style="padding: 10px 0px 0 0px">
        <div class="">{:__('This service will be opened within')}</div>
        <div class="color_666 pad_t10">开通后此付款方式每日可用一次，*最低金额100TWD，最高包括手续费不超过6000TWD.</div>
        <div class="color_666 pad_t20">{:__('Supermerchant payment is only available to customers who')}</div>
        <div class="clearfix pad_t10">
            <div class="input_box">
                <div class="input_rr">
                    <textarea style="height: 100px; padding:10px 15px; line-height: 24px" placeholder="{:__('Please enter the reason')}" name="content" maxlength="300" is_required="true" empty_tip="{:__('Please enter the reason')}"><?=empty($find['apply_text'])?'':$find['apply_text']?></textarea>
                </div>
            </div>
        </div>
        
        <div class="text-center pad_t30">
			<?php
				if(!empty(strlen($find['type']))){
					if($find['type'] == '0'){
			?>
				<a href="javascript:;" class="sub_btn disabled small">{:__('Submiting')}</a>
			<?php
					}else{
			?>
				<a href="javascript:;" class="sub_btn small js_form_sub" data-text="{:__('Submit')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Submit')}</a>
			<?php
					}
				}else{
			?>
				<a href="javascript:;" class="sub_btn small js_form_sub" data-text="{:__('Submit')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Submit')}</a>
			<?php
				}
			?>
        </div>

    </form>
</div>