<div class="container">
	<div class="user_main margin_tb40 clearfix">
		<div class="user_main_line"></div>
		{include file="daifu/nav" /}
		<div class="pad_tb20 pad_lr20 over_hide daifu_div">
			{include file="daifu/tips" /}
			<div class="pad_t30">
				<form action="<?=url('daifu/new_recharge')?>">
					<div class="input_box  ">
						<div class="input_tt">採購單/出貨單:</div>
						<div class="up_bank_img flex_ac">
							<div class="item add js_up_load_bank">
								<img src="__CDN__/assets/img/pc/big_add.png" class="add_icon" alt="">
							</div>
							<div class="hide">
								<input type="file" class="js_up_img_input_bank" data-type="img" accept="image/*"
									multiple data-maxMum="5" data-fileSizeMax="10"
									onchange="filecountup($(this),this.files)" data-filename="img" />
							</div>
							<span class="f12 color_999 ">最多上傳5張照片</span>
						</div>
					</div>
					<div class="input_box  ">
						<div class="input_tt">採購商品名稱:</div>
						<div class="input_rr">
							<input type="text" name="name" value="" is_required="true" placeholder="請輸入採購商品名稱">
						</div>
					</div>
					<div class="input_box  ">
						<div class="input_tt">金额:</div>
						<div class="input_rr">
							<input type="number" name="money" value="" class="js_mz" is_required="true"
								placeholder="請輸入金额">
						</div>
					</div>

					<div class="input_box flex_ac">
						<div class="input_tt">企業帳號:</div>
						<div class="flex_ac">
							<div class="moni_select filter_item" style=" width: 410px;">
								<div class="moni_selected clearfix">
									<img class="arr" src="__CDN__/assets/img/arr.png">
									<span>請選擇</span>
								</div>
								<input type="text" name="company"
									style="width: 0; height: 0; opacity: 0; position: absolute;" value=""
									is_required="true" placeholder="請選擇企業帳號">
								<div class="moni_s_down">
									<?php
										foreach($qiyebank as $ls){
									?>
										<a href="javascript:;" value="<?=$ls['id']?>"><b><?=$ls['head_bank']?>(<?=$ls['bank_name']?>);<?=$ls['bank_username']?>;</b><br/><?=$ls['bank_account']?></a>
									<?php
										}
									?>
								</div>
							</div>
							<a href="{:url('/index/account/add_company')}"
								class="add_btn js_ajax_win daifu_btn margin_l10 active" data-width="610"
								data-height="370"><img src="__CDN__/assets/img/pc/icon_btn_add.png"
									style="position: relative; top: -1px;" class="margin_r10" alt="">新增企業帳戶</a>
						</div>
					</div>
					<input type="hidden" name="key_id" value="<?=$set_type?>" >
					<!-- 不要删 -->
					<div class="input_box clearfix" style="display: none;">
						<div class="input_tt">商品總價:</div>
						<div class="input_rr pull-left">
							<input type="text" placeholder="" style="width: 100px" class="js_need_pay_money"
								name="tmoney" readonly="readonly">
							TWD
							<span class="pad_l10 color_999 f12">商品選擇完成後自動顯示</span>
						</div>
					</div>

					{include file="daifu/common_new" /}
				</form>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	var account_list = [];
	$(function() {
		function func_all() {
			var money = parseFloat($('.js_mz').val()) || 0;
			var _url = "{:url('/index/user/rate_fun')}";
			$.post(_url, {
				money: money,
				type: 1,
				category: 3
			}, function(data) {
				$('.js_need_pay_money').val(data.money)
			})
		}

		$('.js_mz').blur(function() {
			func_all()
		});


		$('.js_up_load_bank').click(function() {
			$('.js_up_img_input_bank').click()
		})


		$(document).on('click', '.js_close_icon', function() {
			var _this = $(this)
			var a = layer.confirm("{:__('Are you sure you want to delete it?')}", {
				title: false,
				area: ['500px', 'auto'],
				skin: 'my_confirm',
				btn: ["{:__('Confirm')}", "{:__('Cancel')}"], //按钮
				btnAlign: 'c'
			}, function() {
				layer.close(a);
				_this.parent('.item').remove();
				check_add()
			});
		})
	})

	function check_add() {
		var max = $('.js_up_img_input_bank').attr('data-maxMum') || 1;
		if ($('.js_close_icon').length == max) {
			$('.js_up_load_bank').css('display', 'none')
		} else {
			$('.js_up_load_bank').css('display', 'flex')
		}
	}

	function getImgData(img, dir, max_w, next) {
		var image = new Image();
		image.onload = function() {
			var degree = 0,
				drawWidth, drawHeight, width, height;
			drawWidth = max_w > this.naturalWidth ? this.naturalWidth : max_w;
			drawHeight = this.naturalHeight * drawWidth / this.naturalWidth;

			var canvas = document.createElement('canvas');
			canvas.width = width = drawWidth;
			canvas.height = height = drawHeight;
			var context = canvas.getContext('2d');
			context.drawImage(this, 0, 0, drawWidth, drawHeight);
			//返回校正图片
			next(canvas.toDataURL("image/jpeg", .7));
		}
		image.src = img;
	}

	function filecountup(_this, files, count) {
		// 文件大小限制
		if (_this.attr('data-fileSizeMax') != '') {
			var max = parseFloat(_this.attr('data-fileSizeMax')) * 1024 * 1024;
			for (i = 0; i < files.length; i++) {
				if (files[i].size > max) {
					tip_show("文件不能大于" + parseFloat(_this.attr('data-fileSizeMax')) + "M", "2");
					return false;
					break;
				}
			}
		}
		var max = _this.attr('data-maxMum') || 1;
		max = max - $('.js_close_icon').length;
		var index = 0

		function load_img() {
			if (index < files.length) {
				var file = files[index];
				var reader = new FileReader();
				reader.onloadend = function() {
					// console.log(reader.result);
					getImgData(reader.result, '', 2000, function(data) {
						console.log(data)
						// $('.js_up_load_btn').addClass('hide').find('input').val(data);
						// $('.js_img_src').removeClass('hide').css('background-image','url('+data+')');
						var html =
							'<div class="item"><a href="javascript:;" class="flex_ac_jc close_icon js_close_icon"><img src="__CDN__/assets/img/pc/icon_img_delete.png" class="" alt=""></a><img src="' +
							data + '" class="img" alt=""><input type="hidden" name="imgs[]" value="' + data +
							'"></div>'
						$('.up_bank_img').prepend(html);
						check_add();
						index++;
						if (index < max) {
							load_img()
						}
					})
				}
				if (file) {
					reader.readAsDataURL(file);
				}
			}
		}
		load_img()

	};
</script>
