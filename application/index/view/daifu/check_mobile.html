<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 pad_b40">
    <div class="color_red pad_t10 my_close_win_title">{:__('Mobile phone verification')}</div>
    <form action="{:url('/index/daifu/checkMobile')}" autocomplete="off" class=" " style="padding: 10px 0px 0 0px">
        <div class="">{:__('the mobile phone number is your login account')}</div>
        <div class="clearfix pad_t20">
            <div class="input_box full_width">
                <div class="input_tt">{:__('Mobile verification code')}</div>
                <div class="input_rr">
                    <a href="javascript:;" class="get_code" data-url="{:url('/index/login/getCode',array('type'=>'2'))}">{:__('Get code')}</a>
                    <input type="text" name="code" data-type="number" maxlength="6" is_required="true" empty_tip="{:__('Please enter mobile verification code')}">
                </div>
            </div>
            <div class="pad_t10">{:__('Current mobile phone number')}：<?=substr_replace($user['mobile'],'****',2,4)?>  <input type="hidden" name="mobile" class="js_code_input" value="<?=$userinfo['mobile']?>"></div>
        </div>
        
        <div class="text-center pad_t30">
            <a href="javascript:;" class="sub_btn small js_form_sub" data-type="func" data-func_name="after_check_mobile" data-text="{:__('Submit')}" data-loadding="{:__('Submitting')}">{:__('Submit')}</a>
        </div>

    </form>
</div>
<script type="text/javascript">
    // after_check_mobile()
    function after_check_mobile(){
        is_check_mobile=true;
        layer.closeAll();

        // 如果需要 插入淘币 数量
        // 插入 淘币
        if($('.js_daifu_goods_list .input_rr span').length>0){
            var _can_use=parseFloat($('.js_gwj_num').html());
            var _need_money=parseFloat($('.js_daifu_goods_list .input_rr span').html());
            var _v=0;

            if(_can_use>=_need_money){
                _v=_need_money;
                // 隐藏 支付方式
                $('.js_paytype_divs').addClass('hide').find('input').attr('is_required',false)
            }else{
                _v=_can_use;
                // 显示 支付方式
                $('.js_paytype_divs').removeClass('hide').find('input').attr('is_required',true)
            }
            $('.js_daifubaobi_val').val(_v);
        }
		
		// 如果是2022年新版代付，
		if($('.js_new_daifu_taobi').length>0){
			if($('.js_mz').length>0){
				var _can_use=$('.js_mz').val();
			}else{
				var _can_use=parseFloat($('.js_gwj_num').html());
			}
			console.log(_can_use)
			console.log(parseFloat(new_user_money))
			//var _can_use=parseFloat($('.js_gwj_num').html());
			$('.js_new_daifu_taobi').val(parseFloat(new_user_money)>_can_use?_can_use:new_user_money);
            if(parseFloat(new_user_money)<=_can_use){
                // 隐藏 支付方式
                $('.js_paytype_divs').addClass('hide').find('input').attr('is_required',false)
            }else{
                // 显示 支付方式
                $('.js_paytype_divs').removeClass('hide').find('input').attr('is_required',true)
            }
		}
        
    }
</script>