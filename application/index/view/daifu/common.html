<div class='js_df_tip' style="margin-left: 20%;margin-top: 20px;color: red;"></div>
<div class="input_box clearfix js_img_tips_item3">
    <div class="input_tt">{:__('Use coins')}:</div>
    <div class="input_rr pull-left">
        <!-- data-max 最大可用 -->
        <input type="text" placeholder="" style="width: 100px" class="js_daifubaobi_val" data-type="money" name="tabobi" readonly="readonly">
        <span class="pad_l10 color_999 f12">{:__('The amount of money available for purchase')}：<span class="js_gwj_num"><?=$user['money']?></span>， <span class="color_red">{:__('No input is considered not to be used')}</span> </span>
    </div>
    
</div>

<div class="input_box margin_t10 charge_div js_paytype_divs js_img_tips_item4">
    <div class="input_tt">{:__('Payment method')}:</div>
    <div class="input_rr pay_type">
        <input type="text" name="pay_type" value="1" is_required="true" class="js_pay_type_val" empty_tip="{:__('Please choose the mode of payment')}" style="position: absolute; width: 0; height: 0; opacity: 0">
        <a class="daifu_btn margin_r10 js_pay_type_btn active" href="javascript:;" data-id="1">{:__('Network ATM')}</a>
		<?php
			if($user['is_super_quotien_daifu']){
		?>
			<!--<a class="daifu_btn margin_r10 js_pay_type_btn" href="javascript:;" data-id="2">{:__('Super payment')}</a>-->
		<?php
			}
		?>
        
		
		<!--<a class="daifu_btn margin_r10 js_pay_type_btn" href="javascript:;" data-id="4">{:__('Credit card payment')}</a>-->
        <div class="pad_t10 js_paytype_div bank_l clearfix" data-id="1">
            <input type="text" name="bank_id" value="" is_required="true" class="js_bank_id" empty_tip="{:__('Please select the bank account number')}" style="position: absolute; width: 0; height: 0; opacity: 0">
            <?php
				foreach($userbank as $ls){
			?>
				<a href="javascript:;" class="boxs js_boxs bg_fff clearfix" data-id="<?=$ls['id']?>">
					<div class="icon ellipsis"><?=$ls['name']?>(<?=$ls['account_name']?>)</div>
					<div class="ellipsis text-center"><?=$ls['account_six']?></div>
				</a>
			<?php
				}
			?>
			<a href="{:url('/index/account/addBank')}" class="boxs bg_fff clearfix js_ajax_win" data-width="600" data-height="880">
				<div class="ellipsis text-center">點擊添加銀行卡</div>
			</a>
            <!--<div><span class="color_999">超過30000金額可多選銀行</span></div>-->
        </div>
        <div class="pad_t10 js_paytype_div sc_tips clearfix hide" data-id="2">
            <div>超商繳費程式碼將傳送之您的行動電話</div>
            <div>超商將向您收取<span class="color_red">30TWD</span>的手續費，最低金額<span class="color_red">100TWD</span>,最高金額加手續費不超過<span class="color_red">6000TWD</span></div>
        </div>
    </div>
</div>
<div class="input_box margin_t10  charge_div js_img_tips_item5">
    <div class="input_tt" style="height:10px"><!--{:__('Invoice information')}:--></div>
    <div class="input_rr pay_type">
        <input type="text" name="fp_type" value="1" is_required="true" class="js_fp_type_val" empty_tip="{:__('Please select the type of invoice')}" style="position: absolute; width: 0; height: 0; opacity: 0">
        <!--<a class="daifu_btn margin_r10 js_fp_type" href="javascript:;" data-id="3">{:__('Donate to charity')}</a>
		<a class="daifu_btn margin_r10 js_fp_type" href="javascript:;" data-id="2">{:__('Electronic invoice')}</a>
		<a class="daifu_btn margin_r10 active js_fp_type" href="javascript:;" data-id="1">{:__('Electronic invoice1')}</a>
        -->
        

        <div class="fp_div pad_t10">
            <!--<div class="js_fp_div" data-id="1_2">
                <div class="input_box" data-id="1">
                    <div class="input_tt">{:__('Unified numbering')}：</div>
                    <div class="input_rr">
                        <input type="text" name="number" is_required="true" empty_tip="{:__('Please enter a unified number of 8 digits')}" placeholder="{:__('Please enter a unified number of 8 digits')}" value="<?=empty($invoice_json['number'])?'':$invoice_json['number']?>" maxlength="8">
                    </div>
                </div>
                <div class="input_box" data-id="1">
                    <div class="input_tt">{:__('Invoice rise')}：</div>
                    <div class="input_rr">
                        <input type="text" name="fp_title" is_required="true" empty_tip="{:__('Please enter the invoice payable')}" placeholder="{:__('Invoice rise')}" value="<?=empty($invoice_json['fp_title'])?'':$invoice_json['fp_title']?>" maxlength="20">
                    </div>
                </div>
                <div class="input_box" >
                    <div class="input_tt">{:__('Mail box')}：</div>
                    <div class="input_rr">
                        <input type="text" name="fp_email" is_required="true" empty_tip="{:__('Please enter your e-mail')}" data-type="email" maxlength="50" placeholder="{:__('Please enter your e-mail')}" value="<?=empty($invoice_json['fp_email'])?'':$invoice_json['fp_email']?>">
                    </div>
                </div>
            </div>
            <div class="js_fp_div pad_t10 hide" data-id="3">
                {:__('Donation unit')}：{:__('OMG Caring for Society Fund')}  <span class="color_red">{:__('Donation is not available')}</span>
            </div>
            <div class="fp_tips js_fp_tips" data-id="3">
				{:__('E-invoice will be issued three days after')}<br />
				{:__('E-invoice will be issued three days after1')}<br />
				{:__('E-invoice will be issued three days after2')}
            </div>
            <div class="fp_tips js_fp_tips hide" data-id="2">
				{:__('E-invoice will be issued three days after3')}<br />
				{:__('E-invoice will be issued three days after1')}<br />
				{:__('E-invoice will be issued three days after2')}
            </div>
            <div class="fp_tips js_fp_tips hide" data-id="1">
                {:__('E-invoice will be issued three days after3')}<br />
				{:__('E-invoice will be issued three days after1')}<br />
				{:__('E-invoice will be issued three days after2')}
            </div>-->

            <div class="clearfix login_tips">
                <label class="moni_check_label">
                    <a href="javascript:;" class="moni_check js_agree2 active">
                    </a>
                    <span class="la_btn">{:__('I have read and agreed')}</span> <a class="color_red js_article_win" data-width="1000" href="{:url('index/login/settlement')}">《{:__('Consent to Settlement Authorization')}》</a>
                </label>
            </div>

            <div class="text-left pad_t20 pad_b20">
                <a href="javascript:;" class="sub_btn small js_form_sub" data-func_before="before_sub" data-text="{:__('Confirm')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Confirm')}</a> 
                <a href="{:url('/index/daifu/cancel')}" class="color_666 pad_l20 js_ajax_confirm" tips="{:__('Do you confirm the cancellation?')}">{:__('Cancellation of this payment')}</a>
            </div>

        </div>
    </div>
</div>

<?php
	//if($set_type == '0' && empty($account_type) && !$type){
?>
<!-- 虚拟和实物弹窗 
<style type="text/css">
    .smark{ display: block; position: fixed; left: 0; top: 0; background: rgba(0,0,0,0.8); width: 100%; height: 100%; z-index: 160 }
    .select_model_win{ width: 400px; position: fixed; left: 50%; margin-left: -200px; top: 50%; margin-top: -100px; padding: 50px; border-radius: 10px; z-index: 170;background: #fff; text-align: center; }
    .select_model_win a{ display: block; line-height: 80px; border-radius: 10px; font-size: 16px; color: #fff; background:#FFBC3B  }
    .select_model_win a.item1{ margin-bottom: 20px; background: #3B80FF; }
     .select_model_win a img{ margin-right: 5px; }
</style>
<div class="smark"></div>
<div class="select_model_win">
    <a href="<?=url('',array('set_type'=>$set_type,'account_type'=>'1'))?>" class="item item1">
        <img src="__CDN__/assets/img/icon_t1.png">虚拟商品      
    </a>
    <a href="<?=url('',array('set_type'=>$set_type,'account_type'=>'2'))?>" class="item">
        <img src="__CDN__/assets/img/icon_t2.png">實物商品      
    </a>
</div>-->
<?php
	//}
?>
<!-- <div>
    测试  其他弹窗
    <div>
     <a href="{:url('/index/daifu/confirm_account')}" class="js_ajax_win" data-width="760" data-height="550">请确认代付申请人帐号弹窗</a>   
    </div>
    <div>
     <a href="{:url('/index/daifu/confirm_goods')}" class="js_ajax_win" data-width="760">请确认代付商品</a>   
    </div>
    <div>
     <a href="{:url('/index/daifu/apply_shop')}" class="js_ajax_win" data-width="600" data-height="420">申请超商</a>   
    </div>
</div> -->


<!-- 这个有用，不要删 -->
<div class="hide">
 <a href="{:url('/index/daifu/checkMobile')}" class="js_ajax_win js_check_mobile_link" data-width="520" data-height="390">手机验证</a>   
</div>


<a href="{:url('/index/user/editIdcard')}?hideclose=1" class="js_ajax_win hide js_one_auth" data-width="600"></a>
<a href="{:url('/index/user/editIdcardTwo')}?hideclose=1" class="js_ajax_win hide js_two_auth" data-width="600"></a>


<script type="text/javascript">
    //新手提示
    $(function(){


        if($('.js_img_tips_item1').length>0){
            $('.img_tips_item.img_tips_item1 .tips_item').css('top',$('.js_img_tips_item1').offset().top);
        }
        if($('.js_img_tips_item2').length>0){
            $('.img_tips_item.img_tips_item2 .tips_item').css('top',$('.js_img_tips_item2').offset().top);
        }
        if($('.js_img_tips_item3').length>0){
            $('.img_tips_item.img_tips_item3 .tips_item').css('top',$('.js_img_tips_item3').offset().top);
        }
        if($('.js_img_tips_item4').length>0){
            $('.img_tips_item.img_tips_item4 .tips_item').css('top',$('.js_img_tips_item4').offset().top);
        }
        if($('.js_img_tips_item5').length>0){
            $('.img_tips_item.img_tips_item5 .tips_item').css('top',$('.js_img_tips_item5').offset().top);
        }

        if($('.js_img_tips_item6').length>0){
            $('.img_tips_item.img_tips_item6 .tips_item').css('top',$('.js_img_tips_item6').offset().top-10);
        }
        
        if($('.js_img_tips_item7').length>0){
            $('.img_tips_item.img_tips_item7 .tips_item').css('top',$('.js_img_tips_item7').offset().top);
        }
        if($('.js_img_tips_item8').length>0){
            $('.img_tips_item.img_tips_item8 .tips_item').css('top',$('.js_img_tips_item8').offset().top);
        }
        
        if($('.js_img_tips_item9').length>0){
            $('.img_tips_item.img_tips_item9 .tips_item').css('top',$('.js_img_tips_item9').offset().top);
        }
        if($('.js_img_tips_item10').length>0){
            $('.img_tips_item.img_tips_item10 .tips_item').css('top',$('.js_img_tips_item10').offset().top);
        }
        if($('.js_img_tips_item11').length>0){
            $('.img_tips_item.img_tips_item11 .tips_item').css('top',$('.js_img_tips_item11').offset().top);
        }
        //是否需要显示提示  1 需要 ， 其他  不用
        var is_need_show_tip=<?=$show_tip?'0':'1'?>;  
        var tips_type=<?=$set_type?>; // 类型
        if(is_need_show_tip==1){
            $('.img_tips_item').eq(0).addClass('active');
        }

        $('.img_tips_item .next_tips').click(function(){
            if($(this).hasClass('end_tips')){
                return false;
            }
            $(this).parents('.img_tips_item').removeClass('active').next('.img_tips_item').addClass('active')
        })

        // 跳过或者 最后一步
        $('.img_tips_item .end_tips').click(function(){
            var _this=$(this);
            _this.parents('.img_tips_item').removeClass('active')
            $.post("{:url('/index/daifu/closeTip')}",{type:tips_type},function(data){
                if(data.code==1){
                    
                }
            })
        })

        // 帮助打开
        $('.js_open_moren').click();

        // 打开认证弹窗
		<?php
			if(!$user['is_card']){
		?>
				$('.js_one_auth').click();
		<?php
			}
			if($user['is_card']){
				if(!$user['is_card_img'] && !$user_frontend_bank){
		?>
					$('.js_two_auth').click();
		<?php
				}
			}
		?>
        // $('.js_one_auth').click();
        // $('.js_two_auth').click()
        // 
        // 从个人中心进来,发送一个请求，打开 选择商品弹框
        
        var is_from_user_center='<?=$type?>'; // 1需要请求 ，其他 不用        

        if(is_from_user_center==1){
            $.post("{:url('/index/daifu/userGoods')}",{},function(data){
                if(data.code==1){
                    // 大夫申请人账号 请求返回商品列表,用弹窗弹出来
                    layer.closeAll();
                    layer.open({
                      type: 1,
                      title:false,
                      area:[$(window).width()>500?'760px':'6rem', 'auto'],
                      offset:"20%",
                      // anim:2,
                      skin: 'win_class_no_hide', //样式类名
                      closeBtn: 0, //不显示关闭按钮
                      shadeClose: false, //开启遮罩关闭
                      content:data.data,
                      move:'.layui-layer-content .my_close_win_title'
                    });
                }else{
                    tip_show(data.msg,"2");
                }
            })
        }

        
    })



    var is_check_mobile=false;
    // 是否需要 验证手机号  1 需要 ， 其他 不需要
    var is_need_check=1;
    function before_sub(){
        // 如果是阿里巴巴 淘宝代付，必须至少有一个代付商品
        if($('.js_daifu_goods_list').length>0 && $('.js_daifu_goods_list .common_table').length<=0){
            tip_show("{:__('Please select at least one commodity')}",'2');
            return false;
        }

        if($('.js_daifu_goods_list').length>0){
            var _items=$('.js_daifu_goods_list .common_table');
            var _arr=[];
            for(var i=0;i<_items.length;i++){
                _arr.push(_items.eq(i).find('.js_ajax_cancel_df').attr('data-id'));
            }

            $('.js_daifu_goods_ids').val(_arr)
        }

        if(!$('.login_tips .moni_check.js_agree2').hasClass('active')){
            tip_show("{:__('Please agree to settlement')}",'2');
            return false;
        }
        return true;
    }
    $(function(){
        
        
        $(document).on('keyup','.js_money_v',function(){
            var _this=$(this);
            var _type=_this.attr('data-id');
            var _category=_this.attr('data-category');
            if(_this.val()==''){
                return false
            }
            setTimeout(function(){
                var _url=$('.js_rate_change').attr('data-url');
                $.post(_url,{money:_this.val(),type:_type,category:_category},function(data){
                    $('.js_dafu_rate_val').html(data.exchange);
                    var _otyps=_type==1?2:1;
                    $('.js_money_v[data-id="'+_otyps+'"]').val(data.money);
                    // 插入购物金
                    //var _can_use=parseFloat(data.canuse_gwj);
                    //$('.js_gwj_num').html(_can_use);
                    //$('.js_daifubaobi_val').attr('data-max',_can_use);
                    //$('.js_daifubaobi_val').val(_can_use);
                })
            },20)
        })
        // 切换 付款方式
        $('.js_pay_type_btn').click(function(){
            var _this=$(this);
            var _id=_this.attr('data-id');
            _this.addClass('active').siblings('a').removeClass('active');
            $('.js_paytype_div').addClass('hide');
            $('.js_paytype_div[data-id="'+_id+'"]').removeClass('hide');
            if(_id==1){
                $('.js_bank_id').attr('is_required',true)
            }else{
                $('.js_bank_id').attr('is_required',false)
            }
            $('.js_pay_type_val').val(_id)
        })
        // 选择银行
        $('.js_paytype_div .js_boxs').click(function(){
            var _money=$('.js_need_pay_money').length>0?parseFloat($('.js_need_pay_money').html()):0;
            if(_money>30000){
                if(!$(this).hasClass('active')){
                    $(this).addClass('active');
                    
                }else{
                    $(this).removeClass('active');
                }
                var _ids=[];
                for(var i=0;i<$('.js_paytype_div .js_boxs.active').length;i++){
                    _ids.push($('.js_paytype_div .js_boxs.active').eq(i).attr('data-id'))
                }
                $('.js_bank_id').val(_ids)
            }else{
                if(!$(this).hasClass('active')){
                    $(this).addClass('active').siblings('.js_boxs').removeClass('active');
                    $('.js_bank_id').val($(this).attr('data-id'))
                }
            }
            console.log( $('.js_bank_id').val())
        })

        // 选择发票
        $('.js_fp_type').click(function(){
            var _this=$(this);
            var _id=_this.attr('data-id');
            _this.addClass('active').siblings('a').removeClass('active');
            if(_id==1){
                $('.js_fp_div[data-id="1_2"]').removeClass('hide').find('input').attr('is_required',true);
                $('.js_fp_div .input_box[data-id="1"]').removeClass('hide');
                $('.js_fp_div[data-id="3"]').addClass('hide')
            }
            if(_id==2){
                $('.js_fp_div[data-id="1_2"]').removeClass('hide').find('input').attr('is_required',true);
                $('.js_fp_div .input_box[data-id="1"]').addClass('hide').find('input').attr('is_required',false);
                $('.js_fp_div[data-id="3"]').addClass('hide')
            }
            if(_id==3){
                $('.js_fp_div[data-id="1_2"]').addClass('hide').find('input').attr('is_required',false);
                $('.js_fp_div[data-id="3"]').removeClass('hide')
            }
            $('.js_fp_tips[data-id="'+_id+'"]').removeClass('hide').siblings('.js_fp_tips').addClass('hide')
            $('.js_fp_type_val').val(_id)
        })
        $('.js_fp_type').eq(0).click()

        // 公用使用淘币
        
        $('.js_daifubaobi_val').click(function(){
            if(!is_check_mobile){
                $('.js_check_mobile_link').click();
            }
        })






        // $('.js_form_sub').click(function(){
        //     alert('模拟跳转，开发删除。。  该代码在  commom.html 页面，注意 pc端，不同代付有不同的成功页面');
        //     window.location.href="{:url('/index/daifu/index_success')}"
        // })


        // 阿里巴巴+天猫代付 公用js
        // 切换账户
        var s_account_index=0;
        $('.js_switch_account').click(function(){
            s_account_index++;
            if(s_account_index>=account_list.length){
                s_account_index=0;
            }
            change_account(s_account_index)
        })

        function change_account(_index){
            $('.js_account_val').val(account_list[_index]);
            $('.js_copy').attr('data-clipboard-text',account_list[_index])
        }
        if(account_list.length>0){
            change_account(0)
        }

        var _time=null;
        var _end_time=10;
        // 请确认代付申请人帐号
        $('.js_search_daifu_goods').click(function(){
            var _val=$('.js_daifu_money').val();
            var _url=$('.js_daifu_url').val();
            var _account=$('.js_account_val').val();
            var _this=$(this);
            if(_this.hasClass('disabled')){
                return false;
            }
            if(_val=='' || _val==0){
                input_tips($('.js_daifu_money'),"{:__('Please enter payment amount')}", 3);
                $('.js_daifu_money').addClass('error').focus();
                return false;
            }
			if(_url=='' || _url==0){
                input_tips($('.js_daifu_url'),"請輸入代付鏈接", 3);
                $('.js_daifu_url').addClass('error').focus();
                return false;
            }
            var _text=_this.html();
            _end_time=10;

            _time=setInterval(function(){
                _end_time--;
                if(_end_time<=0){
                    _this.html(_text).removeClass('disabled');
                    clearInterval(_time);
                    _time=null
                }else{
                    do_search()
                }
            },5000)

            do_search()

            function do_search(){
                _this.html("{:__('Searching···')}"+_end_time+'次').addClass('disabled');
                $.post(_this.attr('data-url'),{money:_val,url:_url,account:_account},function(data){
                    if(data.code==1){
                        // money请求返回账户列表,用弹窗弹出来
                        // 这里避免下次请求覆盖本次
                        if(_time!=null){
                            layer.open({
                              type: 1,
                              title:false,
                              area:['760px', '550px'],
                              offset:"20%",
                              // anim:2,
                              skin: 'win_class_no_hide', //样式类名
                              closeBtn: 0, //不显示关闭按钮
                              shadeClose: false, //开启遮罩关闭
                              content:data.data,
                              move:'.layui-layer-content .my_close_win_title'
                            });
                            _this.html(_text).removeClass('disabled');
							$('.js_df_tip').html('');
                            clearInterval(_time);
                            _time=null
                        }
                    }else{
						$('.js_df_tip').html(data.msg)
					
                        //tip_show(data.msg,"2");
                       //_this.html(_text).removeClass('disabled')
                        //do_search()
                    }
                })
            }
            


            // 直接打开弹窗的测试代码
            // $.post(_this.attr('data-url'),{money:_val},function(data){
            //     layer.open({
            //           type: 1,
            //           title:false,
            //           area:['760px', '550px'],
            //           offset:"20%",
            //           // anim:2,
            //           skin: 'win_class_no_hide', //样式类名
            //           closeBtn: 0, //不显示关闭按钮
            //           shadeClose: false, //开启遮罩关闭
            //           content:data,
            //           move:'.layui-layer-content .my_close_win_title'
            //         });
            // })
            // return false;
            // 直接打开弹窗的测试代码
        })
    })
</script>