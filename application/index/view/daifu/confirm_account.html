<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 pad_b40">
    <div class="color_red pad_t10 my_close_win_title">{:__('Please confirm the applicant account number')}</div>
    <div class="pad_tb30 margin_lr20 pad_lr20 confirm_account_list js_confirm_account_list clearfix">
        <?php
			foreach($userAlipay as $ls){
		?>
			<a href="javascript:;" class="box" data-id="<?=$ls['id']?>">
				<div class="item ellipsis">
					{:__('Name of Alipay')}：<span><?=$ls['other_name']?></span>
				</div>
				<div class="item ellipsis">
					{:__('Alipay account')}：<span><?=$ls['other_account']?></span>
				</div>
				<div class="item ellipsis">
					{:__('Payment amount')}：<span><?=$ls['price']?></span>
				</div>
			</a>
		<?php
			}
		?>
    </div>
    <div class="confirm_account_list_btns text-center">
        <a href="javascript:;" class="sub_btn small dark margin_r20 js_not_my_account">{:__('This is not my account')}</a>
        <a href="javascript:;" class="sub_btn small js_is_my_account" data-url="{:url('/index/daifu/confirmGoods')}">{:__('This is my account')}</a>
    </div>
</div>
<script type="text/javascript">
    // 是否是 吱口令 1  是，其他  不是 
    var is_zhi='1'; 
    //选择账户
    $('.js_confirm_account_list a').click(function(){
        $(this).addClass('active').siblings('a').removeClass('active')
    })
    // 不是我的账户
    $('.js_not_my_account').click(function(){
        layer.closeAll();
    })
    // 是我的账户
    $('.js_is_my_account').click(function(){
        if($('.js_confirm_account_list a.active').length==0){
            tip_show("{:__('Please select a account')}","2")
            return false;
        }

        var _this=$(this);

        var _val=$('.js_confirm_account_list a.active').attr('data-id');
        if(_this.hasClass('disabled')){
            return false;
        }
        var _text=_this.html();
        _this.html("{:__('Submitting')}").addClass('disabled');


        // 直接打开弹窗的测试代码
        // $.post(_this.attr('data-url'),{id:_val},function(data){
        //     layer.closeAll()
        //     layer.open({
        //       type: 1,
        //       title:false,
        //       area:['760px', 'auto'],
        //       offset:"20%",
        //       // anim:2,
        //       skin: 'win_class_no_hide', //样式类名
        //       closeBtn: 0, //不显示关闭按钮
        //       shadeClose: false, //开启遮罩关闭
        //       content:data,
        //       move:'.layui-layer-content .my_close_win_title'
        //     });
        // })
        // return false;
        // 直接打开弹窗的测试代码

        $.post(_this.attr('data-url'),{id:_val,type:is_zhi},function(data){
            if(data.code==1){
                // 大夫申请人账号 请求返回商品列表,用弹窗弹出来
                layer.closeAll();
                layer.open({
                  type: 1,
                  title:false,
                  area:[$(window).width()>500?'760px':'6rem', 'auto'],
                  offset:"20%",
                  // anim:2,
                  skin: 'win_class_no_hide', //样式类名
                  closeBtn: 0, //不显示关闭按钮
                  shadeClose: false, //开启遮罩关闭
                  content:data.data,
                  move:'.layui-layer-content .my_close_win_title'
                });
            }else{
                tip_show(data.msg,"2");
                _this.html(_text).removeClass('disabled')
            }
        })

        
    })
    


</script>