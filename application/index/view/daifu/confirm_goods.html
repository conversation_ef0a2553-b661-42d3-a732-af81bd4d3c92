<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 pad_b40">
    <div class="color_red pad_t10 my_close_win_title">{:__('Please confirm the consignment')}</div>
    <div class="pad_tb20 confirm_account_goods js_confirm_account_goods clearfix">
		<?php
			foreach($product['alipay'] as $ls){
		?>
			<a href="javascript:;" class="box pad_lr10 active" data-id="<?=$ls['alipay_order_id']?>">
				<div class="links pad_tb10 color_999 f14">
					<?=$ls['peerpay_link']?>
				</div>
				<div class="tt"><span class="color_red"><?=$ls['other_name']?>-<?=$ls['other_account']?></span> {:__('Goods purchased')}</div>
				<?php
					foreach($product['product'][$ls['alipay_order_id']] as $val){
				?>
					<div class="item clearfix">
						<span class="m pull-right margin_l20">{:__('Amount')}：<span class="color_red"><?=$val['price']?>RMB</span></span>
						<div class="ellipsis">
							<?=$val['title']?>
						</div>
					</div>
				<?php
					}
				?>
			</a>
		<?php
			}
		?>
    </div>
    <div class="confirm_account_list_btns text-center">
        <a href="javascript:;" class="sub_btn small js_daifu_goods_confirm" data-url="{:url('/index/daifu/confirmGoods_ids')}">{:__('Confirm')}</a>
    </div>
</div>
<script type="text/javascript">
    // 是否是 吱口令 1  是，其他  不是 
    var is_zhi='1'; 
    //商品多选
    $('.js_confirm_account_goods a').click(function(){
        if($(this).hasClass('active')){
            $(this).removeClass('active')
        }else{
            $(this).addClass('active')
        }
    })
    // 确认 选择商品
    $('.js_daifu_goods_confirm').click(function(){
        if($('.js_confirm_account_goods a.active').length==0){
            tip_show("{:__('Please select at least one commodity')}","2")
            return false;
        }

        var _this=$(this);
        var _ids=[];
        for(var i=0;i<$('.js_confirm_account_goods a.active').length;i++){
            _ids.push($('.js_confirm_account_goods a.active').eq(i).attr('data-id'))
        }
        
        if(_this.hasClass('disabled')){
            return false;
        }
        var _text=_this.html();
        _this.html("{:__('Submitting')}").addClass('disabled');
        $.post(_this.attr('data-url'),{ids:_ids,type:is_zhi},function(data){
            if(data.code==1){
                //提交 选中的商品 id 数组，返回 页面的商品列表
                layer.closeAll();
                // 把 返回的商品列表插入页面
                $('.js_daifu_goods_list').html(data.data);
                // 手机端的 标题显示出来
                $('.js_daifu_goods_wap_tt').removeClass('hide');

                // 清掉所选的支付方式和银行，因为不同金额  ，能选择的银行个数不一样
                 $('.js_bank_id').val('')
                 $('.js_paytype_div .js_boxs').removeClass('active')
                // 
                // 淘币 自动填充逻辑
                // 如果 已验证了手机号
                if(is_need_check==1 && is_check_mobile){
                    // 插入 淘币
                    var _can_use=parseFloat($('.js_gwj_num').html());
                    var _need_money=parseFloat($('.js_daifu_goods_list .input_rr span').html());
                    var _v=0;

                    if(_can_use>=_need_money){
                        _v=_need_money;
                        // 隐藏 支付方式
                        $('.js_paytype_divs').addClass('hide').find('input').attr('is_required',false)
                    }else{
                        _v=_can_use;
                        // 显示 支付方式
                        $('.js_paytype_divs').removeClass('hide').find('input').attr('is_required',true)
                    }

                    $('.js_daifubaobi_val').val(_v);

                }
                // console.log(is_check_mobile,is_need_check)
            }else{
                tip_show(data.msg,"2");
                _this.html(_text).removeClass('disabled')
            }
            
        })
    })
</script>