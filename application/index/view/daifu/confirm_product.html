<?php
	foreach($product['alipay'] as $ls){
?>
	<table class="common_table table border_no margin_t10 text-center">
		<tbody>
			<tr>
				<th colspan="3">
					<div class="clearfix text-left pad_lr10"><a href="{:url('/index/daifu/cancel_df')}" class="pull-right js_ajax_cancel_df color_red" tips="{:__('Do you confirm the cancellation?')}" data-id="<?=$ls['alipay_order_id']?>">{:__('Cancellation of payment')}</a> {:__('Commodity message')}</div>
				</th>
			</tr>
			<tr>
				<td>{:__('Purchaser')}</td>
				<td>{:__('Name title')}</td>
				<td>{:__('Amount')}</td>
			</tr>
			<?php
				foreach($product['product'][$ls['alipay_order_id']] as $val){
			?>
				<tr class="color_666">
					<td><?=$ls['other_name']?>-<?=$ls['other_account']?></td>
					<td style="width:400px;"><div class="ellipsis" style="max-width: 380px"><?=$val['title']?></div></td>
					<td class="color_red"><?=$val['price']?>RMB</td>
				</tr>
			<?php
				}
			?>
			<tr class="color_666">
				<td colspan="3" class="f12 color_999 text-left" style="line-height: 22px">
					<div class="pad_lr10"><?=$ls['peerpay_link']?></div>
				</td>
			</tr>
		</tbody>
	</table>
<?php
	}
?>
<div class="input_box">
    <div class="input_tt">{:__('Total sum')}:</div>
    <div class="input_rr">
        <span class="color_red js_need_pay_money"><?=$product['all_price']?>元</span>
    </div>
</div>
