<div class="container">
    <div class="user_main margin_tb40 clearfix">
        <div class="user_main_line"></div>
        {include file="daifu/nav" /}
        <div class="pad_tb20 pad_lr20 over_hide daifu_div">
           {include file="daifu/tips" /}
            <div class="pad_t30">
                <form action="{:url('/index/daifu/douyin_Success')}" autocomplete="off"> 
				<div class="clearfix js_img_tips_item6">
					<div class="rate_input clearfix">
						<div class="clearfix">
							<div class="pull-left tts">{:__('RMB')}</div>
							<div class="over_hide rate_input_r">
								<span class="pull-right color_999 margin_l10 margin_r20">RMB</span>
								<div class="over_hide">
									<input type="text" data-type="money" name="money" class="js_money_v" data-max="100000" data-id="1" data-category="17" placeholder="{:__('Please enter RMB amount')}">
								</div>
							</div>
						</div>
						<div style="padding-left: 75px">
							當前匯率： <span class="color_red js_dafu_rate_val"><?=$exchange['exchange']?></span>,依銀行匯率含手續費
						</div>
					</div>
					<div class="rate_change">
						<a href="javascript:;" class="js_rate_change" data-url="{:url('/index/user/rate_fun')}"><img src="__CDN__/assets/img/pc/rate_change.png" alt=""></a>
					</div>
					<div class="rate_input rate_input_red clearfix">
						<div class="pull-left tts">{:__('TWD')}</div>
						<div class="over_hide rate_input_r">
							<span class="pull-right color_999 margin_l10 margin_r20">TWD</span>
							<div class="over_hide">
								<input type="text" data-type="money" class="js_money_v" data-id="2" data-category="5" placeholder="{:__('Please enter TWD amount')}">
							</div>
						</div>
					</div>
				</div>
                <div class="input_box" >
					<div class="input_tt">上傳圖片:</div>
					<div class="input_rr">
						<a href="javascript:;" class="daifu_btn js_check_pro"><img src="__CDN__/assets/img/pc/icon_btn_add.png" style="position: relative; top: -1px;" class="margin_r10" alt=""><span>上傳QR碼查詢代付商品</span></a>
						<input type="hidden" name="goods_ids" class="js_daifu_goods_ids" value="">
						<div class="js_daifu_goods_list">
                        </div>
					</div>
				</div>
				<div class="input_box" style="display:none;">
					<div class="input_tt">金额:</div>
					<div class="input_rr">
						<input style="width: 200px" type="text" data-type="money" name="money" value="" data-type="number" data-max="50000" class="js_mz" is_required="false" placeholder="請輸入金额">&nbsp;&nbsp;&nbsp;{:__('Current exchange rate')}： <span class="color_red js_dafu_rate_val"><?=$exchange['exchange']?></span>
					</div>
				</div>
				
                <div class="input_box margin_t10 js_img_tips_item2" style="display:none;">
                    <div class="input_tt">代付鏈接:</div>
                    <div class="input_rr">
                        <input type="text" name="url" value="" data-type="url" class="js_daifu_url">
                        <a class="daifu_btn margin_l10 margin_r10 js_search_daifu_goods" data-url="{:url('/index/daifu/confirmAccount')}" href="javascript:;">{:__('Enquiry commodities')}</a>
                        <span class="color_999">{:__('Rate')}：<?=$exchange['exchange']?>依銀行匯率含手續費</span>
                        <input type="hidden" name="goods_ids" class="js_daifu_goods_ids" value="">
                        <div class="js_daifu_goods_list">
                        </div>

                    </div>
                </div>
				
                {include file="daifu/common" /}

                </form>
            </div>
        </div>
    </div>
</div>

<input type="file" class="js_up_img_check_pro" style="display:none" data-type="img" accept="image/*" multiple data-maxMum="1" data-fileSizeMax="2" onchange="filecountup_check($(this),this.files)" data-filename="img" />




<script src="__CDN__/assets/js/pc/copy.js?v={$Think.config.site.version}" ></script>
<script type="text/javascript">
    // 账户列表
    var account_list=<?=json_encode($sysalipay)?>;
    $(function(){
        $('.js_check_pro').click(function(){
			if($(this).find('span').hasClass('disabled')){
				return false;
			}
			$('.js_up_img_check_pro').val('')
			$('.js_up_img_check_pro').click()
		})	
        $(document).on('click','.js_ajax_cancel_df',function(){
            var _this=$(this);
            layer.confirm(_this.attr('tips'), {
              title:false,
              area:['500px', 'auto'],
              skin: 'my_confirm',
              btn: ["{:__('Confirm')}","{:__('Cancel')}"], //按钮
              btnAlign:'c'
            }, function(){
                _this.parents('table').remove();
                layer.closeAll()
            });
            return false; 
        })

    })
	
	var _douyin_time=null;
    var _douyin_end_time=10;
	function dy_getImgData(img,dir,max_w,next){
	 var image=new Image();
	 image.onload=function(){
	  var degree=0,drawWidth,drawHeight,width,height;
	  drawWidth=max_w>this.naturalWidth?this.naturalWidth:max_w;
	  drawHeight=this.naturalHeight*drawWidth/this.naturalWidth;
	  
	  var canvas=document.createElement('canvas');
	  canvas.width=width=drawWidth;
	  canvas.height=height=drawHeight; 
	  var context=canvas.getContext('2d');
	  context.drawImage(this,0,0,drawWidth,drawHeight);
	  //返回校正图片
	  next(canvas.toDataURL("image/jpeg",.7));
	 }
	 image.src=img;
	}
	//图片上传预览 2018.7.11
	function filecountup_check(_this,files,count){
		// 文件大小限制
		// 
		console.log(files)
		if(_this.attr('data-fileSizeMax')!=''){
			var max=parseFloat(_this.attr('data-fileSizeMax'))*1024*1024;
			if(files[0].size > max){
				tip_show("文件不能大于"+parseFloat(_this.attr('data-fileSizeMax'))+"M","2");
				return false;
			} 
		}


		var file = files[0];
		var reader = new FileReader();
		reader.onloadend = function () {
			console.log(reader.result);
			// $('.js_up_load_btn2').addClass('hide').find('input').val(reader.result);
			// $('.js_img_src2').removeClass('hide').css('background-image','url('+reader.result+')')
			//dy_getImgData(reader.result,'',1000,function(data){
				 check_douyin_pro(reader.result)
			//})
		}
		if (file) {
			reader.readAsDataURL(file);
		}
	};
	
	
	function check_douyin_pro(img_code){
		
		var _this=$('.js_check_pro span');
		
		var _text=_this.html();
		_douyin_end_time=10;

		_douyin_time=setInterval(function(){
			_douyin_end_time--;
			if(_douyin_end_time<=0){
				_this.html(_text).removeClass('disabled');
				clearInterval(_douyin_time);
				_douyin_time=null
			}else{
				do_search()
			}
		},5000)
		
		do_search()
		function do_search(){
			let _url = "{:url('/index/daifu/confirmAccount_douyin')}";
			_this.html("{:__('Searching···')}"+_douyin_end_time+'次').addClass('disabled');
			$.post(_url,{code_img:img_code},function(data){
				if(data.code==1){
					// money请求返回账户列表,用弹窗弹出来
					// 这里避免下次请求覆盖本次
					if(_douyin_time!=null){
						layer.open({
						  type: 1,
						  title:false,
						  area:['760px', '550px'],
						  offset:"20%",
						  // anim:2,
						  skin: 'win_class_no_hide', //样式类名
						  closeBtn: 0, //不显示关闭按钮
						  shadeClose: false, //开启遮罩关闭
						  content:data.data,
						  move:'.layui-layer-content .my_close_win_title'
						});
						_this.html(_text).removeClass('disabled');
						$('.js_df_tip').html('');
						clearInterval(_douyin_time);
						_douyin_time=null
					}
				}else{
					$('.js_df_tip').html(data.msg)
				
					//tip_show(data.msg,"2");
				   //_this.html(_text).removeClass('disabled')
					//do_search()
				}
			})
		}
	}

</script>
