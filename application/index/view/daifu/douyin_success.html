<div class="container">
    <div class="user_main margin_tb40 clearfix">
        <div class="user_main_line"></div>
        {include file="daifu/nav" /}
        <div class="pad_tb20 pad_lr20 over_hide daifu_success">
            {include file="daifu/success_common_head" /}

            <table class="common_table table margin_t10 text-center">
                <tbody>
                    <tr>
                        <th class="l" colspan="3">
                            <div class="clearfix pad_lr10">
                                {:__('Commodity message')}
                            </div>
                        </th>
                    </tr>
                    <tr class="color_666">
                        <td>{:__('Purchaser')}</td>
                        <td>{:__('Name title')}</td> 
                        <td>{:__('Amount')}</td>
                    </tr>
                    <?php
						$product_json = json_decode($order['order']['product_json'],true);
						if($order['order']['type'] != '2'){
						foreach($product_json['alipay'] as $ls){
							foreach($product_json['product'][$ls['alipay_order_id']] as $val){
					?>
							<tr class="color_666">
								<td><?=$ls['other_name']?></td>
								<td><?=$val['title']?></td>
								<td class="color_red"><?=$val['price']?>RMB</td>
							</tr>
					<?php
							}
					?>
						<tr class="color_999">
							<td colspan="3">
								<div class="f12 text-left" style="line-height: 22px"><?=$ls['peerpay_link']?></div>
							</td>
						</tr>
					<?php
							}
						}else{
							foreach($product_json as $ll){
						?>
							<tr class="color_666">
								<td><?=$ll['number']?></td>
								<td><?=$ll['title']?></td>
								<td class="color_red"><?=$ll['price']?>RMB</td>
							</tr>
						<?php
							}
						}
					?>
                </tbody>
            </table>

           {include file="daifu/success_common" /}

        </div>
    </div>
</div>