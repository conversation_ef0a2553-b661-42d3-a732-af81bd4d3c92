<div class="container">
    <div class="user_main margin_tb40 clearfix">
        <div class="user_main_line"></div>
        {include file="daifu/nav" /}
        <div class="pad_tb20 pad_lr20 over_hide daifu_div">
           {include file="daifu/tips" /}
            <div class="pad_t30">
                <form action="<?=url('daifu/game_success')?>">
                    <div class="clearfix js_img_tips_item6">
                        <div class="rate_input clearfix">
                            <div class="clearfix">
                                <div class="pull-left tts">{:__('RMB')}</div>
                                <div class="over_hide rate_input_r">
                                    <span class="pull-right color_999 margin_l10 margin_r20">RMB</span>
                                    <div class="over_hide">
                                        <input type="text" data-type="money" name="money" class="js_money_v" data-max="100000" data-id="1" data-category="5" placeholder="{:__('Please enter RMB amount')}">
                                    </div>
                                </div>
                            </div>
                            <div style="padding-left: 100px">
                                {:__('Current exchange rate')}： <span class="color_red js_dafu_rate_val"><?=$exchange['exchange']?></span>
                            </div>
                        </div>
                        <div class="rate_change">
                            <a href="javascript:;" class="js_rate_change" data-url="{:url('/index/user/rate_fun')}"><img src="__CDN__/assets/img/pc/rate_change.png" alt=""></a>
                        </div>
                        <div class="rate_input rate_input_red clearfix">
                            <div class="pull-left tts">{:__('TWD')}</div>
                            <div class="over_hide rate_input_r">
                                <span class="pull-right color_999 margin_l10 margin_r20">TWD</span>
                                <div class="over_hide">
                                    <input type="text" data-type="money" class="js_money_v" data-id="2" data-category="5" placeholder="{:__('Please enter TWD amount')}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="input_box margin_t10 js_img_tips_item11">
                        <div class="input_tt">{:__('Platform link')}:</div>
                        <div class="input_rr">
                            <input type="text" name="url_path" value="" is_required="true" empty_tip="{:__('Please enter platform link')}" class="auto_w" placeholder="{:__('Please enter platform link')}">
                        </div>
                    </div>


                    <div class="input_box margin_t10">
                        <div class="input_tt">{:__('Game Video Account')}:</div>
                        <div class="input_rr">
                            <input type="text" name="account" value="" is_required="true" empty_tip="{:__('Please enter the Game Video Account')}" class="auto_w" placeholder="{:__('Please enter the Game Video Account')}">
                            <div class="clearfix login_tips">
                                <label class="moni_check_label">
                                    <a href="javascript:;" class="moni_check active">
                                        <input type="checkbox" name="is_agree_ali" value="1" data-type="checkbox" checked="checked" is_required="true" empty_tip="{:__('Please understand the payment rules first')}" style="position: absolute; width: 0; height: 0; opacity: 0">
                                    </a>
                                    <span class="la_btn">{:__('I confirm that the account I want to deposit')}
                            </div>
                        </div>
                    </div>
                    {include file="daifu/common_stored" /}

                </form>
            </div>
        </div>
    </div>
</div>

<!-- 新手引导    -->
<div class="img_tips_item img_tips_item6">
    <div class="tips_item">
        <img src="__CDN__/assets/img/tips/alipay1.png" class="t_img">
        <img src="__CDN__/assets/img/img_arrow.png" class="img_arrow">
        <a href="javascript:;" class="jump_tips end_tips">跳過新手引導</a>
        <div class="tips_text">
            第一步：填寫您需要的金額，會自動幫您換算台幣金額 <a href="javascript:;" class="next_tips color_red">查看下一步</a>
        </div>
    </div>
</div>

<div class="img_tips_item img_tips_item11">
    <div class="tips_item">
        <img src="__CDN__/assets/img/tips/game1.png" class="t_img">
        <img src="__CDN__/assets/img/img_arrow.png" class="img_arrow">
        <a href="javascript:;" class="jump_tips end_tips">跳過新手引導</a>
        <div class="tips_text">
            第二步：請填寫正確的遊戲視頻平臺連結和遊戲視頻帳戶 <a href="javascript:;" class="next_tips color_red">查看下一步</a>
        </div>
    </div>
</div>
<div class="img_tips_item img_tips_item8">
    <div class="tips_item" style="top: 492px;">
        <img src="__CDN__/assets/img/tips/alipay3.png" class="t_img">
        <img src="__CDN__/assets/img/img_arrow.png" class="img_arrow">
        <a href="javascript:;" class="jump_tips end_tips">跳過新手引導</a>
        <div class="tips_text">
            第三步：購物金可以按照比例抵扣，賺取購物金方法請您在幫助中心査詢 <a href="javascript:;" class="next_tips color_red">查看下一步</a>
        </div>
    </div>
</div>


<div class="img_tips_item img_tips_item4">
    <div class="tips_item"  style="top:544px">
        <img src="__CDN__/assets/img/tips/taobao4.png" class="t_img">
        <img src="__CDN__/assets/img/img_arrow.png" class="img_arrow">
        <a href="javascript:;" class="jump_tips end_tips">跳過新手引導</a>
        <div class="tips_text">
            第四步：請正確選擇您的付款方式，銀行後6碼需要核實正確，錯誤將無法自動入帳，需要您聯系客服處理 <a href="javascript:;" class="next_tips color_red">查看下一步</a>
        </div>
    </div>
</div>

<div class="img_tips_item img_tips_item5">
    <div class="tips_item"  style="top:695px">
        <img src="__CDN__/assets/img/tips/taobao5.png" class="t_img">
        <img src="__CDN__/assets/img/img_arrow.png" class="img_arrow">
        <div class="tips_text">
            第五步：每筆訂單會開立15塊手續費發票，因付唄並非賣家，活動期間减免手續費，但正常開立手續費發票 <a href="javascript:;" class="next_tips color_red end_tips">我知道了</a>
        </div>
    </div>
</div>

<script type="text/javascript">
    var account_list=[];
    $(function(){
    })

</script>
