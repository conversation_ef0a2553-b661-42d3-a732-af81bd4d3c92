<div class="container">
	<div class="user_main margin_tb40 clearfix">
		<div class="user_main_line"></div>
		{include file="daifu/nav" /}
		<div class="pad_tb20 pad_lr20 over_hide daifu_div">
			{include file="daifu/tips" /}
			<div class="pad_t30">
				<form action="<?=url('daifu/game_success')?>">
					<div class="input_box  ">
						<div class="input_tt">ID:</div>
						<div class="input_rr">
							<input type="text" name="id" value="" is_required="true" placeholder="請輸入ID">
						</div>
					</div>
					<div class="input_box  ">
						<div class="input_tt">帳號:</div>
						<div class="input_rr">
							<input type="text" name="account" value="" is_required="true" placeholder="請輸入帳號">
						</div>
					</div>
					<div class="input_box  ">
						<div class="input_tt">密碼:</div>
						<div class="input_rr">
							<input type="password" name="password" value="" is_required="true" placeholder="請輸入密碼">
						</div>
					</div>
					<div class="input_box  ">
						<div class="input_tt">面额:</div>
						<div class="input_rr">
							<input type="number" name="money" value="" class="js_item_money" is_required="true"
								placeholder="請輸入面额">
						</div>
					</div>
					<div class="input_box  ">
						<div class="input_tt">遊戲區:</div>
						<div class="input_rr">
							<input type="text" name="area" value="" is_required="true" placeholder="請輸入遊戲區">
						</div>
					</div>
					<div class="input_box  ">
						<div class="input_tt">面值:</div>
						<div class="input_rr">
							<input type="text" class="js_mz" name="num"
								style="width: 0; height: 0; opacity: 0; position: absolute;" value="" is_required="true"
								placeholder="請選擇面值">
							<div class="pay_type num_pay_type">
								<a class="daifu_btn margin_r10 js_num_type" href="javascript:;" data-id="10">10個</a>
								<a class="daifu_btn margin_r10 js_num_type" href="javascript:;" data-id="100">100個</a>
								<a class="daifu_btn margin_r10 js_num_type" href="javascript:;" data-id="300">300個</a>
								<a class="daifu_btn margin_r10 js_num_type" href="javascript:;" data-id="1000">1000個</a>
								<a class="daifu_btn margin_r10 js_num_type" href="javascript:;" data-id="">
									<div class="d1"><input type="number" class="js_num_input_d" step="1" min="0">個</div>
									<div class="d2">自定義</div>
								</a>
							</div>

						</div>
					</div>

					<div class="input_box clearfix js_img_tips_item3">
						<div class="input_tt">商品總價:</div>
						<div class="input_rr pull-left">
							<!-- data-max 最大可用 -->
							<input type="text" placeholder="" style="width: 100px" class="js_need_pay_money"
								name="tmoney" readonly="readonly">
							TWD
							<span class="pad_l10 color_999 f12">商品選擇完成後自動顯示</span>
						</div>
					</div>

					{include file="daifu/common_new" /}
				</form>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	var account_list = [];
	$(function() {
		$('.js_num_type').click(function() {
			$(this).addClass('active').siblings('.js_num_type').removeClass('active');
			$('.js_mz').val($(this).attr('data-id'));
			if ($(this).attr('data-id') == '') {
				$('.js_mz').val($(this).find('input').val());
			}
			func_all()
		})
		$('.js_num_input_d').blur(function() {
			if ($('.js_num_type[data-id=""]').hasClass('active')) {
				$('.js_mz').val($(this).val());
			}
			func_all()
		})
		$('.js_item_money').blur(function() {
			func_all()
		})

		function func_all() {
			var money = parseFloat($('.js_item_money').val()) * parseFloat($('.js_mz').val()) || 0;
			var _url = "{:url('/index/user/rate_fun')}";
			$.post(_url, {
				money: money,
				type: 1,
				category: 3
			}, function(data) {
				$('.js_need_pay_money').val(data.money)
			})
		}
	})
</script>
