
<style>
.common_table .cancel_df{border:1px solid #EF436D; background:none;color:#EF436D}
/* .hide_div{display: none;} */
.hide_div.active{display: block;}
</style>
<div class="container">
    <div class="user_main margin_tb40 clearfix">
        <div class="user_main_line"></div>
        {include file="daifu/nav" /}
        <div class="pad_tb20 pad_lr20 over_hide daifu_div">
		 {include file="daifu/tips" /}
            <div class="pad_t30">
                <form action="{:url('/index/daifu/indexSuccess')}" autocomplete="off"> 
                <div class="input_box js_img_tips_item1">
                    <div class="input_tt">{:__('Friend account')}:</div>
                    <div class="input_rr">
                        <input type="text" name="account" class="color_red js_account_val" value="" readonly="readonly">
                        <a class="daifu_btn margin_l10 margin_r10 js_copy" href="javascript:;" data-clipboard-text="">{:__('Copy account')}</a>
                        <a class="daifu_btn margin_r10 js_switch_account" href="javascript:;">{:__('Switch account')}</a>
                    </div>
                </div>
				<!-- <div style="margin-left: 10%;margin-top: 10px;font-size: 15px;color: #ff0000;">申請代付訂單賬戶和搜索訂單賬戶必須一致</div> -->
				
				<!-- 
				<div class="input_box margin_t10 js_img_tips_item2">
                    <div class="input_tt">代付人民幣:</div>
                    <div class="input_rr">
                        <input type="text" name="money" value="" data-type="money" class="js_daifu_money">
                        
                    </div>
                </div>
				-->
                <div class="input_box  margin_t10 js_img_tips_item2">
                    <div class="input_tt">代付鏈接:</div>
                    <div class="input_rr">
                        <input type="text" name="url" value="" data-type="url" class="js_daifu_url">
                        <a class="daifu_btn margin_l10 margin_r10 js_search_daifu_goods" data-url="{:url('/index/daifu/select_product')}" href="javascript:;">{:__('Enquiry commodities')}</a>
                        <span class="color_999">{:__('Rate')}：<?=$exchange['exchange']?>依銀行匯率含手續費</span>
                        <input type="hidden" name="goods_ids" class="js_daifu_goods_ids" value="">
                        
                    </div>
                </div>
				
				<div class="js_daifu_goods_list">
							
                </div>
				<div class="hide_div">
					{include file="daifu/common_taobao" /}
				</div>
                </form>
            </div>
        </div>
    </div> 
</div>




<!-- 新手引导  阿里巴巴 淘宝 公用  -->
<div class="img_tips_item  img_tips_item1">
    <div class="tips_item">
        <img src="__CDN__/assets/img/tips/taobao1.png" class="t_img">
        <img src="__CDN__/assets/img/img_arrow.png" class="img_arrow">
        <a href="javascript:;" class="jump_tips end_tips">跳過新手引導</a>
        <div class="tips_text">
            第一步：請將此帳戶填寫到淘寶代付好友帳戶 <a href="javascript:;" class="next_tips color_red">查看下一步</a>
        </div>
    </div>
</div>

<div class="img_tips_item img_tips_item2">
    <div class="tips_item">
        <img src="__CDN__/assets/img/tips/taobao2.png" class="t_img">
        <img src="__CDN__/assets/img/img_arrow.png" class="img_arrow">
        <a href="javascript:;" class="jump_tips end_tips">跳過新手引導</a>
        <div class="tips_text">
            第二步：代付金額一定要填寫正確，小數點後的都不能錯，否則是査詢不到的 <a href="javascript:;" class="next_tips color_red">查看下一步</a>
        </div>
    </div>
</div>

<div class="img_tips_item img_tips_item3">
    <div class="tips_item">
        <img src="__CDN__/assets/img/tips/taobao3.png" class="t_img">
        <img src="__CDN__/assets/img/img_arrow.png" class="img_arrow">
        <a href="javascript:;" class="jump_tips end_tips">跳過新手引導</a>
        <div class="tips_text">
            第三步：急單請使用淘幣付款，無需等待，儲值淘幣的時候還可抵扣購物金，讓您更省錢 <a href="javascript:;" class="next_tips color_red">查看下一步</a>
        </div>
    </div>
</div>

<div class="img_tips_item img_tips_item4">
    <div class="tips_item">
        <img src="__CDN__/assets/img/tips/taobao4.png" class="t_img">
        <img src="__CDN__/assets/img/img_arrow.png" class="img_arrow">
        <a href="javascript:;" class="jump_tips end_tips">跳過新手引導</a>
        <div class="tips_text">
            第四步：請正確選擇您的付款方式，銀行後6碼需要核實正確，錯誤將無法自動入帳，需要您聯系客服處理 <a href="javascript:;" class="next_tips color_red">查看下一步</a>
        </div>
    </div>
</div>


<div class="img_tips_item img_tips_item5">
    <div class="tips_item">
        <img src="__CDN__/assets/img/tips/taobao5.png" class="t_img">
        <img src="__CDN__/assets/img/img_arrow.png" class="img_arrow">
        <div class="tips_text">
            第五步：每筆訂單會開立15塊手續費發票，因付唄並非賣家，活動期間减免手續費，但正常開立手續費發票 <a href="javascript:;" class="next_tips color_red end_tips">我知道了</a>
        </div>
    </div>
</div>


<script src="__CDN__/assets/js/pc/copy.js?v={$Think.config.site.version}" ></script>
<script type="text/javascript">




    // 账户列表
    var account_list=<?=json_encode($sysalipay)?>;
    $(function(){
        // 复制
        if($('.js_copy').length>0){
            if(document.getElementsByClassName){
                var clipboard_btns = document.getElementsByClassName('js_copy');
                var clipboard = new Clipboard(clipboard_btns);
                clipboard.on('success', function(e) {
                    tip_show("{:__('Copy success')}",'1');
                });
                clipboard.on('error', function(e) {
                    console.log(e);
                });
            }else{
                // 兼容 ie7 8
                $(".js_copy").each(function() {
                    $(this).on("click", function() {
                        var copy_span = $(this);
                        var input_hidden = '<input type="text" class="input_hidden" id="input_hidden" value=""/>';
                        copy_span.after($(input_hidden));
                        $("#input_hidden").val(copy_span.attr("data-clipboard-text"));
                        var obj = document.getElementById("input_hidden");
                        obj.select(); // 选择对象
                        document.execCommand("Copy"); // 执行浏览器复制命令
                        $("#input_hidden").remove();
                        tip_show("{:__('Copy success')}",'1');
                    })
                })
            }
        }


        $(document).on('click','.js_ajax_cancel_df',function(){
            var _this=$(this);
			let index=_this.attr('data-index');
            layer.confirm(_this.attr('tips'), {
              title:false,
              area:['500px', 'auto'],
              skin: 'my_confirm',
              btn: ["{:__('Confirm')}","{:__('Cancel')}"], //按钮
              btnAlign:'c'
            }, function(){
                //_this.parents('tr').remove();

				res_goods.splice(index,1)
				render_goods()
                layer.closeAll()
            });
            return false; 
        })

    })

</script>
