<div class="container">
	<div class="user_main margin_tb40 clearfix">
		<div class="user_main_line"></div>
		{include file="daifu/nav" /}
		<div class="pad_tb20 pad_lr20 over_hide daifu_div">
			{include file="daifu/tips" /}
			<div class="pad_t30">
				<form action="<?=url('daifu/new_recharge')?>">
					<input type="hidden" name="type" value="" >
					<input type="hidden" name="key_id" value="<?=$set_type?>" >
					<?php
						$set_id = json_decode($set_one['set_id'],true);
						if(!empty($set_id)){
							if(in_array('1',$set_id)){
						?>
							<div class="input_box">
								<div class="input_tt">链接:</div>
								<div class="input_rr">
									<input type="text" name="url" value="" is_required="true" placeholder="請輸入链接">
								</div>
							</div>
						<?php
							}
							if(in_array('2',$set_id)){
						?>
							<div class="input_box">
								<div class="input_tt">暱稱:</div>
								<div class="input_rr">
									<input type="text" name="id" value="" is_required="true" placeholder="請輸入暱稱">
								</div>
							</div>
						<?php
							}
							if(in_array('3',$set_id)){
						?>
							<div class="input_box">
								<div class="input_tt">帳號:</div>
								<div class="input_rr">
									<input type="text" name="account" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5]/ig,'')" name="name" autocomplete="off" onpaste="return false" ondragenter="return false" oncontextmenu="return false;" is_required="true" placeholder="請輸入帳號">
								</div>
							</div>
						<?php
							}
							if(in_array('4',$set_id)){
						?>
							<div class="input_box">
								<div class="input_tt">密碼:</div>
								<div class="input_rr">
									<input type="text" name="password" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5]/ig,'')" name="name" autocomplete="off" onpaste="return false" ondragenter="return false" oncontextmenu="return false;" is_required="true" placeholder="請輸入密碼">
								</div>
							</div>
						<?php
							}
							if(in_array('5',$set_id)){
						?>
							<div class="input_box">
								<div class="input_tt">面额:</div>
								<div class="input_rr">
									<input type="number" name="money" value="" data-type="number" data-max="50000" class="js_item_money" is_required="true"
										placeholder="請輸入面额">
								</div>
							</div>
						<?php
							}
							if(in_array('6',$set_id)){
						?>
							<div class="input_box">
								<div class="input_tt">遊戲區:</div>
								<div class="input_rr">
									<input type="text" name="game" value="" class="" is_required="true"
										placeholder="請輸入遊戲區">
								</div>
							</div>
						<?php
							}
						}
						$set_array = json_decode($set_two['set_id'],true);
						if(!empty($set_array)){
					?>
					<input type="hidden" name="money" value="" class="js_select_price">
					<div class="input_box  ">
						<div class="input_tt">類型:</div>
							<div class="input_rr">
								<input type="text" class="js_mz_type" name="num_type"
									style="width: 0; height: 0; opacity: 0; position: absolute;" value="" is_required="true"
									placeholder="請選擇類型">
								<div class="pay_type num_pay_type">
									<?php
										$child = [];
										$price = [];
										foreach($set_array as $key=>$val){
											$child[$key] = '';
											$price[$key] = '';
											foreach($val['child'] as $k=>$ls){
												$child[$key] .= $ls['value'].',';
												$price[$key] .= $ls['money'].',';
											}
											$child[$key] = rtrim($child[$key],',');
											$price[$key] = rtrim($price[$key],',');
									?>
                                        <!-- data-discount  -->
										<a class="daifu_btn margin_r10 js_num_type_sele" href="javascript:;" data-id="<?=$val['title']?>"
											data-unit="" data-discount="<?=$val['discount']?>" data-price="<?=$price[$key]?>" data-arr='<?=$child[$key]?>'>
											<?=$val['title']?>
										</a>
									<?php
										}
									?>	
								</div>
							</div>
					</div>
					<div class="input_box  ">
						<div class="input_tt">面值:</div>
						<div class="input_rr">
							<input type="text" class="js_mz" name="num"
								style="width: 0; height: 0; opacity: 0; position: absolute;" value="" is_required="true"
								placeholder="請選擇面值">
							<div class="pay_type num_pay_type js_mz_options">

							</div>
						</div>
					</div>
					<?php
						}
					?>
					<div class="input_box clearfix js_img_tips_item3">
						<div class="input_tt">商品總價:</div>
						<div class="input_rr pull-left">
							<!-- data-max 最大可用 -->
							<input type="text" placeholder="" style="width: 100px" class="js_need_pay_money"
								name="tmoney" readonly="readonly">
							TWD
							<span class="pad_l10 color_999 f12">商品選擇完成後自動顯示,當前匯率:<?=$exchange['exchange']?>依銀行匯率含手續費</span>
						</div>

					</div>

					{include file="daifu/common_new" /}
				</form>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	var account_list = [];
	var new_user_money=0;
	$(function() {
		$(document).on('click', '.js_num_type', function() {
			$(this).addClass('active').siblings('.js_num_type').removeClass('active');
			$('.js_mz').val($(this).attr('data-id'));
			if ($(this).attr('data-id') == '') {
				$('.js_mz').val($(this).find('input').val());
			}
			func_all()
		})
		$(document).on('blur', '.js_num_input_d', function() {
			if ($('.js_num_type[data-id=""]').hasClass('active')) {
				$('.js_mz').val($(this).val());
			}
			func_all()
		})
		
		$(document).on('keyup', '.js_num_input_d', function() {
			$(this).val($(this).val().replace(/[^\d]/g,''));
			if ($('.js_num_type[data-id=""]').hasClass('active')) {
				$('.js_mz').val($(this).val());
			}
		})
		
		$('.js_item_money').blur(function() {
			func_all()
		})

		function func_all() {
			
			var js_item_money=$('.js_item_money'); //面额输入框
			var type=1;
			var money=0;
			if(js_item_money.length>0){
				type=3;
				money=parseFloat($('.js_item_money').val()) || 0
			}else{
				// 如果自定义 选中
				if ($('.js_num_type[data-id=""]').hasClass('active')) {
					type=2;
					money=parseFloat($('.js_mz').val()) || 0
				}else{
					type=1;
					money=parseFloat($('.js_num_type.active').attr('data-price')) || 0
				}
			}
			$('.js_select_price').val(money);
			$('input[name="type"]').val(type);
			
			// 1.选择类型的额，type=1 price  key_id
			// 2.输入自定义的，type=2 price  key_id
			// 3.输入面额的type=3 price  key_id
			var _url = "{:url('/index/daifu/new_price')}";
			$.post(_url, {
				price: money,
				key_id: <?=$set_type?>,  // 每种充值方式 固定
				type: type,
                num_type:$('.js_mz_type').val()
			}, function(data) {
				$('.js_need_pay_money').val(data.money);
				
				new_user_money=data.user_money
				if(!is_check_mobile && is_need_check==1){
					// 需要验证而没验证，则不填充
				}else{
                    var _can_use=parseFloat($('.js_gwj_num').html());
                    if(_can_use>=parseFloat(new_user_money)){
                        // 隐藏 支付方式
                        $('.js_paytype_divs').addClass('hide').find('input').attr('is_required',false)
                    }else{
                        // 显示 支付方式
                        $('.js_paytype_divs').removeClass('hide').find('input').attr('is_required',true)
                    }
					$('.js_daifubaobi_val').val(data.user_money)
				}
			})
		}

		$('.js_num_type_sele').click(function() {
			$(this).addClass('active').siblings('.js_num_type_sele').removeClass('active');
			$('.js_mz_type').val($(this).attr('data-id'));
			var html = '';
			var arr = $(this).attr('data-arr').split(',');
			var price = $(this).attr('data-price').split(',');
			var unit = $(this).attr('data-unit')
			for (var i = 0; i < arr.length; i++) {
				html += '<a class="daifu_btn margin_r10 js_num_type" href="javascript:;" data-id="' + arr[
					i] + '"  data-price="' + price[i] + '">' + arr[i] + '<br />￥' +price[i] + '</a>'
			}
            
            var is_discount=$(this).attr('data-discount');
            if(is_discount){
                html +=
                	'<a class="daifu_btn margin_r10 js_num_type" href="javascript:;" data-id=""><div class="d1"><input type="number" class="js_num_input_d" step="1" min="0">' +
                	unit + '</div><div class="d2">自定義金額</div></a>'
            }
			
			$('.js_mz_options').html(html);
			$('.js_mz').val('')
			func_all()
		})
		$('.js_num_type_sele').eq(0).click()
	})
</script>
