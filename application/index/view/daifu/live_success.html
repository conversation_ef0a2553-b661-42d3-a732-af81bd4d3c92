<div class="container">
    <div class="user_main margin_tb40 clearfix">
        <div class="user_main_line"></div>
        {include file="daifu/nav" /}
        <div class="pad_tb20 pad_lr20 over_hide daifu_success">
            {include file="daifu/success_live_common_head" /}
            <table class="common_table table margin_t10 text-center">
                <tbody>
                    <tr>
                        <th class="l" colspan="8">
                            <div class="clearfix pad_lr10">
                                {:__('Commodity message')}
                            </div>
                        </th>
                    </tr>
					<tr class="color_666">
						<?php
							if(!empty($order['order']['live_img'])){
						?>
							<td>
								<div class="text-left pad_l10">
									採購單/出貨單:<br />
									<?php
										$live_img = json_decode($order['order']['live_img'],true);
										foreach($live_img as $img_ls){
									?>
										<img width="100px" src="<?=$img_ls?>" />
									<?php
										}
									?>
								</div>
							</td>
						<?php
							}
						?>
					</tr>
                    <tr class="color_666">
						<?php
							if(!empty($order['order']['live_url'])){
								$title = '链接';
								if($set_type == '17'){
									$title = '店鋪名稱/商品名稱';
								}
						?>
							<td><div class="text-left pad_l10"><?=$title?>: <?=$order['order']['live_url']?></div></td>
						<?php
							}
							if(!empty($order['order']['live_id'])){
						?>
							<td><div class="text-left pad_l10">暱稱: <?=$order['order']['live_id']?></div></td>
						<?php
							}
							if(!empty($order['order']['live_account'])){
								$account = '帳號';
								if($set_type == '31'){
									$account = '商品名稱';
								}
						?>
							<td><div class="text-left pad_l10"><?=$account?>: <?=$order['order']['live_account']?></div></td>
						<?php
							}
							if(!empty($order['order']['live_pwd'])){
						?>
							<td><div class="text-left pad_l10">密碼: <?=$order['order']['live_pwd']?></div></td>
						<?php
							}
							if(!empty($order['order']['live_value'])){
						?>
							<td><div class="text-left pad_l10">面額: <?=$order['order']['live_value']?></div></td>
						<?php
							}
							if(!empty($order['order']['live_game'])){
						?>
							<td><div class="text-left pad_l10">遊戲區組: <?=$order['order']['live_game']?></div></td>
						<?php
							}
							if(!empty($order['order']['live_type'])){
						?>
							<td><div class="text-left pad_l10">類型: <?=$order['order']['live_type']?></div></td>
						<?php
							}
							if(!empty($order['order']['live_money'])){
						?>
							<td><div class="text-left pad_l10">面值: <?=$order['order']['live_money']?></div></td>
						<?php
							}
							if(!empty($order['order']['user_remarks'])){
						?>
							<td><div class="text-left pad_l10">備註: <?=$order['order']['user_remarks']?></div></td>
						<?php
							}
						?>
                    </tr>
					<tr class="color_666">
						<?php
							if(!empty($order['order']['address'])){
						?>
							<td>
								<div class="text-left pad_l10">
									收貨地址: 
									<?php
										$address = json_decode($order['order']['address'],true);
										echo $address['type'].$address['city'].$address['address'].'&nbsp&nbsp&nbsp&nbsp'.$address['address_name'].'('.$address['address_mobile'].')'
									?>
								</div>
							</td>
						<?php
							}
						?>
					</tr>
					<tr class="color_666">
						<?php
							if(!empty($order['order']['qiyebank'])){
						?>
							<td>
								<div class="text-left pad_l10">
									企業帳戶: 
									<?php
										$qiyebank = json_decode($order['order']['qiyebank'],true);
										echo $qiyebank['head_bank'].'('.$qiyebank['bank_name'].')'.$qiyebank['bank_username'].';'.$qiyebank['bank_account'];
									?>
								</div>
							</td>
						<?php
							}
						?>
					</tr>
                </tbody>
				<?php
					if($order['order']['type'] == '5'){
				?>
				<tbody>
                    <tr>
                        <th class="l" colspan="2">
                            <div class="clearfix pad_lr10">
                                {:__('Commodity message')}
                            </div>
                        </th>
                    </tr>
                    <tr class="color_666">
                        <td width="50%"><div class="text-left pad_l10">{:__('Platform link')}</div></td>
                        <td><div class="text-left pad_l10">{:__('Game Video Account')}</div></td>
                    </tr>
                    <tr class="color_666">
                        <td width="50%"><div class="text-left pad_l10"><?=$stored_account?></div></td>
                        <td><div class="text-left pad_l10 f12 color_999"><?=$wechat_openid?></div></td>
                    </tr>
                </tbody>
				<?php
					}
				?>
            </table>

           {include file="daifu/success_live_common" /}

        </div>
    </div>
</div>

