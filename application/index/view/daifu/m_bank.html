<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 2.6rem; }
    .footer_new{ display: none }
    .input_box{ background: none }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
	.new_pay_foot{background: #fff; width: 100%; position: fixed; left: 0; bottom: 0; z-index: 30; padding: 0.2rem;}
	.new_pay_foot .sub_btn{width: 100%;}
	.input_box .input_rr  textarea{padding: 0.2rem 0.2rem;}
	body .add_btn{color: #EF4B7B;}
</style>
<div class="pad_t20 pad_lr20">
{include file="daifu/tips" /}
{include file="daifu/m_mianbao" /}

    <form action="{:url('daifu/new_confirm_index')}">
	<input type="hidden" name="key_id" value="<?=$set_type?>" >
	    <div class="input_box" style="overflow: visible;">
	        <div class="input_tt">採購單/出貨單: <span class="f12 color_999 ">(最多上傳5張照片)</span></div>
	        <div class="up_bank_img flex_ac flex_w">
	            <div class="item add js_up_load_bank">
	    			<img src="__CDN__/assets/img/pc/big_add.png" class="add_icon" alt="">
	    		</div>
	    		<div class="hide">
	    		    <input type="file" class="js_up_img_input_bank" data-type="img" accept="image/*" multiple data-maxMum="5" data-fileSizeMax="10" onchange="filecountup($(this),this.files)" data-filename="img" />
	    		</div>
	    		
	        </div>
	    </div>
	    <div class="input_box  ">
	        <div class="input_tt">採購商品名稱:</div>
	        <div class="input_rr">
	            <input type="text" name="name" value="" is_required="true" placeholder="請輸入採購商品名稱">
	        </div>
	    </div>
	    <div class="input_box  ">
	        <div class="input_tt">金额:</div>
	        <div class="input_rr">
	            <input type="number" name="money" value="" class="js_mz" is_required="true" placeholder="請輸入金额">
	        </div>
	    </div>
		
		<div class="input_box">
		    <div class="input_tt">企業帳號:</div>
		    <div class="input_rr" >
				<select name="company" is_required="true" placeholder="請選擇企業帳號">
					<option value="">請選擇</option>
					<?php
						foreach($qiyebank as $ls){
					?>
					<option value="<?=$ls['id']?>"><?=$ls['head_bank']?>(<?=$ls['bank_name']?>);<?=$ls['bank_username']?><?=$ls['bank_account']?></option>
					<?php
						}
					?>
				</select>
		    </div>
		</div>
		<div class="pad_tb20">
			<a href="{:url('/index/account/add_company')}" class="add_btn js_ajax_win margin_l10 active" data-width="300" >新增企業帳戶</a>
		</div>
		
		    
		
		<!-- 不要删 -->
		<!-- <div class="input_box full_width" style="display: none;">
		    <div class="input_tt">商品總價</div>
		    <div class="input_rr pull-left">
		        <input type="text" placeholder="" style="width: 100px" class="js_need_pay_money" name="tmoney" readonly="readonly">
		    </div>
		</div> -->
		
		<div class="new_pay_foot order_detail">
			<a href="javascript:;" class="sub_btn red js_form_sub" data-func_before="before_sub" data-text="{:__('Next')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Next')}</a>
			<!--<a href="{:url('/index/daifu/cancel')}" class="sub_btn margin_t20 js_ajax_confirm" data-width="300" tips="您確定要取消代付？">取消代付</a>-->
		</div>
		
		<a href="{:url('/index/user/editIdcard')}?type=1" class="js_ajax_win hide js_one_auth"></a>
		<a href="{:url('/index/user/editIdcardTwo')}?type=1" class="js_ajax_win hide js_two_auth"></a>
		
    </form>

</div>

<script type="text/javascript">
	function before_sub(){
        return true;
    }
    $(function(){
		
		<?php
			if($user['is_card'] == '0'){
		?>
				$('.js_one_auth').click();
		<?php
			}
			//if($user['is_card'] == '1'){
				//if($df_type > '2' && $df_type != '7'){
				//}else{
					//if($user['is_card_img'] == '0'){
			?>
				//$('.js_two_auth').click();
			<?php
					//}
				//}
			//}
		?>
		
		
		$('.js_up_load_bank').click(function(){
		    $('.js_up_img_input_bank').click()
		})
		$(document).on('click','.js_close_icon',function(){
			var _this=$(this)
			var a=layer.confirm("{:__('Are you sure you want to delete it?')}", {
			  title:false,
			  area:['300px', 'auto'],
			  skin: 'my_confirm',
			  btn: ["{:__('Confirm')}","{:__('Cancel')}"], //按钮
			  btnAlign:'c'
			}, function(){
			    layer.close(a);
			    _this.parent('.item').remove();
				check_add()
			});
		})
    })
	
	
	function check_add(){
		var max=$('.js_up_img_input_bank').attr('data-maxMum') || 1;
		if($('.js_close_icon').length==max){
			$('.js_up_load_bank').css('display','none')
		}else{
			$('.js_up_load_bank').css('display','flex')
		}
	}
	
	function getImgData(img,dir,max_w,next){
	 var image=new Image();
	 image.onload=function(){
	  var degree=0,drawWidth,drawHeight,width,height;
	  drawWidth=max_w>this.naturalWidth?this.naturalWidth:max_w;
	  drawHeight=this.naturalHeight*drawWidth/this.naturalWidth;
	  
	  var canvas=document.createElement('canvas');
	  canvas.width=width=drawWidth;
	  canvas.height=height=drawHeight; 
	  var context=canvas.getContext('2d');
	  context.drawImage(this,0,0,drawWidth,drawHeight);
	  //返回校正图片
	  next(canvas.toDataURL("image/jpeg",.7));
	 }
	 image.src=img;
	}
	
	function filecountup(_this,files,count){
	    // 文件大小限制
	    if(_this.attr('data-fileSizeMax')!=''){
	        var max=parseFloat(_this.attr('data-fileSizeMax'))*1024*1024;
	        for(i=0;i<files.length;i++){
	            if(files[i].size > max){
	                tip_show("文件不能大于"+parseFloat(_this.attr('data-fileSizeMax'))+"M","2");
	                return false;
	                break;
	            }  
	        }
	    }
	    var max=_this.attr('data-maxMum') || 1;
		max=max-$('.js_close_icon').length;
		var index=0
		function load_img(){
			if(index<files.length){
				var file = files[index];
				var reader = new FileReader();
				reader.onloadend = function () {
				    // console.log(reader.result);
				    getImgData(reader.result,'',2000,function(data){
				        console.log(data)
				        // $('.js_up_load_btn').addClass('hide').find('input').val(data);
				        // $('.js_img_src').removeClass('hide').css('background-image','url('+data+')');
						var html='<div class="item"><a href="javascript:;" class="flex_ac_jc close_icon js_close_icon"><img src="__CDN__/assets/img/wap/icon_img_delete.png" class="" alt=""></a><img src="'+data+'" class="img" alt=""><input type="hidden" name="imgs[]" value="'+data+'"></div>'
						$('.up_bank_img').prepend(html);
						check_add();
						index++;
						if(index<max){
							load_img()
						}
				    })
				}
				if (file) {
				    reader.readAsDataURL(file);
				}
			}
		}
		load_img()
	    
	};

</script>
