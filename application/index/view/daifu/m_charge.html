<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .input_box{ background: none }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
</style>
<div class="pad_t20 pad_lr20">
    {include file="daifu/tips" /}
	{include file="daifu/m_mianbao" /}
    <form action="{:url('daifu/confirm_index')}">
	    <div class="input_box full_width">
            <div class="input_tt">{:__('RMB')}</div>
            <div class="input_rr">
                <div class="pull-right pad_l10 pad_r20">RMB</div>
                <div class="over_hide">
                    <input type="text" data-type="money" name="money" class="js_money_v" data-max="{$site.f_max}" data-id="1" data-category="7" placeholder="{:__('Please enter RMB amount')}">
                </div>
            </div>
        </div>
        <a href="javascript:;" class="js_rate_change hide" data-url="{:url('/index/user/rate_fun')}"><img src="__CDN__/assets/img/pc/rate_change.png" alt=""></a>

        <div class="input_box full_width">
            <div class="input_tt">{:__('Conversion TWD')} <span class="f12 color_999">({:__('Rate')}： <span class="js_dafu_rate_val"><?=$exchange['exchange']?></span>)</span></div>
            <div class="input_rr">
                <div class="pull-right pad_l10 pad_r20 color_red">TWD</div>
                <div class="over_hide">
                    <input type="text" data-type="money" class="js_money_v" data-id="2" data-category="7" placeholder="{:__('Please enter TWD amount')}">
                </div>
            </div>
        </div>

	    <!--<div class="item_tt pad_tb20">
	        {:__('Need TWD')}
	    </div>

	    <div class="order_rate">
	        <div class="rate clearfix">
	            <div class="r_item">
	                <div class="pp">{:__('Order amount')}</div>
	                <div class="tt">1000.00RMB</div>
	            </div>
	            <div class="c_item">
	                <span class="color_666">{:__('Rate')}：</span>4.5330
	            </div>
	            <div class="r_item">
	                <div class="pp">{:__('Need TWD')}</div>
	                <div class="tt"><span class="color_red">4533.00</span>TWD</div>
	            </div>
	        </div>
	    </div>-->


	    <div class="pad_tb30 margin_t20 order_detail">
	    	<a href="javascript:;" class="sub_btn margin_b30 red js_form_sub" data-text="{:__('Next')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Next')}</a>


	        <a href="{:url('index/user/order_dele')}" class="sub_btn js_ajax_dele" tips="{:__('Do you confirm the cancellation?')}" data-id="1">{:__('Cancellation of payment')}</a>
	    </div>

    </form>

</div>
<a href="{:url('/index/user/editIdcard')}?type=1" class="js_ajax_win hide js_one_auth"></a>
<a href="{:url('/index/user/editIdcardTwo')}?type=1" class="js_ajax_win hide js_two_auth"></a>
<script type="text/javascript">
	$(function(){
	
		$('.js_ajax_win').attr('data-width',$(window).width()*0.96).attr('data-height',$(window).height()*1)
		<?php
			if($user['is_card'] == '0'){
		?>
				$('.js_one_auth').click();
		<?php
			}
		?>
	})
	// 储值 汇率换算
    $(document).on('keyup','.js_money_v',function(){
        var _this=$(this);
        var _type=_this.attr('data-id');
		var _category=_this.attr('data-category');
        if(_this.val()==''){
            return false
        }
        setTimeout(function(){
            var _url=$('.js_rate_change').attr('data-url');
            $.post(_url,{money:$('.js_money_v').val(),type:_type,category:_category},function(data){
                $('.js_dafu_rate_val').html(data.exchange);
                var _otyps=_type==1?2:1;
                $('.js_money_v[data-id="'+_otyps+'"]').val(data.money)
            })
        },20)
    })
	
</script>