<style type="text/css">
    .my_close_win .input_box .input_rr input{ border: 1px solid #eee }
</style>
<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 pad_b40">
    <div class="color_red pad_t10 my_close_win_title">{:__('Mobile phone verification')}</div>
    <form action="{:url('/index/daifu/checkMobile')}" autocomplete="off" class=" " style="padding: 10px 0px 0 0px">
        <div class="f12 color_999">{:__('the mobile phone number is your login account')}</div>
        <div class="clearfix pad_t20">
            <div class="input_box full_width">
                <div class="input_tt">{:__('Mobile verification code')}</div>
                <div class="input_rr">
                    <a href="javascript:;" class="get_code" data-url="{:url('/index/login/getCode',array('type'=>'2'))}">{:__('Get code')}</a>
                    <input type="text" name="code" data-type="number" maxlength="6" is_required="true" empty_tip="{:__('Please enter mobile verification code')}">
                </div>
            </div>
            <div class="pad_t10">{:__('Current mobile phone number')}：<?=substr_replace($user['mobile'],'****',2,4)?>  <input type="hidden" name="mobile" class="js_code_input" value="<?=$userinfo['mobile']?>"></div>
        </div>
        
        <div class="text-center pad_t30">
            <a href="javascript:;" class="sub_btn small js_form_sub" data-type="func" data-func_name="after_check_mobile" data-text="{:__('Submit')}" data-loadding="{:__('Submitting')}">{:__('Submit')}</a>
        </div>

    </form>
</div>
<script type="text/javascript">
    function after_check_mobile(){
        is_check_mobile=true;
        layer.closeAll();

        input_coin()
    }
</script>