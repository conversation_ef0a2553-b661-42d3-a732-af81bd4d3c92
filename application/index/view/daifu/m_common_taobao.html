<!--<div class="item_tt pad_b20">
    {:__('Need TWD')}
</div>
<div class="order_rate ">
    <div class="rate clearfix">
        <div class="r_item">
            <div class="pp">{:__('Order amount')}</div>
            <div class="tt">1000.00RMB</div>
        </div>
        <div class="c_item">
            <span class="color_666">{:__('Rate')}：</span>4.5330
        </div>
        <div class="r_item">
            <div class="pp">{:__('Need TWD')}</div>
            <div class="tt"><span class="color_red">4533.00</span>TWD</div>
        </div>
    </div>
</div>-->

<div class='js_df_tip' style="margin-left: 20%;margin-top: 20px;color: red;"></div>
<div></div>
<div class="pad_tb30 margin_t20 order_detail">
	<a href="{:url('/index/daifu/cancel')}" class="sub_btn margin_b30 blue js_ajax_confirm" tips="{:__('Do you confirm the cancellation?')}">{:__('Cancellation of this payment')}</a>
    <a href="javascript:;" class="sub_btn margin_b30 red js_form_sub" data-func_before="before_sub" data-text="{:__('Next')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Next')}</a>
	
    <!-- <a href="{:url('/index/daifu/cancel')}" class="sub_btn js_ajax_dele" tips="{:__('Do you confirm the cancellation?')}" data-id="1">{:__('Cancellation of payment')}</a> -->
</div>

<a href="{:url('/index/user/editIdcard')}?type=1" class="js_ajax_win hide js_one_auth"></a>
<a href="{:url('/index/user/editIdcardTwo')}?type=1" class="js_ajax_win hide js_two_auth"></a>




<?php
	//if($df_type == '0' && empty($account_type) && empty($type)){
?>
<!-- 虚拟和实物弹窗 
<style type="text/css">
    .smark{ display: block; position: fixed; left: 0; top: 0; background: rgba(0,0,0,0.8); width: 100%; height: 100%; z-index: 160 }
    .select_model_win{ width: 80%; position: fixed; left: 10%;  top: 50%; margin-top: -100px; padding:40px 20px; border-radius: 10px; z-index: 170;background: #fff; text-align: center; }
    .select_model_win a{ display: block; line-height: 60px; border-radius: 10px; font-size: 14px; color: #fff; background:#FFBC3B  }
    .select_model_win a.item1{ margin-bottom: 20px; background: #3B80FF; }
     .select_model_win a img{ margin-right: 5px; width: 24px; }
</style>
<div class="smark"></div>
<div class="select_model_win">
    <a href="<?=url('',array('set_type'=>$set_type,'account_type'=>'1'))?>" class="item item1">
        <img src="__CDN__/assets/img/icon_t1.png">虚拟商品      
    </a>
    <a href="<?=url('',array('set_type'=>$set_type,'account_type'=>'2'))?>" class="item">
        <img src="__CDN__/assets/img/icon_t2.png">實物商品      
    </a>
</div>-->

<?php
	//}
?>

<script type="text/javascript">
	var res_goods=[]
    function before_sub(){
        // 如果是阿里巴巴 淘宝代付，必须至少有一个代付商品
        if($('.js_daifu_goods_list').length>0 && $('.js_daifu_goods_list .item_div').length<=0){
            tip_show("{:__('Please select at least one commodity')}",'2');
            return false;
        }

        if($('.js_daifu_goods_list').length>0){
            var _items=$('.js_daifu_goods_list .item_div');
            var _arr=[];
            for(var i=0;i<_items.length;i++){
                _arr.push(_items.eq(i).find('.js_ajax_cancel_df').attr('data-id'));
            }

            $('.js_daifu_goods_ids').val(_arr)
        }

        // if(!$('.login_tips .moni_check.js_agree2').hasClass('active')){
        //     tip_show("{:__('Please agree to settlement')}",'2');
        //     return false;
        // }
        return true;
    }
	
	
	function render_goods(){
		let list_html='';
		let money=0;
		for(let i=0;i<res_goods.length;i++){
			list_html+=`<div class="item_div" style="margin-bottom:0">
				
				<div class="item clearfix">
					<div class="pull-left t">{:__('Purchaser')}：</div>
					<div class="over_hide r_text"><span class="color_666">`+res_goods[i].other_name+`</span></div>
				</div>
				<div class="item clearfix">
					<div class="pull-left t">名稱：</div>
					<div class="over_hide r_text color_666">`+res_goods[i].title+`</div>
				</div>
				
				<div class="item clearfix flex_ac">
					<div class="pull-left t">{:__('Amount')}：</div>
					<div class="over_hide flex1 r_text color_red">`+res_goods[i].price+`RMB</div>
					<a href="{:url('/index/daifu/cancel_df')}" data-index="`+i+`" class="sub_btn  red js_ajax_cancel_df" tips="{:__('Do you confirm the cancellation?')}" data-id="`+res_goods[i].id+`">取消代付</a>
				</div>
			</div>`
			money+=parseFloat(res_goods[i].price)
		}
		money=money.toFixed(2)
		list_html+=`<input name="money" type="hidden" value="`+money+`" />`
		let html=`<div class="flex_ac big_tt margin_tb20 js_daifu_goods_wap_tt ">{:__('Commodity message')}</div><div class="wi_block margin_t20">`+list_html+`</div>`
		html+=`<div class="flex_ac big_tt margin_tb20 js_daifu_goods_wap_tt ">應支付新臺幣</div>
			<div class="wi_block hl_info flex_ac margin_t20">
				<div class="hlitem">
					<div class="t">
						訂單金額
					</div>
					<div class="p">
						`+money+`RMB
					</div>
				</div>
				<div class="flex1 hl text-center">
					匯率:<?=$exchange['exchange']?>
				</div>
				<div class="hlitem">
					<div class="t">
						新臺幣
					</div>
					<div class="p">
						<span class="red">`+(money*parseFloat(<?=$exchange['exchange']?>)).toFixed(2)+`</span>TWD
					</div>
				</div>
			</div>
		`
		$('.js_daifu_goods_list').html(html);
	}
	
    $(function(){
        $('.js_ajax_win').attr('data-width',$(window).width()*0.96).attr('data-height',$(window).height()*1)
        // 是否是 吱口令 1  是，其他  不是 
        var is_zhi=<?=empty($zhi)?'0':'1'?>; 

		<?php
			if(!$user['is_card']){
		?>
				$('.js_one_auth').click();
		<?php
			}
			if($user['is_card']){
				if(!$user['is_card_img'] && !$user_frontend_bank){
		?>
					$('.js_two_auth').click();
		<?php
				}
			}
		?>
        // 打开认证弹窗
        // $('.js_one_auth').click();
        // $('.js_two_auth').click();
        // 从个人中心进来,发送一个请求，打开 选择商品弹框
        
        var is_from_user_center=<?=empty($type)?'0':'1'?>; // 1需要请求 ，其他 不用        

        if(is_from_user_center==1){
            $.post("{:url('/index/daifu/userGoods')}",{},function(data){
                if(data.code==1){
                    // 大夫申请人账号 请求返回商品列表,用弹窗弹出来
                    layer.closeAll();
                    layer.open({
                      type: 1,
                      title:false,
                      area:[$(window).width()>500?'760px':'6rem', 'auto'],
                      offset:"20%",
                      // anim:2,
                      skin: 'win_class_no_hide', //样式类名
                      closeBtn: 0, //不显示关闭按钮
                      shadeClose: false, //开启遮罩关闭
                      content:data.data,
                      move:'.layui-layer-content .my_close_win_title'
                    });
                }else{
                    tip_show(data.msg,"2");
                }
            })
        }
        
        // 阿里巴巴+天猫代付 公用js
        // 切换账户
        var s_account_index=0;
        $('.js_switch_account').click(function(){
            s_account_index++;
            if(s_account_index>=account_list.length){
                s_account_index=0;
            }
            change_account(s_account_index)
        })

        function change_account(_index){
            $('.js_account_val').val(account_list[_index]);
            $('.js_copy').attr('data-clipboard-text',account_list[_index])
        }
        if(account_list.length>0){
            change_account(0)
        }

        // 请确认代付申请人帐号
        // $('.js_search_daifu_goods').click(function(){
        //     var _val=$('.js_daifu_money').val();
        //     var _this=$(this);
        //     if(_this.hasClass('disabled')){
        //         return false;
        //     }
        //     if(_val=='' || _val==0){
        //         input_tips($('.js_daifu_money'),"{:__('Please enter payment amount')}", 3);
        //         $('.js_daifu_money').addClass('error').focus();
        //         return false;
        //     }
        //     var _text=_this.html();

        //     _this.html("{:__('Searching···')}").addClass('disabled');


        //     // 直接打开弹窗的测试代码
        //     // $.post(_this.attr('data-url'),{money:_val},function(data){
        //     //     layer.open({
        //     //           type: 1,
        //     //           title:false,
        //     //           area:['760px', '550px'],
        //     //           offset:"20%",
        //     //           // anim:2,
        //     //           skin: 'win_class_no_hide', //样式类名
        //     //           closeBtn: 0, //不显示关闭按钮
        //     //           shadeClose: false, //开启遮罩关闭
        //     //           content:data,
        //     //           move:'.layui-layer-content .my_close_win_title'
        //     //         });
        //     // })
        //     // return false;
        //     // 直接打开弹窗的测试代码



        //     $.post(_this.attr('data-url'),{money:_val},function(data){
        //         if(data.code==1){
        //             // money请求返回账户列表,用弹窗弹出来
        //             layer.open({
        //               type: 1,
        //               title:false,
        //               area:['6rem', 'auto'],
        //               offset:"20%",
        //               // anim:2,
        //               skin: 'win_class_no_hide', //样式类名
        //               closeBtn: 0, //不显示关闭按钮
        //               shadeClose: false, //开启遮罩关闭
        //               content:data.data,
        //               move:'.layui-layer-content .my_close_win_title'
        //             });
        //             _this.html(_text).removeClass('disabled')
        //         }else{
        //             tip_show(data.msg,"2");
        //             _this.html(_text).removeClass('disabled')
        //         }
        //     })
        // })




        // 请确认代付申请人帐号
        $('.js_search_daifu_goods').click(function(){
            var _val=$('.js_daifu_money').val();
            var _url=$('.js_daifu_url').val();
			var _account=$('.js_account_val').val();
            var _this=$(this);
            if(_this.hasClass('disabled')){
                return false;
            }
            if(_val=='' || _val==0){
                input_tips($('.js_daifu_money'),$('.js_daifu_money').attr('empty_tip'), 3);
                $('.js_daifu_money').addClass('error').focus();
                return false;
            }
			
            var _text=_this.html();
            _end_time=10;

            _time=setInterval(function(){
                _end_time--;
                if(_end_time<=0){
                    _this.html(_text).removeClass('disabled');
                    clearInterval(_time);
                    _time=null
                }else{
                    do_search()
                }
            },5000)

            do_search()

            function do_search(){
                _this.html("{:__('Searching···')}"+_end_time+'次').addClass('disabled');
                console.log({
					money:_val,
					type:is_zhi,
					url:_url,
					account:_account
				})
                $.post(_this.attr('data-url'),{
					money:_val,
					type:is_zhi,
					url:_url,
					account:_account
				},function(data){
                    if(data.code==1){
                        // 吱口令直接插入 商品
                        if(is_zhi==1){
                            // 把 返回的商品列表插入页面
                            $('.js_daifu_goods_list').html(data.data);
                            // 手机端的 标题显示出来
                            $('.js_daifu_goods_wap_tt').removeClass('hide');
                            _this.html(_text).removeClass('disabled');
                            clearInterval(_time);
                            _time=null
                        }else{
                            // money请求返回账户列表,用弹窗弹出来
                            // 这里避免下次请求覆盖本次
                            if(_time!=null){
                                // res_goods=data.list
                                let list=data.list;
                                let arr=[];
                                function has_good(item){
                                    for(let i=0;i<res_goods.length;i++){
                                        if(res_goods[i].id==item.id){
                                            return true;
                                        }
                                    }
                                    return false;
                                }

                                for(let i=0;i<list.length;i++){
                                    if(!has_good(list[i])){
                                        arr.push(list[i])
                                    }
                                    
                                }


                                $('.js_daifu_url').val('');
                                res_goods=res_goods.concat(arr)
								render_goods()
                                _this.html(_text).removeClass('disabled');
								$('.js_df_tip').html('');
                                clearInterval(_time);
                                _time=null

                            }
                        }
                        
                        
                    }else{
                        // 吱口令  吱口令错误弹窗
                        if(is_zhi==1 && data.msg==1){
                            tip_show('吱口令格式錯誤',"2");
                            _this.html(_text).removeClass('disabled');
                            clearInterval(_time);
                            _time=null
                        }
						$('.js_df_tip').html(data.msg)
                         //tip_show(data.msg,"2");
                         //_this.html(_text).removeClass('disabled')
                         //clearInterval(_time);
                    }
                })
            }
            


            // 直接打开弹窗的测试代码
            // $.post(_this.attr('data-url'),{money:_val},function(data){
            //     layer.open({
            //           type: 1,
            //           title:false,
            //           area:['760px', '550px'],
            //           offset:"20%",
            //           // anim:2,
            //           skin: 'win_class_no_hide', //样式类名
            //           closeBtn: 0, //不显示关闭按钮
            //           shadeClose: false, //开启遮罩关闭
            //           content:data,
            //           move:'.layui-layer-content .my_close_win_title'
            //         });
            // })
            // return false;
            // 直接打开弹窗的测试代码
        })
        


        // 储值 汇率换算
        $(document).on('keyup','.js_money_v',function(){
            var _this=$(this);
            var _type=_this.attr('data-id');
            var _category=_this.attr('data-category');
            if(_this.val()==''){
                return false
            }
            setTimeout(function(){
                var _url=$('.js_rate_change').attr('data-url');
                $.post(_url,{money:_this.val(),type:_type,category:_category},function(data){
                    $('.js_dafu_rate_val').html(data.exchange);
                    var _otyps=_type==1?2:1;
                    $('.js_money_v[data-id="'+_otyps+'"]').val(data.money)
                })
            },20)
        })

    })
</script>