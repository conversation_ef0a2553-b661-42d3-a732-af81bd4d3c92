<?php
	foreach($product['alipay'] as $ls){
?>
<div class="item_div">
	<a href="{:url('/index/daifu/cancel_df')}" class="dele js_ajax_cancel_df" tips="{:__('Do you confirm the cancellation?')}" data-id="<?=$ls['alipay_order_id']?>"><img src="__CDN__/assets/img/wap/delete.png"></a>
	<div class="item clearfix">
		<div class="pull-left t">{:__('Purchaser')}：</div>
		<div class="over_hide r_text"><span class="color_666"><?=$ls['other_name']?></span></div>
	</div>
	<?php
		foreach($product['product'][$ls['alipay_order_id']] as $val){
	?>
		<div class="item clearfix">
			<div class="pull-left t">名稱：</div>
			<div class="over_hide r_text color_666"><?=$val['title']?></div>
		</div>
	<?php
		}
	?>
	<div class="item clearfix">
		<div class="pull-left t">{:__('Url')}：</div>
		<div class="over_hide r_text color_999"><?=$ls['peerpay_link']?></div>
	</div>
	<div class="item clearfix">
		<div class="pull-left t">{:__('Amount')}：</div>
		<div class="over_hide r_text color_red"><?=$product['all_price']?>RMB</div>
	</div>
</div>
<?php
	}
?>
<input name="money" type="hidden" value="<?=$product['all_price']?>" />
