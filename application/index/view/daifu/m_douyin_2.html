<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 2.6rem; }
    .footer_new{ display: none }
    .input_box{ background: none }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
	.new_pay_foot{background: #fff; width: 100%; position: fixed; left: 0; bottom: 0; z-index: 30; padding: 0.2rem;}
	.new_pay_foot .sub_btn{width: 100%;}
	.input_box .input_rr  textarea{padding: 0.2rem 0.2rem;}
	body .add_btn{color: #EF4B7B;}
</style>
<div class="pad_t20 pad_lr20">

	{include file="daifu/tips" /}
	{include file="daifu/m_mianbao" /}
    <form action="{:url('daifu/new_confirm_index')}">
		<input type="hidden" name="key_id" value="<?=$set_type?>" >
		<div class="input_box  " >
	        <div class="input_tt">上傳圖片:</div>
	        <div class="">
	            <a href="javascript:;" class="sub_btn red js_check_pro" >上傳圖片查詢代付商品</a>
			</div>
	    </div>
	    <div class="input_box  " style="display:none;">
	        <div class="input_tt">金额:</div>
	        <div class="input_rr">
	            <input type="text" data-type="money" name="money" value="" class="js_mz" is_required="true" placeholder="請輸入金额">
				<span class="f12 color_999">({:__('Rate')}： <span class="js_dafu_rate_val"><?=$exchange['exchange']?></span>)
			</div>
	    </div>
		<?php
			if($set_type != '31'){
		?>
		<div class="input_box">
		    <div class="input_tt">收貨地址:</div>
		    <div class="input_rr" >
				<select name="address" is_required="true" placeholder="請選擇收貨地址">
				<option value="">請選擇</option>
					<?php
						foreach($address as $ls){
					?>
						<option value="<?=$ls['id']?>"><?=$ls['address']?>-<?=$ls['address_name']?>(<?=$ls['address_mobile']?>)</option>
					<?php
						}
					?>
				</select>
		    </div>
		</div>
		<div class="pad_tb20">
			<a href="{:url('/index/account/win_add_address')}" class="add_btn js_ajax_win margin_l10 active" data-width="300" >新增收貨地址</a>
		</div>
		<?php
			}
		?>
		
		
		    
		
		<!-- 不要删 -->
		<!-- <div class="input_box full_width" style="display: none;">
		    <div class="input_tt">商品總價</div>
		    <div class="input_rr pull-left">
		        <input type="text" placeholder="" style="width: 100px" class="js_need_pay_money" name="tmoney" readonly="readonly">
		    </div>
		</div> -->
		<div class="new_pay_foot order_detail">
			<a href="javascript:;" class="sub_btn red js_form_sub" data-func_before="before_sub" data-text="{:__('Next')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Next')}</a>
			<!--<a href="{:url('/index/daifu/cancel')}" class="sub_btn margin_t20 js_ajax_confirm" data-width="300" tips="您確定要取消代付？">取消代付</a>-->
		</div>
		
		<a href="{:url('/index/user/editIdcard')}?type=1" class="js_ajax_win hide js_one_auth"></a>
		<a href="{:url('/index/user/editIdcardTwo')}?type=1" class="js_ajax_win hide js_two_auth"></a>
		
    </form>
	<input type="file" class="js_up_img_check_pro" style="display:none" data-type="img" accept="image/*" multiple data-maxMum="1" data-fileSizeMax="2" onchange="filecountup_check($(this),this.files)" data-filename="img" />
</div>

<script type="text/javascript">
	function before_sub(){
        return true;
    }
	
	function getImgData(img,dir,max_w,next){
	 var image=new Image();
	 image.onload=function(){
	  var degree=0,drawWidth,drawHeight,width,height;
	  drawWidth=max_w>this.naturalWidth?this.naturalWidth:max_w;
	  drawHeight=this.naturalHeight*drawWidth/this.naturalWidth;
	  
	  var canvas=document.createElement('canvas');
	  canvas.width=width=drawWidth;
	  canvas.height=height=drawHeight; 
	  var context=canvas.getContext('2d');
	  context.drawImage(this,0,0,drawWidth,drawHeight);
	  //返回校正图片
	  next(canvas.toDataURL("image/jpeg",.7));
	 }
	 image.src=img;
	}
	//图片上传预览 2018.7.11
	function filecountup_check(_this,files,count){
		// 文件大小限制
		// 
		console.log(files)
		if(_this.attr('data-fileSizeMax')!=''){
			var max=parseFloat(_this.attr('data-fileSizeMax'))*1024*1024;
			if(files[0].size > max){
				tip_show("文件不能大于"+parseFloat(_this.attr('data-fileSizeMax'))+"M","2");
				return false;
			} 
		}


		var file = files[0];
		var reader = new FileReader();
		reader.onloadend = function () {
			// console.log(reader.result);
			// $('.js_up_load_btn2').addClass('hide').find('input').val(reader.result);
			// $('.js_img_src2').removeClass('hide').css('background-image','url('+reader.result+')')
			getImgData(reader.result,'',1000,function(data){
				
				var _url = "{:url('/index/daifu/confirmAccount_douyin')}";
				$.post(_url, {
					img:data
				}, function(res) {
					
				})
			})
		}
		if (file) {
			reader.readAsDataURL(file);
		}
	};
    $(function(){
		
		<?php
			if($user['is_card'] == '0'){
		?>
				$('.js_one_auth').click();
		<?php
			}
			//if($user['is_card'] == '1'){
				//if($df_type > '2' && $df_type != '7'){
				//}else{
					//if($user['is_card_img'] == '0'){
			?>
				//$('.js_two_auth').click();
			<?php
					//}
				//}
			//}
		?>
		
		$('.js_check_pro').click(function(){
			$('.js_up_img_check_pro').click()
		})	
		// function func_all(){
		// 	var money=parseFloat($('.js_mz').val()) || 0;
		// 	var _url="{:url('/index/user/rate_fun')}";
		// 	$.post(_url,{money:money,type:1,category:3},function(data){
		// 		$('.js_need_pay_money').val(data.money)
		// 	})
		// }
		
		// $('.js_mz').blur(function(){
		// 	func_all()
		// })
    })

</script>
