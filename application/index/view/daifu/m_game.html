<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .input_box{ background: none }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
    .input_box .input_rr .arr{ height: 0.2rem; margin-top: 0.3rem; margin-left:10px; margin-right: 0.2rem }
</style>
<div class="pad_t20 pad_lr20">
    {include file="daifu/tips" /}
	{include file="daifu/m_mianbao" /}
    <form action="{:url('daifu/confirm_index')}">
        <div class="input_box full_width">
            <div class="input_tt">{:__('RMB')}</div>
            <div class="input_rr">
                <div class="pull-right pad_l10 pad_r20">RMB</div>
                <div class="over_hide">
                    <input type="text" data-type="money" name="money" class="js_money_v" data-max="100000" data-id="1" data-category="5" placeholder="{:__('Please enter RMB amount')}">
                </div>
            </div>
        </div>
        <a href="javascript:;" class="js_rate_change hide" data-url="{:url('/index/user/rate_fun')}"><img src="__CDN__/assets/img/pc/rate_change.png" alt=""></a>

        <div class="input_box full_width">
            <div class="input_tt">{:__('Conversion TWD')} <span class="f12 color_999">({:__('Rate')}： <span class="js_dafu_rate_val"><?=$exchange['exchange']?></span>)</span></div>
            <div class="input_rr">
                <div class="pull-right pad_l10 pad_r20 color_red">TWD</div>
                <div class="over_hide">
                    <input type="text" data-type="money" class="js_money_v" data-id="2" data-category="5" placeholder="{:__('Please enter TWD amount')}">
                </div>
            </div>
        </div>

        <div class="input_box full_width">
            <div class="input_tt">{:__('Game Video Platform')} <span class="f12 color_999">({:__('Platform link')})</span></div>
            <div class="input_rr">
                <div class="pull-right pad_l10 pad_r20 color_red"></div>
                <div class="over_hide">
                    <input type="text" name="web" value="" is_required="true" empty_tip="{:__('Please enter platform link')}" class="auto_w" placeholder="{:__('Please enter platform link')}">
                </div>
            </div>
        </div>
         <div class="input_box margin_t10">
            <div class="input_tt">{:__('Game Video Account')}:</div>
            <div class="input_rr">
                <input type="text" name="account" value="" is_required="true" empty_tip="{:__('Please enter the Game Video Account')}" class="auto_w" placeholder="{:__('Please enter the Game Video Account')}">
            </div>
        </div>
        <div class="clearfix login_tips pad_tb20">
            <label class="moni_check_label clearfix">
                <a href="javascript:;" class="moni_check pull-left active">
                    <input type="checkbox" name="is_agree_ali" value="1" data-type="checkbox" checked="checked" is_required="true" empty_tip="{:__('Please understand the payment rules first')}" style="position: absolute; width: 0; height: 0; opacity: 0">
                </a>
                <span class="la_btn over_hide" style="display: block;">{:__('I confirm that the account I want to deposit')}</span>
            </label>
        </div>


        {include file="daifu/m_common" /}

    </form>

</div>

<script type="text/javascript">
    var account_list=[];
    $(function(){
    })
</script>