<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 2.6rem; }
    .footer_new{ display: none }
    .input_box{ background: none }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
	.new_pay_foot{background: #fff; width: 100%; position: fixed; left: 0; bottom: 0; z-index: 30; padding: 0.2rem;}
	.new_pay_foot .sub_btn{width: 100%;}
	
	.input_box .pay_type_div{background: none;}
	.num_pay_type a{display: flex; align-items: center; justify-content: center; width: 2.12rem; height:  0.92rem; border-radius: 0.1rem; background: #fff; margin-right: 0.2rem; margin-bottom: 0.2rem; color: #999999; border: 1px solid #fff;}
	.num_pay_type a.active{border-color: #F04C7C; color: #F04C7C;}
	.num_pay_type a input{width: 100%; text-align: center;}
	.num_pay_type a:nth-child(3n){margin-right: 0;}
</style>
<div class="pad_t20 pad_lr20">

	{include file="daifu/tips" /}
	{include file="daifu/m_mianbao" /}
    <form action="{:url('daifu/confirm_index')}">
	    <div class="input_box  ">
	        <div class="input_tt">ID:</div>
	        <div class="input_rr">
	            <input type="text" name="id" value="" is_required="true" placeholder="請輸入ID">
	        </div>
	    </div>
	    <div class="input_box  ">
	        <div class="input_tt">帳號:</div>
	        <div class="input_rr">
	            <input type="text" name="account" value="" is_required="true" placeholder="請輸入帳號">
	        </div>
	    </div>
	    <div class="input_box  ">
	        <div class="input_tt">密碼:</div>
	        <div class="input_rr">
	            <input type="password" name="password" value="" is_required="true" placeholder="請輸入密碼">
	        </div>
	    </div>
	    <div class="input_box">
	        <div class="input_tt">面额:</div>
	        <div class="input_rr">
	            <input type="number" name="money" value="" class="js_item_money" is_required="true" placeholder="請輸入面额">
	        </div>
	    </div>
	    <div class="input_box  ">
	        <div class="input_tt">遊戲區:</div>
	        <div class="input_rr">
	            <input type="text" name="area" value="" is_required="true" placeholder="請輸入遊戲區">
	        </div>
	    </div>
	    <div class="input_box  ">
	        <div class="input_tt">面值:</div>
	        <div class="input_rr pay_type_div">
	            <input type="text" class="js_mz" name="num" style="width: 0; height: 0; opacity: 0; position: absolute;" value="" is_required="true" placeholder="請選擇面值">
	    		<div class="pay_type flex flex_w num_pay_type">
	    			<a class="daifu_btn margin_r10 js_num_type" href="javascript:;" data-id="10">10個</a>
	    			<a class="daifu_btn margin_r10 js_num_type" href="javascript:;" data-id="100">100個</a>
	    			<a class="daifu_btn margin_r10 js_num_type" href="javascript:;" data-id="300">300個</a>
	    			<a class="daifu_btn margin_r10 js_num_type" href="javascript:;" data-id="1000">1000個</a>
	    			<a class="daifu_btn margin_r10 js_num_type" href="javascript:;" data-id=""><input type="number" class="js_num_input_d" step="1" min="0" placeholder="自定義"></a>
	    		</div>
	    		
	        </div>
	    </div>
	    
	    <div class="input_box clearfix">
	        <div class="input_tt">商品總價:</div>
	        <div class="input_rr">
	            <input type="text" placeholder="商品選擇完成後自動顯示" class="js_need_pay_money" name="tmoney" readonly="readonly">
	        </div>
	    </div>
		
		<div class="new_pay_foot order_detail">
			<a href="javascript:;" class="sub_btn red js_form_sub" data-func_before="before_sub" data-text="{:__('Next')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Next')}</a>
			<a href="{:url('/index/daifu/cancel')}" class="sub_btn margin_t20 js_ajax_confirm" data-width="300" tips="您確定要取消代付？">取消代付</a>
		</div>
		
		<a href="{:url('/index/user/editIdcard')}?type=1" class="js_ajax_win hide js_one_auth"></a>
		<a href="{:url('/index/user/editIdcardTwo')}?type=1" class="js_ajax_win hide js_two_auth"></a>
		
    </form>

</div>

<script type="text/javascript">
	function before_sub(){	
        return true;
    }
    $(function(){
		
		<?php
			if($user['is_card'] == '0'){
		?>
				$('.js_one_auth').click();
		<?php
			}
			//if($user['is_card'] == '1'){
				//if($df_type > '2' && $df_type != '7'){
				//}else{
					//if($user['is_card_img'] == '0'){
			?>
				//$('.js_two_auth').click();
			<?php
					//}
				//}
			//}
		?>
		
		
		$('.js_num_type').click(function(){
			$(this).addClass('active').siblings('.js_num_type').removeClass('active');
			$('.js_mz').val($(this).attr('data-id'));
			if($(this).attr('data-id')==''){
				$('.js_mz').val($(this).find('input').val());
			}else{
				
			}
			func_all()
		})
		$('.js_num_input_d').blur(function(){
			if($('.js_num_type[data-id=""]').hasClass('active')){
				$('.js_mz').val($(this).val());
			}
			func_all();
			$(this).attr('placeholder','自定義')
		})
		$('.js_num_input_d').focus(function(){
			$(this).attr('placeholder','')
		})
		$('.js_item_money').blur(function(){
			func_all()
		})
		function func_all(){
			var money=parseFloat($('.js_item_money').val())*parseFloat($('.js_mz').val()) || 0;
			var _url="{:url('/index/user/rate_fun')}";
			$.post(_url,{money:money,type:1,category:3},function(data){
				$('.js_need_pay_money').val(data.money+' TWD')
			})
		}
    })

</script>
