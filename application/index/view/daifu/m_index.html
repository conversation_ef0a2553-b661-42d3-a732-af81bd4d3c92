<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .input_box{ background: none }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
    .input_box .op_icon{ color: #999 }
    .input_box .op_icon img{ width: 0.36rem; height: 0.36rem; }
    .order_detail .item_div{ position: relative; overflow: hidden; margin-bottom: 0.2rem }
    .order_detail .item_div .dele{ display: block; width: 0.3rem; height: 0.3rem; position: absolute; right: 0; top: 0 }
	.order_detail .item_div .dele img{ width: 100%; float: left; }
	.order_detail .item_div .sub_btn.red{width:110px; background:none; min-width:0; line-height:0.58rem; font-size:0.24rem; color:#EF4B7B; border:1px solid #EF4B7B}
	.big_tt{height: 34px; color:#fff; background:#1F93B6; font-size: 15px; border-radius:5px; padding:0 10px}
	.wi_block{background:#fff; border-radius:5px; padding:10px}
	
	.hl_info .hlitem{width:33%; text-align:center; padding:12px 0}
	.hl_info .hlitem .t{font-size:14px; color:#666;margin-bottom:6px}
	.hl_info .hlitem .p{font-size:16px; color:#333}
	.hl_info .hlitem .hl{font-size:12px;}
	body .sub_btn.blue{background: #309FC0; margin-left:0px; color:#fff}
</style>
<div class="pad_t20 pad_lr20">

	{include file="daifu/tips" /}
	{include file="daifu/m_mianbao" /}
    <form action="{:url('daifu/confirm_index')}">
		<div class="wi_block margin_t20">
			<div class="input_box full_width">
				<div class="input_tt">{:__('Friend account')}</div>
				<div class="input_rr">
					<a href="javascript:;" class="pull-right pad_l10 op_icon pad_r30 js_copy" data-clipboard-text="要复制的文字"><img src="__CDN__/assets/img/wap/icon_pay_copy.png" class=""></a>
					<a href="javascript:;" class="pull-right pad_l10 op_icon pad_r20 js_switch_account"><img src="__CDN__/assets/img/wap/icon_pay_change.png" class=""></a>
					<div class="over_hide">
						<input type="text" class="color_red js_account_val" value="" readonly="readonly" name="account">
					</div>
				</div>
			</div>
			<!-- <div style="margin-top: 10px;font-size: 24px;color: #ff0000;">申請代付訂單賬戶和搜索訂單賬戶必須一致</div> -->
			<!--<div class="input_box full_width">
				<div class="input_tt">{:__('Payment RMB')} </div>
				<div class="input_rr">
					<div class="over_hide">
						<input type="text" class="js_daifu_money" placeholder="{:__('Please enter RMB amount')}" data-type="money" name="money" maxlength="10" value="" is_required="true" empty_tip="{:__('Please enter RMB amount')}">
					</div>
				</div>
			</div>
			-->
			<div class="input_box full_width">
				<div class="input_tt">代付鏈接 <span class="f12 color_999">({:__('Rate')}：<?=$exchange['exchange']?>)</span></div>
				<div class="input_rr">
					<a href="javascript:;" class="pull-right pad_l10 op_icon pad_r30 js_search_daifu_goods" data-url="{:url('/index/daifu/select_product')}"><img src="__CDN__/assets/img/wap/icon_pay_search.png" class=""></a>
					<div class="over_hide">
						<input type="text" class="js_daifu_url" placeholder="請輸入代付鏈接" data-type="url" name="url"
						value="" is_required="false" empty_tip="請輸入代付鏈接">
					</div>
				</div>
			</div>
		</div>
	    
		
		
		
		<input type="hidden" name="goods_ids" class="js_daifu_goods_ids" value="">
	    <div class="order_detail js_daifu_goods_list">
		    <!-- <div class="item_div">
		    	<a href="{:url('/index/user/appeal_dele')}" class="dele js_ajax_cancel_df" tips="{:__('Do you confirm the cancellation?')}" data-id="1"><img src="__CDN__/assets/img/wap/delete.png"></a>
	            <div class="item clearfix">
	                <div class="pull-left t">{:__('Purchaser')}：</div>
	                <div class="over_hide r_text"><span class="color_666">MANDAREN</span></div>
	            </div>
	            <div class="item clearfix">
	                <div class="pull-left t">名称一：</div>
	                <div class="over_hide r_text color_666">花花公子休闲裤男士夏季超薄款冰丝透气小脚裤子男韩版潮流速干裤</div>
	            </div>
	            <div class="item clearfix">
	                <div class="pull-left t">名称二：</div>
	                <div class="over_hide r_text color_666">花花公子休闲裤男士夏季超薄款冰丝</div>
	            </div>
	            <div class="item clearfix">
	                <div class="pull-left t">{:__('Url')}：</div>
	                <div class="over_hide r_text color_999">https://ai.taobao.com/search/index.htm?pimm_99396806_46570087_1688416330&ud=&source_id=search&key=%E8%A3%</div>
	            </div>
	            <div class="item clearfix">
	                <div class="pull-left t">{:__('Amount')}：</div>
	                <div class="over_hide r_text color_red">1000.00RMB</div>
	            </div>
	        </div>
	        <div class="item_div">
		    	<a href="{:url('/index/user/appeal_dele')}" class="dele js_ajax_cancel_df" tips="{:__('Do you confirm the cancellation?')}" data-id="1"><img src="__CDN__/assets/img/wap/delete.png"></a>
	            <div class="item clearfix">
	                <div class="pull-left t">{:__('Purchaser')}：</div>
	                <div class="over_hide r_text"><span class="color_666">MANDAREN</span></div>
	            </div>
	            <div class="item clearfix">
	                <div class="pull-left t">名称一：</div>
	                <div class="over_hide r_text color_666">花花公子休闲裤男士夏季超薄款冰丝透气小脚裤子男韩版潮流速干裤</div>
	            </div>
	            <div class="item clearfix">
	                <div class="pull-left t">名称二：</div>
	                <div class="over_hide r_text color_666">花花公子休闲裤男士夏季超薄款冰丝</div>
	            </div>
	            <div class="item clearfix">
	                <div class="pull-left t">{:__('Url')}：</div>
	                <div class="over_hide r_text color_999">https://ai.taobao.com/search/index.htm?pimm_99396806_46570087_1688416330&ud=&source_id=search&key=%E8%A3%</div>
	            </div>
	            <div class="item clearfix">
	                <div class="pull-left t">{:__('Amount')}：</div>
	                <div class="over_hide r_text color_red">1000.00RMB</div>
	            </div>
	        </div> -->
        </div>


        {include file="daifu/m_common_taobao" /}


<!-- 
        <div>
            <a href="{:url('/index/daifu/confirm_account')}" class="js_ajax_win">请确认代付申请人帐号弹窗</a>   
        </div>

        <div>
         <a href="{:url('/index/daifu/confirm_goods')}" class="js_ajax_win">请确认代付商品</a>   
        </div>
 -->
    </form>

</div>


<script src="__CDN__/assets/js/pc/copy.js?v={$Think.config.site.version}" ></script>
<script type="text/javascript">
    // 账户列表
    var account_list=<?=json_encode($sysalipay)?>;
    $(function(){
    // 复制
    if($('.js_copy').length>0){
        if(document.getElementsByClassName){
            var clipboard_btns = document.getElementsByClassName('js_copy');
            var clipboard = new Clipboard(clipboard_btns);
            clipboard.on('success', function(e) {
                tip_show("{:__('Copy success')}",'1');
            });
            clipboard.on('error', function(e) {
                console.log(e);
            });
        }else{
            // 兼容 ie7 8
            $(".js_copy").each(function() {
                $(this).on("click", function() {
                    var copy_span = $(this);
                    var input_hidden = '<input type="text" class="input_hidden" id="input_hidden" value=""/>';
                    copy_span.after($(input_hidden));
                    $("#input_hidden").val(copy_span.attr("data-clipboard-text"));
                    var obj = document.getElementById("input_hidden");
                    obj.select(); // 选择对象
                    document.execCommand("Copy"); // 执行浏览器复制命令
                    $("#input_hidden").remove();
                    tip_show("{:__('Copy success')}",'1');
                })
            })
        }
    }


		$(document).on('click','.js_ajax_cancel_df',function(){
			var _this=$(this);
			let index=_this.attr('data-index');
			layer.confirm(_this.attr('tips'), {
			  title:false,
			  area:['6rem', 'auto'],
			  skin: 'my_confirm',
			  btn: ["{:__('Confirm')}","{:__('Cancel')}"], //按钮
			  btnAlign:'c'
			}, function(){
				res_goods.splice(index,1)
				render_goods()
				if($('.js_daifu_goods_list .item_div').length<=0){
					$('.js_daifu_goods_wap_tt').addClass('hide')
				}
				layer.closeAll()
			});
			return false; 
		})

    })

</script>
