<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 2.6rem; }
    .footer_new{ display: none }
    .input_box{ background: none }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
	.new_pay_foot{background: #fff; width: 100%; position: fixed; left: 0; bottom: 0; z-index: 30; padding: 0.2rem;}
	.new_pay_foot .sub_btn{width: 100%;}
	
	.input_box .pay_type_div{background: none;}
	.num_pay_type a{display: flex; align-items: center; justify-content: center; width: 2.12rem; height:  0.92rem; border-radius: 0.1rem; background: #fff; margin-right: 0.2rem; margin-bottom: 0.2rem; color: #999999; border: 1px solid #fff;}
	.num_pay_type a.active{border-color: #F04C7C; color: #F04C7C;}
	.num_pay_type a input{width: 100%; text-align: center;}
	.num_pay_type a:nth-child(3n){margin-right: 0;}
</style>
<div class="pad_t20 pad_lr20">

	{include file="daifu/tips" /}
	{include file="daifu/m_mianbao" /}
    <form action="{:url('daifu/new_confirm_index')}">
		<input type="hidden" name="type" value="" >
		<input type="hidden" name="key_id" value="<?=$set_type?>" >
		<?php
			$set_id = json_decode($set_one['set_id'],true);
			if(!empty($set_id)){
				if(in_array('1',$set_id)){
			?>
				<div class="input_box">
					<div class="input_tt">链接:</div>
					<div class="input_rr">
						<input type="text" name="url" value="" is_required="true" placeholder="請輸入链接">
					</div>
				</div>
			<?php
				}
				if(in_array('2',$set_id)){
			?>
				<div class="input_box">
					<div class="input_tt">暱稱:</div>
					<div class="input_rr">
						<input type="text" name="id" value="" is_required="true" placeholder="請輸入暱稱">
					</div>
				</div>
			<?php
				}
				if(in_array('3',$set_id)){
			?>
				<div class="input_box">
					<div class="input_tt">帳號:</div>
					<div class="input_rr">
						<input type="text" name="account" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5]/ig,'')" name="name" autocomplete="off" onpaste="return false" ondragenter="return false" oncontextmenu="return false;" is_required="true" placeholder="請輸入帳號">
					</div>
				</div>
			<?php
				}
				if(in_array('4',$set_id)){
			?>
				<div class="input_box">
					<div class="input_tt">密碼:</div>
					<div class="input_rr">
						<input type="text" name="password" value="" onkeyup="value=value.replace(/[\u4e00-\u9fa5]/ig,'')" name="name" autocomplete="off" onpaste="return false" ondragenter="return false" oncontextmenu="return false;" is_required="true" placeholder="請輸入密碼">
					</div>
				</div>
			<?php
				}
				if(in_array('5',$set_id)){
			?>
				<div class="input_box">
					<div class="input_tt">面额:</div>
					<div class="input_rr">
						<input type="number" name="money" value="" class="js_item_money" is_required="true"
							placeholder="請輸入面额">
					</div>
				</div>
			<?php
				}
				if(in_array('6',$set_id)){
			?>
				<div class="input_box">
					<div class="input_tt">遊戲區:</div>
					<div class="input_rr">
						<input type="text" name="game" value="" class="" is_required="true"
							placeholder="請輸入遊戲區">
					</div>
				</div>
			<?php
				}
			}
			$set_array = json_decode($set_two['set_id'],true);
			if(!empty($set_array)){
		?>
		<input type="hidden" name="money" value="" class="js_select_price">
		<div class="input_box  ">
			<div class="input_tt">類型:</div>
				<div class="input_rr pay_type_div">
					<input type="text" class="js_mz_type" name="num_type"
						style="width: 0; height: 0; opacity: 0; position: absolute;" value="" is_required="true"
						placeholder="請選擇類型">
					<div class="pay_type flex flex_w num_pay_type">
						<?php
							$child = [];
							$price = [];
							foreach($set_array as $key=>$val){
								$child[$key] = '';
								$price[$key] = '';
								foreach($val['child'] as $k=>$ls){
									$child[$key] .= $ls['value'].',';
									$price[$key] .= $ls['money'].',';
								}
								$child[$key] = rtrim($child[$key],',');
								$price[$key] = rtrim($price[$key],',');
						?>
                            <!-- data-discount    -->
							<a class="daifu_btn margin_r10 js_num_type_sele" href="javascript:;" data-id="<?=$val['title']?>"
								data-unit="" data-price="<?=$price[$key]?>" data-discount="<?=$val['discount']?>" data-arr='<?=$child[$key]?>'>
								<?=$val['title']?>
							</a>
						<?php
							}
						?>	
					</div>
				</div>
		</div>
		<div class="input_box  ">
	        <div class="input_tt">面值:</div>
	        <div class="input_rr pay_type_div">
	            <input type="text" class="js_mz" name="num" style="width: 0; height: 0; opacity: 0; position: absolute;" value="" is_required="true" placeholder="請選擇面值">
	    		<div class="pay_type flex flex_w num_pay_type js_mz_options">
	    		</div>
	        </div>
	    </div>
		<?php
			}
		?>
	    <div class="input_box clearfix">
	        <div class="input_tt">商品總價:</div>
	        <div class="input_rr">
	            <input type="text" placeholder="商品選擇完成後自動顯示" class="js_need_pay_money" name="tmoney" readonly="readonly">
	        </div>
	    </div>
		
		<div class="new_pay_foot order_detail">
			<a href="javascript:;" class="sub_btn red js_form_sub" data-func_before="before_sub" data-text="{:__('Next')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Next')}</a>
			<!--<a href="{:url('/index/daifu/cancel')}" class="sub_btn margin_t20 js_ajax_confirm" data-width="300" tips="您確定要取消代付？">取消代付</a>-->
		</div>
		
		<a href="{:url('/index/user/editIdcard')}?type=1" class="js_ajax_win hide js_one_auth"></a>
		<a href="{:url('/index/user/editIdcardTwo')}?type=1" class="js_ajax_win hide js_two_auth"></a>
		
    </form>

</div>

<script type="text/javascript">
	function before_sub(){	
        return true;
    }
    $(function(){
		
		<?php
			if($user['is_card'] == '0'){
		?>
				$('.js_one_auth').click();
		<?php
			}
			//if($user['is_card'] == '1'){
				//if($df_type > '2' && $df_type != '7'){
				//}else{
					//if($user['is_card_img'] == '0'){
			?>
				//$('.js_two_auth').click();
			<?php
					//}
				//}
			//}
		?>
		
		
		$(document).on('click','.js_num_type',function(){
			$(this).addClass('active').siblings('.js_num_type').removeClass('active');
			$('.js_mz').val($(this).attr('data-id'));
			if($(this).attr('data-id')==''){
				$('.js_mz').val($(this).find('input').val());
			}
			func_all()
		})
		$(document).on('blur','.js_num_input_d',function(){
			if($('.js_num_type[data-id=""]').hasClass('active')){
				$('.js_mz').val($(this).val());
			}
			func_all();
			$(this).attr('placeholder','自定義')
		})
		$(document).on('focus','.js_num_input_d',function(){
			$(this).attr('placeholder','')
		})
		$(document).on('keyup', '.js_num_input_d', function() {
			$(this).val($(this).val().replace(/[^\d]/g,''))
		})
		
		$('.js_item_money').blur(function(){
			func_all()
		})
		function func_all(){
			
			var js_item_money=$('.js_item_money'); //面额输入框
			var type=1;
			var money=0;
			if(js_item_money.length>0){
				type=3;
				money=parseFloat($('.js_item_money').val()) || 0
			}else{
				// 如果自定义 选中
				if ($('.js_num_type[data-id=""]').hasClass('active')) {
					type=2;
					money=parseFloat($('.js_mz').val()) || 0
				}else{
					type=1;
					money=parseFloat($('.js_num_type.active').attr('data-price')) || 0
				}
			}
			$('.js_select_price').val(money);
			$('input[name="type"]').val(type);
			
			// 1.选择类型的额，type=1 price  key_id
			// 2.输入自定义的，type=2 price  key_id
			// 3.输入面额的type=3 price  key_id
			var _url = "{:url('/index/daifu/new_price')}";
			$.post(_url, {
				price: money,
				key_id: <?=$set_type?>,  // 每种充值方式 固定
				type: type,
                num_type:$('.js_mz_type').val()
			}, function(data) {
				$('.js_need_pay_money').val(data.money+' TWD')
			})
		}
		
		$('.js_num_type_sele').click(function(){
			$(this).addClass('active').siblings('.js_num_type_sele').removeClass('active');
			$('.js_mz_type').val($(this).attr('data-id'));
			var html='';
			var arr=$(this).attr('data-arr').split(',');
			var price = $(this).attr('data-price').split(',');
			var unit=$(this).attr('data-unit')
			for(var i=0;i<arr.length;i++){
				html+='<a style="height: 1.5rem" class="daifu_btn margin_r10 js_num_type" href="javascript:;" data-id="'+arr[i]+'" data-price="' + price[i] + '">' + arr[i] + '<br />￥' +price[i] + '</a>'
			}
            
            var is_discount=$(this).attr('data-discount');
            if(is_discount){
                html+='<a class="daifu_btn margin_r10 js_num_type" href="javascript:;" data-id=""><input type="number" class="js_num_input_d" step="1" min="0" placeholder="自定義金額"></a>'
			}
            $('.js_mz_options').html(html);
			$('.js_mz').val('')
			func_all()
		})
		$('.js_num_type_sele').eq(0).click()
		
		
    })

</script>
