<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .input_box{ background: none }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
    .input_tt .moni_check{ width: 0.28rem; height: 0.28rem; margin-top: 0.22rem; }
    .moni_check.new_bg{background: url(__CDN__/assets/img/pc/radio.png); background-size: 100% 100%}
    .moni_check.new_bg.active{ background: url(__CDN__/assets/img/pc/radio_on.png);  background-size: 100% 100%}

    .moni_select{ background:none; min-width: 10px; border: none }

    .fp_div .input_box .input_rr input{ border: 1px solid #eee }
</style>
<div class="pad_t20 pad_lr20">
    <div class="help_title bg_fff clearfix">
        <?=$special['name']?>
    </div>
        <form action="{:url('daifu/new_recharge')}">
		<?php
			foreach($new_field as $key=>$ls){
				if($key == 'imgs'){
					foreach($new_field[$key] as $val){
		?>
				<input type="hidden" name="imgs[]" value="<?=$val?>">
		<?php
					}
				}else{
		?>
			<input type="hidden" name="<?=$key?>" value="<?=$ls?>">
		<?php
				}
			}
		?>
        <div class="input_box full_width margin_t20">
            <div class="input_rr">
                <!--  js_need_pay_money   data-rmb  折合多少人民币    data-canuse_gwj   可使用的 购物金数量-->
                <span class="pull-right pad_lr20"><span class="color_red js_need_pay_money" data-rmb="<?=$new_field['num']?>" data-canuse_gwj="1"><?=$new_field['tmoney']?></span>TWD</span>
                <div class="over_hide">
                    <div class="input pad_l20">{:__('Need TWD')}</div>
                </div>
            </div>
        </div>

        <div class="input_box full_width">
            <div class="input_tt">
                <label class="moni_check_label clearfix">
                    <a href="javascript:;" class="moni_check new_bg js_use_taobi pull-left">

                    </a>
                    <span class="la_btn over_hide" style="display: block;">
						使用F幣<span class="f12 color_999">({:__('Available Quantity')}：<span class="js_gwj_num"><?=$user['money']?></span>)</span>
                    </span>
                </label>
            </div>


             <div class="input_rr">
				<!-- <a href="javascript:;" class="pull-right color_red pad_l10 op_icon pad_r30 js_use_all">{:__('All use')}</a> -->
				<div class="over_hide">
					<!-- data-max  可使用  -->
					<input type="text" class="js_taobi_val js_daifubaobi_val" readonly="readonly" is_required="false" empty_tip="請輸入使用F幣的數量" placeholder="請輸入使用F幣的數量" data-type="money" name="tabobi" data-max="<?=$user['money']?>" >
				</div>
			</div>
        </div>

        <div class="comfirm_index_d margin_t40 js_paytype_divs" style="padding-bottom: 2px;">
            <div class="c_tt border_b">
                {:__('Payment method')}<!--<span class="color_red">192.6388</span>TWD-->

                <input type="text" name="pay_type" value="1" class="js_pay_type_val"  style="position: absolute; width: 0; height: 0; opacity: 0">
            </div>
            <div class="c_tt have_icon border_b clearfix">
                <input type="text" name="bank_id" value="" is_required="true" class="js_bank_id" empty_tip="{:__('Please select the bank account number')}" style="position: absolute; width: 0; height: 0; opacity: 0">
                <a href="{:url('/index/daifu/select_bank',array('type'=>'1'))}" class="pull-right color_999 js_to_select f12"><span>{:__('Select bank card')}</span> <img class="arr" src="__CDN__/assets/img/wap/arr.png"></a>


                <img src="__CDN__/assets/img/wap/daifu_icon1.png" class="icon">
                <div class="over_hide">
                    <span class="color_666">{:__('Bank pay')}</span>
                </div>
            </div>

            <?php
                if(($df_type < '3' || $user['is_super_quotien'] || $df_type == '7') && $user['is_super_quotien_daifu']){
            ?>
               <!-- <label class="c_tt have_icon border_b have_check moni_check_label clearfix">
                    <a href="javascript:;" class="moni_check new_bg js_cs_pay pull-right margin_l20" data-id="2">
                    </a>
                    <img src="__CDN__/assets/img/wap/daifu_icon2.png" class="icon">
                    <div class="over_hide la_btn items color_999">
                        <div><span class="color_666">{:__('Super payment')}</span> <span class="f12 color_999">(超商繳費程式碼將傳送至您的行動電話）</span></div>
                        <div class="f12">超商將向您收取30TWD的手續費</div>
                        <div class="f12">最低金額100TWD，最高報礦手續費不超過6000TWD</div>
                    </div>
                </label>-->
            <?php
                }else{
            ?>
                <div class="c_tt have_icon border_b clearfix">
                    <a href="{:url('/index/daifu/apply_shop')}" class="apply_btn sub_btn pull-right">{:__('Apply')}</a>
                    <img src="__CDN__/assets/img/wap/daifu_icon2.png" class="icon">
                    <div class="over_hide">
                        <span class="color_666">{:__('Super payment')}</span>
                    </div>
                </div>
            <?php
                }
            ?>



            <!-- 信用卡 -->
            <!--<label class="c_tt have_icon have_check moni_check_label clearfix" style="">
                <a href="javascript:;" class="moni_check new_bg js_cs_pay pull-right margin_l20"  data-id="3">
                </a>
                <img src="__CDN__/assets/img/wap/daifu_icon4.png" class="icon">
                <div class="over_hide la_btn items color_999">
                    <div><span class="color_666">信用卡</span> <span class="f12 color_999">(信用卡说明）</span></div>
                </div>
            </label>-->
            <!-- 信用卡 -->
        </div>


        <div class="comfirm_index_d margin_t40 hide_fap">
            <div class="c_tt have_icon border_b clearfix">
                <input type="text" name="fp_type" value="3" is_required="true" class="js_fp_type_val" empty_tip="{:__('Please select the type of invoice')}" style="position: absolute; width: 0; height: 0; opacity: 0">
                <div class="moni_select pull-right" style="width:3rem">
                    <div class="moni_selected clearfix">
                        <img class="arr" src="__CDN__/assets/img/arr.png">
                        <span class="text-right">{:__('All types')}</span>
                    </div>
                    <div class="moni_s_down">
                        <a class="ellipsis margin_r10 active js_fp_type" href="javascript:;" data-id="3">{:__('Donate to charity')}</a>
                         <a class="ellipsis margin_r10  js_fp_type" href="javascript:;" data-id="1">{:__('Electronic invoice')}</a>
                        <a class="ellipsis margin_r10 js_fp_type" href="javascript:;" data-id="2">{:__('Electronic invoice1')}</a>
                        
                    </div>
                </div>
                <img src="__CDN__/assets/img/wap/daifu_icon3.png" class="icon">
                <div class="over_hide">
                    <span class="color_666">{:__('Invoice type')}</span>
                </div>
            </div>

            <div class="fp_div pad_t10 pad_r20">
                <div class="js_fp_div hide" data-id="1_2">
                    <div class="input_box" data-id="1">
                        <div class="input_tt">{:__('Unified numbering')}：</div>
                        <div class="input_rr">
                            <input type="text" name="number" is_required="false" empty_tip="{:__('Please enter a unified number of 8 digits')}" placeholder="{:__('Please enter a unified number of 8 digits')}" value="88888888" maxlength="8">
                        </div>
                    </div>
                    <div class="input_box" data-id="1">
                        <div class="input_tt">{:__('Invoice rise')}：</div>
                        <div class="input_rr">
                            <input type="text" name="fp_title" is_required="false" empty_tip="{:__('Please enter the invoice payable')}" placeholder="{:__('Invoice rise')}" value="" maxlength="20">
                        </div>
                    </div>
                    <div class="input_box" >
                        <div class="input_tt">{:__('Mail box')}：</div>
                        <div class="input_rr">
                            <input type="text" name="fp_email" is_required="false" empty_tip="{:__('Please enter your e-mail')}" data-type="email" maxlength="50" placeholder="{:__('Please enter your e-mail')}" value="">
                        </div>
                    </div>
                </div>
                <div class="js_fp_div  pad_t10" data-id="3">
                    {:__('Donation unit')}：{:__('OMG Caring for Society Fund')}  <span class="color_red">{:__('Donation is not available')}</span>
                </div>
                <div class="fp_tips js_fp_tips" data-id="3">
                    {:__('E-invoice will be issued three days after')}<br />
                    {:__('E-invoice will be issued three days after1')}<br />
                    {:__('E-invoice will be issued three days after2')}
                </div>
                <div class="fp_tips js_fp_tips hide" data-id="2">
                    {:__('E-invoice will be issued three days after3')}<br />
                    {:__('E-invoice will be issued three days after1')}<br />
                    {:__('E-invoice will be issued three days after2')}
                </div>
                <div class="fp_tips js_fp_tips hide" data-id="1">
                    {:__('E-invoice will be issued three days after3')}<br />
                    {:__('E-invoice will be issued three days after1')}<br />
                    {:__('E-invoice will be issued three days after2')}
                </div>
            </div>

        </div>

        <div class="clearfix pad_t20 login_tips">
            <label class="moni_check_label">
                <a href="javascript:;" class="moni_check js_agree2 active">
                </a>
                <span class="la_btn">{:__('I have read and agreed')}</span> <a class="color_red js_article_win" href="/haigou/public/index/login/agreement.html">《{:__('Consent to Settlement Authorization')}》</a>
            </label>
        </div>

        <div class="pad_tb30 margin_b20 order_detail">
            <a href="javascript:;" class="sub_btn margin_b30 red js_form_sub" data-text="{:__('Confirm')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Confirm')}</a>
            <a href="{:url('/index/daifu/cancel')}" class="sub_btn js_ajax_dele" tips="{:__('Do you confirm the cancellation?')}" data-id="1">{:__('Cancellation of payment')}</a>
        </div>
    </form>
</div>
<!-- 这个有用，不要删 -->
<div class="hide">
 <a href="{:url('/index/daifu/checkMobile')}" class="js_ajax_win js_check_mobile_link" >手机验证</a>   
</div>
<script type="text/javascript">

    var is_check_mobile=false;

    // 是否需要 验证手机号  1 需要 ， 其他 不需要
    var is_need_check='1';



    // 插入淘币/ 购物金
    // 在手机验证 以后， 在选择 是否使用 淘币/购物金 以后，， 会调用一下
    function input_coin(){
        // if(!$('.js_use_taobi').hasClass('active')){
        //     return false;
        // }
        // 如果是 淘币
        if($('.js_is_shopping_gold').length<=0){
            // 如果需要 插入淘币 数量
            // 插入 淘币
            if(is_need_check==1 && !is_check_mobile){
                return false;
            }
            var _can_use=parseFloat($('.js_gwj_num').html());
            var _need_money=parseFloat($('.js_need_pay_money').attr('data-rmb'));
            var _v=0;
            if(_can_use>=_need_money){
                _v=_need_money;
            }else{
                _v=_can_use;
            }
            // 隐藏/显示 支付方式
            // 只有在 能使用的 淘币不够，或者 没有使用淘币支付时  才显示
            if(!$('.js_use_taobi').hasClass('active') || _can_use<_need_money){
                $('.js_paytype_divs').removeClass('hide').find('input').attr('is_required',true)
            }else{
                $('.js_paytype_divs').addClass('hide').find('input').attr('is_required',false)
            }
            $('.js_daifubaobi_val').val(_v);
        }else{
             // 插入购物金
            var _can_use=parseFloat($('.js_need_pay_money').attr('data-canuse_gwj'));

            $('.js_gwj_num').html(_can_use);
            $('.js_daifubaobi_val').attr('data-max',_can_use);
            $('.js_daifubaobi_val').val(_can_use);
        }
    }


    $(function(){
        // 刚进来 把  可使用的 购物金 加上去
        if($('.js_is_shopping_gold').length>0){
            var _can_use=parseFloat($('.js_need_pay_money').attr('data-canuse_gwj'));
            $('.js_gwj_num').html(_can_use);
        }


        $('.js_use_all').click(function(){
            if(!is_check_mobile && is_need_check==1){
                $('.js_check_mobile_link').click();
                return false;
            }

            var _input=$(this).parents('.input_rr').find('input');
            _input.val(_input.attr('data-max'))
        })

        // 选择发票
        $('.js_fp_type').click(function(){
            var _this=$(this);
            var _id=_this.attr('data-id');
            _this.addClass('active').siblings('a').removeClass('active');
            if(_id==1){
                $('.js_fp_div[data-id="1_2"]').removeClass('hide').find('input').attr('is_required',true);
                $('.js_fp_div .input_box[data-id="1"]').removeClass('hide');
                $('.js_fp_div[data-id="3"]').addClass('hide')
            }
            if(_id==2){
                $('.js_fp_div[data-id="1_2"]').removeClass('hide').find('input').attr('is_required',true);
                $('.js_fp_div .input_box[data-id="1"]').addClass('hide').find('input').attr('is_required',false);
                $('.js_fp_div[data-id="3"]').addClass('hide')
            }
            if(_id==3){
                $('.js_fp_div[data-id="1_2"]').addClass('hide').find('input').attr('is_required',false);
                $('.js_fp_div[data-id="3"]').removeClass('hide')
            }
            $('.js_fp_tips[data-id="'+_id+'"]').removeClass('hide').siblings('.js_fp_tips').addClass('hide')
            $('.js_fp_type_val').val(_id)
        })

        $('.js_use_taobi').click(function(){
            var _this=$(this)
            setTimeout(function(){
                $('.js_taobi_val').attr('is_required',_this.hasClass('active'));
                if(_this.hasClass('active')){
                    if(!is_check_mobile && is_need_check==1){
                        $('.js_check_mobile_link').click();
                    }else{
                        input_coin()
                    }
                }
            },30)
        })

        $('.js_cs_pay').click(function(){
            var _this=$(this)
            setTimeout(function(){
                $('.js_bank_id').attr('is_required',!_this.hasClass('active'));
                $('.js_to_select').find('span').html("{:__('Select bank card')}");
                if(_this.hasClass('active')){
                    _this.parents('.moni_check_label').siblings('.moni_check_label').find('.js_cs_pay').removeClass('active')
                }
                $('.js_pay_type_val').val((_this.hasClass('active')?_this.attr('data-id'):"1"));
            },30)
        })



        // 公用使用淘币
        
        $('.js_daifubaobi_val').click(function(){
            if(!is_check_mobile && is_need_check==1){
                $('.js_check_mobile_link').click();
            }
        })
        // 如果有 fB信息
        if(localStorage.fobj){
            var _data2=JSON.parse(localStorage.fobj);
			console.log(_data2);
            // 清楚本地缓存
            localStorage.fobj='0';
            // 赋值
            if(_data2.is_use){
                $('.js_daifubaobi_val').val(_data2.money)
				$('.js_use_taobi').addClass('active')
            }else{
                $('.js_use_taobi').removeClass('active')
            }
        }
        // 定时器取本地选择的银行卡信息
        // 保存金额，方便 到银行卡页面 获取，判断能否多选银行
        localStorage.select_bank_money=$('.js_need_pay_money').html(); 
        localStorage.is_usdt_bank=<?=$new_field['key_id']?>; 
        var _bank_time=null;

        function check_lo(){
            _bank_time=setInterval(function(){
                // 如果有银行信息
                if(localStorage.select_bank_data){
                    var _data=JSON.parse(localStorage.select_bank_data);
                    // 清楚本地缓存
                    localStorage.select_bank_data='';
                    // 清除定时器
                    clearInterval(_bank_time);
                    _bank_time=null;
                    // 赋值
                    //console.log('银行卡 id:'+_data.id)
                    $('.js_bank_id').val(_data.id);
                    $('.js_to_select').find('span').html(_data.title)
                }

                
            },100)
        }
        check_lo()
        $('.js_to_select').click(function(){
			let fobj={
                is_use:$('.js_use_taobi').hasClass('active')?1:0,
                money:$('.js_daifubaobi_val').val()
            }
            localStorage.fobj=JSON.stringify(fobj);
            if(_bank_time==null){
                check_lo()
            }
        })
        
    })
</script>
