<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 2.6rem; }
    .footer_new{ display: none }
    .input_box{ background: none }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
	.new_pay_foot{background: #fff; width: 100%; position: fixed; left: 0; bottom: 0; z-index: 30; padding: 0.2rem;}
	.new_pay_foot .sub_btn{width: 100%;}
</style>
<div class="pad_t20 pad_lr20">

	{include file="daifu/tips" /}
	{include file="daifu/m_mianbao" /}
    <form action="{:url('daifu/confirm_index')}">
	    <div class="input_box full_width">
	        <div class="input_tt">链接</div>
	        <div class="input_rr">
	        	<input type="text" name="url" value="" is_required="true" placeholder="請輸入链接">
	        </div>
	    </div>
		<div class="input_box full_width">
		    <div class="input_tt">帳號</div>
		    <div class="input_rr">
		    	<input type="text" name="account" value="" is_required="true" placeholder="請輸入帳號">
		    </div>
		</div>
		<div class="input_box full_width">
		    <div class="input_tt">密碼</div>
		    <div class="input_rr">
		    	<input type="password" name="password" value="" is_required="true" placeholder="請輸入密碼">
		    </div>
		</div>
		<div class="input_box full_width">
		    <div class="input_tt">金额</div>
		    <div class="input_rr">
		    	<input type="number" class="js_mz" name="money" value="" is_required="true" placeholder="請輸入金额">
		    </div>
		</div>
		
		<!-- 不要删 -->
		<!-- <div class="input_box full_width" style="display: none;">
		    <div class="input_tt">商品總價</div>
		    <div class="input_rr pull-left">
		        <input type="text" placeholder="" style="width: 100px" class="js_need_pay_money" name="tmoney" readonly="readonly">
		    </div>
		</div> -->
		
		<div class="new_pay_foot order_detail">
			<a href="javascript:;" class="sub_btn red js_form_sub" data-func_before="before_sub" data-text="{:__('Next')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Next')}</a>
			<a href="{:url('/index/daifu/cancel')}" class="sub_btn margin_t20 js_ajax_confirm" data-width="300" tips="您確定要取消代付？">取消代付</a>
		</div>
		
		<a href="{:url('/index/user/editIdcard')}?type=1" class="js_ajax_win hide js_one_auth"></a>
		<a href="{:url('/index/user/editIdcardTwo')}?type=1" class="js_ajax_win hide js_two_auth"></a>
		
    </form>

</div>

<script type="text/javascript">
	function before_sub(){
        return true;
    }
    $(function(){
		
		<?php
			if($user['is_card'] == '0'){
		?>
				$('.js_one_auth').click();
		<?php
			}
			//if($user['is_card'] == '1'){
				//if($df_type > '2' && $df_type != '7'){
				//}else{
					//if($user['is_card_img'] == '0'){
			?>
				//$('.js_two_auth').click();
			<?php
					//}
				//}
			//}
		?>
		
		
		// function func_all(){
		// 	var money=parseFloat($('.js_mz').val()) || 0;
		// 	var _url="{:url('/index/user/rate_fun')}";
		// 	$.post(_url,{money:money,type:1,category:3},function(data){
		// 		$('.js_need_pay_money').val(data.money)
		// 	})
		// }
		
		// $('.js_mz').blur(function(){
		// 	func_all()
		// })
    })

</script>
