<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 pad_b40">
    <div class="color_red pad_t10 my_close_win_title">{:__('Select alipay account')}</div>
    <div class="pad_tb30 margin_lr20 pad_lr20 alipay_account_list js_confirm_account_list clearfix">
        <?php
			foreach($userali as $ls){
		?>
			<a href="javascript:;" class="box clearfix pad_lr40" data-id="<?=$ls['id']?>">
				<div class="text-left pull-left a"><?=$ls['ali_account']?></div>
				<div class="over_hide text-right n"><?=$ls['name']?></div>
			</a>
		<?php
			}
		?>
    </div>

    <div class="pad_lr30 pad_b30">
        <a href="{:url('/index/account/add_alipay')}" class="win_add_btn  js_ajax_win"><img src="__CDN__/assets/img/jia.png"></a>
    </div>
    <div class="confirm_account_list_btns text-center">
        <a href="javascript:;" class="sub_btn small js_confirm">{:__('Confirm')}</a>
    </div>
</div>
<script type="text/javascript">
    $('.js_confirm_account_list a').click(function(){
        $(this).addClass('active').siblings('a').removeClass('active')
    })
    $('.js_confirm').click(function(){
        if($('.js_confirm_account_list a.active').length<=0){
            tip_show("{:__('Please select the Alipay account')}");
            return false;
        }
        var _item=$('.js_confirm_account_list a.active');
        $('.js_account_dom .input').remove();
        $('.js_account_dom').append('<div class="input ellipsis pad_l20">'+_item.find('.a').html()+'  <span class="pad_l20 color_666">'+_item.find('.n').html()+'</span></div>');
        $('.js_account_dom input').val(_item.attr('data-id'));
        layer.closeAll();
    })
</script>