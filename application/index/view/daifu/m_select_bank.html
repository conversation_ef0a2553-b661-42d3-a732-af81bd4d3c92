<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .input_box{ background: none }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
    .input_box .input_rr .arr{ height: 0.2rem; margin-top: 0.3rem; margin-left:10px; margin-right: 0.2rem }
    .bank_l .box .item{ width: 35%; }
    .checks_icon{ width: 0.36rem; height: 0.36rem; position: relative; top: -1px; display: none }
    .bank_l .box.active .checks_icon{ display: inline-block; }
</style>
<div class="pad_t20 pad_lr20">
    <form action="<?=$form_url?>" class="f12">
        <div class="pad_t20">超过30000金额可多选银行</div>
        <div class="bank_l js_bank_l pad_t20 clearfix">
			<?php
				foreach($userbank as $ls){
			?>
				<a href="javascript:;" class="box b" data-id="<?=$ls['id']?>">
					<div class="clearfix">
						<div class="text-left item bn ellipsis"><?=$ls['name']?></div>
						<div class="text-center item"><?=$ls['account_name']?></div>
						<div class="text-right ellipsis pad_r20"><?=$ls['account_six']?> <img class="checks_icon margin_l10" src="__CDN__/assets/img/wap/w_check.png"></div>
					</div>
				</a>
			<?php
				}
			?>
            <a href="{:url('/index/account/addBank',array('type'=>'1'))}" class="box add">
                <div class="clearfix">
                    <img src="__CDN__/assets/img/wap/add_bank.png">
                    <div>{:__('Add bank cards')}</div>
                </div>
            </a>
        </div>

        <div class="pad_lr20 color_999 f12 pad_t30">
            *{:__('There are three new caps for payment bank')}
        </div>

        <div class="pad_tb30 margin_b20 order_detail">

            <a href="javascript:;" class="sub_btn margin_b30 red js_confirm">{:__('Confirm')}</a>
        </div>
    </form>
</div>

<script type="text/javascript">
    var _money=localStorage.select_bank_money?parseFloat(localStorage.select_bank_money):0;


    $('.js_bank_l .box.b').click(function(){
        if(_money>30000){
            if(!$(this).hasClass('active')){
                $(this).addClass('active');
            }else{
                $(this).removeClass('active');
            }
            
        }else{
            if(!$(this).hasClass('active')){
                $(this).addClass('active').siblings('.box.b').removeClass('active');
            }
        }
    })


    $('.js_confirm').click(function(){
        if($('.js_bank_l .box.active').length<=0){
            tip_show("{:__('Please select a bank account first')}");
            return false;
        }

        var _ids=[];
        for(var i=0;i<$('.js_bank_l .box.active').length;i++){
            _ids.push($('.js_bank_l .box.active').eq(i).attr('data-id'))
        }

        var _title=$('.js_bank_l .box.b.active').eq(0).find('.bn').html();
        var _obj={
            id:_ids,
            title:_ids.length==1?_title:_title+'等'+_ids.length+'家銀行'
        }
        var str = JSON.stringify(_obj); 
        localStorage.select_bank_data= str; 
        setTimeout(function(){
            if(localStorage.is_usdt_bank==1){
                window.location.href="{:url('/index/daifu/usdt')}"
            }else if(localStorage.is_usdt_bank==2){
                window.location.href="{:url('/index/daifu/usdtout')}"
            }else if(localStorage.is_usdt_bank==3){
                window.location.href="{:url('/index/daifu/confirm_indexs')}"
            }else{
				window.location.href='<?=$form_url?>'
			}
            
        },100)
    })
</script>