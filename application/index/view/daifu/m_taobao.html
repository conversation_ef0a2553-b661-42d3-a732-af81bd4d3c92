
<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .input_box{ background: none }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
    .input_box .op_icon{ color: #999 }
    .input_box .op_icon img{ width: 0.36rem; height: 0.36rem; }
    .order_detail .item_div{ position: relative; overflow: hidden; margin-bottom: 0.2rem }
    .order_detail .item_div .dele{ display: block; width: 0.3rem; height: 0.3rem; position: absolute; right: 0; top: 0 }
    .order_detail .item_div .dele img{ width: 100%; float: left; }


</style>
<div class="pad_t20 pad_lr20">
    {include file="daifu/tips" /}
    {include file="daifu/m_mianbao" /}

    <form action="{:url('daifu/confirm_index')}">
		<div class="input_box full_width">
            <div class="input_tt">{:__('RMB')}</div>
            <div class="input_rr">
                <div class="pull-right pad_l10 pad_r20">RMB</div>
                <div class="over_hide">
                    <input type="text" data-type="money" name="money" class="js_money_v" data-max="50000" data-id="1" data-category="17" placeholder="{:__('Please enter RMB amount')}">
                </div>
            </div>
        </div>
        <a href="javascript:;" class="js_rate_change hide" data-url="{:url('/index/user/rate_fun')}"><img src="__CDN__/assets/img/pc/rate_change.png" alt=""></a>

        <div class="input_box full_width">
            <div class="input_tt">{:__('Conversion TWD')} <span class="f12 color_999">({:__('Rate')}： <span class="js_dafu_rate_val"><?=$exchange['exchange']?></span>)</span></div>
            <div class="input_rr">
                <div class="pull-right pad_l10 pad_r20 color_red">TWD</div>
                <div class="over_hide">
                    <input type="text" data-type="money" class="js_money_v" data-id="2" data-category="17" placeholder="{:__('Please enter TWD amount')}">
                </div>
            </div>
        </div>
        <div class="input_box  " >
	        <div class="input_tt">上傳圖片:</div>
	        <div class="">
	            <a href="javascript:;" class="sub_btn red js_check_pro" >上傳QR碼查詢代付商品</a>
			</div>
	    </div>

        <div class="input_box full_width" style="display:none;">
            <div class="input_tt">{:__('Payment RMB')} </div>
            <div class="input_rr">
                <div class="over_hide">
                    <input type="text" class="js_daifu_money" placeholder="{:__('Please enter RMB amount')}" data-type="money" name="money" maxlength="10" is_required="false" empty_tip="{:__('Please enter RMB amount')}">
                </div>
            </div>
        </div>
		<div class="input_box full_width" style="display:none;">
            <div class="input_tt">代付鏈接 <span class="f12 color_999">({:__('Rate')}：<?=$exchange['exchange']?>)</span></div>
            <div class="input_rr">
                <a href="javascript:;" class="pull-right pad_l10 op_icon pad_r30 js_search_daifu_goods" data-url="{:url('/index/daifu/confirmAccount')}"><img src="__CDN__/assets/img/wap/icon_pay_search.png" class=""></a>
                <div class="over_hide">
                    <input type="text" class="js_daifu_url" placeholder="請輸入代付鏈接" data-type="url" name="url"  is_required="false" empty_tip="請輸入代付鏈接">
                </div>
            </div>
        </div>
        <div class="item_tt pad_tb20 ">
            <div class="js_daifu_goods_wap_tt hide">{:__('Commodity message')}</div>
        </div>
		<input type="hidden" name="goods_ids" class="js_daifu_goods_ids" value="">
        <div class="order_detail js_daifu_goods_list">
            <!-- <div class="item_div">
                <a href="{:url('/index/user/appeal_dele')}" class="dele js_ajax_cancel_df" tips="{:__('Do you confirm the cancellation?')}" data-id="1"><img src="__CDN__/assets/img/wap/delete.png"></a>
                <div class="item clearfix">
                    <div class="pull-left t">{:__('Purchaser')}：</div>
                    <div class="over_hide r_text"><span class="color_666">MANDAREN</span></div>
                </div>
                <div class="item clearfix">
                    <div class="pull-left t">名称一：</div>
                    <div class="over_hide r_text color_666">花花公子休闲裤男士夏季超薄款冰丝透气小脚裤子男韩版潮流速干裤</div>
                </div>
                <div class="item clearfix">
                    <div class="pull-left t">名称二：</div>
                    <div class="over_hide r_text color_666">花花公子休闲裤男士夏季超薄款冰丝</div>
                </div>
                <div class="item clearfix">
                    <div class="pull-left t">{:__('Url')}：</div>
                    <div class="over_hide r_text color_999">https://ai.taobao.com/search/index.htm?pimm_99396806_46570087_1688416330&ud=&source_id=search&key=%E8%A3%</div>
                </div>
                <div class="item clearfix">
                    <div class="pull-left t">{:__('Amount')}：</div>
                    <div class="over_hide r_text color_red">1000.00RMB</div>
                </div>
            </div>
            <div class="item_div">
                <a href="{:url('/index/user/appeal_dele')}" class="dele js_ajax_cancel_df" tips="{:__('Do you confirm the cancellation?')}" data-id="1"><img src="__CDN__/assets/img/wap/delete.png"></a>
                <div class="item clearfix">
                    <div class="pull-left t">{:__('Purchaser')}：</div>
                    <div class="over_hide r_text"><span class="color_666">MANDAREN</span></div>
                </div>
                <div class="item clearfix">
                    <div class="pull-left t">名称一：</div>
                    <div class="over_hide r_text color_666">花花公子休闲裤男士夏季超薄款冰丝透气小脚裤子男韩版潮流速干裤</div>
                </div>
                <div class="item clearfix">
                    <div class="pull-left t">名称二：</div>
                    <div class="over_hide r_text color_666">花花公子休闲裤男士夏季超薄款冰丝</div>
                </div>
                <div class="item clearfix">
                    <div class="pull-left t">{:__('Url')}：</div>
                    <div class="over_hide r_text color_999">https://ai.taobao.com/search/index.htm?pimm_99396806_46570087_1688416330&ud=&source_id=search&key=%E8%A3%</div>
                </div>
                <div class="item clearfix">
                    <div class="pull-left t">{:__('Amount')}：</div>
                    <div class="over_hide r_text color_red">1000.00RMB</div>
                </div>
            </div> -->
        </div>


        {include file="daifu/m_common" /}


<!-- 
        <div>
            <a href="{:url('/index/daifu/confirm_account')}" class="js_ajax_win">请确认代付申请人帐号弹窗</a>   
        </div>

        <div>
         <a href="{:url('/index/daifu/confirm_goods')}" class="js_ajax_win">请确认代付商品</a>   
        </div>
 -->
    </form>

</div>
<input type="file" class="js_up_img_check_pro" style="display:none" data-type="img" accept="image/*" multiple data-maxMum="1" data-fileSizeMax="2" onchange="filecountup_check($(this),this.files)" data-filename="img" />


<script src="__CDN__/assets/js/pc/copy.js?v={$Think.config.site.version}" ></script>
<script type="text/javascript">
    // 账户列表
    var account_list=<?=json_encode($sysalipay)?>;
    $(function(){
		$('.js_check_pro').click(function(){
			if($(this).hasClass('disabled')){
				return false;
			}
			$('.js_up_img_check_pro').val('')
			$('.js_up_img_check_pro').click()
		})	


		$(document).on('click','.js_ajax_cancel_df',function(){
			var _this=$(this);
			layer.confirm(_this.attr('tips'), {
			  title:false,
			  area:['6rem', 'auto'],
			  skin: 'my_confirm',
			  btn: ["{:__('Confirm')}","{:__('Cancel')}"], //按钮
			  btnAlign:'c'
			}, function(){
				_this.parents('.item_div').remove();
				if($('.js_daifu_goods_list .item_div').length<=0){
					$('.js_daifu_goods_wap_tt').addClass('hide')
				}
				layer.closeAll()
			});
			return false; 
		})

    })
	
	
	var _douyin_time=null;
    var _douyin_end_time=10;
	function dy_getImgData(img,dir,max_w,next){
	 var image=new Image();
	 image.onload=function(){
	  var degree=0,drawWidth,drawHeight,width,height;
	  drawWidth=max_w>this.naturalWidth?this.naturalWidth:max_w;
	  drawHeight=this.naturalHeight*drawWidth/this.naturalWidth;
	  
	  var canvas=document.createElement('canvas');
	  canvas.width=width=drawWidth;
	  canvas.height=height=drawHeight; 
	  var context=canvas.getContext('2d');
	  context.drawImage(this,0,0,drawWidth,drawHeight);
	  //返回校正图片
	  next(canvas.toDataURL("image/jpeg",.7));
	 }
	 image.src=img;
	}
	//图片上传预览 2018.7.11
	function filecountup_check(_this,files,count){
		// 文件大小限制
		// 
		console.log(files)
		if(_this.attr('data-fileSizeMax')!=''){
			var max=parseFloat(_this.attr('data-fileSizeMax'))*1024*1024;
			if(files[0].size > max){
				tip_show("文件不能大于"+parseFloat(_this.attr('data-fileSizeMax'))+"M","2");
				return false;
			} 
		}


		var file = files[0];
		var reader = new FileReader();
		reader.onloadend = function () {
			console.log(reader.result);
			// $('.js_up_load_btn2').addClass('hide').find('input').val(reader.result);
			// $('.js_img_src2').removeClass('hide').css('background-image','url('+reader.result+')')
			//dy_getImgData(reader.result,'',1000,function(data){
				 check_douyin_pro(reader.result)
			//})
		}
		if (file) {
			reader.readAsDataURL(file);
		}
	};
	
	
	function check_douyin_pro(img_code){
		
		var _this=$('.js_check_pro');
		
		var _text=_this.html();
		_douyin_end_time=10;

		_douyin_time=setInterval(function(){
			_douyin_end_time--;
			if(_douyin_end_time<=0){
				_this.html(_text).removeClass('disabled');
				clearInterval(_douyin_time);
				_douyin_time=null
			}else{
				do_search()
			}
		},5000)
		
		do_search()
		function do_search(){
			let _url = "{:url('/index/daifu/confirmAccount_douyin')}";
			_this.html("{:__('Searching···')}"+_douyin_end_time+'次').addClass('disabled');
			$.post(_url,{code_img:img_code},function(data){
				if(data.code==1){
					// money请求返回账户列表,用弹窗弹出来
					// 这里避免下次请求覆盖本次
					if(_douyin_time!=null){
						layer.open({
						  type: 1,
						  title:false,
						  area:['760px', '550px'],
						  offset:"20%",
						  // anim:2,
						  skin: 'win_class_no_hide', //样式类名
						  closeBtn: 0, //不显示关闭按钮
						  shadeClose: false, //开启遮罩关闭
						  content:data.data,
						  move:'.layui-layer-content .my_close_win_title'
						});
						_this.html(_text).removeClass('disabled');
						$('.js_df_tip').html('');
						clearInterval(_douyin_time);
						_douyin_time=null
					}
				}else{
					$('.js_df_tip').html(data.msg)
				
					//tip_show(data.msg,"2");
				   //_this.html(_text).removeClass('disabled')
					//do_search()
				}
			})
		}
	}

</script>
