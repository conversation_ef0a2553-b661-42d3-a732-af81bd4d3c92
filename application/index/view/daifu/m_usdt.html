<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .input_box{ background: none }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }

    .buy_or_mai .flex{ display: flex; justify-content: space-between; }
    .buy_or_mai a{display: block; width:3.26rem; height: 0.8rem; line-height: 0.8rem;border-radius: 5px; background: #eee; text-align: center; font-size:0.28rem; color: #666; }
    .buy_or_mai a.active{ background: #EF436D; color: #fff }
    .buy_or_mai a img{ position: relative; top: -1px; margin-right: 5px; width:0.36rem }

    .input_rr.pay_type{ background: none }
    .input_rr .daifu_btn{ display: inline-block; line-height: 0.7rem; font-size: 0.24rem;  text-align: center;border-color: #ddd;color: #333333;background: none;background: #fff; padding: 0 0.2rem; border-radius: 5px;}
    .input_rr .daifu_btn.active{border-color: #EF436D;background: #EF436D;color: #fff;}

    .input_tt .moni_check{ width: 0.28rem; height: 0.28rem; margin-top: 0.22rem; }
    .moni_check.new_bg{background: url(__CDN__/assets/img/pc/radio.png); background-size: 100% 100%}
    .moni_check.new_bg.active{ background: url(__CDN__/assets/img/pc/radio_on.png);  background-size: 100% 100%}

    .moni_select{ background:none; min-width: 10px; border: none }

    .fp_div .input_box .input_rr input{ border: 1px solid #eee }

    .select{-webkit-appearance: none; border: none; width: 4rem; direction: rtl; float: right; margin-right: 0.1rem; background: none; outline: none;}

</style>
<div class="pad_t20 pad_lr20">
    
    <a class="help_title bg_fff clearfix" href="<?=url('/index/helps/list',array('type'=>'7'))?>">
        <img src="__CDN__/assets/img/wap/icon_common_01.png" class="icon s">USDT交易常見問題 >> 點擊查看
    </a>

    <form action="{:url('daifu/usdt_success')}">
        <div class="buy_or_mai pad_t30 pad_b10">
            <div class="flex">
                <a class="active" href="<?=url('daifu/usdt')?>"><img src="__CDN__/assets/img/gouwuche_a.png" alt="">我要買</a>
                <a  href="<?=url('daifu/usdtout')?>"><img src="__CDN__/assets/img/jinbi.png" alt="">我要賣</a>
            </div>
            <div class="pad_t20" style="font-size: 0.24rem; color: #666">交易時間為工作日上午10點至下午3點，最低购买数量为 <span class="color_red"><span class="js_min_buy">{$site.buy_number}</span> USDT</span> <span style="padding-left: 0.3rem">今日價格: <span class="color_red">1</span>USDT=<span class="color_red js_rate_money">{$site.usdt}</span>臺幣</span></div>
        </div>
	    <div class="input_box full_width">
            <div class="input_tt">{:__('Purchase quantity')}</div>
            <div class="input_rr">
                <div class="pull-right pad_l10 pad_r20">USDT</div>
                <div class="over_hide">
                    <input type="text" is_required="true" data-type="number" name="money" class="js_money_v" data-max="100000" data-id="1" data-category="7" placeholder="{:__('Please enter purchase quantity')}">
                </div>
            </div>
        </div>
        <a href="javascript:;" class="js_rate_change hide" data-url="{:url('/index/user/rate_fun')}"><img src="__CDN__/assets/img/pc/rate_change.png" alt=""></a>

        <div class="input_box full_width">
            <div class="input_tt">{:__('Conversion TWD')} <span class="f12 color_999">({:__('Rate')}： <span class="js_dafu_rate_val"></span>)</span></div>
            <div class="input_rr">
                <div class="pull-right pad_l10 pad_r20 color_red">TWD</div>
                <div class="over_hide">
                    <input type="text" readonly="readonly" data-type="money" class="js_money_v" data-id="2" data-category="7" placeholder="{:__('Please enter TWD amount')}">
                </div>
            </div>
        </div>

        <div class="input_box margin_t10 charge_div">
            <div class="input_tt">收幣地址類型:</div>
            <div class="input_rr pay_type pay_address_type">
                <input type="text" name="address_type" value="1" is_required="true" class="js_address_type_val"  style="position: absolute; width: 0; height: 0; opacity: 0">
				<?php
					foreach($bi_type as $key=>$ls){
						if($ls != 'ERC20'){
				?>
					<a class="daifu_btn margin_r10 js_address_type_btn <?php if($key==1){echo 'active';}?>" href="javascript:;" data-id="<?=$key?>"><?=$ls?></a>
				<?php
						}
					}
				?>
				<!-- <a class="daifu_btn margin_r10 js_address_type_btn active" href="javascript:;" data-id="1">OMNI</a>
                <a class="daifu_btn margin_r10 js_address_type_btn" href="javascript:;" data-id="2">ERC20</a> -->
            </div>
        </div>
        <div class="input_box margin_t10 charge_div js_addresstype_divs">
            <div class="input_tt">收幣地址:</div>
            <div class="input_rr pay_type">
                <div class="rate_input rate_input_red clearfix">
                    <div class="over_hide rate_input_r">
                        <div class="over_hide">
                            <input type="text" name="address" is_required="true" placeholder="請認真核實OMNI收貨地址">
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="input_box margin_t10">
            <div class="input_tt">{:__('Payment method')}:</div>
        </div>

        <div class="comfirm_index_d margin_t10 js_paytype_divs" style="padding-bottom: 2px">
            <input type="text" name="pay_type" value="1" class="js_pay_type_val"  style="position: absolute; width: 0; height: 0; opacity: 0">
            <div class="c_tt have_icon border_b clearfix">
                <!-- <input type="text" name="bank_id" value="" is_required="true" class="js_bank_id" empty_tip="{:__('Please select the bank account number')}" style="position: absolute; width: 0; height: 0; opacity: 0"> -->
                <!-- <a href="{:url('/index/daifu/select_bank')}" class="pull-right color_999 js_to_select f12"><span>{:__('Select bank card')}</span>  -->
                <img class="arr pull-right" src="__CDN__/assets/img/wap/arr.png" style="margin-top: 0.14rem;"></a>
                <select class="select" name="bank_id" is_required="true" class="js_bank_id" empty_tip="{:__('Please select the bank account number')}">
                    <option value="">{:__('Select bank card')}</option>
					<?php
						foreach($userbank as $ls){
					?>
						<option value="<?=$ls['id']?>"><?=$ls['name']?>><?=$ls['account_name']?>><?=$ls['account_six']?></option>
					<?php
						}
					?>
                </select>
                <img src="__CDN__/assets/img/wap/daifu_icon1.png" class="icon">
                <div class="over_hide">
                    <span class="color_666">{:__('Bank pay')}</span>
                </div>
            </div>


            <!-- 超商ATM  禁用 -->
            <!-- <div class="border_b"></div> -->
           <!--  <div class="c_tt have_icon border_b clearfix" style="background: #eee; margin-left: -0.2rem; padding-left: 0.9rem">
                <a href="javascript:;" class="pull-right color_999 f12"><span></span> <img class="arr" src="__CDN__/assets/img/wap/arr.png"></a>
                <img src="__CDN__/assets/img/wap/daifu_icon2.png" class="icon margin_l20" >
                <div class="over_hide">
                    <span class="color_666">超商ATM</span>
                </div>
            </div> -->
            <!-- 超商ATM -->



            <!-- 超商 -->
            <!--<label class="c_tt have_icon border_b have_check moni_check_label clearfix">
                <a href="javascript:;" class="moni_check new_bg js_cs_pay pull-right margin_l20" data-id="2">
                </a>
                <img src="__CDN__/assets/img/wap/daifu_icon2.png" class="icon">
                <div class="over_hide la_btn items color_999">
                    <div><span class="color_666">{:__('Super payment')}</span> <span class="f12 color_999">(超商繳費程式碼將傳送至您的行動電話）</span></div>
                    <div class="f12">超商將向您收取30TWD的手續費</div>
                    <div class="f12">最低金額100TWD，最高報礦手續費不超過6000TWD</div>
                </div>
            </label>-->
            <!-- 超商 -->



            <!-- 信用卡 -->
            <!--<label class="c_tt have_icon have_check moni_check_label clearfix" style="">
                <a href="javascript:;" class="moni_check new_bg js_cs_pay pull-right margin_l20"  data-id="4">
                </a>
                <img src="__CDN__/assets/img/wap/daifu_icon4.png" class="icon">
                <div class="over_hide la_btn items color_999">
                    <div><span class="color_666">信用卡</span> <span class="f12 color_999">(信用卡支付）</span></div>
                </div>
            </label>-->
            <!-- 信用卡 -->
        </div>


        <div class="pad_t20">
            <div class="input_tt">手續費:  <span class="color_red js_sxf_money">{$site.charge}</span>臺幣</div>
        </div>
        <div class="pad_t20">
            <div class="input_tt">支付總額: <span class="color_red js_zzf_money">0</span>新臺幣</div>
        </div>


	    <div class="clearfix pad_t20 login_tips">
            <label class="moni_check_label">
                <a href="javascript:;" class="moni_check js_agree2 active">
                </a>
                <span class="la_btn">{:__('I have read and agreed')}</span> <a class="color_red js_article_win" href="/haigou/public/index/login/agreement.html">《{:__('Consent to Settlement Authorization')}》</a>
            </label>
        </div>

        <div class="pad_tb30 margin_b20 order_detail">
            <a href="javascript:;" class="sub_btn margin_b30 red js_form_sub" data-text="{:__('Confirm')}" data-loadding="{:__('Submitting')}" data-type="new_location" data-func_before="before_sub">{:__('Confirm')}</a>
            <a href="{:url('/index/daifu/cancel')}" class="sub_btn js_ajax_dele" tips="{:__('Do you confirm the cancellation?')}" data-id="1">{:__('Cancellation of payment')}</a>
        </div>
		
    </form>
	<a href="{:url('/index/user/editIdcard')}?type=1" class="js_ajax_win hide js_one_auth"></a>
	<a href="{:url('/index/user/editIdcardTwo')}?type=1" class="js_ajax_win hide js_two_auth"></a>
</div>
<!-- 这个有用，不要删 -->
<div class="hide">
 <a href="{:url('/index/daifu/checkMobile')}" class="js_ajax_win js_check_mobile_link" >手机验证</a>   
</div>
<script type="text/javascript">


    function before_sub(){
        if(parseFloat($('.js_money_v[data-id="1"]').val())<parseFloat($('.js_min_buy').html())){
            tip_show("最低购买数量为"+$('.js_min_buy').html()+'USDT','2');
            return false;
        }
        if(!$('.login_tips .moni_check.js_agree2').hasClass('active')){
            tip_show("{:__('Please agree to settlement')}",'2');
            return false;
        }
        return true;
    }
	// 储值 汇率换算
    var _ratetime=null;
    $(document).on('keyup','.js_money_v',function(){
        var _this=$(this);
        var rate=parseFloat($('.js_rate_money').html());
        var _type=_this.attr('data-id');
        var money='';
        if(_type==1){
            money=_this.val()==''?'':(parseFloat(_this.val())*rate)
        }
        if(_type==2){
            money=_this.val()==''?'':(parseFloat(_this.val())/rate)
        }
        money=money==''?'':parseInt(money);
        var _otyps=_type==1?2:1;
        $('.js_money_v[data-id="'+_otyps+'"]').val(money);
        var sxf=parseFloat($('.js_sxf_money').html());
        $('.js_zzf_money').html(parseInt(sxf+(money==''?0:money)))
    })
    // 切换 地址
    $('.js_address_type_btn').click(function(){
        var _this=$(this);
        var _id=_this.attr('data-id');
        _this.addClass('active').siblings('a').removeClass('active');
        $('.js_address_type_val').val(_id);
        $('.js_addresstype_divs .tts').html(_this.html());
        $('.js_addresstype_divs input').attr('placeholder','請認真核實'+_this.html()+'收貨地址')
        
    })




    $(function(){
       
       
        $('.js_cs_pay').click(function(){
            var _this=$(this)
            setTimeout(function(){
                $('.js_bank_id').attr('is_required',!_this.hasClass('active'));
                $('.js_to_select').find('span').html("{:__('Select bank card')}");
                if(_this.hasClass('active')){
                    _this.parents('.moni_check_label').siblings('.moni_check_label').find('.js_cs_pay').removeClass('active')
                }
                $('.js_pay_type_val').val((_this.hasClass('active')?_this.attr('data-id'):"1"));
            },30)
        })


        // 定时器取本地选择的银行卡信息
        // 保存金额，方便 到银行卡页面 获取，判断能否多选银行
        localStorage.select_bank_money=$('.js_zzf_money').html(); 
        localStorage.is_usdt_bank='1'; 
        var _bank_time=null;

        function check_lo(){
            _bank_time=setInterval(function(){
                // 如果有银行信息
                if(localStorage.select_bank_data){
                    var _data=JSON.parse(localStorage.select_bank_data);
                    // 清楚本地缓存
                    localStorage.select_bank_data='';
                    // 清除定时器
                    clearInterval(_bank_time);
                    _bank_time=null;
                    // 赋值
                    console.log('银行卡 id:'+_data.id)
                    $('.js_bank_id').val(_data.id);
                    $('.js_to_select').find('span').html(_data.title)
                }
            },100)
        }
        check_lo()
        $('.js_to_select').click(function(){
            if(_bank_time==null){
                check_lo()
            }
        })
        <?php
			if(!$user['is_card']){
		?>
				$('.js_one_auth').click();
		<?php
			}
			if($user['is_card']){
				if(!$user['is_card_img']){
		?>
					$('.js_two_auth').click();
		<?php
				}
			}
		?>
    })
</script>