<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .id_input input{border: 1px solid #eee; height: 36px; padding-left: 10px; width: 200px}
    .id_input a{ display: inline-block; height: 36px; line-height: 36px; margin-left: 10px; border-radius: 5px; color: #fff; background: #EF436D; padding: 0 12px; }
</style>
<div class="pad_t20 pad_lr20">
    <div class="help_title bg_fff clearfix">
        <span class="pull-right f12"><span class="color_666">{:__('Numbering')}：</span><?=$order['order']['order_no']?></span>
        <!-- pay1 pay2 pay3···· -->
		<?php
			if(!empty($usdt_type)){
				$_img = 'pay'.$order['order']['type'];
				$_title = $site['order_type'][$order['order']['type']];
			}else{
				$_img = 'icon_common_01';
				$_title = 'USDT';
			}
		?>
		
		<img src="__CDN__/assets/img/wap/<?=$_img?>.png" class="icon s"><?=$_title?>
        
    </div>

    <div class="order_detail">


		<div class="item_tt">
            {:__('Need TWD')}
        </div>
        <div class="order_rate">
            <div class="rate clearfix">
                <div class="r_item">
                    <div class="pp">購買數量</div>
                    <div class="tt"><?=$order['order']['number']?></div>
                </div>
                <div class="c_item">
                    <span class="color_666">{:__('Rate')}：</span><?=$order['order']['exchange']?>
                </div>
                <div class="r_item">
                    <div class="pp">{:__('Need TWD')}</div>
                    <div class="tt"><span class="color_red"><?=$order['order']['actual_money']?></span>TWD</div>
                </div>
            </div>

            <div class="items">
                <div class="item"><span class="color_666">{:__('Handling fee')}：</span><?=$order['order']['service'] + $order['order']['bank_service']?>TWD</div>
                <div class="item"><span class="color_666">{:__('Calculation formula')}：</span>
				<?php
					if($order['order']['type']){
						echo $order['order']['number'] .'*'. $order['order']['exchange'] .'-'. $order['order']['service'] .'-'. $order['order']['bank_service'] .'='. $order['order']['actual_money'];
					}else{
						echo $order['order']['number'] .'*'. $order['order']['exchange'] .'-'. $order['order']['service'] .'+'. $order['order']['bank_service'] .'='. $order['order']['actual_money'];
					}
				?>
				TWD</div>
            </div>
        </div>

        <div class="item_tt clearfix">
			收幣类型
		</div>
		<div class="item_div">
			<span class="color_red">
			<?=$bi_type[$order['order']['currency_type']]?>
			</span>
		</div>
		<div class="item_tt clearfix">
			收幣地址
		</div>
		<div class="item_div">
			<?php
				if($order['order']['type'] == '1'){
					echo $order['order']['site_address'];
				}else{
					echo $order['order']['address'];
				}
			?>
		</div>
		<?php
			if($order['order']['type'] == '1'){
		?>
			<div class="item_tt clearfix">
				交易ID
			</div>
			<div class="item_div">
				<!--有id 1212323 -->
				<?php
						if($order['order']['transaction_id']){
							echo $order['order']['transaction_id'];
					?>
					<?php
						}else{
					?>
					<div class="text-left pad_l10 id_input"><input type="text" placeholder="請輸入交易ID"> <a href="javascript:;" class="js_sub_id" data-url="{:url('daifu/sub_id')}">提交</a></div>
					<?php
						}
				?>
			</div>
		<?php
			}
		?>
		<div class="color_red pad_b10 pad_t20 f12">請確認收幣地址為
		<?=$bi_type[$order['order']['currency_type']]?>
		地址，否則會交易失敗！</div>
		
			<!-- 泰达币 -->
			<?php
				if($order['order']['pay_status'] == '1' && $order['order']['type'] == '0'){
			?>
				<div class="item_tt">
					{:__('Payment method')}
				</div>
				<div class="order_payment pad_lr20">
					<div class="clearfix zffs">
						<div class="status color_red"><?=$site['pay_type'][$order['order']['pay_type']]?></div>
						<!-- account1银行  account2 微信  account3支付宝 -->
						<img src="__CDN__/assets/img/wap/account1.png">
						<?=$site['pay_status'][$order['order']['pay_status']]?>
					</div>
					<?php
						foreach($order['bank'] as $ls){
					?>
					<div class="clearfix color_666">
						<div class="d">台湾银行</div>
						<div class="d text-center"><?=$ls['name']?></div>
						<div class="d text-right"><?=$ls['account_six']?></div>
					</div>
					<?php
						}
					?>
				</div>
				<div class="order_payment margin_t20">
					<div class="ttt pad_lr20">{:__('Our Payment Bank Account')}</div>
					<div class="pad_lr20 pad_tb20">
						<div class="item color_red">{$site.bank_name}</div>
						<div class="item"><span class="color_666">{:__('Account')}：</span>{$site.bank_account}</div>
					</div>
				</div>
			<?php
				}
			?>
        


        <!--<div class="tipsd pad_tb20">
            <p>{:__('We do not accept over-the-counter remittance')}</p>
            <p>{:__('Receiving bank account is for this order only')}</p>
            <p>{:__('Please use ATM/WEBATM pipeline to transfer to the account')}</p>
            <p>{:__('We don not need to inform you after payment is completed')}</p>
            <p>{:__('Bank transfers usually take time to arrive. If the')}</p>
            <p>{:__('If you can not transfer money and may encounter bank')}</p>
        </div>
        <div class="order_payment pad_lr20">
            <div class="clearfix zffs">
                <div class="status color_999">{:__('Electronic invoice')}({:__('Compilation')})</div>
                <img src="__CDN__/assets/img/wap/fapiao.png">
                {:__('Invoice type')}
            </div>
            <div class="items">
                <div class="item"><span class="color_666">{:__('Unified numbering')}：</span>A123456789</div>
                <div class="item"><span class="color_666">{:__('Invoice rise')}：</span>某某</div>
                <div class="item"><span class="color_666">{:__('Mail box')}：</span><EMAIL></div>
            </div>
            <div class="fapiaotip">
                {:__('* E-invoice will be issued three days after')}
            </div>
        </div>-->
		<div class="f12 color_999 tips_text pad_b20 pad_t30" style="line-height: 0.4rem">
            <p>訂單處理倒數計時： <span class="color_red js_time_down" data-time="120"><span class="js_h">00</span>時<span class="js_m">00</span>分<span class="js_s">00</span>秒</span> 請耐心等待，我們正在為您處理中！</p>
            <p>我們正在處理您的這筆訂單，期間無需提醒客服人員！</p>
            <p>現在請您耐心等待處理倒數計時之後，通知收款方查帳。</p>
        </div>

        <script type="text/javascript">
        	// 倒计时
		    function end_time(obj,fun) {
		        // 个位数转 两位数  负数转 0
		        var time_x = parseInt(obj.attr('data-time'))
		        function c_data(str) {
		            if (str < 0) {
		                str = "00"
		            } else {
		                str = String(str);
		                if (str.length == 1) {
		                    str = "0" + str;
		                }
		            }
		            return str;
		        }
		        var now_time = 0;
		        updata_time()
		        setInterval(function() {
		            now_time++;
		            updata_time()
		        }, 1000);
		        function updata_time(){
		            var time_d = time_x - now_time; //更新时间差
		            var hour = parseInt(time_d / 3600); // 小时
		            var minute = parseInt(time_d % 3600 / 60); //分
		            var second = parseInt(time_d % 3600 % 60); //秒
		            obj.find('.js_h').html(c_data(hour));
		            obj.find('.js_m').html(c_data(minute));
		            obj.find('.js_s').html(c_data(second));
		            if (time_d == 0 && fun.after_time) {
		                fun.after_time();
		            }
		        }
		    }
		    $(function(){
		        end_time($('.js_time_down'),{
		            after_time:function(){
		                window.location.reload();
		            }
		        });
		    })
        </script>
    </div>
</div>
<script type="text/javascript">
	$('.js_sub_id').click(function(){
        var _this=$(this);
		var id = <?=$order['order']['id']?>;
        var _val=_this.parent('.id_input').find('input').val();
        if(_val==''){
            tip_show('請輸入交易ID','2');
            return false;
        }
        var _url=_this.attr('data-url');
        $.post(_url,{transaction_id:_val,id:id},function(res){
            if (res.code == "1") {
                tip_show(res.msg, '1');
                setTimeout(function(){
                    window.location.href=window.location.href
                },1000*parseInt(res.wait))

            } else {
                tip_show(res.msg, '2');
            }
        })
    })
</script>