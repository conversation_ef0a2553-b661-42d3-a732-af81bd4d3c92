<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .input_box{ background: none }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
    .input_box .input_rr .arr{ height: 0.2rem; margin-top: 0.52rem; margin-left:10px; margin-right: 0.2rem }
    .input .avatar{ width: 0.88rem; height: 0.88rem; border-radius: 50%; float: left; }
    .input.input_avatar{ padding-top: 0.2rem; padding-bottom: 0.2rem; line-height: 0.88rem }
    .input_rr .f12{ margin-top: 0.22rem }
</style>
<div class="pad_t20 pad_lr20">
    {include file="daifu/tips" /}

    <a class="help_title bg_fff clearfix" href="<?=url('/index/helps/list',array('type'=>'4'))?>">
        <img src="__CDN__/assets/img/wap/pay4.png" class="icon s">{:__('WeChat storage value')}常見問題 >> 點擊查看
    </a>

    <form action="{:url('daifu/confirm_index')}">
        <div class="input_box full_width">
            <div class="input_tt">{:__('RMB')}</div>
            <div class="input_rr">
                <div class="pull-right pad_l10 pad_r20">RMB</div>
                <div class="over_hide">
                    <input type="text" data-type="money" name="money" class="js_money_v" data-max="20000" data-id="1" data-category="4"  placeholder="{:__('Please enter RMB amount')}">
                </div>
            </div>
        </div>
        <a href="javascript:;" class="js_rate_change hide" data-url="{:url('/index/user/rate_fun')}"><img src="__CDN__/assets/img/pc/rate_change.png" alt=""></a>

        <div class="input_box full_width">
            <div class="input_tt">{:__('Conversion TWD')} <span class="f12 color_999">({:__('Rate')}： <span class="js_dafu_rate_val"><?=$exchange['exchange']?></span>)</span></div>
            <div class="input_rr">
                <div class="pull-right pad_l10 pad_r20 color_red">TWD</div>
                <div class="over_hide">
                    <input type="text" data-type="money" class="js_money_v" data-id="2" data-category="4" placeholder="{:__('Please enter TWD amount')}">
                </div>
            </div>
        </div>


        <div class="input_box full_width">
            <div class="input_tt">{:__('Please fill in the Wechat Account for Storage Value')}</div>
            <div class="input_rr">
                <img class="arr pull-right" src="__CDN__/assets/img/wap/arr.png">
                <a href="{:url('/index/daifu/select_wechat')}" class="pull-right color_999 margin_l20 f12 js_ajax_win">{:__('Click to select more')}</a>
                <div class="over_hide js_account_dom">
                    <input type="text" style="position: absolute; width: 0; height: 0; opacity: 0" name="account" value="" readonly="readonly" is_required="true" empty_tip="{:__('Please fill in the Wechat Account for Storage Value')}">
                    
                    
                    <!-- 未选择 -->
                    <div class="input input_avatar pad_l20 ellipsis"><span class="color_999">{:__('Please fill in the Wechat Account for Storage Value')}</span></div>
                    <!-- 未选择 -->

                    <!-- 已选择 -->
                    <!-- <div class="input input_avatar ellipsis pad_l20"><img class="avatar margin_r30" src="__CDN__/assets/img/wap/avatar.png">张三</div> -->
                    <!-- 已选择 -->
                </div>
            </div>
        </div>


        <div class="clearfix login_tips pad_tb20">
            <label class="moni_check_label clearfix">
                <a href="javascript:;" class="moni_check pull-left active">
                    <input type="checkbox" name="is_agree_ali" value="1" data-type="checkbox" checked="checked" is_required="true" empty_tip="{:__('Please understand the payment rules first')}" style="position: absolute; width: 0; height: 0; opacity: 0">
                </a>
                <span class="la_btn over_hide" style="display: block;">{:__('I confirm that the account I want to deposit')}</span>
            </label>
        </div>


        {include file="daifu/m_common" /}

    </form>

</div>

<script type="text/javascript">
    var account_list=[];
    $(function(){
    })
</script>