<div class="container">
	<div class="user_main margin_tb40 clearfix">
		<div class="user_main_line"></div>
		{include file="daifu/nav" /}
		<div class="pad_tb20 pad_lr20 over_hide daifu_div">
			{include file="daifu/tips" /}
			<div class="pad_t30">
				<form action="<?=url('daifu/game_success')?>">
					<div class="input_box  ">
						<div class="input_tt">帳號:</div>
						<div class="input_rr">
							<input type="text" name="account" value="" is_required="true" placeholder="請輸入帳號">
						</div>
					</div>
					<div class="input_box  ">
						<div class="input_tt">密碼:</div>
						<div class="input_rr">
							<input type="password" name="password" value="" is_required="true" placeholder="請輸入密碼">
						</div>
					</div>
					<div class="input_box  ">
						<div class="input_tt">充值類型:</div>
						<div class="input_rr">
							<input type="text" class="js_mz_type" name="num_type"
								style="width: 0; height: 0; opacity: 0; position: absolute;" value="" is_required="true"
								placeholder="請選擇充值類型">
							<div class="pay_type num_pay_type">
								<a class="daifu_btn margin_r10 js_num_type_sele" href="javascript:;" data-id="1"
									data-arr='10,100,500,1000'>快充</a>
								<a class="daifu_btn margin_r10 js_num_type_sele" href="javascript:;" data-id="2"
									data-arr='10,20,30,100'>慢充</a>
							</div>
						</div>
					</div>
					<div class="input_box  ">
						<div class="input_tt">充值金額:</div>
						<div class="input_rr">
							<input type="text" class="js_mz" name="num"
								style="width: 0; height: 0; opacity: 0; position: absolute;" value="" is_required="true"
								placeholder="充值金額">
							<div class="pay_type num_pay_type js_mz_options">

							</div>
						</div>
					</div>

					<!-- 不要删 -->
					<div class="input_box clearfix " style="display: none;">
						<div class="input_tt">商品總價:</div>
						<div class="input_rr pull-left">
							<input type="text" placeholder="" style="width: 100px" class="js_need_pay_money"
								name="tmoney" readonly="readonly">
							TWD
							<span class="pad_l10 color_999 f12">商品選擇完成後自動顯示</span>
						</div>
					</div>

					{include file="daifu/common_new" /}
				</form>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	var account_list = [];
	$(function() {
		$(document).on('click', '.js_num_type', function() {
			$(this).addClass('active').siblings('.js_num_type').removeClass('active');
			$('.js_mz').val($(this).attr('data-id'));
			func_all()
		})

		function func_all() {
			var money = parseFloat($('.js_mz').val()) || 0;
			var _url = "{:url('/index/user/rate_fun')}";
			$.post(_url, {
				money: money,
				type: 1,
				category: 3
			}, function(data) {
				$('.js_need_pay_money').val(data.money)
			})
		}

		$('.js_num_type_sele').click(function() {
			$(this).addClass('active').siblings('.js_num_type_sele').removeClass('active');
			$('.js_mz_type').val($(this).attr('data-id'));
			var html = '';
			var arr = $(this).attr('data-arr').split(',');
			for (var i = 0; i < arr.length; i++) {
				html += '<a class="daifu_btn margin_r10 js_num_type" href="javascript:;" data-id="' + arr[
					i] + '">￥' + arr[i] + '</a>'
			}
			$('.js_mz_options').html(html);
			$('.js_mz').val('')
			func_all()
		})
		$('.js_num_type_sele').eq(0).click()
	})
</script>
