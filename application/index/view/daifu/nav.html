<div class="user_new_left js_user_new_left pull-left">
    <div class="n_tt flex_ac">
        <img src="__CDN__/assets/img/pc/icon_title_server_center.png" class="icon margin_r10">
        服務中心
    </div>
	<?php
		foreach($one_nav as $key=>$ls){
	?>
		<div class="nav_click_item flex_ac">
			<div class="icon flex_ac_jc margin_r10">
				<img src="__CDN__/assets/img/pc/images/icon_business0<?=$key+1?>.png" class="img1" alt="">
				<img src="__CDN__/assets/img/pc/images/icon_business0<?=$key+1?>_red.png" class="img2" alt="">
			</div>
			<?=$ls['name']?>
			<div class="margin_a flex_ac_jc icon">
				<img src="__CDN__/assets/img/pc/images/nav_icon_more.png" class="img1" alt="">
				<img src="__CDN__/assets/img/pc/images/nav_icon_more_red.png" class="img2" alt="">
			</div>
		</div>
		<div class="n_tt_menu">
			<?php
			if(!empty($two_nav[$ls['id']])){
				foreach($two_nav[$ls['id']] as $k=>$val){
					if($val['key_id'] != '2'){
			?>
				<a href="<?=url($val['nickname'],array('set_type'=>$val['key_id']))?>" class="flex_ac <?php if($set_type == $val['key_id']) echo 'active'?>">
					<img src="<?=$val['image']?>" class="item_icon" alt="">
					<?=$val['name']?>
				</a>
			<?php
					}
				}
			}
			?>
		
		</div>
		
	<?php
		}
	?>
</div>

<script>
	if($('.n_tt_menu a.active').length>0){
		$('.n_tt_menu a.active').parent('.n_tt_menu').css('display','block');
		$('.n_tt_menu a.active').parent('.n_tt_menu').prev('.nav_click_item').addClass('active')
	}
	$('.js_user_new_left .nav_click_item').click(function(){
		if($(this).hasClass('active')){
			$(this).removeClass('active');
			$(this).next('.n_tt_menu').stop().slideUp()
		}else{
			$(this).addClass('active').siblings('.nav_click_item').removeClass('active');
			$(this).next('.n_tt_menu').stop().slideDown()
			$(this).next('.n_tt_menu').siblings('.n_tt_menu').stop().slideUp();
		}
	})
</script>

<!-- 原链接 -->
<!-- <div class="n_tt_menu">
	<a href="{:url('/index/daifu/index')}" class="<?php if($nav_url == 'daifu/index')echo 'active';?>">{:__('Alibaba pays')}</a>
	<a href="{:url('/index/daifu/taobao')}" class="<?php if($nav_url == 'daifu/taobao')echo 'active';?>">{:__('Taobao Payment')}</a>
	<a href="{:url('/index/daifu/alipay')}" class="<?php if($nav_url == 'daifu/alipay')echo 'active';?>">{:__('Alipay Reserve Value')}</a>
	<a href="{:url('/index/daifu/wechatpay')}" class="<?php if($nav_url == 'daifu/wechatpay')echo 'active';?>">{:__('WeChat storage value')}</a>
	<a href="{:url('/index/daifu/game')}" class="<?php if($nav_url == 'daifu/game')echo 'active';?>">{:__('Game/Video Storage')}</a>
	<a href="{:url('/index/daifu/other')}" class="<?php if($nav_url == 'daifu/other')echo 'active';?>">{:__('Other payment')}</a>
	<a href="{:url('/index/daifu/charge')}" class="<?php if($nav_url == 'daifu/charge')echo 'active';?>">{:__('Coin storage')}</a>
	<a href="{:url('/index/daifu/usdt')}" class="<?php if($nav_url == 'daifu/usdt')echo 'active';?>">{:__('USDT transaction')}</a>
</div> -->
