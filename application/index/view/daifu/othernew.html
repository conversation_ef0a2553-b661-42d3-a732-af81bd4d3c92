<div class="container">
	<div class="user_main margin_tb40 clearfix">
		<div class="user_main_line"></div>
		{include file="daifu/nav" /}
		<div class="pad_tb20 pad_lr20 over_hide daifu_div">
			{include file="daifu/tips" /}
			<div class="pad_t30">
				<form action="<?=url('daifu/game_success')?>">
					<div class="input_box  ">
						<div class="input_tt">链接:</div>
						<div class="input_rr">
							<input type="text" name="url" value="" class="auto_w" is_required="true"
								placeholder="請輸入链接">
						</div>
					</div>
					<div class="input_box  ">
						<div class="input_tt">帳號:</div>
						<div class="input_rr">
							<input type="text" name="account" value="" is_required="true" placeholder="請輸入帳號">
						</div>
					</div>
					<div class="input_box  ">
						<div class="input_tt">密碼:</div>
						<div class="input_rr">
							<input type="password" name="password" value="" is_required="true" placeholder="請輸入密碼">
						</div>
					</div>
					<div class="input_box  ">
						<div class="input_tt">金额:</div>
						<div class="input_rr">
							<input type="number" name="money" value="" class="js_mz" is_required="true"
								placeholder="請輸入金额">
						</div>
					</div>

					<!-- 不要删 -->
					<div class="input_box clearfix" style="display: none;">
						<div class="input_tt">商品總價:</div>
						<div class="input_rr pull-left">
							<input type="text" placeholder="" style="width: 100px" class="js_need_pay_money"
								name="tmoney" readonly="readonly">
							TWD
							<span class="pad_l10 color_999 f12">商品選擇完成後自動顯示</span>
						</div>
					</div>

					{include file="daifu/common_new" /}
				</form>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	var account_list = [];
	$(function() {
		function func_all() {
			var money = parseFloat($('.js_mz').val()) || 0;
			var _url = "{:url('/index/user/rate_fun')}";
			$.post(_url, {
				money: money,
				type: 1,
				category: 3
			}, function(data) {
				$('.js_need_pay_money').val(data.money)
			})
		}

		$('.js_mz').blur(function() {
			func_all()
		})
	})
</script>
