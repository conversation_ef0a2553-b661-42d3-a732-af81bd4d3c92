<?php
	if($order['order']['pay_status'] == '0'){
?>
	<table class="common_table table margin_t10 text-center">
		<tbody>
			<tr>
				<th class="l" colspan="3">
					<div class="clearfix pad_lr10">
						{:__('Payment information')}
					</div>
				</th>
			</tr>
			<tr class="color_666">
				<td>{:__('Payment method')}</td>
				<td>{:__('Coin payment')}</td>
			</tr>
		</tbody>
	</table>
<?php
	}
?>
<?php
	if($order['order']['pay_status'] == '1'){
?>
 <table class="common_table table margin_t10 text-center">
    <tbody>
        <tr>
            <th class="l" colspan="3">
                <div class="clearfix pad_lr10">
                    {:__('Payment information')}
                </div>
            </th>
        </tr>
		<tr class="color_666">
			<td>{:__('Payment method')}</td>
			<td>{:__('Bank account')}</td>
			<td>{:__('Account number')}</td>
		</tr>
		<?php
			if(!empty($order['bank'])){
			foreach($order['bank'] as $ls){
		?>
        <tr class="color_666">
			<td class="color_red">{:__('ATM Bank Transfer')}</td>
			
				<td><?=$ls['name']?></td>
				<td><?=$ls['account_name']?>-<?=$ls['account_six']?></td>
			<?php
					}
				}else{
			?>
				<td>-</td>
				<td>-</td>
        </tr>
		<?php
			}
		?>
    </tbody>
</table>
<?php
	}
?>
<?php
	if($order['order']['pay_status'] == '3' || $order['order']['pay_status'] == '1'){
?>
<div class="f12 color_999 tips_text pad_b20">*{:__('Please use your bank card account transfer and payment')}</div>
<table class="common_table table margin_t10 text-center">
    <tbody>
        <tr>
            <th class="l" colspan="2">
                <div class="clearfix pad_lr10">
                    {:__('Our Payment Bank Account')}
                </div>
            </th>
        </tr>
		<?php
			if($order['order']['pay_status'] == '3'){
		?>
			<tr class="text-left" style="color:#f30e0e;">
				<td width="50%"><div class="pad_l10">822 中國信託（虛擬賬戶）</div></td>
				<td>{:__('Account')}：<?=$order['order']['cs_code']?></td>
			</tr>
		<?php
			}else{
		?>
			<tr class="text-left" style="color:#f30e0e;">
				<?php
					if(!empty($order['sys_bank'])){
				?>
				<td width="50%"><div class="pad_l10"><?=$order['sys_bank']['account_name']?></div></td>
				<td>{:__('Account')}：<?=$order['sys_bank']['account_num']?></td>
				<?php
					}else{
				?>
					<td width="50%"><div class="pad_l10">-</div></td>
					<td>{:__('Account')}：-</td>
				<?php
					}
				?>
			</tr>
		<?php
			}
		?>
        
    </tbody>
</table>
<?php
	}
?>
<?php
	if($order['order']['pay_status'] == '2'){
?>
	<table class="common_table table margin_t10 text-center">
		<tbody>
			<tr>
				<th class="l" colspan="3">
					<div class="clearfix pad_lr10">
						{:__('Payment information')}
					</div>
				</th>
			</tr>
			<tr class="color_666">
				<td>{:__('Payment method')}</td>
				<td>{:__('Super number')}</td>
			</tr>
			<tr class="color_666">
				<td class="color_red">{:__('Super payment')}</td>
					<td><?=$order['order']['cs_code']?$order['order']['cs_code']:'已發送注册手機簡訊，請在24小時內缴款'?></td>
			</tr>
		</tbody>
	</table>
<?php
	}
?>
<div class="f12 color_999 tips_text pad_b20">
    <p>{:__('We do not accept over-the-counter remittance')}</p>
    <p>{:__('Receiving bank account is for this order only')}</p>
    <p>{:__('Please use ATM/WEBATM pipeline to transfer to the account')}</p>
    <p>{:__('We don not need to inform you after payment is completed')}</p>
    <p>{:__('Bank transfers usually take time to arrive. If the')}</p>
    <p>{:__('If you can not transfer money and may encounter bank')}</p>
</div>


<?php
	if($order['order']['order_status'] != '4' && $order['order']['order_status'] != '6'){
?>
	<div class="pad_tb20">
		<a href="<?=url('/index/daifu/cancelDf',array('set_type'=>$set_type))?>" class="sub_btn small dark js_ajax_cancel_df" data-id="<?=$order['order']['id']?>" tips="{:__('Do you confirm the cancellation?')}">{:__('Cancellation of this payment')}</a>
	</div>
<?php
	}
?>




<script type="text/javascript">
    $(function(){
        $(document).on('click','.js_ajax_cancel_df',function(){
            var _this=$(this);
            layer.confirm(_this.attr('tips'), {
              title:false,
              area:['500px', 'auto'],
              skin: 'my_confirm',
              btn: ["{:__('Confirm')}","{:__('Cancel')}"], //按钮
              btnAlign:'c'
            }, function(){
                 var _data={};
                _data={
                    id:_this.attr('data-id')
                }
                $.post(_this.attr('href'),_data,function(data){
                    if(data.code=="1"){
                        tip_show(data.msg,'1');
                        setTimeout(function(){
                            if(data.url){
                                window.location.href = data.url;
                            }else{
                                location.reload();
                            }
                        },1000*parseInt(data.wait))
                    }else{
                        tip_show(data.msg,'2');
                        return false
                    }
                })
            });
            return false; 
        })
    })
</script>