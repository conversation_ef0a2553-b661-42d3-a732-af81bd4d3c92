<style type="text/css">
    .daifu_div .rate_input{ width: 300px; }
    .daifu_div .input_box .input_rr input{width: 200px}
    .daifu_div .rate_change{ padding: 4px 0 }
    .buy_or_mai a{display: block; width: 300px; height: 60px; line-height: 60px;border-radius: 5px; background: #eee; text-align: center; font-size:16px; color: #666; float: left; }
    .buy_or_mai a.active{ background: #EF436D; color: #fff }
    .buy_or_mai a img{ position: relative; top: -1px; margin-right: 5px; width:20px }
    .buy_or_mai .zw{width: 50px; height: 50px; float: left;}
    
    .daifu_div .pay_type.pay_address_type .daifu_btn{min-width: 100px}
    .daifu_div .pay_address_type .rate_input{ width: 600px; }
    .daifu_div .input_box .pay_address_type.input_rr input{width: 600px; text-align: left;}
</style> 
<div class="container">
    <div class="user_main margin_tb40 clearfix">
        <div class="user_main_line"></div>
        {include file="daifu/nav" /}
        <div class="pad_tb20 pad_lr20 over_hide daifu_div">
            
            <div class="pad_t30">
                <form action="<?=url('daifu/usdt_success')?>">
                    <div class="input_box margin_t10 charge_div">
                        <div class="input_tt" style="height: 20px"></div>
                        <div class="input_rr buy_or_mai">
                            <div class="clearfix">
                                <a class="active" href="<?=url('daifu/usdt')?>"><img src="__CDN__/assets/img/gouwuche_a.png" alt="">我要買</a>
                                <div class="zw"></div>
                                <a  href="<?=url('daifu/usdtout')?>"><img src="__CDN__/assets/img/jinbi.png" alt="">我要賣</a>
                            </div>
							<div>交易時間為工作日上午10點至下午3點，最低购买数量为 <span class="color_red"><span class="js_min_buy">{$site.buy_number}</span> USDT</span> <span style="padding-left: 70px">今日價格: <span class="color_red">1</span>USDT=<span class="color_red js_rate_money">{$site.usdt}</span>臺幣</span></div>
                        </div>
                    </div>
                    <div class="input_box margin_t10 charge_div">
                        <div class="input_tt">{:__('Purchase quantity')}:</div>
                        <div class="input_rr ">
                            <div class="rate_input clearfix">
                                <div class="clearfix">
                                    <div class="pull-left tts">USDT</div>
                                    <div class="over_hide rate_input_r">
                                        <div class="over_hide">
                                            <input type="text" data-type="number" name="money" class="js_money_v" data-max="100000" data-id="1"  is_required="true" empty_tip="{:__('Please enter purchase quantity')}" data-category="7" placeholder="{:__('Please enter purchase quantity')}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="rate_change">
                                <a href="javascript:;" class="js_rate_change" data-url="{:url('/index/user/rate_fun')}"><img src="__CDN__/assets/img/pc/rate_change.png" alt=""></a>
                            </div>
                            <div class="rate_input rate_input_red clearfix">
                                <div class="pull-left tts">{:__('TWD')}</div>
                                <div class="over_hide rate_input_r">
                                    <span class="pull-right color_999 margin_l10 margin_r20">TWD</span>
                                    <div class="over_hide">
                                        <input type="text" readonly="readonly" style="width: 150px" data-type="money" class="js_money_v" data-id="2" data-category="7" placeholder="{:__('Please enter TWD amount')}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="input_box margin_t10 charge_div">
                        <div class="input_tt">收幣地址類型:</div>
                        <div class="input_rr pay_type pay_address_type">
                            <input type="text" name="address_type" value="1" is_required="true" class="js_address_type_val"  style="position: absolute; width: 0; height: 0; opacity: 0">
                            <?php
								foreach($bi_type as $key=>$ls){
									if($ls != 'ERC20'){
							?>
								<a class="daifu_btn margin_r10 js_address_type_btn <?php if($key==1){echo 'active';}?>" href="javascript:;" data-id="<?=$key?>"><?=$ls?></a>
							<?php
									}
								}
							?>
							
                            <!-- <a class="daifu_btn margin_r10 js_address_type_btn" href="javascript:;" data-id="2">ERC20</a> -->
                        </div>
                    </div>
                    <div class="input_box margin_t10 charge_div js_addresstype_divs">
                        <div class="input_tt">收幣地址:</div>
                        <div class="input_rr pay_type pay_address_type">
                            <div class="rate_input rate_input_red clearfix">
                                <div class="pull-left tts">OMNI</div>
                                <div class="over_hide rate_input_r">
                                    <div class="over_hide">
                                        <input type="text" name="address" is_required="true" placeholder="請認真核實OMNI收貨地址">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="input_box margin_t10 charge_div js_paytype_divs">
                        <div class="input_tt">{:__('Payment method')}:</div>
                        <div class="input_rr pay_type">
                            <input type="text" name="pay_type" value="1" is_required="true" class="js_pay_type_val" empty_tip="{:__('Please choose the mode of payment')}" style="position: absolute; width: 0; height: 0; opacity: 0">
                            <a class="daifu_btn margin_r10 js_pay_type_btn active" href="javascript:;" data-id="1">{:__('Network ATM')}</a>
                            <?php
                            
                                /*if($user['is_super_quotien'] == '1'){
                            ?>
                                <!--<a class="daifu_btn margin_r10 js_pay_type_btn" href="javascript:;" data-id="2">{:__('Super payment')}</a>-->
                            <?php
                                }else{
                                    if($super){
                                        if($super['type'] == '0'){
                                ?>
                                    <a class="daifu_btn margin_r10 js_ajax_win" href="javascript:;">{:__('Submitted, under system audit')}</a>
                                <?php
                                        }
                                        if($super['type'] == '2'){
                                ?>
                                    <a class="daifu_btn margin_r10 js_ajax_win" href="{:url('/index/daifu/apply_shop')}" data-width="600" data-height="420">超商申請失敗,原因:<?=$super['check_text']?>,點擊重新申請</a>
                                <?php
                                        }
                                    }else{
                                ?>
                                <a class="daifu_btn margin_r10 js_ajax_win" href="{:url('/index/daifu/apply_shop')}" data-width="600" data-height="420">{:__('Apply for super quotient')}</a>
                                <?php
                                    }
                            ?>
                                
                            <?php
                                }*/
                            ?>

                            <!--<a class="daifu_btn margin_r10 js_pay_type_btn" href="javascript:;" data-id="4">信用卡付款</a>-->


                            <div class="pad_t10 js_paytype_div bank_l clearfix" data-id="1">
                                <input type="text" name="bank_id" value="" is_required="true" class="js_bank_id" empty_tip="{:__('Please select the bank account number')}" style="position: absolute; width: 0; height: 0; opacity: 0">
                                <?php
                                    foreach($userbank as $ls){
                                ?>
                                    <a href="javascript:;" class="boxs bg_fff clearfix" data-id="<?=$ls['id']?>">
                                        <div class="icon ellipsis"><?=$ls['name']?>(<?=$ls['account_name']?>)</div>
                                        <div class="ellipsis text-center"><?=$ls['account_six']?></div>
                                    </a>
                                <?php
                                    }
                                ?>
                                <a href="{:url('/index/account/addBank')}" class="boxs bg_fff clearfix js_ajax_win" data-width="600" data-height="880">
                                    <div class="ellipsis text-center">點擊添加銀行卡</div>
                                </a>
                                <div><span class="color_999">超過30000金額可多選銀行</span></div>
                            </div>
                            <div class="pad_t10 js_paytype_div sc_tips clearfix hide" data-id="2">
                                <div>超商繳費程式碼將傳送之您的行動電話</div>
                                <div>超商將向您收取<span class="color_red">30TWD</span>的手續費，最低金額<span class="color_red">100TWD</span>,最高金額加手續費不超過<span class="color_red">6000TWD</span></div>
                            </div>

                            <div class="pad_t10 js_paytype_div sc_tips clearfix hide" data-id="3">
                                <div>这是超商ATM的相关介绍</div>
                            </div>
                        </div>
                    </div>
					
					<!--<div class="input_box margin_t10 charge_div js_img_tips_item5">
						<div class="input_tt">{:__('Invoice information')}:</div>
						<div class="input_rr pay_type">
							<input type="text" name="fp_type" value="1" is_required="true" class="js_fp_type_val" empty_tip="{:__('Please select the type of invoice')}" style="position: absolute; width: 0; height: 0; opacity: 0">
							<a class="daifu_btn margin_r10 js_fp_type" href="javascript:;" data-id="3">{:__('Donate to charity')}</a>
							<a class="daifu_btn margin_r10 js_fp_type" href="javascript:;" data-id="2">{:__('Electronic invoice')}</a>
							<a class="daifu_btn margin_r10 active js_fp_type" href="javascript:;" data-id="1">{:__('Electronic invoice1')}</a>
							
							

							<div class="fp_div pad_t10">
								<div class="js_fp_div" data-id="1_2">
									<div class="input_box" data-id="1">
										<div class="input_tt">{:__('Unified numbering')}：</div>
										<div class="input_rr">
											<input type="text" name="number" is_required="true" empty_tip="{:__('Please enter a unified number of 8 digits')}" placeholder="{:__('Please enter a unified number of 8 digits')}" value="<?=empty($invoice_json['number'])?'':$invoice_json['number']?>" maxlength="8">
										</div>
									</div>
									<div class="input_box" data-id="1">
										<div class="input_tt">{:__('Invoice rise')}：</div>
										<div class="input_rr">
											<input type="text" name="fp_title" is_required="true" empty_tip="{:__('Please enter the invoice payable')}" placeholder="{:__('Invoice rise')}" value="<?=empty($invoice_json['fp_title'])?'':$invoice_json['fp_title']?>" maxlength="20">
										</div>
									</div>
									<div class="input_box" >
										<div class="input_tt">{:__('Mail box')}：</div>
										<div class="input_rr">
											<input type="text" name="fp_email" is_required="true" empty_tip="{:__('Please enter your e-mail')}" data-type="email" maxlength="50" placeholder="{:__('Please enter your e-mail')}" value="<?=empty($invoice_json['fp_email'])?'':$invoice_json['fp_email']?>">
										</div>
									</div>
								</div>
								<div class="js_fp_div pad_t10 hide" data-id="3">
									{:__('Donation unit')}：{:__('OMG Caring for Society Fund')}  <span class="color_red">{:__('Donation is not available')}</span>
								</div>
								<div class="fp_tips js_fp_tips" data-id="3">
									{:__('E-invoice will be issued three days after')}<br />
									{:__('E-invoice will be issued three days after1')}<br />
									{:__('E-invoice will be issued three days after2')}
								</div>
								<div class="fp_tips js_fp_tips hide" data-id="2">
									{:__('E-invoice will be issued three days after3')}<br />
									{:__('E-invoice will be issued three days after1')}<br />
									{:__('E-invoice will be issued three days after2')}
								</div>
								<div class="fp_tips js_fp_tips hide" data-id="1">
									{:__('E-invoice will be issued three days after3')}<br />
									{:__('E-invoice will be issued three days after1')}<br />
									{:__('E-invoice will be issued three days after2')}
								</div>
							</div>
						</div>
					</div>-->
                    <div class="input_box  charge_div">
                        <div class="input_tt">手續費:</div>
                        <div class="input_rr">
                            <span class="color_red js_sxf_money">{$site.charge}</span>臺幣
                        </div>
                    </div>

                    <div class="input_box  charge_div">
                        <div class="input_tt">支付總額:</div>
                        <div class="input_rr">
                            <span class="color_red js_zzf_money">0</span>新臺幣
                        </div>
                    </div>
                    <div class="input_box  charge_div">
                        <div class="input_tt" style="height: 20px"></div>
                        <div class="input_rr pay_type">
                            <div class="fp_div ">

                                <div class="clearfix login_tips">
                                    <label class="moni_check_label">
                                        <a href="javascript:;" class="moni_check js_agree2 active">
                                        </a>
                                        <span class="la_btn">{:__('I have read and agreed')}</span> <a class="color_red js_article_win" data-width="1000" href="{:url('index/login/settlement')}">《{:__('Consent to Settlement Authorization')}》</a>
                                    </label>
                                </div>

                                <div class="text-left pad_t10 pad_b20">
                                    <a href="javascript:;" class="sub_btn small js_form_sub" data-func_before="before_sub" data-text="{:__('Confirm')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Confirm')}</a> 
                                    <a href="{:url('/index/daifu/cancel')}" class="color_666 pad_l20 js_ajax_confirm" tips="{:__('Do you confirm the cancellation?')}">{:__('Cancellation of this payment')}</a>
                                </div>

                            </div>
                        </div>
                    </div>


                    <!-- 这个有用，不要删 -->
                    <div class="hide">
                     <a href="{:url('/index/daifu/checkMobile')}" class="js_ajax_win js_check_mobile_link" data-width="520" data-height="390">手机验证</a>   
                    </div>

                    <a href="{:url('/index/user/editIdcard')}?hideclose=1" class="js_ajax_win hide js_one_auth" data-width="600"></a>
                    <a href="{:url('/index/user/editIdcardTwo')}?hideclose=1" class="js_ajax_win hide js_two_auth" data-width="600"></a>



                    <script type="text/javascript">

                      


                        
                        var is_check_mobile=false;

                        // 是否需要 验证手机号  1 需要 ， 其他 不需要
                        var is_need_check=0;

                        function before_sub(){
                            if(parseFloat($('.js_money_v[data-id="1"]').val())<parseFloat($('.js_min_buy').html())){
                                tip_show("最低购买数量为"+$('.js_min_buy').html()+'USDT','2');
                                return false;
                            }
                            if(!$('.login_tips .moni_check.js_agree2').hasClass('active')){
                                tip_show("{:__('Please agree to settlement')}",'2');
                                return false;
                            }
                            return true;
                        }
                        $(function(){
                            // 切换 地址
                            $('.js_address_type_btn').click(function(){
                                var _this=$(this);
                                var _id=_this.attr('data-id');
                                _this.addClass('active').siblings('a').removeClass('active');
                                $('.js_address_type_val').val(_id);
                                $('.js_addresstype_divs .tts').html(_this.html());
                                $('.js_addresstype_divs input').attr('placeholder','請認真核實'+_this.html()+'收貨地址')
                                
                            })
                            // 切换 付款方式
                            $('.js_pay_type_btn').click(function(){
                                var _this=$(this);
                                var _id=_this.attr('data-id');
                                _this.addClass('active').siblings('a').removeClass('active');
                                $('.js_paytype_div').addClass('hide');
                                $('.js_paytype_div[data-id="'+_id+'"]').removeClass('hide');
                                if(_id==1){
                                    $('.js_bank_id').attr('is_required',true)
                                }else{
                                    $('.js_bank_id').attr('is_required',false)
                                }
                                $('.js_pay_type_val').val(_id)
                            })
                            // 选择银行
                            // 选择银行
                            $('.js_paytype_div .boxs').click(function(){
                                var _money=$('.js_money_v[data-id="2"]').val()!=''?parseFloat($('.js_money_v[data-id="2"]').val()):0;
                                if(_money>30000){
                                    if(!$(this).hasClass('active')){
                                        $(this).addClass('active');
                                        
                                    }else{
                                        $(this).removeClass('active');
                                    }
                                    var _ids=[];
                                    for(var i=0;i<$('.js_paytype_div .boxs.active').length;i++){
                                        _ids.push($('.js_paytype_div .boxs.active').eq(i).attr('data-id'))
                                    }
                                    $('.js_bank_id').val(_ids)
                                }else{
                                    if(!$(this).hasClass('active')){
                                        $(this).addClass('active').siblings('.boxs').removeClass('active');
                                        $('.js_bank_id').val($(this).attr('data-id'))
                                    }
                                }
                                console.log( $('.js_bank_id').val())
                            })

                            var _ratetime=null;
                            $(document).on('keyup','.js_money_v',function(){
                                var _this=$(this);
                                var rate=parseFloat($('.js_rate_money').html());
                                var _type=_this.attr('data-id');
                                var money='';
                                if(_type==1){
                                    money=_this.val()==''?'':(parseFloat(_this.val())*rate)
                                }
                                if(_type==2){
                                    money=_this.val()==''?'':(parseFloat(_this.val())/rate)
                                }
                                money=money==''?'':parseInt(money);
                                var _otyps=_type==1?2:1;
                                $('.js_money_v[data-id="'+_otyps+'"]').val(money);
                                var sxf=parseFloat($('.js_sxf_money').html());
                                $('.js_zzf_money').html(parseInt(sxf+(money==''?0:money)))
                            })
							<?php
								if(!$user['is_card']){
							?>
									$('.js_one_auth').click();
							<?php
								}
								if($user['is_card']){
									if(!$user['is_card_img']){
							?>
										$('.js_two_auth').click();
							<?php
									}
								}
							?>
                        })
						// 选择发票
						$('.js_fp_type').click(function(){
							var _this=$(this);
							var _id=_this.attr('data-id');
							_this.addClass('active').siblings('a').removeClass('active');
							if(_id==1){
								$('.js_fp_div[data-id="1_2"]').removeClass('hide').find('input').attr('is_required',true);
								$('.js_fp_div .input_box[data-id="1"]').removeClass('hide');
								$('.js_fp_div[data-id="3"]').addClass('hide')
							}
							if(_id==2){
								$('.js_fp_div[data-id="1_2"]').removeClass('hide').find('input').attr('is_required',true);
								$('.js_fp_div .input_box[data-id="1"]').addClass('hide').find('input').attr('is_required',false);
								$('.js_fp_div[data-id="3"]').addClass('hide')
							}
							if(_id==3){
								$('.js_fp_div[data-id="1_2"]').addClass('hide').find('input').attr('is_required',false);
								$('.js_fp_div[data-id="3"]').removeClass('hide')
							}
							$('.js_fp_tips[data-id="'+_id+'"]').removeClass('hide').siblings('.js_fp_tips').addClass('hide')
							$('.js_fp_type_val').val(_id)
						})
						$('.js_fp_type').eq(0).click()
                    </script>
                </form>
            </div>
        </div>
    </div>
</div>



<script type="text/javascript">
    var account_list=[];
    $(function(){
    })

</script>
