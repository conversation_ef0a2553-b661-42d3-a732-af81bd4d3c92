<div class="container">
    <style type="text/css">
        .id_input input{border: 1px solid #eee; height: 36px; padding-left: 10px}
        .id_input a{ display: inline-block; height: 36px; line-height: 36px; margin-left: 10px; border-radius: 5px; color: #fff; background: #EF436D; padding: 0 12px; }
    </style>
    <div class="user_main margin_tb40 clearfix">
        <div class="user_main_line"></div>
        {include file="daifu/nav" /}
        <div class="pad_tb20 pad_lr20 over_hide daifu_success">

            <div class="daifu_status text-center pad_t20">
                <img src="__CDN__/assets/img/pc/ing_order.png" class="success_icon">
                <div class="daifu_text pad_tb20 color_666"><?=$order['order_status'][$order['order']['order_status']]?></div>
            </div>

            <table class="common_table table margin_t10 text-center">
                <tbody>
                    <tr>
                        <!-- colspan  有几个字段  给几  -->
                        <!-- colspan  有几个字段  给几  -->
                        <!-- colspan  有几个字段  给几  -->
                        <!-- colspan  有几个字段  给几  -->
                        <th class="l" colspan="7">

                            <div class="clearfix pad_lr10">
                                <span class="pull-right">{:__('Order number')}:<?=$order['order']['order_no']?></span>
                                {:__('Order details')}
                            </div>
                        </th>
                    </tr>
                    <tr>
                        <!-- 
                            具体字段请按实际情况减去 多余字段。
                        -->
                        <td>購買數量</td>
                        <td>價格</td>
                        <td>手續費</td>
                        <td class="color_red">支付總額</td>


                         <!-- 
                            具体字段请按实际情况减去 多余字段。
                        -->
                    </tr>
                    <tr class="color_666">
                        <td><?=$order['order']['number']?></td>
                        <td><span class="color_red">1</span>USDT=<span class="color_red"><?=$order['order']['exchange']?></span>臺幣</td>
                        <td><?=$order['order']['service'] + $order['order']['bank_service']?>臺幣</td>
                        <td class="color_red"><?=$order['order']['actual_money']?>TWD</td>
                    </tr>
                    <tr class="color_666">
                        <!-- colspan  有几个字段  给几  -->
                        <!-- colspan  有几个字段  给几  -->
                        <!-- colspan  有几个字段  给几  -->
                        <!-- colspan  有几个字段  给几  -->
                        <td colspan="7">支付總額計算公式:
							<?php
								if($order['order']['type']){
									echo $order['order']['number'] .'*'. $order['order']['exchange'] .'-'. $order['order']['service'] .'-'. $order['order']['bank_service'] .'='. $order['order']['actual_money'];
								}else{
									echo $order['order']['number'] .'*'. $order['order']['exchange'] .'-'. $order['order']['service'] .'+'. $order['order']['bank_service'] .'='. $order['order']['actual_money'];
								}
							?>
							TWD</td>
                    </tr>
                </tbody>
            </table>










            <table class="common_table table margin_t10 text-center">
                <tbody>
                    <tr>
                        <th class="l" colspan="2">
                            <div class="clearfix pad_lr10">
                                收幣地址
                            </div>
                        </th>
                    </tr>
                    <tr class="color_666">
                        <td width="50%"><div class="text-left pad_l10">類型</div></td>
                        <td><div class="text-left pad_l10">地址</div></td>
                    </tr>
                    <tr class="color_666">
                        <td width="50%"><div class="text-left pad_l10 color_red">
							<?=$bi_type[$order['order']['currency_type']]?>
						</div></td>
						<?php
							if($order['order']['type'] == '1'){
						?>
							<td><div class="text-left pad_l10"><?=$order['order']['site_address']?></div></td>
						<?php
							}else{
						?>
							<td><div class="text-left pad_l10"><?=$order['order']['address']?></div></td>
						<?php
							}
						?>
                        
                    </tr>
					<?php
						if($order['order']['type'] == '1'){
					?>
						<tr class="color_666">
							<td width="50%"><div class="text-left pad_l10 color_red">交易ID</div></td>
							<td>
								<?php
									if($order['order']['transaction_id']){
								?>
									<div class="text-left pad_l10">><?=$order['order']['transaction_id']?></div>
								<?php
									}else{
								?>
									<div class="text-left pad_l10 id_input"><input type="text" placeholder="請輸入交易ID"> <a href="javascript:;" class="js_sub_id" data-url="{:url('daifu/sub_id')}">提交</a></div>
								<?php
									}
								?>
							</td>
						</tr>
					<?php
						}
					?>
                    
                </tbody>
            </table>
            <div class="color_red pad_b10 f12">請確認收幣地址為<?=$bi_type[$order['order']['currency_type']]?>地址，否則會交易失敗！</div>

            

			<?php
				if($order['order']['pay_status'] == '1' || $order['order']['pay_status'] == '3'){
			?>
			 <table class="common_table table margin_t10 text-center">
				<tbody>
					<tr>
						<th class="l" colspan="3">
							<div class="clearfix pad_lr10">
								{:__('Payment information')}
							</div>
						</th>
					</tr>
					<tr class="color_666">
						<td>{:__('Payment method')}</td>
						<td>{:__('Bank account')}</td>
						<td>{:__('Account number')}</td>
					</tr>
					<?php
						if(!empty($order['bank'])){
						foreach($order['bank'] as $ls){
					?>
					<tr class="color_666">
						<td class="color_red">{:__('ATM Bank Transfer')}</td>
						
							<td><?=$ls['name']?></td>
							<td><?=$ls['account_name']?>-<?=$ls['account_six']?></td>
						<?php
								}
							}else{
						?>
							<td>-</td>
							<td>-</td>
					</tr>
					<?php
						}
					?>
				</tbody>
			</table>
			<div class="f12 color_999 tips_text pad_b20">*{:__('Please use your bank card account transfer and payment')}</div>
			<table class="common_table table margin_t10 text-center">
				<tbody>
					<tr>
						<th class="l" colspan="2">
							<div class="clearfix pad_lr10">
								{:__('Our Payment Bank Account')}
							</div>
						</th>
					</tr>
					<tr class="text-left" style="color:#f30e0e;">
						
						<td width="50%"><div class="pad_l10">{$site.bank_name}</div></td>
						<td>{:__('Account')}：{$site.bank_account}</td>
						
					</tr>
				</tbody>
			</table>
			<?php
				}
			?>

            <div class="color_red pad_b10 f12">[請先將款項轉到以上銀行帳戶後再提交付款資訊]</div>


            <!--<table class="common_table table margin_t10 text-center">
                <tbody>
                    <tr>
                        <th class="l" colspan="2">
                            <div class="clearfix pad_lr10">
                                付款方式
                            </div>
                        </th>
                    </tr>
                    <tr class="color_666">
                        <td width="50%"><div class="text-left pad_l10">類型</div></td>
                        <td><div class="text-left pad_l10">银行付款</div></td>
                    </tr>
                    <tr class="color_666">
                        <td width="50%"><div class="text-left pad_l10 color_red">金額</div></td>
                        <td><div class="text-left pad_l10">124TWD</div></td>
                    </tr>
                </tbody>
            </table>-->


            <div class="f12 color_999 tips_text pad_b20">
                <p>訂單處理倒數計時： <span class="color_red js_time_down" data-time="120"><span class="js_h">00</span>時<span class="js_m">00</span>分<span class="js_s">00</span>秒</span> 請耐心等待，我們正在為您處理中！</p>
                <p>我們正在處理您的這筆訂單，期間無需提醒客服人員！</p>
                <p>現在請您耐心等待處理倒數計時之後，通知收款方查帳。</p>
            </div>


        </div>
    </div>
</div>

<script type="text/javascript">

    // 倒计时
    function end_time(obj,fun) {
        // 个位数转 两位数  负数转 0
        var time_x = parseInt(obj.attr('data-time'))
        function c_data(str) {
            if (str < 0) {
                str = "00"
            } else {
                str = String(str);
                if (str.length == 1) {
                    str = "0" + str;
                }
            }
            return str;
        }
        var now_time = 0;
        updata_time()
        setInterval(function() {
            now_time++;
            updata_time()
        }, 1000);
        function updata_time(){
            var time_d = time_x - now_time; //更新时间差
            var hour = parseInt(time_d / 3600); // 小时
            var minute = parseInt(time_d % 3600 / 60); //分
            var second = parseInt(time_d % 3600 % 60); //秒
            obj.find('.js_h').html(c_data(hour));
            obj.find('.js_m').html(c_data(minute));
            obj.find('.js_s').html(c_data(second));
            if (time_d == 0 && fun.after_time) {
                fun.after_time();
            }
        }
    }

    $(function(){
        end_time($('.js_time_down'),{
            after_time:function(){
                window.location.reload();
            }
        });
        $(document).on('click','.js_ajax_cancel_df',function(){
            var _this=$(this);
            layer.confirm(_this.attr('tips'), {
              title:false,
              area:['500px', 'auto'],
              skin: 'my_confirm',
              btn: ["{:__('Confirm')}","{:__('Cancel')}"], //按钮
              btnAlign:'c'
            }, function(){
                 var _data={};
                _data={
                    id:_this.attr('data-id')
                }
                $.post(_this.attr('href'),_data,function(data){
                    if(data.code=="1"){
                        tip_show(data.msg,'1');
                        setTimeout(function(){
                            if(data.url){
                                window.location.href = data.url;
                            }else{
                                location.reload();
                            }
                        },1000*parseInt(data.wait))
                    }else{
                        tip_show(data.msg,'2');
                        return false
                    }
                })
            });
            return false; 
        })

        $('.js_sub_id').click(function(){
            var _this=$(this);
			var id = <?=$order['order']['id']?>;
            var _val=_this.parent('.id_input').find('input').val();
            if(_val==''){
                tip_show('請輸入交易ID','2');
                return false;
            }
            var _url=_this.attr('data-url');
            $.post(_url,{transaction_id:_val,id:id},function(res){
                if (res.code == "1") {
                    tip_show(res.msg, '1');
                    setTimeout(function(){
                        window.location.href=window.location.href
                    },1000*parseInt(res.wait))

                } else {
                    tip_show(res.msg, '2');
                }
            })
        })
    })
</script>