<div class="index_banner js_index_banner js_banner_head" style="background: url(__CDN__/assets/img/pc/index_coupon_banner.jpg) center center; background-size: cover;">
    <div class="index_banner_mark text-center">
        <div class="title js_need_active bottom_top_trans">有好货文案，有好货文案，有好货文案，有好货文案。</div>
        <div class="slogo_items js_need_active bottom_top_trans">
            <div>
                <img src="__CDN__/assets/img/pc/icon_banner01.png">
                {$site.Savings}
            </div>
            <div>
                <img src="__CDN__/assets/img/pc/icon_banner02.png">
                {$site.Delivery}
            </div>
            <div>
                <img src="__CDN__/assets/img/pc/icon_banner03.png">
                {$site.Simple}
            </div>
        </div>
        <div class="text-center coupon_search_banner start_enter js_need_active bottom_top_trans">
            <form method="get" action="">
				<input type="text" class="js_index_search_input" name="keyword" maxlength="20" placeholder="請輸入想要搜索的商品資訊">
				<a href="javascript:;">
					<img src="__CDN__/assets/img/pc/icon_search_white.png">
					</a>
            </form>
        </div>
    </div>
</div>
<div class="index_quanlist text-center pad_b30" style="margin-top: 10px;">
    <div class="container">
        <div class="clearfix quan_list pad_t20">
            <?php
			if($list){
				foreach($list as $ls){
			?>
				<div  class="box clearfix">
					<a href="{:url('goods/detail')}" class="img img_no pull-left">
						<div class="img_src" style="background-image: url(<?=$ls['pict_url']?>);"></div>
					</a>
					<a href="{:url('goods/detail')}" class="text over_hide">
						<div class="tt ellipsis-3">
							<?=$ls['title']?>
						</div>
						<div class="price color_red">
							<div><span>¥</span><?=$ls['zk_final_price']-$ls['coupon_amount']?></div>
                            <div class="o_price">￥<?=$ls['zk_final_price']?></div>
						</div>
					</a>
					<div class="buy_btns clearfix">
						<a href="{:url('goods/detail')}">
							<div class="bicon"><img src="__CDN__/assets/img/pc/icon_pay_gwc.png" alt="" class="icon1"><img src="__CDN__/assets/img/pc/icon_pay_gwc_hover.png" alt="" class="icon2"></div>
							<div class="h_text">立即購買</div>
						</a>
						<a href="{:url('goods/detail')}">
							<div class="bicon"><img src="__CDN__/assets/img/pc/icon_pay_gm.png" alt="" class="icon1"><img src="__CDN__/assets/img/pc/icon_pay_gm_hover.png" alt="" class="icon2"></div>
							<div class="h_text">立即購買</div>
						</a>
					</div>
				</div>
			<?php
				}
			}
			?>
        </div>
		<?php
			if($list){
				if($p == '1'){
					$up = '1';
					$down = $p + 1;
				}else{
					$up = $p - 1;
					$down = $p + 1;
				}
		?>
			<div class="new_pages text-center">
				<a href="<?=url('goods/index',array('p'=>$up))?>">{:__('Prev')}</a>
				<a href="<?=url('goods/index',array('p'=>$down))?>">{:__('Nexts')}</a>
			</div>
		<?php
			}else{
		?>
			{include file="common/nodata" /}
		<?php
			}
		?>
    </div>
</div>
<script type="text/javascript">
    $(function(){

        $('.js_index_search_input').focus(function(){
            $(this).attr('data-pla',$(this).attr('placeholder'));
            $(this).attr('placeholder','')
        })
        $('.js_index_search_input').blur(function(){
            $(this).attr('placeholder',$(this).attr('data-pla'));
        })
        $('.js_index_search_input').siblings('a').click(function(){
            $(this).parents('form').submit()
        })
    })
</script>