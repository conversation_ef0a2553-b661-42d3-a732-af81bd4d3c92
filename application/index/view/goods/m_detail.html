<style type="text/css">
	.header .close_b.close {
		display: block;
	}

	body {
		padding-bottom: 1.38rem;
	}

	.footer_new {
		display: none
	}

	.cover img {
		width: 100%;
	}

	.goods_detail .tt {
		font-size: 0.3rem;
		line-height: 0.48rem;
		font-weight: bold;
		word-break: break-all;
	}

	.goods_detail .text {
		color: #999;
	}

	.goods_detail .text .red {
		font-size: 0.54rem;
		color: #FE3D2D;
	}

	.under_l {
		text-decoration: line-through;
	}

	.skus .item {
		padding: 0.2rem 0;
		line-height: 0.6rem;
		color: #333;
	}

	.skus .item .t {
		color: #666;
		margin-right: 0.36rem;
	}

	.skus .item a {
		line-height: 0.42rem;
		margin: 4px;
		margin-left: 0;
		margin-right: 0.16rem;
		background: #F5F5F5;
		border-radius: 0.42rem;
		color: #666;
		font-size: 0.22rem;
		padding: 0 0.16rem;
	}

	.skus .item a.active {
		background: #EF436D;
		color: #fff;
	}

	.detail_tt {
		height: 1rem;
		font-size: 0.3rem;
		font-weight: bold;
	}

	.detail_tt .line {
		width: 3px;
		height: 0.3rem;
		background: #00A4FF;
		margin-right: 0.1rem;
	}

	.detail_content img {
		width: 100%;
		height: auto !important;
	}
	.foot_bt{width: 100%; height:1.38rem; position: fixed; left: 0; bottom: 0;  z-index: 20; padding:0 0.3rem;} 
	.foot_bt .sub_btn{width: 100%; line-height: 1rem;}
</style>
<div class="goods_detail">
	<div class="cover"><img src="__CDN__/assets/img/cache/img.jpg" alt=""></div>
	<div class="bg_fff pad_lr30 pad_tb20 margin_b20">
		<div class="tt">Redmi K40S 骁龙870 三星E4 AMOLED 120Hz直屏 OIS光学防抖 67W快充亮黑8GB+128GB5G智能手机 小米红米</div>
		<div class="text pad_t20">
			<span class="red margin_r20"><span>¥</span>1999.00</span>
			<span class="under_l">¥2999.00</span>
		</div>
	</div>
	<div class="bg_fff skus pad_lr30 margin_b20">
		<div class="item flex">
			<div class="t">重量</div>
			<div class="flex1">0.524kg</div>
		</div>
		<div class="common_border"></div>
		<div class="item flex">
			<div class="t">颜色</div>
			<div class="flex1 flex js_sku_s flex_w">
				<a href="javascript:;">亮黑</a><a href="javascript:;">银色</a><a href="javascript:;">金色</a>
			</div>
		</div>
		<div class="common_border"></div>
		<div class="item flex">
			<div class="t">版本</div>
			<div class="flex1 flex js_sku_s flex_w">
				<a href="javascript:;">6GB+128GB</a><a href="javascript:;">8GB+128GB</a><a
					href="javascript:;">8GB+128GB</a>
			</div>
		</div>
		<div class="common_border"></div>
	</div>
	<div class="bg_fff skus pad_lr30 margin_b20">
		<div class="item flex">
			<div class="t">聯繫方式</div>
			<div class="margin_a">183 9546 5792</div>
		</div>
	</div>
	<div class="bg_fff flex_ac detail_tt pad_lr30">
		<div class="line"></div>商品詳情
	</div>
	<div class="detail_content">
		<img src="__CDN__/assets/img/cache/detail1.jpg" alt="">
		<img src="__CDN__/assets/img/cache/detail2.jpg" alt="">
	</div>
	<div class="bg_fff foot_bt flex_ac">
		<a href="{:url('goods/buy')}" class="sub_btn js_ajax_win">立即購買</a>
	</div>
</div>

<script type="text/javascript">
	$(function() {
		$('.js_sku_s a').click(function() {
			$(this).addClass('active').siblings('a').removeClass('active')
		})

	})
</script>
