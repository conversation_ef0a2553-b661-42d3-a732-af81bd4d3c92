<style type="text/css">
    .body{ padding-bottom: 1rem }
	.index_search{ padding: 0; padding-bottom: 0.3rem }
	.index_search .s_input{ margin: 0 0.2rem; width: auto }
</style>
<div class="pad_t30">
  <div class="index_search">
      <div class="s_input ">
          <form action="<?=url('index/goods/index')?>" method="get">
              <input type="input" class="js_index_search_input" name="keyword" value="<?=$keyword?>" placeholder="請輸入想要搜索的商品資訊" maxlength="30">
              <a href="javascript:;"><img src="__CDN__/assets/img/pc/icon_search_red.png"></a>
          </form>
      </div>
   </div>
    <div class="clearfix quan_list pad_t20 js_scroll_more_list" data-url="{:url('index/goods/lists')}">
        {include file="goods/m_slists" /}
    </div>
    <!-- 上拉加载的 无数据我自己判断 -->
    <div class="loadding_d hide">
        <img src='__CDN__/assets/img/loading.png'>
        <span>{:__('Pull-up Load More')}</span>
    </div>
    <div class="js_no_data pad_tb30 hide">
        {include file="common/nodata" /}
    </div>
    <!-- 上拉加载的 无数据我自己判断 -->
</div>

<script type="text/javascript">
    $(function(){
        

          $('.js_index_search_input').focus(function(){
              $(this).attr('data-pla',$(this).attr('placeholder'));
              $(this).attr('placeholder','')
          })
          $('.js_index_search_input').blur(function(){
              $(this).attr('placeholder',$(this).attr('data-pla'));
          })
          $('.js_index_search_input').siblings('a').click(function(){
              $(this).parents('form').submit()
          })
    })
</script>