<div class="container">
    <div class="user_main margin_tb40 clearfix">
        <div class="user_main_line"></div>
        <div class="user_art_left js_user_new_left pull-left">
            <div class="n_tt">
                <img src="__CDN__/assets/img/pc/icon_left_list_up.png" class="arr pull-right">
                <img src="__CDN__/assets/img/pc/icon_left_list_help.png" class="icon">
                {:__('Help center')}
            </div>
				<?php
					foreach($one_nav as $key=>$ls){
				?>
					<div class="nav_click_item flex_ac">
						<div class="icon flex_ac_jc margin_r10">
							<img src="__CDN__/assets/img/pc/images/icon_business0<?=$key+1?>.png" class="img1" alt="">
							<img src="__CDN__/assets/img/pc/images/icon_business0<?=$key+1?>_red.png" class="img2" alt="">
						</div>
						<?=$ls['name']?>
						<div class="margin_a flex_ac_jc icon">
							<img src="__CDN__/assets/img/pc/images/nav_icon_more.png" class="img1" alt="">
							<img src="__CDN__/assets/img/pc/images/nav_icon_more_red.png" class="img2" alt="">
						</div>
					</div>
					<div class="n_tt_menu">
						<?php
						if(!empty($two_nav[$ls['id']])){
							foreach($two_nav[$ls['id']] as $k=>$val){
								if($val['key_id'] != '2'){
						?>
								<a href="<?=url('index/helps/index',array('set_type'=>$val['key_id']))?>" class="flex_ac <?php if($set_type == $val['key_id'])echo 'active'?>">
									<img style="height: 25px;" src="<?=$val['image']?>" class="item_icon" alt="">
									<?=$val['name']?>
								</a>
						<?php
									}
								}
							}
						?>
					</div>
				<?php
					}
				?>
        </div>
		<script>
        	if($('.n_tt_menu a.active').length>0){
        		$('.n_tt_menu a.active').parent('.n_tt_menu').css('display','block');
        		$('.n_tt_menu a.active').parent('.n_tt_menu').prev('.nav_click_item').addClass('active')
        	}
        	$('.js_user_new_left .nav_click_item').click(function(){
        		if($(this).hasClass('active')){
        			$(this).removeClass('active');
        			$(this).next('.n_tt_menu').stop().slideUp()
        		}else{
        			$(this).addClass('active').siblings('.nav_click_item').removeClass('active');
        			$(this).next('.n_tt_menu').stop().slideDown()
        			$(this).next('.n_tt_menu').siblings('.n_tt_menu').stop().slideUp();
        		}
        	})
        </script>
        <div class="pad_b20 over_hide">
            <div class="user_art_title clearfix">
                <div class="ellipsis">{:__('Frequently asked questions')}<img src="__CDN__/assets/img/pc/help_arr.png" class="margin_lr10"><span class="color_red"><?=$info['title']?></span></div>
            </div>
            <div class="article_div pad_lr30">
                <div class="content">
					<?=$info['newscontent']?>
                </div>
            </div>
        </div>
    </div>
</div>