<div class="index_banner js_index_banner js_banner_head" style="background: url(__CDN__/assets/img/pc/index_banner.jpg) center center; background-size: cover;">
    <div class="index_banner_mark text-center">
        <div class="title js_need_active bottom_top_trans">{:__('haigou slogo')}</div>
        <div class="slogo_items js_need_active bottom_top_trans">
            <div>
                <img src="__CDN__/assets/img/pc/icon_banner01.png">
                {$site.Savings}
            </div>
            <div>
                <img src="__CDN__/assets/img/pc/icon_banner02.png">
                {$site.Delivery}
            </div>
            <div>
                <img src="__CDN__/assets/img/pc/icon_banner03.png">
                {$site.Simple}
            </div>
        </div>

        <div class="text-center js_need_active start_enter bottom_top_trans">
            <a href="<?=url('index/login/register')?>" class="sub_btn">{:__('Start to save money forever')}</a>
        </div>
    </div>
</div>
<style>
    .pay_step .container .business{
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        width: 70%;
        margin: 40px auto 0 auto;
    }
    .pay_step .container .business-item{
        display: flex;
        flex-direction: row;
        align-items: center;
        cursor: pointer;
    }
    .pay_step .container .business-item img{
        margin-right: 5px;
    }
    .pay_step .container .business-item .hideimg{
        display: block;
    }
    .pay_step .container .business-item .showimg{
        display: none;
    }
    .pay_step .container .business-item-active .hideimg{
        display: none;
    }
    .pay_step .container .business-item-active .showimg{
        display: block;
    }
    .pay_step .container .business-item-active  span{
        color:#EF436D;
    }
    .pay_step .container .business-list{
        display: flex;
        flex-direction: row;
		justify-content:space-between;
        width: 80%;
        margin:40px auto;
    }
    .pay_step .container .business-list .business-list-item{
        width: calc(100% /8);
		
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color:#333333;
    }
    .pay_step .container .business-list .business-list-item:hover{
        color: #EF436D;
    }
    .pay_step .container .business-list .business-list-item img{
		width: 60px;
		height: 62px;
        margin-bottom: 10px;
    }
    .my-merchandise{
        margin-top:100px;
    }
    .my-merchandise .container .my-merchandise-label{
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 60px;
        display: flex;
        flex-direction: column;
        position: relative;
    }
    .my-merchandise .container .my-merchandise-label .labelwrap{
        background: #ffffff;
        height: 60px;
        width: fit-content;
        padding:0 30px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: absolute;
        left:50%;
        transform: translateX(-50%);
        z-index: 5;
    }
    .my-merchandise .container .my-merchandise-label::after{
        position: absolute;
        content: '';
        left:0;
        top:35px;
        height: 1px;
        width: 100%;
        background: #d9d9d9;
        z-index: 3; 
    }
    .my-merchandise .container .my-merchandise-label::before{
        position: absolute;
        top:35px;
        left:50%;
        transform: translateX(-50%);
        width: 340px;
        height: 1px;
        content: '';
        background: #EF436D;
        z-index: 4; 
    }
    .my-merchandise .container .my-merchandise-label .title{
        font-size: 24px;
    }
    .my-merchandise-bg{
        height: 800px;
        padding-top:200px;
        padding-bottom:200px;
        box-sizing: border-box;
        background: #ecf0f5;
        position: relative;
        z-index: 10;
    }
    .my-merchandise-bg .bgcover{
        position: absolute;
        left:0;
        top:0;
        width: 100%;
        height: 800px;
        z-index: -1;
    }

    .my-merchandise-bg .container .my-merchandise-label .labelwrap{
        background: #ecf0f5;
    }
    .my-merchandise-bg .container .flow-list-wrap{
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        width: 85%;
        justify-content: space-between;
        margin:150px auto 0 auto;
        position: relative;
    }
    .my-merchandise-bg .container .flow-list-wrap::after{
        position: absolute;
        left:4%;
        width: 90%;
        top:150px;
        content:'';
        height: 1px;
        background: #cccccc;
        z-index: -1;
    }
    .my-merchandise-bg .container .flow-list-wrap .flow-item{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }
    .my-merchandise-bg .container .flow-list-wrap .flow-item img{
        height: 113px;
    }
    .my-merchandise-bg .container .flow-list-wrap .flow-item .num{
        font-size: 16px;
        height: 25px;
        width: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 25px;
        border-radius: 50%;
        background: #ecf0f5;
        color: #cccccc;
        border: 1px solid #cccccc;
    }
    .my-merchandise-bg .container .flow-list-wrap .flow-item .hideimg{
        display: block;
        transition: all 0.5s;
    }
    .my-merchandise-bg .container .flow-list-wrap .flow-item .showimg{
        display: none;
        transition: all 0.5s;
    }
    .my-merchandise-bg .container .flow-list-wrap .flow-item .title{
        margin-top: 5px;
        color:#999999;
    }
    .my-merchandise-bg .container .flow-list-wrap .flow-item-active .title{
        margin-top: 5px;
        color: unset;
    }
    .my-merchandise-bg .container .flow-list-wrap .flow-item-active .num{
        color:#ef436d;
        border: 1px solid #ef436d;
    }
    .my-merchandise-bg .container .flow-list-wrap .flow-item-active .showimg{
        display: block!important;
        transition: all 0.5s;
    }
    .my-merchandise-bg .container .flow-list-wrap .flow-item-active .hideimg{
        display: none!important;
        transition: all 0.5s;
    }

</style>

<div class="pay_step js_need_active">
    <div class="container">
        <div class="title">
            我們的服務
        </div>
        <div class="line"></div>
        <div class="business">
            <div class="business-item business-item-active" type="1">
                <img src="__CDN__/assets/img/pc/images/icon_business01.png" class="hideimg" />
                <img src="__CDN__/assets/img/pc/images/icon_business01_red.png" class="showimg" />
                <span>商品代購專區</span>
            </div>
            <div class="business-item" type="2">
                <img src="__CDN__/assets/img/pc/images/icon_business02.png" class="hideimg"/>
                <img src="__CDN__/assets/img/pc/images/icon_business02_red.png" class="showimg" />
                <span>直播點數專區</span>
            </div>
            <div class="business-item" type="3">
                <img src="__CDN__/assets/img/pc/images/icon_business03.png" class="hideimg"/>
                <img src="__CDN__/assets/img/pc/images/icon_business03_red.png" class="showimg" />
                <span>遊戲點數專區</span>
            </div>
            <div class="business-item" type="4">
                <img src="__CDN__/assets/img/pc/images/icon_business04.png"  class="hideimg"/>
                <img src="__CDN__/assets/img/pc/images/icon_business04_red.png" class="showimg" />
                <span>其他代購專區</span>
            </div>
        </div>
        <div class="business-list bottom_top_trans js_need_active">
            <!-- <div class="business-list-item">
                <img src="__CDN__/assets/img/pc/images/img_busi_02zb_01dy.png" />
                <span>抖音代購</span>
            </div>
            <div class="business-list-item">
                <img src="__CDN__/assets/img/pc/images/img_busi_02zb_02hy.png" />
                <span>虎牙直播</span>
            </div>
            <div class="business-list-item">
                <img src="__CDN__/assets/img/pc/images/img_busi_02zb_03bx.png" />
                <span>比心直播</span>
            </div>
            <div class="business-list-item">
                <img src="__CDN__/assets/img/pc/images/img_busi_02zb_04yk.png" />
                <span>映客直播</span>
            </div>
            <div class="business-list-item">
                <img src="__CDN__/assets/img/pc/images/img_busi_02zb_05yy.png" />
                <span>YY直播</span>
            </div>
            <div class="business-list-item">
                <img src="__CDN__/assets/img/pc/images/img_busi_02zb_06yf.png" />
                <span>音符直播</span>
            </div>
            <div class="business-list-item">
                <img src="__CDN__/assets/img/pc/images/img_busi_02zb_07ks.png" />
                <span>一直播</span>
            </div>
            <div class="business-list-item">
                <img src="__CDN__/assets/img/pc/images/img_busi_02zb_08more.png" />
                <span>更多</span>
            </div> -->
			<div class="business-list-item">
                <img src="__CDN__/assets/img/pc/images/img_busi_02zb_08more.png" />
                <span>更多</span>
            </div>
        </div>
    </div>
</div>

<div class="my-merchandise js_need_active">
    <div class="container">
        <div class="my-merchandise-label bottom_top_trans js_need_active">
            <div class="labelwrap">
                <div class="title">
                    我們的商品
                </div>
                <div class="des">
                    購誠信，購時尚，購精彩
                </div>
            </div>
        </div>
        <div id="certify" class="bottom_top_trans js_need_active">
            <div class="swiper-container">
              <div class="swiper-wrapper">
                <div class="swiper-slide"><a href="<?=url('index/index/perfect')?>"><img src="__CDN__/assets/img/pc/images/nature-1.png" /></a></div>
                <div class="swiper-slide"><a href="<?=url('index/index/perfect')?>"><img src="__CDN__/assets/img/pc/images/nature-2.png" /></a></div>
                <div class="swiper-slide"><a href="<?=url('index/index/perfect')?>"><img src="__CDN__/assets/img/pc/images/nature-3.png" /></a></div>
                <div class="swiper-slide"><a href="<?=url('index/index/perfect')?>"><img src="__CDN__/assets/img/pc/images/nature-4.png" /></a></div>
                <div class="swiper-slide"><a href="<?=url('index/index/perfect')?>"><img src="__CDN__/assets/img/pc/images/nature-5.png" /></a></div>
              </div>
            </div>
            <div class="swiper-pagination"></div>
          </div>
    </div>
</div>


<div class="my-merchandise my-merchandise-bg js_need_active">
    <img class="bgcover" src="__CDN__/assets/img/pc/images/bg_jiyun.jpg" />
    <div class="container">
        <div class="my-merchandise-label bottom_top_trans js_need_active">
            <div class="labelwrap">
                <div class="title">
                    集運流程
                </div>
                <div class="des">
                    躍萬水千山  只為溫暖入戶
                </div>
            </div>
        </div>
        <div class="flow-list-wrap bottom_top_trans js_need_active">
            <div class="flow-item flow-item-active">
                <img src="__CDN__/assets/img/pc/images/img_jy_step_01.png"  class="hideimg"/>
                <img src="__CDN__/assets/img/pc/images/img_jy_step_01_red.png"  class="showimg"/>
                <div class="num">
                    1
                </div>
                <div class="title">
                    免費註冊會員
                </div>
            </div>
            <div class="flow-item">
                <img src="__CDN__/assets/img/pc/images/img_jy_step_02.png"  class="hideimg"/>
                <img src="__CDN__/assets/img/pc/images/img_jy_step_02_red.png"  class="showimg"/>
                <div class="num">
                    2
                </div>
                <div class="title">
                    到各大平臺購物
                </div>
            </div>
            <div class="flow-item">
                <img src="__CDN__/assets/img/pc/images/img_jy_step_03.png" class="hideimg" />
                <img src="__CDN__/assets/img/pc/images/img_jy_step_03_red.png" class="showimg" />
                <div class="num">
                    3
                </div>
                <div class="title">
                    購物網填寫付唄倉庫地址<br/>
                    東莞倉作收貨
                </div>
            </div>
            <div class="flow-item">
                <img src="__CDN__/assets/img/pc/images/img_jy_step_04.png" class="hideimg" />
                <img src="__CDN__/assets/img/pc/images/img_jy_step_04_red.png" class="showimg" />
                <div class="num">
                    4
                </div>
                <div class="title">
                    前往付唄倉庫地址網頁預報<br/>
                    快遞單號
                </div>
            </div>
            <div class="flow-item">
                <img src="__CDN__/assets/img/pc/images/img_jy_step_05.png" class="hideimg" />
                <img src="__CDN__/assets/img/pc/images/img_jy_step_05_red.png" class="showimg" />
                <div class="num">
                    5
                </div>
                <div class="title">
                    選擇要集運貨品收獲<br/>
                    方式和付款方式
                </div>
            </div>
            <div class="flow-item">
                <img src="__CDN__/assets/img/pc/images/img_jy_step_06.png" class="hideimg"/>
                <img src="__CDN__/assets/img/pc/images/img_jy_step_06_red.png" class="showimg"/>
                <div class="num">
                    6
                </div>
                <div class="title">
                    貨物到自提點後會議<br/>
                    whatsapp通知客戶送<br/>
                    貨上門會有快遞聯絡
                </div>
            </div>
        </div>
    </div>
</div>
<div class="index_foot">
    <div class="" style="max-width: 80%; margin: 0 auto">
        <div class="title">{:__('Join the Hi Shopping Band that millions')}</div>
        <div class="pp">{:__('Thank you very much for meeting us for a moment')}</div>
        <div class="text-center start_enter">
            <a href="<?=url('index/login/register')?>" class="sub_btn">{:__('Start to save money forever')}</a>
        </div>
    </div>
</div>

<script type="text/javascript">

    $(function(){

        // 倒计时
            // 补0
          function c_data(str) {
              if (str < 0) {
                  str = "00"
              } else {
                  str = String(str);
                  if (str.length == 1) {
                      str = "0" + str;
                  }
              }
              return str;
          }
          setInterval(function() {
              render_time()
          }, 1000);

          function render_time(){
            var js_time_down=$('.js_time_down');
            for(var i=0;i<js_time_down.length;i++){
              var obj=js_time_down.eq(i);
              var now_time=obj.attr('data-now')?parseInt(obj.attr('data-now')):1;
              obj.attr('data-now',now_time+1);
              var end_time_s=parseInt(obj.attr('data-time'));
              var time_d = end_time_s - now_time; //更新时间差
                  var hour = parseInt(time_d / 3600)%24; // 小时
                  var minute = parseInt(time_d % 3600 / 60); //分
                  var second = parseInt(time_d % 3600 % 60); //秒
                  var day=parseInt(time_d/3600/24);
                  obj.find('.js_d').html(day);
                  obj.find('.js_h').html(c_data(hour));
                  obj.find('.js_m').html(c_data(minute));
                  obj.find('.js_s').html(c_data(second));
            }
          }
          render_time();


        $('.js_index_search_input').focus(function(){
            $(this).attr('data-pla',$(this).attr('placeholder'));
            $(this).attr('placeholder','')
        })
        $('.js_index_search_input').blur(function(){
            $(this).attr('placeholder',$(this).attr('data-pla'));
        })
        $('.js_index_search_input').siblings('a').click(function(){
            $(this).parents('form').submit()
        })
        // 轮播图
        var modify;
        var certifySwiper = new Swiper('#certify .swiper-container', {
            watchSlidesProgress: true,
            slidesPerView: 'auto',
            centeredSlides: true,
            loop: true,
            loopedSlides: 5,
            autoplay: true,
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },
            on: {
                progress: function(progress) {
                    for (i = 0; i < this.slides.length; i++) {
                    var slide = this.slides.eq(i);
                    var slideProgress = this.slides[i].progress;

                    if (Math.abs(slideProgress) > 1) {
                        modify = (Math.abs(slideProgress) - 1) * 0.4 + 1;
                    }
                    translate = slideProgress * modify * 508 + 'px';
                    scale = 1 - Math.abs(slideProgress) / 5;
                    zIndex = 999 - Math.abs(Math.round(10 * slideProgress));
                    slide.transform('translateX(' + translate + ') scale(' + scale + ')');
                    slide.css('zIndex', zIndex);
                    slide.css('opacity', 1);
                    if (Math.abs(slideProgress) > 3) {
                        slide.css('opacity', 0);
                    }
                    }
                },
                setTransition: function(transition) {
                    for (var i = 0; i < this.slides.length; i++) {
                    var slide = this.slides.eq(i)
                    slide.transition(transition);
                    }

                }
            }

        })


        // 流程移动事事件
        $(".flow-list-wrap .flow-item").hover(function (){
           $(this).addClass('flow-item-active')
           $(this).siblings().removeClass('flow-item-active')
        },function (){  
            // $(this).removeClass('flow-item-active') 
        }); 
        // var list = [
        //        {
        //            url:'__CDN__/assets/img/pc/images/img_busi_02zb_01dy.png',
        //            href:'https://www.baidu.com',
        //            name:'抖音代購'
        //        },
        //        {
        //            url:'__CDN__/assets/img/pc/images/img_busi_02zb_02hy.png',
        //            href:'https://www.baidu.com',
        //            name:'抖音代購'
        //        },
        //        {
        //            url:'__CDN__/assets/img/pc/images/img_busi_02zb_03bx.png',
        //            href:'https://www.baidu.com',
        //            name:'比心直播'
        //        },
        //        {
        //            url:'__CDN__/assets/img/pc/images/img_busi_02zb_04yk.png',
        //            href:'https://www.baidu.com',
        //            name:'映客直播'
        //        },
        //        {
        //            url:'__CDN__/assets/img/pc/images/img_busi_02zb_05yy.png',
        //            href:'https://www.baidu.com',
        //            name:'YY直播'
        //        },
        //        {
        //            url:'__CDN__/assets/img/pc/images/img_busi_02zb_06yf.png',
        //            href:'https://www.baidu.com',
        //            name:'音符直播'
        //        },
        //        {
        //            url:'__CDN__/assets/img/pc/images/img_busi_02zb_07ks.png',
        //            href:'https://www.baidu.com',
        //            name:'一直播'
        //        }
        // ]

        // var more = [
        //     {
        //         url:'__CDN__/assets/img/pc/images/img_busi_02zb_08more.png',
        //         href:'https://www.baidu.com',
        //         name:'更多'
        //     }
        // ]
        function htmlTodo(type){
          
            // list.sort(function(){
            //     return Math.random()-0.5;
            // });
            //这里开始写ajax请求数据 type为请求不同的数据 数据格式参考list,最好是有8个长度 ，注释前面的代码是随机的
            $.ajax({ url: "{:url('/index/index/business')}?type="+type+"&status=1", success: function(data){
               data = JSON.parse(data)
               if(data.status == 1){
                    for(var i = 0,l = data.list.length;i<l;i++){
                        if(!data.list[i].image){
                            data.list[i].image = '__CDN__/assets/img/pc/images/img_busi_02zb_07ks.png'
                        }
                        if(!data.list[i].name){
                            data.list[i].name = '一直播'
                        }
                        if(!data.list[i].nickname){
                            data.list[i].nickname = 'https://www.baidu.com'
                        }
                    }
                    var htmlSource = data.list
                    var html = ''
                    for(var i = 0,l = htmlSource.length;i<l;i++){
                        html += '<a class="business-list-item" href="'+htmlSource[i].nickname+'">'
                        html += '<img src="'+htmlSource[i].image+'" />'
                        html += '<span>'+htmlSource[i].name+'</span>'
                        html += '</a>'
                    }
                    $('.business-list').html(html)
               }
                
            }});

        }
        htmlTodo(1)
        //业务移动事件
        $(".business .business-item").hover(function (){
            var type = $(this).attr('type')
            htmlTodo(type)
           $(this).addClass('business-item-active')
           $(this).siblings().removeClass('business-item-active')
        },function (){  
            // $(this).removeClass('business-item-active') 
        });  
    })
</script>
