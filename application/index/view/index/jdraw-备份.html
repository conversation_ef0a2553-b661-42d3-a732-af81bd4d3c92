<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <!-- 引入样式 -->
    <link rel="stylesheet" href="__CDN__/assets/css/element-ui-index.css">
    <style>
        * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        html {
            background-color: #787878;
        }

        .main-box{
            height: 100vh;width:100vw;background: rgba(0,0,0,0.6);position: fixed;top: 0;left: 0;z-index: 100;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .pointer {
            cursor: pointer;
        }

        .el-dialog {
            border-radius: 16px;
            overflow: hidden;
        }

        .activity-register {
            /*关闭按钮样式同步这里宽高和margin-top*/
            /* margin-top: 100px; */
            position: relative;
            width: 867px;
            height: 780px;
            background: linear-gradient(180deg, #EC9CD0 0%, #FFFEFE 100%);
            border: 1px solid #FFF4D5;
            border-radius: 32px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .title {
            margin-top: 31px;
            font-size: 48px;
            font-weight: 500;
            line-height: normal;
            text-align: center;
            color: #FFFFFF;
        }

        .title2-box {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .title2 {
            font-size: 20px;
            line-height: 22px;
            text-align: center;
            color: #EF436D;
        }

        .label-line {
            width: 3px;
            height: 24px;
            transform: rotate(-90deg);
            border-radius: 16px;
            background: #EF436D;
        }

        .center-box {
            width: 786px;
            height: 340px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.52);
            /*padding: 28px;*/
        }

        .bottom-btn {
            width: 180px;
            height: 48px;
            line-height: 48px;
            text-align: center;
            color: #FFFFFF;
            font-size: 20px;
            border-radius: 290px;
            background: #EF436D;
            position: absolute;
            bottom: 32px;
            left: 50%;
            transform: translateX(-50%);
        }

        .bgimg1 {
            position: absolute;
            width: 144px;
            height: 144px;
            left: 0;
            top: 10px;
        }

        .bgimg2 {
            position: absolute;
            width: 90px;
            height: 144px;
            right: 40px;
            bottom: 10px;
        }

        /* 抽奖大转盘样式 */
        .lottery-container {
            width: 100%;
            max-width: 600px;
            margin: 0 auto;
        }

        .lottery-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 10px;
            position: relative;
            width: 320px;
            max-width: 500px;
            height: 320px;
            margin: 0 auto;
            background-color: #FFF9E6;
            padding: 10px;
            border-radius: 15px;
            border: 8px solid #FF9800;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .prize-dialog-content {
            text-align: center;
            padding: 20px;
        }

        .err-dialog-content {
            text-align: center;
        }

        .prize-dialog-icon {
            width: 80px;
            height: 80px;
            object-fit: contain;
            margin-bottom: 15px;
        }

        .lottery-item {
            background-color: #FFFCF0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            border: 2px solid #FFD699;
            position: relative;
            flex-direction: column;
            padding: 5px;
        }

        .lottery-item.active {
            background-color: #fb9037;
            transform: scale(1.05);
            box-shadow: 0 0 15px rgba(255, 215, 0, 0.7);
            border-color: #FF9800;
            z-index: 5;
        }

        .lottery-content {
            text-align: center;
            padding: 5px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        .prize-icon {
            min-height: 34px;
            font-size: 16px;
            color: #f17e86;
            font-weight: bold;
        }

        .prize-name {
            font-size: 10px;
            color: #f17e86;
            margin-top: 5px;
        }

        .lottery-button {
            background-color: #FF5722;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-weight: bold;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.3s;
            z-index: 10;
            grid-column: 2;
            grid-row: 2;
            font-size: 18px;
            border: 4px solid #FFC107;
        }

        .lottery-button:hover {
            background-color: #E64A19;
            transform: scale(1.05);
        }

        .lottery-button:active {
            transform: scale(0.95);
        }

        /* 活动提示 */
        .tips-item {
            width: 786px;
            display: flex;
            align-items: center;
            color: #333333;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .tips-dot {
            width: 8px;
            height: 8px;
            background: rgba(242, 105, 138, 0.6);
            border-radius: 50%;
            margin: 0 10px;
        }

        .custom-notice-bar {
            width: 786px;
            height: 36px;
            display: flex;
            align-items: center;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.52);;
            border-radius: 8px;
            margin: 10px 0;
        }

        .notice-icon {
            width: 20px;
            height: 20px;
            margin-right: 8px;
        }

        .notice-icon img{
            width: 100%;
            height: 100%;
        }

        .notice-content-wrapper {
            flex: 1;
            overflow: hidden;
            position: relative;
        }

        .notice-content {
            display: inline-block;
            white-space: nowrap;
        }

        .marquee {
            animation: marquee linear infinite;
        }

        @keyframes marquee {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-50%);
            }
        }

        /* 保证动画在缩放时流畅 */
        .notice-content {
            will-change: transform;
        }

        .integral-box{
            width: 100%;
            height: 60px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            /*align-items: center;*/
        }

        .integral{
            color: #ff0000;
            font-size: 16px;
            font-weight: bold;
        }
    </style>
    <script src="__CDN__/assets/js/vue.js"></script>

    <!-- 引入组件库 -->
    <script src="__CDN__/assets/js/element-ui-index.js"></script>
</head>

<body>
<div id="app" class="activity-register">
    <!--    顶部关闭-->
    <a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>

    <div class="title"><?=$dr['title']?></div>
    <div class="title2-box" style="margin: 32px 0 24px 0;">
        <div class="title2">參與抽獎</div>
        <div class="label-line"></div>
    </div>
    <div class="center-box">
        <div class="section" :class="{ active: currentSection === 2 }">
            <div class="features flex-jc-ac flex-col">
                <div class="lottery-container">
                    <div class="lottery-grid">
                        <!-- 第一行 -->
                        <div class="lottery-item" :class="{ active: currentPrizeIndex === 0 && isRotating }">
                            <div class="lottery-content">
                                <p class="prize-icon">{{prizes[0].icon}}</p>
                                <p class="prize-name">{{ prizes[0].name }}</p>
                            </div>
                        </div>
                        <div class="lottery-item" :class="{ active: currentPrizeIndex === 1 && isRotating }">
                            <div class="lottery-content">
                                <p class="prize-icon">{{prizes[1].icon}}</p>
                                <p class="prize-name">{{ prizes[1].name }}</p>
                            </div>
                        </div>
                        <div class="lottery-item" :class="{ active: currentPrizeIndex === 2 && isRotating }">
                            <div class="lottery-content">
                                <p class="prize-icon">{{prizes[2].icon}}</p>
                                <p class="prize-name">{{ prizes[2].name }}</p>
                            </div>
                        </div>

                        <!-- 第二行 -->
                        <div class="lottery-item" :class="{ active: currentPrizeIndex === 7 && isRotating }">
                            <div class="lottery-content">
                                <image src="__CDN__/assets/img/pc/new_index/red.png" style="width: 34px;height: 34px;" mode="scaleToFill" />
                                <p class="prize-name">{{ prizes[7].name }}{{prizes[7].icon}}</p>
                            </div>
                        </div>
                        <div class="lottery-button" @click="startLottery" :disabled="isRotating">
                            <span v-if="!isRotating">抽奖</span>
                            <span v-else>抽奖中...</span>
                        </div>
                        <div class="lottery-item" :class="{ active: currentPrizeIndex === 3 && isRotating }">
                            <div class="lottery-content">
                                <p class="prize-icon">{{prizes[3].icon}}</p>
                                <p class="prize-name">{{ prizes[3].name }}</p>
                            </div>
                        </div>

                        <!-- 第三行 -->
                        <div class="lottery-item" :class="{ active: currentPrizeIndex === 6 && isRotating }">
                            <div class="lottery-content">
                                <p class="prize-icon">{{prizes[6].icon}}</p>
                                <p class="prize-name">{{ prizes[6].name }}</p>
                            </div>
                        </div>
                        <div class="lottery-item" :class="{ active: currentPrizeIndex === 5 && isRotating }">
                            <div class="lottery-content">
                                <p class="prize-icon">{{prizes[5].icon}}</p>
                                <p class="prize-name">{{ prizes[5].name }}</p>
                            </div>
                        </div>
                        <div class="lottery-item" :class="{ active: currentPrizeIndex === 4 && isRotating }">
                            <div class="lottery-content">
                                <p class="prize-icon">{{prizes[4].icon}}</p>
                                <p class="prize-name">{{ prizes[4].name }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 中奖弹窗 -->
                <el-dialog title="恭喜中奖" :visible.sync="prizeDialogVisible" width="200px" center :show-close="false">
                    <div class="prize-dialog-content">
                        <p class="prize-icon"><strong>{{ selectedPrize.icon }}</strong></p>
                        <p style="margin-top: 20px;">恭喜您获得了 <strong>{{ selectedPrize.name }}</strong></p>
                    </div>
                    <span slot="footer" class="dialog-footer">
                            <el-button type="success" @click="prizeDialogVisible = false">確定</el-button>
                        </span>
                </el-dialog>

                <!-- 错误提示弹窗 -->
                <el-dialog title="提示" :visible.sync="errDialogVisible" width="200px"  center top="35vh" :show-close="false">
                    <div class="errDialogContent">
                        <p>{{ errMsg }}</p>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="success" @click="errDialogVisible = false">確定</el-button>
                    </span>
                </el-dialog>

                <!--購物金抽奖提示-->
                <el-dialog :show-close="false" title="提示" :visible.sync="isDialogVisible" center top="35vh"
                           width="266px">
                    <div class="subTitle">{{gwMsg}}</div>
                    <div class="integral-box">
                        <div>當前共<span class="integral"> {{totalGw}} </span>購物金</div>
                        <div>是否消耗<span class="integral"> {{useGw}} </span>購物金進行抽獎</div>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-button size="mini" @click="isDialogVisible = false">取 消</el-button>
                        <el-button size="mini" type="success" @click="queryGw">确 定</el-button>
                    </span>
                </el-dialog>

                <el-dialog title="提示" :visible.sync="gwDialogVisible" width="200px"  center top="35vh" :show-close="false">
                    <div class="errDialogContent">
                        <span>{{gwMsg}},<span v-if="gwStatus">剩餘{{totalGw}}購物金</span></span>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <el-button type="success" @click="gwDialogVisible = false">確定</el-button>
                    </span>
                </el-dialog>
            </div>
        </div>
    </div>

    <div class="custom-notice-bar">
        <div class="notice-icon">
            <img src="__CDN__/assets/img/pc/new_index/broadcast.png" alt="">
        </div>
        <div class="notice-content-wrapper" ref="wrapper">
            <div
                    class="notice-content"
                    :class="{ 'marquee': shouldScroll }"
                    :style="scrollStyle"
                    @mouseenter="pauseScroll"
                    @mouseleave="resumeScroll"
            >
          <span v-for="(notice, index) in duplicatedNotices" :key="index">
            {{ notice }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          </span>
            </div>
        </div>
    </div>

    <div class="title2-box" style="margin: 26px 0 24px 0">
        <div class="title2">活動注意事項</div>
        <div class="label-line"></div>
    </div>
    <div class="tips-box">
        <div class="tips-item">
            <div class="tips-dot"> </div>
            <div class="tips-text">
                每位會員每日限抽 <?=$dr['freec']?> 次，請於當日使用機會；
            </div>
        </div>
        <div class="tips-item">
            <div class="tips-dot"> </div>
            <div class="tips-text">
                折抵券僅限單筆訂單使用，不得與其他折扣同時使用；
            </div>
        </div>
        <div class="tips-item">
            <div class="tips-dot"> </div>
            <div class="tips-text">
                優惠券有效期：自發放日起 30 天內有效  。
            </div>
        </div>
    </div>

    <!-- 背景图 -->
    <image src="__CDN__/assets/img/pc/new_index/bgimg1.png" class="bgimg1" mode="scaleToFill" />
    <image src="__CDN__/assets/img/pc/new_index/bgimg2.png" class="bgimg2" mode="scaleToFill" />
</div>
<!--改成本地资源-->
<script src="__CDN__/assets/js/axios.min.js"></script>
<script>
    var dr = <?php echo json_encode($dr['id']); ?>;
    var list = <?php echo json_encode($list); ?>;
    var data = [];
    for(var i=0; i<list.length;i++){
        var money = Math.round(list[i]['money']);
        if(money >= 10000){
            var str = (money/10000) + "万";
            data[i] = { id:list[i]['id'],name: list[i]['title'], icon: 'NT$' + str};
        }else{
            data[i] = { id:list[i]['id'],name: list[i]['title'], icon: 'NT$' + money};
        }
    }

    var ulist = <?php echo json_encode($ulist); ?>;
    var udata = [];
    for(var i=0; i<ulist.length;i++){
        udata[i] = "恭喜"+ulist[i]['username']+"用户抽中【"+ulist[i]['title']+"】";
    }
    const app = new Vue({
        el: '#app',
        data: {
            currentSection: 0,
            isScrolling: false,
            prizes: data,
            currentPrizeIndex: 0,
            isRotating: false,
            rotationTimer: null,
            rotationSpeed: 100,
            rotationCount: 0,
            selectedPrize: {},
            isDialogVisible: false,
            prizeDialogVisible: false,
            errDialogVisible: false,
            gwDialogVisible: false,
            errMsg: '',
            notices: udata,
            contentWidth: 0,
            wrapperWidth: 0,
            isHovering: false,
            gwMsg:'',
            totalGw: '',
            useGw:'',
            drawId:'',
            gwStatus:false,
            dr:dr,
        },
        beforeDestroy() {
            clearTimeout(this.rotationTimer);

            // 移除窗口大小变化事件监听器
            window.removeEventListener('resize', this.calculateWidth);
        },
        computed: {
            // 复制内容实现无缝滚动
            duplicatedNotices() {
                return [...this.notices, ...this.notices];
            },
            shouldScroll() {
                return this.contentWidth > this.wrapperWidth;
            },
            scrollStyle() {
                return {
                    animationDuration: `${this.contentWidth / 50}s`,
                    animationPlayState: this.isHovering ? 'paused' : 'running'
                }
            }
        },
        mounted() {
            console.log(this.prizes, 999);
            this.$nextTick(() => {
                this.calculateWidth();
                window.addEventListener('resize', this.calculateWidth);
            });
        },
        methods: {

            calculateWidth() {
                this.wrapperWidth = this.$refs.wrapper.offsetWidth;
                this.contentWidth = this.$refs.wrapper.querySelector('.notice-content').scrollWidth / 2;
            },
            pauseScroll() {
                this.isHovering = true;
            },
            resumeScroll() {
                this.isHovering = false;
            },

            scrollToNext() {
                if (this.currentSection < 3) {
                    this.scrollToSection(this.currentSection + 1)
                }
            },
            onResize() {
                // 处理窗口大小变化
            },

            startLottery() {
                this.isDialogVisible = false;
                // 重置旋转速度和计数
                this.rotationSpeed = 100;
                this.rotationCount = 0;

                if (this.isRotating) return;
                this.isRotating = true;
                axios({
                    method: 'post',
                    url: '/index/index/jdraw',
                    data: { dr: this.dr }
                }).then(res => {
                    if(res.data.rs === 0) {
                        this.errMsg = res.data.msg;
                        this.errDialogVisible = true;
                        this.isRotating = false;
                    } else if(res.data.rs === 2) {
                        this.drawId = res.data.id;
                        this.gwMsg = res.data.msg;
                        this.totalGw = res.data.gw;
                        this.useGw = res.data.sub;
                        this.isDialogVisible = true;
                        this.isRotating = false;
                    } else if(res.data.rs === 1) {
                        this.handlePrizeResult(res.data.id);
                    }
                }).catch(error => {
                    this.isRotating = false;
                    this.errMsg = '抽獎失敗，請重試';
                    this.errDialogVisible = true;
                });
            },

            // 旋转奖品
            rotatePrize() {
                this.rotationTimer = setTimeout(() => {
                    // 更新当前高亮的奖品索引
                    this.currentPrizeIndex = (this.currentPrizeIndex + 1) % this.prizes.length
                    this.rotationCount++
                    // 根据旋转次数调整速度
                    if (this.rotationCount > 10) {  // 减少旋转次数
                        this.rotationSpeed += 20  // 加快速度增长
                    }
                    // 判断是否停止旋转
                    if (this.rotationSpeed >= 150 && this.currentPrizeIndex === this.prizes.indexOf(this.selectedPrize)) {
                        this.isRotating = false
                        this.prizeDialogVisible = true
                        clearTimeout(this.rotationTimer)
                    } else {
                        this.rotatePrize()
                    }
                }, this.rotationSpeed)
            },

            // 抽奖逻辑
            handlePrizeResult(prizeId) {
                this.prizes.forEach((item, index) => {
                    if (item.id === prizeId) {
                        this.selectedPrize = this.prizes[index];
                        this.rotatePrize();
                    }
                });
            },
            confirmLottery() {
                this.isDialogVisible = false;
                this.isRotating = true; // 重新开始旋转状态
                this.handlePrizeResult(this.drawId);
            },

            // 查询购物金
            queryGw() {
                this.isDialogVisible = false;
                if(this.totalGw >= this.useGw) {
                    axios({
                        method: 'post',
                        url: '/index/index/get_draw_cout',
                        data: {
                            dr: this.dr,
                            sub: this.useGw,
                        }
                    }).then(res => {
                        console.log('res+++++++++', res);
                        this.gwMsg = res.data.msg;
                        this.totalGw = res.data.gw;
                        this.gwStatus = res.data.rs;
                        if (res.data.rs === 1) {
                            this.gwDialogVisible = true;
                            this.startLottery();
                        } else {
                            this.gwDialogVisible = true;
                            this.isRotating = false;
                        }
                    }).catch(error => {
                        console.error(error);
                        this.isRotating = false;
                        this.gwMsg = '请求失败，请重试';
                        this.gwDialogVisible = true;
                    });
                } else {
                    this.gwMsg = '您的購物金餘額不足';
                    this.gwDialogVisible = true;
                    this.isRotating = false;
                }
            }
        }
    })
</script>
</body>

</html>