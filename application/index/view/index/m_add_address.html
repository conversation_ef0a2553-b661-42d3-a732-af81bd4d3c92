<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 pad_b40" style="overflow: visible;">
    <div class="color_red pad_t10 my_close_win_title">恭喜您中獎啦！請選擇收貨地址吧！</div>
    <form action="{:url('index/index/add_address')}" autocomplete="off" class="edit_info_form small_input">
        <div class="input_box full_width">
            <div class="input_tt">姓名：</div>
            <div class="input_rr">
                <input type="text" name="name" maxlength="15" value="" is_required="true" placeholder="請輸入" empty_tip="請輸入姓名">
            </div>
        </div>
        <div class="input_box full_width">
            <div class="input_tt">聯繫方式：</div>
            <div class="input_rr">
                <input type="text" name="mobile" data-type="mobile" maxlength="10" value="" is_required="true"  placeholder="請輸入聯繫方式">
            </div>
        </div>
        <div class="input_box full_width" style="overflow: visible; ">
            <div class="input_tt">選擇地區：</div>
            <div class="input_rr" style="overflow: visible; ">
                <div class="moni_select js_province" style="width: 100% ">
                    <input type="text" name="address" value="" is_required="true" empty_tip="請選擇地區">
                    <div class="moni_selected clearfix">
                        <img class="arr" src="__CDN__/assets/img/arr.png">
                        <span>請選擇</span>
                    </div>
                    <div class="moni_s_down">
                        
                    </div>
                </div>
            </div>
        </div>

        <div class="input_box full_width" style="overflow: visible; ">
            <div class="input_tt">選擇地區：</div>
            <div class="input_rr" style="overflow: visible; ">
                <div class="moni_select js_city" style="width: 100% ">
                    <input type="text" name="city" value="" is_required="true" empty_tip="請選擇地區">
                    <div class="moni_selected clearfix">
                        <img class="arr" src="__CDN__/assets/img/arr.png">
                        <span>請選擇</span>
                    </div>
                    <div class="moni_s_down">
                        
                    </div>
                </div>
            </div>
        </div>

        <div class="input_box full_width">
            <div class="input_tt">詳細地址：</div>
            <div class="input_rr">
                <input type="text" name="detail" maxlength="30" value="" is_required="true" placeholder="請輸入詳細地址">
            </div>
        </div>
        <div class="pad_t30 text-center">
            <a href="<?=url('index/sele_address')?>" class="sub_btn2 dark js_ajax_win js_sele" >返回上一步</a>
			<a href="javascript:;" class="sub_btn2 js_form_sub" data-func_name="sub_after" data-text="添加新地址" data-loadding="添加中···" data-type="func">添加新地址</a>
        </div>
    </form>
</div>
<style type="text/css">
    body .sub_btn2{
        color: #fff;
        display: inline-block;
        width: 100px;
        line-height: 28px;
        border-radius: 2px;
        text-align: center;
        color: #fff;
        background: #EF436D;
        border: 1px solid #EF436D;
        margin-right: 10px;
         font-size: 12px;
    }
    body .sub_btn2.disabled{ opacity: 0.7 }
    body .sub_btn2.dark{border-color: #DDDDDD; color: #999999; background: none}
    .small_input .moni_select{height: 34px;}
    .small_input .moni_select .moni_selected{height: 32px; line-height: 32px}
    .small_input .moni_select .moni_s_down{top: 34px}
    .small_input .moni_select .moni_selected img.arr{margin-top: 13px}
    .input_box .input_tt{font-size: 0.24rem; line-height: 0.6rem}
    .input_box input{height: 32px; font-size: 0.24rem}
    .moni_select .moni_selected span{font-size: .24rem}
</style>
<script type="text/javascript">

    var address=<?=$address?>;
    var html='';
    for(var i=0;i<address.length;i++){
        html+='<a href="javascript:;" value="'+address[i].id+'">'+address[i].name+'</a>'
    }
    $('.js_province .moni_s_down').html(html);

    $(document).on('click','.js_province .moni_s_down a',function(){
        var index=$('.js_province .moni_s_down a').index(this);
        $('.js_city .moni_selected span').html('請選擇');
        $('.js_city input').val('')
        var html2='';
        for(var i=0;i<address[index].list.length;i++){
            html2+='<a href="javascript:;" value="'+address[index].list[i].id+'">'+address[index].list[i].name+'</a>'
        }
        $('.js_city .moni_s_down').html(html2);
    })

    function sub_after(){
        $('.js_sele').click()
    }
</script>