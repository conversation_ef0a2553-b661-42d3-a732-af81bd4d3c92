<div class="body">
    <style>
        body {
            padding-bottom: 1rem;
            background: #fff;
        }

        .head_login_btn {
            margin-left: auto;
        }

        .header .logo img {
            margin-top: 0;
        }

        .head_login_btn a {
            font-size: 0.24rem;
            color: #666666;
            text-decoration: underline;
            line-height: 0.44rem;
            font-weight: normal;
        }

        .head_login_btn a.b {
            background: #EF436D;
            margin-left: 0.24rem;
            color: #fff;
            padding: 0 0.2rem;
            border-radius: 0.44rem;
            text-decoration: none;
        }
    </style>
    <style>
        .mySwiper {
            width: 100%;
            height: 4rem;
        }

        .mySwiper .swiper-slide {
            text-align: center;
            background: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .mySwiper .swiper-slide img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .mySwiper .swiper-pagination-bullet {
            width: 10px;
            height: 10px;
            text-align: center;
            line-height: 10px;
            font-size: 12px;
            color: #000;
            opacity: 1;
            background: rgba(0, 0, 0, 0.2);
        }

        .mySwiper .swiper-pagination-bullet-active {
            color: #fff;
            background: #007aff;
        }

        .swiper_point {
            text-align: right;
            padding-right: 10px;
        }

        .swiper_point .swiper-pagination-bullet {
            background: rgba(241, 1, 7, 0.3);
            transform: scale(0.5);
        }

        .swiper_point .swiper-pagination-bullet.swiper-pagination-bullet-active {
            background: url('__CDN__/assets/img/wap/new_index/next.png');
            transform: scale(1);
            background-size: 100%;
        }

        .home_tt {
            font-size: 0.32rem;
            padding: 0.4rem 0;
            font-weight: bold;
            color: #3D3D3D;
            line-height: 1;
            text-align: center;
        }

        .home_tt .line {
            width: 0.96rem;
            height: 3px;
            background: #EF436D;
            margin: 0 auto;
            margin-top: 0.2rem;
            border-radius: 3px;
        }

        .btn_items {
            padding: 0 0.16rem;
        }

        .btn_items a {
            width: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 0.2rem;
        }

        .btn_items a img {
            width: 95%;
        }

        .djcy {
            font-size: 0.24rem;
            line-height: 0.44rem;
            font-weight: normal;
            border: 1px solid #EF436D;
            margin-left: 0.24rem;
            color: #EF436D;
            padding: 0 0.2rem;
            border-radius: 0.44rem;
        }
    </style>
    <div class="swiper mySwiper">
        <div class="swiper-wrapper">
<!--            <div class="swiper-slide"><img src="__CDN__/assets/img/wap/new_index/banner1.jpg" alt=""/></div>-->
<!--            <div class="swiper-slide"><img src="__CDN__/assets/img/wap/new_index/banner2.jpg" alt=""/></div>-->
<!--            <div class="swiper-slide"><img src="__CDN__/assets/img/wap/new_index/banner3.jpg" alt=""/></div>-->
<!--            <div class="swiper-slide"><img src="__CDN__/assets/img/wap/new_index/banner4.jpg" alt=""/></div>-->
            {volist name="banner" id="item"  }
            <tr>
                <div class="swiper-slide"><img src="__CDN__{$item['path_image']}" alt="" /></div>
            </tr>
            {/volist}
        </div>
        <div class="swiper-pagination swiper_point"></div>
    </div>
    <script>
        var swiper = new Swiper(".mySwiper", {
            autoplay: {
                delay: 6000,
                disableOnInteraction: false,
            },
            loop: true,
            pagination: {
                el: ".swiper-pagination",
                clickable: true,
                renderBullet: function (index, className) {
                    return '<span class="' + className + '"></span>';
                },

            },
        });
    </script>

    <div class="home_tt">
        <div>领取专属优惠券，畅享购物乐趣!</div>
        <div class="line"></div>
    </div>
    <div class="btn_items flex flex_w">
        <a href="{:url('/index/index/reggift')}">
            <img src="__CDN__/assets/img/wap/new_index/item1.png" alt="">
        </a>
        <a href="{:url('/index/index/rmfriends')}">
            <img src="__CDN__/assets/img/wap/new_index/item2.png" alt="">
        </a>
        <a href="{:url('/index/index/jdraw')}">
            <img src="__CDN__/assets/img/wap/new_index/item3.png" alt="">
        </a>
        <a href="{:url('/index/index/ptip')}">
            <img src="__CDN__/assets/img/wap/new_index/item4.png" alt="">
        </a>
    </div>

<!--    <div class="pad_b30 pad_t20 flex_ac_jc">-->
<!--        <a href="" class="djcy">點擊參與>></a>-->
<!--    </div>-->


    <style>
        .nums_div {
            box-shadow: 0px 4px 16px 0px rgba(239, 67, 109, 0.2);
            border-radius: 0.32rem;
            padding: 0.48rem 0.48rem 0.2rem 0.48rem;
        }

        .nums_div .title {
            font-size: 0.4rem;
            color: #3D3D3D;
            text-align: center;
        }

        .nums_div .title b {
            font-size: 0.72rem;
            font-weight: normal;
        }

        .nums_div .tt .span {
            font-size: 0.24rem;
        }

        .nums_div .items {
            padding: 0 0.6rem;
        }

        .nums_div .items .item {
            width: 50%;
            padding: 0.3rem 0;
        }

        .nums_div .items .item:nth-child(2n) {
            text-align: right;
        }

        .nums_div .items .item .tt {
            line-height: 0.9rem;
            height: 1rem;
            color: #3D3D3D;
            font-size: 0.64rem;
        }

        .nums_div .items .item .tt img {
            height: 1rem;
        }

        .nums_div .items .item .pp {
            font-size: 0.24rem;
            color: #3D3D3D;
        }
    </style>
    <div style="padding: 0 0.2rem;">
        <div class="nums_div">
            <div class="title">
                <div>從2018年成立至今</div>
                <div>付唄由 <b>0</b> 發展至</div>
            </div>
            <div class="items flex_ac flex_w">
                <div class="item">
                    <div class="tt"><img src="__CDN__/assets/img/wap/new_index/num1.png" alt=""></div>
                    <div class="pp">會員人數</div>
                </div>
                <div class="item">
                    <div class="tt"><img src="__CDN__/assets/img/wap/new_index/num2.png" alt=""></div>
                    <div class="pp">完成采購數</div>
                </div>

                <div class="item">
                    <div class="tt"><span class="timer" data-to="97.35" data-speed="1500">97.35</span><span
                            class="span">%</span></div>
                    <div class="pp">會員滿意度</div>
                </div>
                <div class="item">
                    <div class="tt"><span class="timer" data-to="99.93" data-speed="1500">99.93</span><span
                            class="span">%</span></div>
                    <div class="pp">持續使用率</div>
                </div>
            </div>
        </div>
    </div>
    <!-- <script src="__CDN__/assets/js/number.js"></script>
    <script>
        let no_run=true;
        $(window).scroll(function () {
            var scrollTop = $(window).scrollTop();
            if(scrollTop>300 && no_run){
                no_run=false
                $('.timer').each(count); 
                function count(options) {
                    var $this = $(this);
                    options = $.extend({}, options || {}, $this.data('countToOptions') || {});
                    $this.countTo(options);
                }
                $(".math").removeClass("timer");
            }
        })
        
    </script> -->
    <style>
        .steps_div {
            background: url('__CDN__/assets/img/wap/new_index/stepbg.png');
            background-size: cover;
            box-shadow: 0px 4px 16px 0px rgba(239, 67, 109, 0.2);
            border-radius: 0.3rem;
            position: relative;
            z-index: 2;
        }

        .tab_a {
            width: 4.6rem;
            border-radius: 0.3rem 0.3rem 0 0;
            background: rgba(239, 67, 109, 0.5);
            margin: 0 auto;
            height: 0.64rem;
        }

        .tab_a a {
            width: 50%;
            text-align: center;
            line-height: 0.64rem;
            font-size: 0.4rem;
            color: #fff;
            text-decoration: none;
        }

        .tab_a a.active {
            background: #fff;
            border-radius: 0.3rem 0.3rem 0 0;
            color: #EF436D;
        }

        .steps_div .steps {
            padding: 0.24rem;
            justify-content: center;
            display: none;
        }

        .steps_div .steps.active {
            display: flex;
        }

        .steps_div .steps .item {
            width: 33.3%;
            padding: 0.24rem 0.1rem;
            align-items: center;
            position: relative;
        }

        .steps_div .steps .item .round {
            width: 0.96rem;
            border-radius: 50%;
            font-size: 0.24rem;
            font-weight: bold;
            height: 0.96rem;
            text-align: center;
            line-height: 0.96rem;
            background: #FFF5F5;
            color: #EF436D;
        }

        .steps_div .steps .item .pp {
            color: #4C4848;
            font-size: 0.20rem;
            line-height: 1.3;
            text-align: center;
            padding-top: 0.2rem;
        }

        .steps_div .steps .item .icon {
            height: 0.20rem;
            position: absolute;
            right: -0.5rem;
            top: 0.6rem;
        }

        .flow-list-wrap {
            display: flex;
            flex-direction: column;
            width: 100%;
            box-sizing: border-box;
            justify-content: space-between;
            margin: 0.50rem auto 0 auto;
            position: relative;
            font-size: 0.22rem;
        }

        .flow-list-wrap::after {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 1px;
            top: 0.5rem;
            content: '';
            height: 85%;
            background: #cccccc;
            z-index: -1;
        }

        .flow-list-wrap .flow-item {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            width: 100%;
            padding-bottom: 1rem;
            box-sizing: border-box;
        }

        .flow-list-wrap .flow-item .imgwrap {
            width: 40%;
            display: flex;
        }

        .flow-list-wrap .flow-item img {
            height: 1.13rem;
            position: relative;
        }

        .flow-list-wrap .flow-item .num {
            font-size: 16px;
            height: 25px;
            width: 25px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            background: #ecf0f5;
            color: #cccccc;
            border: 1px solid #cccccc;
        }

        .flow-list-wrap .flow-item .hideimg {
            display: block;
            transition: all 0.5s;
        }

        .flow-list-wrap .flow-item .showimg {
            display: none;
            transition: all 0.5s;
        }

        .flow-list-wrap .flow-item .title {
            width: 40%;
            color: #999999;
        }

        .flow-list-wrap .flow-item-active .title {
            width: 40%;
            color: unset;
        }

        .flow-list-wrap .flow-item-active .num {
            color: #ef436d;
            border: 1px solid #ef436d;
        }

        .flow-list-wrap .flow-item-active .showimg {
            display: block !important;
            transition: all 0.5s;
        }

        .flow-list-wrap .flow-item-active .hideimg {
            display: none !important;
            transition: all 0.5s;
        }

        .flow-list-wrap .flow-item:nth-child(even) {
            flex-direction: row-reverse;
        }

        .flow-list-wrap .flow-item:nth-child(odd) .imgwrap {
            justify-content: flex-end;
            align-items: center;
        }

        .flow-list-wrap .flow-item:nth-child(even) .title {
            display: flex;
            justify-content: flex-end;
            text-align: end;
        }
    </style>
    <div style="padding: 0.4rem 0.2rem;">
        <div class="flex_ac tab_a js_tab_a">
            <a href="javascript:;" class="active">購物流程</a>
            <a href="javascript:;">集運流程</a>
        </div>

        <div class="steps_div">
            <div class="steps flex flex_w active">
                <div class="item flex flex_d">
                    <div class="round">STEP 1</div>
                    <div class="pp">將電商平臺收貨地更改爲付唄大陸倉庫</div>
                    <img src="__CDN__/assets/img/wap/new_index/zx.png" class="icon" alt=""/>
                </div>
                <div class="item flex flex_d">
                    <div class="round">STEP 2</div>
                    <div class="pp">在電商平臺購物</div>
                    <img src="__CDN__/assets/img/wap/new_index/zx.png" class="icon" alt=""/>
                </div>
                <div class="item flex flex_d">
                    <div class="round">STEP 3</div>
                    <div class="pp">登陸付款網站，新增要集運的包裹單</div>
                    <!-- <img src="__CDN__/assets/img/wap/new_index/zx.png" class="icon" alt="" /> -->
                </div>
                <div class="item flex flex_d">
                    <div class="round">STEP 4</div>
                    <div class="pp">貨物到達倉庫后，自由申請集運出貨，並支付運費</div>
                    <img src="__CDN__/assets/img/wap/new_index/zx.png" class="icon" alt=""/>
                </div>
                <div class="item flex flex_d">
                    <div class="round">STEP 5</div>
                    <div class="pp">貨品抵達台灣倉庫，聯絡派送，坐等收貨</div>
                </div>
            </div>

            <div class="steps">
                <div class="flow-list-wrap">
                    <div class="flow-item flow-item-active">
                        <div class="imgwrap">
                            <img src="__CDN__/assets/img/pc/images/img_jy_step_01.png" class="hideimg"/>
                            <img src="__CDN__/assets/img/pc/images/img_jy_step_01_red.png" class="showimg"/>
                        </div>
                        <div class="num">
                            1
                        </div>
                        <div class="title">
                            免費註冊會員
                        </div>
                    </div>
                    <div class="flow-item flow-item-active"
                    ">
                    <div class="imgwrap">
                        <img src="__CDN__/assets/img/pc/images/img_jy_step_02_red.png"/>
                    </div>
                    <div class="num">
                        2
                    </div>
                    <div class="title">
                        到各大平臺購物
                    </div>
                </div>
                <div class="flow-item flow-item-active"
                ">
                <div class="imgwrap">
                    <img src="__CDN__/assets/img/pc/images/img_jy_step_03_red.png"/>
                </div>
                <div class="num">
                    3
                </div>
                <div class="title">
                    購物網填寫付唄倉庫地址
                </div>
            </div>
            <div class="flow-item flow-item-active"
            ">
            <div class="imgwrap">
                <img src="__CDN__/assets/img/pc/images/img_jy_step_04_red.png"/>
            </div>

            <div class="num">
                4
            </div>
            <div class="title">
                前往付唄倉庫地址網頁預報<br/>
                快遞單號
            </div>
        </div>
        <div class="flow-item flow-item-active"
        ">
        <div class="imgwrap">
            <img src="__CDN__/assets/img/pc/images/img_jy_step_05_red.png"/>
        </div>

        <div class="num">
            5
        </div>
        <div class="title">
            選擇要集運貨品收貨方式和付款方式
        </div>
    </div>
    <div class="flow-item flow-item-active"
    ">
    <div class="imgwrap">
        <img src="__CDN__/assets/img/pc/images/img_jy_step_06_red.png"/>
    </div>
    <div class="num">
        6
    </div>
    <div class="title">
        宅配到府
    </div>
</div>
</div>
</div>
</div>
<script>
    $('.js_tab_a a').click(function () {
        let index = $('.js_tab_a a').index(this);
        $(this).addClass('active').siblings('a').removeClass('active');
        $('.steps_div .steps').eq(index).addClass('active').siblings('.steps').removeClass('active');
    })

</script>
</div>

<style>
    .mySwiper2 {
        width: 800px;
        height: 3rem;
        position: absolute;
        left: 50%;
        margin-left: -400px;
        top: 0;
    }

    .mySwiper2 .swiper-slide {
        background: #fff;
        display: flex;
        align-items: center;
    }

    .mySwiper2 .item {
        width: 100%;
        height: 2.7rem;
        background: rgba(255, 255, 255, 0.5);
        box-shadow: 0px 2px 12px 0px rgba(239, 67, 109, 0.2);
        border-radius: 0.24rem;
        padding: 0.32rem;
    }

    .mySwiper2 .item .avatar_tt {
        color: #333333;
        font-size: 0.22rem;
        line-height: 1.3;
    }

    .mySwiper2 .item .avatar_tt .tx {
        height: 0.64rem;
        margin-right: 0.24rem;
    }

    .mySwiper2 .item .xx {
        padding: 0.24rem 0;
    }

    .mySwiper2 .item .xx img {
        width: 0.2rem;
        margin-right: 2px;
    }

    .mySwiper2 .item .cont {
        font-size: 0.24rem;
        line-height: 1.4;
    }

    .mySwiper2 .swiper-slide .item:last-child {
        margin-right: 0;
    }

    .mySwiper2_div .swiper-button-next, .mySwiper2_div .swiper-button-prev {
        width: 32px;
        margin-top: 5px;
        height: 32px;
        position: initial;
        left: auto;
        right: auto;
        top: auto;
        opacity: 0.5;
        transition: 0.3s;
        background: url('__CDN__/assets/img/wap/new_index/snext.png');
        background-size: 100% 100%;
    }

    .mySwiper2_div .swiper-button-next:after, .mySwiper2_div .swiper-button-prev:after {
        display: none;
    }

    .mySwiper2_div .swiper-button-prev {
        transform: rotate(180deg);
    }

    .mySwiper2_div .swiper-button:hover {
        opacity: 1;
    }

</style>
<div class="home_tt">
    <div>會員的評價</div>
    <div class="line"></div>
</div>
<div style="overflow: hidden; position: relative;height: 3rem;">
    <div class="swiper mySwiper2">
        <div class="swiper-wrapper">
            <div class="swiper-slide">
                <div class="item">
                    <div class="flex_ac avatar_tt">
                        <img src="__CDN__/assets/img/wap/new_index/pjtx.png" alt="" class="tx">
                        <div class="flex1">
                            <div>袁**</div>
                            <div>12小時前</div>
                        </div>
                        <div>6則評論</div>
                    </div>
                    <div class="flex_ac xx">
                        <img src="__CDN__/assets/img/wap/new_index/xx.png" class="active">
                        <img src="__CDN__/assets/img/wap/new_index/xx.png">
                        <img src="__CDN__/assets/img/wap/new_index/xx.png">
                        <img src="__CDN__/assets/img/wap/new_index/xx.png">
                        <img src="__CDN__/assets/img/wap/new_index/xx.png">
                    </div>
                    <div class="cont ellipsis-2">
                        服務超棒 有問題都可以及時回復。
                    </div>
                </div>
            </div>
            <div class="swiper-slide">
                <div class="item">
                    <div class="flex_ac avatar_tt">
                        <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                        <div class="flex1">
                            <div>陶**</div>
                            <div>1小時前</div>
                        </div>
                        <div>8則評論</div>
                    </div>
                    <div class="flex_ac xx">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png" class="active">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                    </div>
                    <div class="cont">
                        原本以為沒差，結果每次運費都折，久了超有感！
                    </div>
                </div>
            </div>
            <div class="swiper-slide">
                <div class="item">
                    <div class="flex_ac avatar_tt">
                        <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                        <div class="flex1">
                            <div>沈**</div>
                            <div>1小時前</div>
                        </div>
                        <div>8 則評論</div>
                    </div>
                    <div class="flex_ac xx">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png" class="active">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                    </div>
                    <div class="cont">
                        商品完整送達，第一次空運寄送，速度還可以接受。
                    </div>
                </div>

            </div>
            <div class="swiper-slide">
                <div class="item">
                    <div class="flex_ac avatar_tt">
                        <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                        <div class="flex1">
                            <div>劉**</div>
                            <div>7小時前</div>
                        </div>
                        <div>3則評論</div>
                    </div>
                    <div class="flex_ac xx">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png" class="active">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                    </div>
                    <div class="cont">
                        皆已順利收到...謝謝!
                    </div>
                </div>
            </div>
            <div class="swiper-slide">
                <div class="item">
                    <div class="flex_ac avatar_tt">
                        <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                        <div class="flex1">
                            <div>陶**</div>
                            <div>9小時前</div>
                        </div>
                        <div>9則評論</div>
                    </div>
                    <div class="flex_ac xx">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png" class="active">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                    </div>
                    <div class="cont">
                        原本以為沒差，結果每次運費都折，久了超有感！真的是太方便了！
                    </div>
                </div>
            </div>
            <div class="swiper-slide">
                <div class="item">
                    <div class="flex_ac avatar_tt">
                        <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                        <div class="flex1">
                            <div>袁**</div>
                            <div>12小時前</div>
                        </div>
                        <div>6則評論</div>
                    </div>
                    <div class="flex_ac xx">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png" class="active">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                    </div>
                    <div class="cont">
                        服務超棒 有問題都可以及時回復。
                    </div>
                </div>
            </div>
            <div class="swiper-slide">
                <div class="item">
                    <div class="flex_ac avatar_tt">
                        <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                        <div class="flex1">
                            <div>季**</div>
                            <div>21小時前</div>
                        </div>
                        <div>17則評論</div>
                    </div>
                    <div class="flex_ac xx">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png" class="active">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                    </div>
                    <div class="cont">
                        不像其他支付方式還要輸入一堆資訊，幾秒就能完成交易，超級方便！
                    </div>
                </div>
            </div>
            <div class="swiper-slide">
                <div class="item">
                    <div class="flex_ac avatar_tt">
                        <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                        <div class="flex1">
                            <div>熊**</div>
                            <div>1天前</div>
                        </div>
                        <div>11 則評論</div>
                    </div>
                    <div class="flex_ac xx">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png" class="active">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                    </div>
                    <div class="cont">
                        從商品下單到收貨，服務佳，運送也很快！
                    </div>
                </div>
            </div>
            <div class="swiper-slide">
                <div class="item">
                    <div class="flex_ac avatar_tt">
                        <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                        <div class="flex1">
                            <div>李**</div>
                            <div>2天 前</div>
                        </div>
                        <div>21則評論</div>
                    </div>
                    <div class="flex_ac xx">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png" class="active">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                    </div>
                    <div class="cont">
                        集運省下來的錢，剛好拿去買杯珍奶，爽！
                    </div>
                </div>
            </div>
            <div class="swiper-slide">
                <div class="item">
                    <div class="flex_ac avatar_tt">
                        <img src="__CDN__/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                        <div class="flex1">
                            <div>李**</div>
                            <div>12天前</div>
                        </div>
                        <div>17則評論</div>
                    </div>
                    <div class="flex_ac xx">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png" class="active">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                        <img src="__CDN__/assets/img/pc/new_index/xx.png">
                    </div>
                    <div class="cont">
                        真是太合我心意了，集運省下來的錢，剛好拿去買杯珍奶，爽！
                    </div>
                </div>
            </div>
        </div>
        <!-- <div class="swiper-pagination swiper_point"></div> -->
    </div>
</div>


<script>
    var swiper2 = new Swiper(".mySwiper2", {
        autoplay: {
            delay: 2500,
            disableOnInteraction: false,
        },
        slidesPerView: 3,
        spaceBetween: 15,
        navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
        },
        loop: true
    });
    document.addEventListener("DOMContentLoaded", () => {
        document.querySelectorAll('.js_play_video').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const coverDiv = this.querySelector('.cover_d');
                const placeholder = coverDiv.querySelector('.video-placeholder');
                const iframe = coverDiv.querySelector('iframe');

                if (!iframe.src) {
                    placeholder.innerHTML = '<div class="loading">加载中...</div>';
                    iframe.src = iframe.dataset.src;
                    iframe.style.display = 'block';
                    placeholder.style.display = 'none';

                    // 通过 YouTube API 触发自动播放
                    const player = new YT.Player(iframe, {
                        events: {
                            'onReady': (event) => event.target.playVideo()
                        }
                    });
                }
            });
        });
    });
</script>


<style>
    .active_list {
        overflow-x: auto;
        padding: 0 10px; /* 允许水平滚动 */
        width: 100%; /* 或具体宽度 */
    }

    .active_list .item {
        display: block;
        position: relative;
        flex: 0 0 auto; /* 不允许子元素伸缩 */
        margin-bottom: 15px;
        width: 227px;
        margin-right: 15px;
    }

    .active_list .item:nth-child(5n) {
        margin-right: 0;
    }

    .active_list .item .cover {
        width: 100%;
        height: 100%;
        float: left;
        object-fit: cover;
    }

    .active_list .item .tt {
        padding: 6px 15px;
        background: rgba(0, 0, 0, 0.5);
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        z-index: 2;
        color: #fff;
        font-size: 14px;
    }

    .active_list .item .time {
        padding: 4px 10px;
        background: rgb(247 144 55);
        border-radius: 0 0 0 5px;
        position: absolute;
        font-size: 12px;
        right: 0;
        top: 0;
        z-index: 2;
        color: #fff;
    }

    .active_list .item2 {
        height: auto;
    }

    .active_list .item2 .cover_d {
        height: 132px;
        position: relative;
    }

    .active_list .item2 .cover {
        height: 132px;
        float: none;
        border-radius: 3px;
    }

    .active_list .item .tt2 {
        color: #333;
        margin: 10px 0;
    }

    .active_list .item2 .play {
        width: 34px;
        height: 24px;
        position: absolute;
        left: 50%;
        top: 50%;
        margin-left: -17px;
        margin-top: -12px;
    }

    .video_play_div {
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        position: fixed;
        left: 0;
        top: 0;
        z-index: 300;
    }

    .video_play_div iframe {
        width: 100%;
        height: 300px;
        background: #fff;
        position: absolute;
        left: 0;
        top: 50%;
        margin-top: -150px;
    }

    .video_play_div .close_win_btns {
        display: block;
        width: 30px;
        height: 60px;
        text-align: center;
        position: absolute;
        left: 50%;
        margin-left: -15px;
        top: 50%;
        margin-top: -210px;
        z-index: 20;
    }

    .video_play_div .close_win_btns img {
        width: 100%;
    }

    /* 占位符样式 */
    .video-placeholder {
        position: relative;
        width: 100%;
        height: 100%;
        background: #000;
        cursor: pointer;
    }
    .thumbnail {
        width: 100%;
        height: 100%;
        object-fit: cover;
        opacity: 0.7;
    }
    .play-btn {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 50px;
        height: 50px;
        border: none;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        font-size: 20px;
        cursor: pointer;
    }
    .loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
    }
</style>
<div class="container clearfix">
    <div class="home_tt">
        <div>代付教學視頻</div>
        <div class="line"></div>
    </div>
    <div class="active_list flex">

        <?php
                foreach($video as $ls){
            ?>
        <a href="javascript:;" class="item item2 js_play_video">
            <div class="cover_d">
                <div class="video-placeholder">
                    <button class="play-btn">▶</button>
                </div>
                <iframe width="100%" height="100%" data-src="<?=$ls['video_url']?>&autoplay=1&enablejsapi=1" title="YouTube video player"
                        frameborder="0"
                        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                        referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
            </div>
            <div class="tt2 ellipsis-2"><?=$ls['title']?></div>
            <!-- <div class="time">32天后结束</div> -->
        </a>
        <?php
                }
            ?>


    </div>
</div>


<style>
    .start_buy {
        background: #272727;
        color: #fff;
        text-align: center;
        padding: 0.6rem 0;
    }

    .start_buy .tt {
        font-size: 0.44rem;
        line-height: 0.68rem;
        padding: 0 0.2rem;
    }

    .start_buy .pp {
        font-size: 0.27rem;
        padding: 0.46rem 0.4rem;
    }

    .start_buy a {
        width: 2.6rem;
        display: inline-block;
        line-height: 0.48rem;
        border-radius: 0.48rem;
        background: #EF436D;
        color: #fff;
        font-size: 0.32rem;
    }
</style>
<div class="start_buy">
    <div class="tt">
        <div>加入數百萬人使用的付唄購買你</div>
        <div>最愛的產品！</div>
    </div>
    <div class="pp">
        很感謝您遇見我們的時間，有片刻的停留。我們是一群過著簡單的生活，有美好理想的一群人。將每一點點小心思都放在這繁瑣重複的工作上，只希望帶給您的不僅僅是產品，更是一份美麗的心情！
    </div>
    <a href="<?=url('index/login/register')?>">開始~永遠省錢</a>
</div>

</div>