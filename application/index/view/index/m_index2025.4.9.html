<style>
    .header {
        display: none
    }

    body {
        padding-top: 0 !important;
        background: #fff;
    }

    .body {
        padding-bottom: 1rem
    }
</style>
<div class="index_banner">
    <a href="__CDN__/" class="logo"><img src="__CDN__/assets/img/wap/logo.png"></a>
    <div class="title js_need_active bottom_top_trans">{:__('haigou slogo')}</div>

    <div class="slogo_items text-center js_need_active bottom_top_trans">
        <div>
            <img src="__CDN__/assets/img/wap/icon_banner01.png">
            {$site.Savings}
        </div>
        <div>
            <img src="__CDN__/assets/img/wap/icon_banner02.png">
            {$site.Delivery}
        </div>
        <div>
            <img src="__CDN__/assets/img/wap/icon_banner03.png">
            {$site.Simple}
        </div>
    </div>
    <div class="text-center js_need_active start_enter bottom_top_trans">
        <a href="<?=url('index/login/register')?>" class="sub_btn">{:__('Start to save money forever')}</a>
    </div>
</div>
<style>
    .pay_step .serverwrap {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        padding: 0.5rem 0.4rem;
    }

    .pay_step .serverwrap .serveritem {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 50%;
        padding-top: 0.4rem;
        padding-bottom: 0.5rem;
        box-sizing: border-box;
        color:#333333;

    }

    .pay_step .serverwrap .serveritem:nth-child(1) {
        border-right: 1px solid #cdcdcd;
        border-bottom: 1px solid #cdcdcd;
    }

    .pay_step .serverwrap .serveritem:nth-child(2) {
        border-bottom: 1px solid #cdcdcd;
    }

    .pay_step .serverwrap .serveritem:nth-child(3) {
        border-right: 1px solid #cdcdcd;
    }

    .pay_step .serverwrap .serveritem img {
        width: 1.5rem;
        height: 1.5rem;
    }

    .pay_step .serverwrap .serveritem .titles {
        font-size: 0.16rem;
    }

    .pay_step .server-more {
        display: block;
        width: fit-content;
        padding: 0 0.5rem;
        box-sizing: border-box;
        border: 1px solid #EF436D;
        height: 32px;
        line-height: 32px;
        border-radius: 32px;
        font-size: 0.24rem;
        color: #EE436D;
        text-align: center;
        box-shadow: 0 5px 8px rgba(238, 67, 109, 0.1) !important;
        margin: 0rem auto 0.5rem auto;
    }

    .my-merchandise {
        margin-top: 1rem;
    }

    .my-merchandise .container .my-merchandise-label {
        justify-content: space-between;
        align-items: center;
        width: 100%;
        height: 0.80rem;
        margin-bottom: 0.4rem;
        display: flex;
        flex-direction: column;
        position: relative;
    }

    .my-merchandise .container .my-merchandise-label .labelwrap {
        background: #ffffff;
        height: 0.80rem;
        width: fit-content;
        padding: 0 0.3rem;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        z-index: 5;
    }

    .my-merchandise .container .my-merchandise-label::after {
        position: absolute;
        content: '';
        left: 50%;
        transform: translateX(-50%);
        top: 0.35rem;
        height: 1px;
        width: calc(100% - 0.4rem);
        background: #d9d9d9;
        z-index: 3;
    }

    .my-merchandise .container .my-merchandise-label::before {
        position: absolute;
        top: 0.35rem;
        left: 50%;
        transform: translateX(-50%);
        width: 5.00rem;
        height: 1px;
        content: '';
        background: #EF436D;
        z-index: 4;
    }

    .my-merchandise .container .my-merchandise-label .title {
        font-size: 0.36rem;
    }

    .my-merchandise-bg {
        min-height: 8rem;
        padding-top: 1rem;
        padding-bottom: 0rem;
        box-sizing: border-box;
        background: #ecf0f5;
        position: relative;
        z-index: 10;
    }

    .my-merchandise-bg .bgcover {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
    }

    .my-merchandise-bg .container .my-merchandise-label .labelwrap {
        background: #ecf0f5;
    }

    .my-merchandise-bg .container .flow-list-wrap {
        display: flex;
        flex-direction: column;
        width: 100%;
        padding: 0 0.2rem;
        box-sizing: border-box;
        justify-content: space-between;
        margin: 0.50rem auto 0 auto;
        position: relative;
    }

    .my-merchandise-bg .container .flow-list-wrap::after {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        width: 1px;
        top: 0.5rem;
        content: '';
        height: 85%;
        background: #cccccc;
        z-index: -1;
    }

    .my-merchandise-bg .container .flow-list-wrap .flow-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;
        width: 100%;
        padding-bottom: 1rem;
        box-sizing: border-box;
    }

    .my-merchandise-bg .container .flow-list-wrap .flow-item .imgwrap {
        width: 40%;
        display: flex;
    }

    .my-merchandise-bg .container .flow-list-wrap .flow-item img {
        height: 1.13rem;
        position: relative;
    }

    .my-merchandise-bg .container .flow-list-wrap .flow-item .num {
        font-size: 16px;
        height: 25px;
        width: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background: #ecf0f5;
        color: #cccccc;
        border: 1px solid #cccccc;
    }

    .my-merchandise-bg .container .flow-list-wrap .flow-item .hideimg {
        display: block;
        transition: all 0.5s;
    }

    .my-merchandise-bg .container .flow-list-wrap .flow-item .showimg {
        display: none;
        transition: all 0.5s;
    }

    .my-merchandise-bg .container .flow-list-wrap .flow-item .title {
        width: 40%;
        color: #999999;
    }

    .my-merchandise-bg .container .flow-list-wrap .flow-item-active .title {
        width: 40%;
        color: unset;
    }

    .my-merchandise-bg .container .flow-list-wrap .flow-item-active .num {
        color: #ef436d;
        border: 1px solid #ef436d;
    }

    .my-merchandise-bg .container .flow-list-wrap .flow-item-active .showimg {
        display: block !important;
        transition: all 0.5s;
    }

    .my-merchandise-bg .container .flow-list-wrap .flow-item-active .hideimg {
        display: none !important;
        transition: all 0.5s;
    }

    .my-merchandise-bg .container .flow-list-wrap .flow-item:nth-child(even) {
        flex-direction: row-reverse;
    }

    .my-merchandise-bg .container .flow-list-wrap .flow-item:nth-child(odd) .imgwrap {
        justify-content: flex-end;
        align-items: center;
    }

    .my-merchandise-bg .container .flow-list-wrap .flow-item:nth-child(even) .title {
        display: flex;
        justify-content: flex-end;
        text-align: end;
    }
</style>
<div class="pay_step js_need_active">
    <div class="container">
        <div class="title bottom_top_trans js_need_active">
            我們的服務
        </div>
        <div class="line"></div>
        <div class="serverwrap bottom_top_trans js_need_active">
            <a class="serveritem" href="<?=url('index/daifu/nav')?>">
                <img src="__CDN__/assets/img/wap/images/icon_business01.png" />
                <div class="titles">商品代購專區</div>
            </a>
            <a class="serveritem"   href="<?=url('index/daifu/nav')?>">
                <img src="__CDN__/assets/img/wap/images/icon_business02.png" />
                <div class="titles">直播點數專區</div>
            </a>
            <a class="serveritem"   href="<?=url('index/daifu/nav')?>">
                <img src="__CDN__/assets/img/wap/images/icon_business03.png" />
                <div class="titles">遊戲點數專區</div>
            </a>
            <a class="serveritem"   href="<?=url('index/daifu/nav')?>">
                <img src="__CDN__/assets/img/wap/images/icon_business04.png" />
                <div class="titles">其他代購專區</div>
            </a>
        </div>
        <a class="server-more bottom_top_trans js_need_active"   href="<?=url('index/daifu/nav')?>">
            查看更多>>
        </a>
    </div>
</div>


<div class="my-merchandise js_need_active">
    <div class="container">
        <div class="my-merchandise-label bottom_top_trans js_need_active">
            <div class="labelwrap">
                <div class="title">
                    我們的商品
                </div>
                <div class="des">
                    購誠信，購時尚，購精彩
                </div>
            </div>
        </div>
        <div id="certify" class="bottom_top_trans js_need_active">
            <div class="swiper-container">
                <div class="swiper-wrapper">
                    <div class="swiper-slide"><a href="<?=url('index/index/perfect')?>"><img src="__CDN__/assets/img/pc/images//nature-1.png" /></a></div>
                    <div class="swiper-slide"><a href="<?=url('index/index/perfect')?>"><img src="__CDN__/assets/img/pc/images//nature-2.png" /></a></div>
                    <div class="swiper-slide"><a href="<?=url('index/index/perfect')?>"><img src="__CDN__/assets/img/pc/images//nature-3.png" /></a></div>
                    <div class="swiper-slide"><a href="<?=url('index/index/perfect')?>"><img src="__CDN__/assets/img/pc/images//nature-4.png" /></a></div>
                    <div class="swiper-slide"><a href="<?=url('index/index/perfect')?>"><img src="__CDN__/assets/img/pc/images//nature-5.png" /></a></div>
                </div>
            </div>
            <div class="swiper-pagination"></div>
        </div>
    </div>
</div>


<div class="my-merchandise my-merchandise-bg js_need_active">
    <img class="bgcover" src="__CDN__/assets/img/wap/images/bg_jiyun.jpg" />
    <div class="container">
        <div class="my-merchandise-label bottom_top_trans js_need_active">
            <div class="labelwrap">
                <div class="title">
                    集運流程
                </div>
                <div class="des">
                    躍萬水千山 只為溫暖入戶
                </div>
            </div>
        </div>
        <div class="flow-list-wrap bottom_top_trans js_need_active">
            <div class="flow-item flow-item-active">
                <div class="imgwrap">
                    <img src="__CDN__/assets/img/pc/images/img_jy_step_01.png" class="hideimg" />
                    <img src="__CDN__/assets/img/pc/images/img_jy_step_01_red.png" class="showimg" />
                </div>
                <div class="num">
                    1
                </div>
                <div class="title">
                    免費註冊會員
                </div>
            </div>
            <div class="flow-item">
                <div class="imgwrap">
                    <img src="__CDN__/assets/img/pc/images/img_jy_step_02.png" class="hideimg" />
                    <img src="__CDN__/assets/img/pc/images/img_jy_step_02_red.png" class="showimg" />
                </div>
                <div class="num">
                    2
                </div>
                <div class="title">
                    到各大平臺購物
                </div>
            </div>
            <div class="flow-item">
                <div class="imgwrap">
                    <img src="__CDN__/assets/img/pc/images/img_jy_step_03.png" class="hideimg" />
                    <img src="__CDN__/assets/img/pc/images/img_jy_step_03_red.png" class="showimg" />
                </div>
                <div class="num">
                    3
                </div>
                <div class="title">
                    購物網填寫付唄倉庫地址<br />
                    東莞倉作收貨
                </div>
            </div>
            <div class="flow-item">
                <div class="imgwrap">
                    <img src="__CDN__/assets/img/pc/images/img_jy_step_04.png" class="hideimg" />
                    <img src="__CDN__/assets/img/pc/images/img_jy_step_04_red.png" class="showimg" />
                </div>

                <div class="num">
                    4
                </div>
                <div class="title">
                    前往付唄倉庫地址網頁預報<br />
                    快遞單號
                </div>
            </div>
            <div class="flow-item">
                <div class="imgwrap">
                    <img src="__CDN__/assets/img/pc/images/img_jy_step_05.png" class="hideimg" />
                    <img src="__CDN__/assets/img/pc/images/img_jy_step_05_red.png" class="showimg" />
                </div>

                <div class="num">
                    5
                </div>
                <div class="title">
                    選擇要集運貨品收獲<br />
                    方式和付款方式
                </div>
            </div>
            <div class="flow-item">
                <div class="imgwrap">
                    <img src="__CDN__/assets/img/pc/images/img_jy_step_06.png" class="hideimg" />
                    <img src="__CDN__/assets/img/pc/images/img_jy_step_06_red.png" class="showimg" />
                </div>
                <div class="num">
                    6
                </div>
                <div class="title">
                    貨物到自提點後會議<br />
                    whatsapp通知客戶送<br />
                    貨上門會有快遞聯絡
                </div>
            </div>
        </div>
    </div>
</div>



<div class="index_foot">
    <div class="" style="margin: 0 auto">
        <div class="title">{:__('Join the Hi Shopping Band that millions')}</div>
        <div class="pp">{:__('Thank you very much for meeting us for a moment')}</div>
        <div class="text-center start_enter">
            <a href="<?=url('index/login/register')?>" class="sub_btn">{:__('Start to save money forever')}</a>
        </div>
    </div>
</div>



<script type="text/javascript">

    $(function () {

        // 倒计时
        // 补0
        function c_data(str) {
            if (str < 0) {
                str = "00"
            } else {
                str = String(str);
                if (str.length == 1) {
                    str = "0" + str;
                }
            }
            return str;
        }
        setInterval(function () {
            render_time()
        }, 1000);

        function render_time() {
            var js_time_down = $('.js_time_down');
            for (var i = 0; i < js_time_down.length; i++) {
                var obj = js_time_down.eq(i);
                var now_time = obj.attr('data-now') ? parseInt(obj.attr('data-now')) : 1;
                obj.attr('data-now', now_time + 1);
                var end_time_s = parseInt(obj.attr('data-time'));
                var time_d = end_time_s - now_time; //更新时间差
                var hour = parseInt(time_d / 3600) % 24; // 小时
                var minute = parseInt(time_d % 3600 / 60); //分
                var second = parseInt(time_d % 3600 % 60); //秒
                var day = parseInt(time_d / 3600 / 24);
                obj.find('.js_d').html(day);
                obj.find('.js_h').html(c_data(hour));
                obj.find('.js_m').html(c_data(minute));
                obj.find('.js_s').html(c_data(second));
            }
        }
        render_time();

        $('.js_index_search_input').focus(function () {
            $(this).attr('data-pla', $(this).attr('placeholder'));
            $(this).attr('placeholder', '')
        })
        $('.js_index_search_input').blur(function () {
            $(this).attr('placeholder', $(this).attr('data-pla'));
        })
        $('.js_index_search_input').siblings('a').click(function () {
            $(this).parents('form').submit()
        })

        // 轮播图
        var modify;
        var certifySwiper = new Swiper('#certify .swiper-container', {
            watchSlidesProgress: true,
            slidesPerView: 'auto',
            centeredSlides: true,
            loop: true,
            loopedSlides: 5,
            autoplay: true,
            pagination: {
                el: '.swiper-pagination',
                clickable: true,
            },


            on: {
                progress: function (progress) {
                    for (i = 0; i < this.slides.length; i++) {
                        var slide = this.slides.eq(i);
                        var slideProgress = this.slides[i].progress;

                        if (Math.abs(slideProgress) > 1) {
                            modify = (Math.abs(slideProgress) - 1) * 0.55 + 1;
                        }
                        translate = slideProgress * modify * 355 / 100 + 'rem';
                        scale = 1 - Math.abs(slideProgress) / 5;
                        zIndex = 999 - Math.abs(Math.round(10 * slideProgress));
                        slide.transform('translateX(' + translate + ') scale(' + scale + ')');
                        slide.css('zIndex', zIndex);
                        slide.css('opacity', 1);
                        if (Math.abs(slideProgress) > 3) {
                            slide.css('opacity', 0);
                        }
                    }
                },
                setTransition: function (transition) {
                    for (var i = 0; i < this.slides.length; i++) {
                        var slide = this.slides.eq(i)
                        slide.transition(transition);
                    }

                }
            }

        })

        // 流程移动事事件
        $(document).on('click', '.flow-list-wrap .flow-item', function () {
            $(this).addClass('flow-item-active')
            $(this).siblings().removeClass('flow-item-active')
        });

    })
</script>