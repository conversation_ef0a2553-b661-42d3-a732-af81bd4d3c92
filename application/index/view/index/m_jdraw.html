<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <title></title>
    <!-- 引入样式 -->
    <link href="__CDN__/assets/css/element-ui-index.css" rel="stylesheet">
    <style>
        * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        html,body{
            display: flex;
            justify-content: center;
            /* align-items: center; */
        }

        html {
            font-size: calc(100vw / 10); /* 核心公式 */
            /* 响应式限制 */
            @media (min-width: 768px) { font-size: 37.5px; }
            @media (max-width: 320px) { font-size: 32px; }
        }

        body { font-family: "Microsoft YaHei","PingFang SC"; }

        .el-dialog {
            border-radius: 16px;
            overflow: hidden;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 1.25rem;
            padding: 0 0.2667rem;
        }
        .logo {
            height: 1.25rem;
        }

        .logo img {
            height: 100%;
            object-fit: contain;
        }

        .home-page {
            width: 1.96rem; /* 73.53/37.5≈1.96rem */
            height: 0.694rem; /* 26.03/37.5≈0.694rem */
        }

        .back {
            width: 0.56rem; /* 21/37.5=0.56rem */
            height: 0.56rem;
            line-height: 0;
        }

        .back img {
            display: block;
            width: 100%;
            height: 100%;
            vertical-align: middle;
        }

        .head_login_btn {
            display: flex;
            justify-content: center;
            align-items: flex-end;
        }

        .subTitle {
            text-indent: 3ch;
            text-align: justify;
        }

        .login-btn {
            height: 0.59rem;
            line-height: 0.59rem;
            font-size: 0.35rem;
            color: #666666;
            text-align: center;
        }

        .login-btn a{
            font-weight: normal;
            color:inherit;
            cursor:auto;
        }

        .reg-btn {
            text-align: center;
            line-height: 0.53rem;
            width: 1.87rem;
            height: 0.59rem;
            font-size: 0.32rem;
            background: #EF436D;
            margin-left: 0.32rem;
            color: #fff;
            border-radius: 290px;
            border: 1px solid #EF436D;
        }

        .reg-btn a{
            font-weight: normal;
            text-decoration:none;
            color:inherit;
            cursor:auto;
        }

        .activity-register {
            width: 10rem;
            height: 100vh;
            background: linear-gradient(180deg, #EC9CD0 0%, #FFFEFE 100%);
            border: 1px solid #FFF4D5;
            display: flex;
            position: relative;
            flex-direction: column;
            align-items: center;
        }

        .activities-date{
            width: 100%;
            font-size: 0.2934rem;
            transform: translate(-50%, -50%);
            position: absolute;
            top: 17%;
            left: 50%;
            color: #ffffff;
            font-weight: bold;
            text-align: center;
        }

        .title {
            font-size: 0.48rem;
            font-weight: bold;
            line-height: normal;
            text-align: center;
            color: #FFFFFF;
            margin-top: 0.96rem;
        }

        .title2-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0.43rem 0 0.53rem 0;
        }

        .title2 {
            font-size: 0.43rem;
            line-height: 0.43rem;
            text-align: center;
            color: #EF436D;
        }

        .label {
            width: 0.054rem;
            height: 0.32rem;
            transform: rotate(-90deg);
            border-radius: 16px;
            background: #EF436D;
        }

        .bottom-btn {
            width: 4.8rem;
            height: 1.28rem;
            line-height: 1.28rem;
            text-align: center;
            color: #FFFFFF;
            font-size: 0.5334rem;
            border-radius: 290px;
            background: #EF436D;
            position: absolute;
            bottom: 0.8533rem;
            left: 50%;
            transform: translateX(-50%);
        }

        .bgimg1 {
            position: absolute;
            width: 1.8933rem;
            height: 1.8933rem;
            left: 0;
            top: 0.1066rem;
        }

        .bgimg2 {
            position: absolute;
            width: 1.12rem;
            height: 1.8133rem;
            right: 0.1066rem;
            top: 0;
        }

        .bgimg3 {
            position: absolute;
            width: 0.64rem;
            height: 0.64rem;
            left: 50%;
            transform: translate(-50%);
            bottom: -1.0933rem;
        }

        /* 抽奖大转盘样式 */
        .lottery-container {
            width: 8.72rem;
            max-width: 9.3866rem;
            margin: 0 auto;
        }

        .lottery-grid {
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 0.2667rem;
            position: relative;
            width: 8.72rem;
            max-width: 10rem;
            height: 8.6933rem;
            background-color: #FFF9E6;
            padding: 0.2667rem;
            border-radius: 15px;
            border: 8px solid #FF9800;
            box-shadow: 0 0 0.2667rem rgba(0, 0, 0, 0.1);
        }

        .prize-dialog-content {
            text-align: center;
            padding: 0.5334rem;
        }

        .err-dialog-content {
            text-align: center;
        }

        .prize-dialog-icon {
            width: 2.1333rem;
            height: 2.1333rem;
            object-fit: contain;
            margin-bottom: 0.4rem;
        }

        .lottery-item {
            background-color: #FFFCF0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
            border: 2px solid #FFD699;
            position: relative;
            flex-direction: column;
        }

        .lottery-item.active {
            background-color: #fb9037;
            transform: scale(1.05);
            box-shadow: 0 0 0.4rem rgba(255, 215, 0, 0.7);
            border-color: #FF9800;
            z-index: 5;
        }

        .lottery-content {
            text-align: center;
            padding: 0.1333rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        .prize-icon {
            min-height: 0.5866rem;
            font-size: 0.2667rem;
            color: #f17e86;
            font-weight: bold;
        }

        .prize-name {
            font-size: 0.2667rem;
            color: #f17e86;
            margin-top: 0.1333rem;
        }

        .lottery-button {
            background-color: #FF5722;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            box-shadow: 0 0.1066rem 0.2133rem rgba(0, 0, 0, 0.2);
            transition: all 0.3s;
            z-index: 10;
            grid-column: 2;
            grid-row: 2;
            font-size: 0.4266rem;
            border: 4px solid #FFC107;
        }

        .lottery-button:hover {
            background-color: #E64A19;
            transform: scale(1.05);
        }

        .lottery-button:active {
            transform: scale(0.95);
        }

        /* 活动提示 */

        .tips-box {
            margin-bottom: 1.0666rem;
        }

        .tips-item {
            width: 8.72rem;
            display: flex;
            align-items: center;
            color: #333333;
            font-size: 0.2934rem;
            line-height: 0.2934rem;
            margin-bottom: 0.2133rem;
        }

        .tips-dot {
            position: relative;
            width: 0.0266rem;
            height: 0.0266rem;
            transform: translateZ(0);
            margin: 0 0.32rem 0 0.2133rem;
        }

        .tips-dot::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            width: 0.32rem;
            height: 0.32rem;
            background: rgba(242, 105, 138, 0.6);
            border-radius: 50%;
            transform: translate(-50%, -50%) scale(0.5);
            transform-origin: center;
            box-shadow: 0 0 0.5px rgba(0, 0, 0, 0.1);
        }

        .custom-notice-bar {
            font-size: 0.2934rem;
            width: 8.72rem;
            height: 0.64rem;
            display: flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.52);;
            border-radius: 8px;
            margin: 0.2667rem 0;
        }

        .notice-icon {
            width: 0.5334rem;
            height: 0.5334rem;
            margin-right: 8px;
        }

        .notice-icon img{
            width: 100%;
            height: 100%;
        }

        .notice-content-wrapper {
            flex: 1;
            overflow: hidden;
            position: relative;
        }

        .notice-content {
            display: inline-block;
            white-space: nowrap;
        }

        .marquee {
            animation: marquee linear infinite;
        }

        @keyframes marquee {
            0% {
                transform: translateX(0);
            }
            100% {
                transform: translateX(-50%);
            }
        }

        /* 保证动画在缩放时流畅 */
        .notice-content {
            will-change: transform;
        }

        .integral-box{
            width: 100%;
            display: flex;
            /*gap: 0.16rem;*/
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            margin-top: 8px;
        }

        .integral{
            color: #ff0000;
            font-size: 0.4266rem;
            font-weight: bold;
        }
    </style>
    <script src="__CDN__/assets/js/vue.js"></script>

    <!-- 引入组件库 -->
    <script src="__CDN__/assets/js/element-ui-index.js"></script>
</head>

<body>
<div id="application">
    <div class="header clearfix flex_ac pad_lr20">
        <a href="__CDN__/" class="logo"><img class="home-page" src="__CDN__/assets/img/wap/logo.png"></a>

        <?php
		if($user){
	?>
        <a href="javascript:window.history.go(-1);" class="close_b back margin_a"><img class="back" src="__CDN__/assets/img/wap/header_close2.png"></a>
        <?php
		}else{
	?>
        <div class="flex_ac head_login_btn">
            <div class="login-btn">
                <a  href="{:url('/index/login/login')}">會員登陸</a>
            </div>
            <div class="reg-btn">
                <a  href="{:url('/index/login/register')}">免費註冊</a>
            </div>
        </div>
        <?php
		}
	?>
    </div>

    <div class="activity-register pad_t20 pad_lr20">
        <div class="activities-date">活動時間：{{timeConversion(activitiesTime.begTime)}}到{{timeConversion(activitiesTime.endTime)}}</div>
        <div class="title"><?=$dr['title']?></div>
        <div class="title2-box" style="margin-bottom: 0.72rem">
            <div class="title2">參與抽獎</div>
            <div class="label"></div>
        </div>
        <div class="center-box">
            <div :class="{ active: currentSection === 2 }" class="section">
                <div class="features flex-jc-ac flex-col">
                    <div class="lottery-container">
                        <div class="lottery-grid">
                            <!-- 第一行 -->
                            <div :class="{ active: currentPrizeIndex === 0 && isRotating }" class="lottery-item">
                                <div class="lottery-content">
                                    <p class="prize-icon">{{prizes[0].icon}}</p>
                                    <p class="prize-name">{{ prizes[0].name }}</p>
                                </div>
                            </div>
                            <div :class="{ active: currentPrizeIndex === 1 && isRotating }" class="lottery-item">
                                <div class="lottery-content">
                                    <p class="prize-icon">{{prizes[1].icon}}</p>
                                    <p class="prize-name">{{ prizes[1].name }}</p>
                                </div>
                            </div>
                            <div :class="{ active: currentPrizeIndex === 2 && isRotating }" class="lottery-item">
                                <div class="lottery-content">
                                    <p class="prize-icon">{{prizes[2].icon}}</p>
                                    <p class="prize-name">{{ prizes[2].name }}</p>
                                </div>
                            </div>

                            <!-- 第二行 -->
                            <div :class="{ active: currentPrizeIndex === 7 && isRotating }" class="lottery-item">
                                <div class="lottery-content">
                                    <image mode="scaleToFill" src="__CDN__/assets/img/wap/new_index/red.png"
                                           style="width: 0.9067rem; height: 0.9067rem;"/>
                                    <p class="prize-name">{{ prizes[7].name }}{{prizes[7].icon}}</p>
                                </div>
                            </div>
                            <div :disabled="isRotating" @click="startLottery" class="lottery-button">
                                <span v-if="!isRotating">抽奖</span>
                                <span style="font-size: 0.32rem;" v-else>抽奖中...</span>
                            </div>
                            <div :class="{ active: currentPrizeIndex === 3 && isRotating }" class="lottery-item">
                                <div class="lottery-content">
                                    <p class="prize-icon">{{prizes[3].icon}}</p>
                                    <p class="prize-name">{{ prizes[3].name }}</p>
                                </div>
                            </div>

                            <!-- 第三行 -->
                            <div :class="{ active: currentPrizeIndex === 6 && isRotating }" class="lottery-item">
                                <div class="lottery-content">
                                    <p class="prize-icon">{{prizes[6].icon}}</p>
                                    <p class="prize-name">{{ prizes[6].name }}</p>
                                </div>
                            </div>
                            <div :class="{ active: currentPrizeIndex === 5 && isRotating }" class="lottery-item">
                                <div class="lottery-content">
                                    <p class="prize-icon">{{prizes[5].icon}}</p>
                                    <p class="prize-name">{{ prizes[5].name }}</p>
                                </div>
                            </div>
                            <div :class="{ active: currentPrizeIndex === 4 && isRotating }" class="lottery-item">
                                <div class="lottery-content">
                                    <p class="prize-icon">{{prizes[4].icon}}</p>
                                    <p class="prize-name">{{ prizes[4].name }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 中奖弹窗 -->
                    <el-dialog :show-close="false" :visible.sync="prizeDialogVisible" center title="恭喜中奖" top="27vh"  width="5.8667rem">
                        <div class="prize-dialog-content">
                            <p style="margin-top: 0.5334rem;">恭喜您获得了<br>
                                <span class="prize-icon"><strong style="font-size: 0.3733rem">{{ selectedPrize.icon }}</strong></span>{{ selectedPrize.name }}</p>
                        </div>
                        <span class="dialog-footer" slot="footer">
                            <el-button @click="prizeDialogVisible = false" type="success">确定</el-button>
                        </span>
                    </el-dialog>

                    <!-- 错误信息弹窗-->
                    <el-dialog :show-close="false" :visible.sync="errDialogVisible" center title="提示" top="27vh" width="5.8667rem">
                        <div class="errDialogContent" style="width: 100%; text-align: center;">
                            <p>{{ errMsg }}</p>
                        </div>
                        <span class="dialog-footer" slot="footer">
                        <el-button @click="errDialogVisible = false" type="success">确定</el-button>
                    </span>
                    </el-dialog>

                    <!--購物金抽奖提示-->
                    <el-dialog :show-close="false" title="提示" :visible.sync="isDialogVisible" center top="27vh" width="5.8667rem">
                        <div v-if="totalGw>useGw" class="subTitle">{{gwMsg}}</div>
                        <div class="integral-box">
                            <div>剩餘購物金<span class="integral"> {{totalGw}} </span></div>
                            <div v-if="totalGw>useGw">消耗<span class="integral"> {{useGw}} </span>購物金去抽獎</div>
                            <div v-else>購物金不足</div>
                        </div>
                        <span slot="footer" class="dialog-footer">
                        <el-button size="mini" @click="isDialogVisible = false">取 消</el-button>
                        <el-button size="mini" type="success" @click="queryGw">确 定</el-button>
                    </span>
                    </el-dialog>

                    <el-dialog title="提示" :visible.sync="gwDialogVisible" width="5.8667rem"  center top="27vh" :show-close="false">
                        <div class="errDialogContent">
                            <span>{{gwMsg}},<span v-if="gwStatus">剩餘{{totalGw}}購物金</span></span>
                        </div>
                        <span slot="footer" class="dialog-footer">
                        <el-button type="success" @click="gwDialogVisible = false">確定</el-button>
                    </span>
                    </el-dialog>
                </div>
            </div>
        </div>
        <div class="custom-notice-bar">
            <div class="notice-icon">
                <img src="__CDN__/assets/img/wap/new_index/broadcast.png" alt="">
            </div>
            <div class="notice-content-wrapper" ref="wrapper">
                <div
                        class="notice-content"
                        :class="{ 'marquee': shouldScroll }"
                        :style="scrollStyle"
                        @mouseenter="pauseScroll"
                        @mouseleave="resumeScroll"
                >
          <span v-for="(notice, index) in duplicatedNotices" :key="index">
            {{ notice }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
          </span>
                </div>
            </div>
        </div>
        <div class="title2-box">
            <div class="title2">活動註意事項</div>
            <div class="label"></div>
        </div>
        <div class="tips-box">
            <div class="tips-item">
                <div class="tips-dot"></div>
                <div class="tips-text">
                    每位會員每日免費抽取<span style="color: #ff0000; font-weight: bold; font-size: 0.32rem"> <?=$dr['freec']?></span> 次，未使用不可累加；
                </div>
            </div>
            <div class="tips-item">
                <div class="tips-dot"> </div>
                <div class="tips-text">
                    每日可消耗購物金<span style="color: #ff0000; font-weight: bold; font-size: 0.32rem"> <?=$dr['money']?> </span>獲取一次抽獎機會，每日可兌換<span style="color: #ff0000; font-weight: bold; font-size: 0.32rem"> <?=$dr['egc']?> </span>次
                </div>
            </div>
            <div class="tips-item">
                <div class="tips-dot"></div>
                <div class="tips-text">
                    折抵券僅限單筆訂單使用，不得與其他折扣同時使用；
                </div>
            </div>
            <div class="tips-item">
                <div class="tips-dot"></div>
                <div class="tips-text">
                    優惠券有效期：自發放日起 30 天內有效 。
                </div>
            </div>
        </div>

        <!-- 背景图 -->
        <image class="bgimg1" mode="scaleToFill" src="__CDN__/assets/img/wap/new_index/bgimg1.png"/>
        <image class="bgimg2" mode="scaleToFill" src="__CDN__/assets/img/wap/new_index/bgimg2.png"/>
        <!--    <image @click="closePage" class="bgimg3" mode="scaleToFill" src="__CDN__/assets/img/wap/new_index/close.png"/>-->
    </div>
</div>
<!--改成本地资源-->
<script src="__CDN__/assets/js/axios.min.js"></script>
<script>
    var dr = <?php echo json_encode($dr['id']); ?>;
    var list = <?php echo json_encode($list); ?>;
    var data = [];
    for (var i = 0; i < list.length; i++) {
        var money = Math.round(list[i]['money']);
        if (money >= 10000) {
            var str = (money / 10000) + "万";
            data[i] = {id: list[i]['id'], name: list[i]['title'], icon: 'NT$' + str};
        } else {
            if(list[i]['type']==1){
                // data[i] = { id:list[i]['id'],name: list[i]['title'], icon: '淘幣' + money};
                data[i] = { id:list[i]['id'],name: list[i]['title'], icon: list[i]['title'] + money};
            }else{
                data[i] = { id:list[i]['id'],name: list[i]['title'], icon: 'NT$' + money};
            }
        }
    }
    var ulist = <?php echo json_encode($ulist); ?>;
    var udata = [];
    for(var i=0; i<ulist.length;i++){
        udata[i] = "恭喜"+ulist[i]['username']+"用户抽中【"+ulist[i]['title']+"】";
    }
    const activitiesTime = {
        begTime: <?=$dr['begtime']?>,
        endTime: <?=$dr['endtime']?>
    }
    const app = new Vue({
        el: '#application',
        data: {
            currentSection: 0,
            isScrolling: false,
            prizes: data,
            currentPrizeIndex: 0,
            isRotating: false,
            rotationTimer: null,
            rotationSpeed: 100,
            rotationCount: 0,
            selectedPrize: {},
            isDialogVisible: false,
            prizeDialogVisible: false,
            errDialogVisible: false,
            gwDialogVisible: false,
            errMsg: '',
            notices: udata,
            contentWidth: 0,
            wrapperWidth: 0,
            gwMsg:'',
            totalGw: '',
            useGw:'',
            drawId:'',
            gwStatus:false,
            dr:dr,
            activitiesTime: activitiesTime
        },
        beforeDestroy() {
            clearTimeout(this.rotationTimer);
            window.removeEventListener('resize', this.calculateWidth);
        },
        computed: {
            // 复制内容实现无缝滚动
            duplicatedNotices() {
                return [...this.notices, ...this.notices];
            },
            shouldScroll() {
                return this.contentWidth > this.wrapperWidth;
            },
            scrollStyle() {
                return {
                    animationDuration: `${this.contentWidth / 50}s`,
                    animationPlayState: this.isHovering ? 'paused' : 'running'
                }
            }
        },
        mounted() {
            console.log(this.prizes, 999);
            this.$nextTick(() => {
                this.calculateWidth();
                window.addEventListener('resize', this.calculateWidth);
            });
        },
        methods: {
            timeConversion(time){
                const date = new Date(time * 1000);
                const year = date.getFullYear();
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const day = date.getDate().toString().padStart(2, '0');
                return `${year}-${month}-${day}`;
            },
            calculateWidth() {
                this.wrapperWidth = this.$refs.wrapper.offsetWidth;
                this.contentWidth = this.$refs.wrapper.querySelector('.notice-content').scrollWidth / 2;
            },
            pauseScroll() {
                this.isHovering = true;
            },
            resumeScroll() {
                this.isHovering = false;
            },

            // scrollToNext() {
            //     if (this.currentSection < 3) {
            //         this.scrollToSection(this.currentSection + 1)
            //     }
            // },

            startLottery() {
                this.isDialogVisible = false;
                // 重置旋转速度和计数
                this.rotationSpeed = 100;
                this.rotationCount = 0;

                if (this.isRotating) return;
                this.isRotating = true;
                axios({
                    method: 'post',
                    url: '/index/index/jdraw',
                    data: { dr: this.dr }
                }).then(res => {
                    if(res.data.rs === 0) {
                        this.errMsg = res.data.msg;
                        this.errDialogVisible = true;
                        this.isRotating = false;
                    } else if(res.data.rs === 2) {
                        this.drawId = res.data.id;
                        this.gwMsg = res.data.msg;
                        const gw = res.data.gw;
                        this.totalGw = Number(gw)
                        this.useGw = res.data.sub;
                        this.isDialogVisible = true;
                        this.isRotating = false;
                    } else if(res.data.rs === 1) {
                        this.handlePrizeResult(res.data.id);
                    }
                }).catch(error => {
                    this.isRotating = false;
                    this.errMsg = '抽獎失敗，請重試';
                    this.errDialogVisible = true;
                });
            },

            // 旋转奖品
            rotatePrize() {
                this.rotationTimer = setTimeout(() => {
                    // 更新当前高亮的奖品索引
                    this.currentPrizeIndex = (this.currentPrizeIndex + 1) % this.prizes.length
                    this.rotationCount++
                    // 根据旋转次数调整速度
                    if (this.rotationCount > 10) {  // 减少旋转次数
                        this.rotationSpeed += 20  // 加快速度增长
                    }
                    // 判断是否停止旋转
                    if (this.rotationSpeed >= 150 && this.currentPrizeIndex === this.prizes.indexOf(this.selectedPrize)) {
                        this.isRotating = false
                        this.prizeDialogVisible = true
                        clearTimeout(this.rotationTimer)
                    } else {
                        this.rotatePrize()
                    }
                }, this.rotationSpeed)
            },

            // 抽奖逻辑
            handlePrizeResult(prizeId) {
                this.prizes.forEach((item, index) => {
                    if (item.id === prizeId) {
                        this.selectedPrize = this.prizes[index];
                        this.rotatePrize();
                    }
                });
            },
            confirmLottery() {
                this.isDialogVisible = false;
                this.isRotating = true;
                this.handlePrizeResult(this.drawId);
            },

            // 查询购物金
            queryGw() {
                this.isDialogVisible = false;
                if(Number(this.totalGw) >= this.useGw) {
                    axios({
                        method: 'post',
                        url: '/index/index/get_draw_cout',
                        data: {
                            dr: this.dr,
                            sub: this.useGw,
                        }
                    }).then(res => {
                        console.log('res+++++++++', res);
                        this.gwMsg = res.data.msg;
                        this.totalGw = res.data.gw;
                        this.gwStatus = res.data.rs;
                        if (res.data.rs === 1) {
                            this.gwDialogVisible = true;
                            this.startLottery();
                        } else {
                            this.gwDialogVisible = true;
                            this.isRotating = false;
                        }
                    }).catch(error => {
                        console.error(error);
                        this.isRotating = false;
                        this.gwMsg = '请求失败，请重试';
                        this.gwDialogVisible = true;
                    });
                }
            },
        }
    })
</script>
</body>

</html>