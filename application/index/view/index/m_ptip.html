<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta id="viewportMeta" name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title></title>
    <style>
        * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        html,body{
            display: flex;
            justify-content: center;
            /* align-items: center; */
        }

        html {
            font-size: calc(100vw / 10); /* 核心公式 */
            /* 响应式限制 */
            @media (min-width: 768px) { font-size: 37.5px; }
            @media (max-width: 320px) { font-size: 32px; }
        }

        body { font-family: "Microsoft YaHei","PingFang SC"; }

        .fs12 {
            font-size: 0.32rem;
        }

        .fs14 {
            font-size: 0.37rem;
        }

        .fs16 {
            font-size: 0.43rem;
        }

        .fs18 {
            font-size: 0.48rem;
        }

        .fs20 {
            font-size: 0.53rem;
        }

        .fw {

            font-weight: bold;
        }

        .color-666 {
            color: #666666;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 1.25rem;
            padding: 0 0.2667rem;
        }
        .logo {
            height: 1.25rem;
        }

        .logo img {
            height: 100%;
            object-fit: contain;
        }

        .home-page {
            width: 1.96rem; /* 73.53/37.5≈1.96rem */
            height: 0.694rem; /* 26.03/37.5≈0.694rem */
        }

        .back {
            width: 0.56rem; /* 21/37.5=0.56rem */
            height: 0.56rem;
            line-height: 0;
        }

        .back img {
            display: block;
            width: 100%;
            height: 100%;
            vertical-align: middle;
        }

        .head_login_btn {
            display: flex;
            justify-content: center;
            align-items: flex-end;
        }

        .login-btn {
            height: 0.59rem;
            line-height: 0.59rem;
            font-size: 0.35rem;
            color: #666666;
            text-align: center;
        }

        .login-btn a{
            font-weight: normal;
            color:inherit;
            cursor:auto;
        }

        .reg-btn {
            text-align: center;
            line-height: 0.53rem;
            width: 1.87rem;
            height: 0.59rem;
            font-size: 0.32rem;
            background: #EF436D;
            margin-left: 0.32rem;
            color: #fff;
            border-radius: 7.73rem;
            border: 0.03rem solid #EF436D;
        }

        .reg-btn a{
            font-weight: normal;
            text-decoration:none;
            color:inherit;
            cursor:auto;
        }

        .activity-register {
            width: 10rem;
            height: 100vh;
            background: linear-gradient(180deg, #EC9CD0 0%, #FFFEFE 100%);
            border: 0.03rem solid #FFF4D5;
            display: flex;
            position: relative;
            flex-direction: column;
            align-items: center;
        }

        .title {
            font-size: 0.53rem;
            font-weight: 500;
            line-height: normal;
            text-align: center;
            color: #FFFFFF;
            margin-top: 0.96rem;
        }

        .title2-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0.43rem 0 0.21rem 0;
        }

        .title2 {
            font-size: 0.43rem;
            line-height: 0.43rem;
            text-align: center;
            color: #EF436D;
        }

        .label {
            width: 0.054rem;
            height: 0.32rem;
            transform: rotate(-90deg);
            border-radius: 0.43rem;
            background: #EF436D;
        }

        .center-box {
            width: 8.72rem;
            height: auto;
            border-radius: 0.43rem;
            background: rgba(255, 255, 255, 0.52);
            padding: 1.55rem 0.67rem;
        }

        .color-red {
            color: #EF436D;
        }

        .tc {
            text-align: center;
            font-size: 0.37rem;
            line-height: 0.59rem;
        }

        .center-box-item {
            display: flex;
            justify-content: space-between;

        }

        .center-box-item .img-box {
            width: 65%;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .center-box-item image {
            width: 5.73rem;
            height: 2.88rem;
        }


        /* 活动提示 */
        .tips-box{
            margin-top: 0.37rem;
        }

        .tips-item {
            width: 8.72rem;
            display: flex;
            align-items: center;
            color: #333333;
            font-size: 0.29rem;
            line-height: 0.37rem;
            margin-bottom: 0.21rem;
        }

        .tips-dot {
            position: relative;
            width: 0.03rem;
            height: 0.03rem;
            transform: translateZ(0);
            margin: 0 0.32rem 0 0.21rem;
        }

        .tips-dot::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            width: 0.32rem;
            height: 0.32rem;
            background: rgba(242, 105, 138, 0.6);
            border-radius: 50%;
            transform: translate(-50%, -50%) scale(0.5);
            transform-origin: center;
            box-shadow: 0 0 0.5px rgba(0, 0, 0, 0.1);
        }

        .tips-text{
            text-align: justify;
        }

        .bgimg1 {
            position: absolute;
            width: 1.89rem;
            height: 1.89rem;
            left: 0;
            top: 0.11rem;
        }

        .bgimg2 {
            position: absolute;
            width: 1.12rem;
            height: 1.81rem;
            right: 0.11rem;
            top: 0;
        }

        .bgimg3{
            position: absolute;
            width: 0.64rem;
            height: 0.64rem;
            left: 50%;
            transform: translate(-50%);
            bottom: -1.09rem;
        }

    </style>
    <script src="__CDN__/assets/js/vue.js"></script>
</head>

<body>
<div id="id">
    <div class="header clearfix flex_ac pad_lr20">
        <a href="__CDN__/" class="logo"><img class="home-page" src="__CDN__/assets/img/wap/logo.png"></a>

        <?php
		if($user){
	?>
        <a href="javascript:window.history.go(-1);" class="close_b back margin_a"><img src="__CDN__/assets/img/wap/header_close2.png"></a>
        <?php
		}else{
	?>
        <div class="flex_ac head_login_btn">
            <div class="login-btn">
                <a  href="{:url('/index/login/login')}">會員登陸</a>
            </div>
            <div class="reg-btn">
                <a  href="{:url('/index/login/register')}">免費註冊</a>
            </div>
        </div>
        <?php
		}
	?>
    </div>
    <div id="app" class="activity-register">
        <div class="title">抵扣金優惠</div>
        <div class="title2-box">
            <div class="title2">優惠内容</div>
            <div class="label"></div>
        </div>
        <div class="center-box">
            <div class="tc">滿 <span class="color-red fs14 fw ">100</span> 元人民幣消費，即可抵扣 <span
                    class="color-red fs14 fw ">2</span> 元</div>
            <div class="tc">滿 <span class="color-red fs14 fw ">200</span> 元人民幣消費，即可抵扣 <span
                    class="color-red fs14 fw ">4</span> 元</div>
            <div class="tc">滿 <span class="color-red fs14 fw ">300</span> 元人民幣消費，即可抵扣 <span
                    class="color-red fs14 fw ">6</span> 元</div>
            <div class="tc">...</div>
            <div class="tc">（以此類推，抵扣無上限）</div>
            <!-- <view>滿 100 元人民幣消費，即可抵扣 2 元
                滿 200 元人民幣消費，即可抵扣 4 元
                滿 300 元人民幣消費，即可抵扣 6 元
                ...
                （以此類推，抵扣無上限）</view> -->
        </div>
        <div class="title2-box" style="margin-bottom: 0;">
            <div class="title2">活動註意事項</div>
            <div class="label"></div>
        </div>
        <div class="tips-box">
            <div class="tips-item">
                <div class="tips-dot"> </div>
                <div class="tips-text">
                    適用範圍：所有使用人民幣計價之代付／代購訂單；
                </div>
            </div>
            <div class="tips-item">
                <div class="tips-dot" style="margin-top: -0.32rem"> </div>
                <div class="tips-text">
                    自動計算：結帳時計算符合條件之抵扣金，自動扣減應付金額；
                </div>
            </div>
            <div class="tips-item">
                <div class="tips-dot"> </div>
                <div class="tips-text">
                    無需申請：系統自動累計並折抵，長期有效。
                </div>
            </div>
        </div>
        <!-- <div class="bottom-btn pointer">
            立即參與
        </div> -->

        <!-- 背景图 -->
        <image src="__CDN__/assets/img/wap/new_index/bgimg1.png" class="bgimg1" mode="scaleToFill"/>
        <image src="__CDN__/assets/img/wap/new_index/bgimg2.png" class="bgimg2" mode="scaleToFill"/>
        <!--    <image src="__CDN__/assets/img/wap/new_index/close.png" class="bgimg3" mode="scaleToFill" @click="closePop" />-->
    </div>
</div>

<script>
    function setViewportScale() {
        const designWidth = 375; // 设计稿宽度
        const currentWidth = document.documentElement.clientWidth;
        const scale = currentWidth / designWidth;
        document.getElementById('viewportMeta').content =
            `width=${designWidth}, initial-scale=${scale}, maximum-scale=${scale}, minimum-scale=${scale}, user-scalable=no`;
    }
    // 初始化时设置
    setViewportScale();
    // 处理横竖屏切换（可选）
    window.addEventListener('resize', setViewportScale);
    const app = new Vue({
        el: '#app',
        data: {
            showPop:true,
        },
        mounted() {
            console.log(getComputedStyle(document.documentElement).fontSize)
            document.querySelector('.header').offsetHeight // 应≈46.88px
        },
        methods: {
            // closePop() {
            //     if (window.history.length > 1) {
            //         window.history.back();
            //     } else {
            //         // 如果浏览器没有历史记录，跳转到指定页面
            //         window.location.href = '/'; // 替换为你的默认首页
            //     }
            // }
        }
    })
</script>
</body>

</html>