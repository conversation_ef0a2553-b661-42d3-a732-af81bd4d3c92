<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta id="viewportMeta" name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title></title>
    <style>
        * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        html,body{
            display: flex;
            justify-content: center;
            /* align-items: center; */
        }

        html {
            font-size: calc(100vw / 10); /* 核心公式 */
            /* 响应式限制 */
            @media (min-width: 768px) { font-size: 37.5px; }
            @media (max-width: 320px) { font-size: 32px; }
        }

        body { font-family: "Microsoft YaHei","PingFang SC"; }

        img {
            max-width: 100%;
            height: auto;
        }

        .lh {
            line-height: 0.37rem;
        }

        .fs10 {
            font-size: 0.27rem;
        }

        .fs11 {
            font-size: 0.29rem;
        }

        .fs12 {
            font-size: 0.32rem;
        }

        .fs14 {
            font-size: 0.37rem;
        }

        .fs16 {
            font-size: 0.43rem;
        }

        .fs18 {
            font-size: 0.48rem;
        }

        .fs20 {
            font-size: 0.53rem;
        }

        .fw {
            font-weight: 700;
        }

        .color-666 {
            color: #666666;
        }

        .color-333 {
            color: #333333;
        }


        .none-default {
            text-decoration: none;
            color: inherit;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 1.25rem;
            padding: 0 0.2667rem;
        }
        .logo {
            height: 1.25rem;
        }

        .logo img {
            height: 100%;
            object-fit: contain;
        }

        .home-page {
            width: 1.96rem; /* 73.53/37.5≈1.96rem */
            height: 0.694rem; /* 26.03/37.5≈0.694rem */
        }

        .back {
            width: 0.56rem; /* 21/37.5=0.56rem */
            height: 0.56rem;
            line-height: 0;
        }

        .back img {
            display: block;
            width: 100%;
            height: 100%;
            vertical-align: middle;
        }

        .head_login_btn {
            display: flex;
            justify-content: center;
            align-items: flex-end;
        }

        .login-btn {
            height: 0.59rem;
            line-height: 0.59rem;
            font-size: 0.35rem;
            color: #666666;
            text-align: center;
        }

        .login-btn a{
            font-weight: normal;
            color:inherit;
            cursor:auto;
        }

        .reg-btn {
            text-align: center;
            line-height: 0.53rem;
            width: 1.87rem;
            height: 0.59rem;
            font-size: 0.32rem;
            background: #EF436D;
            margin-left: 0.32rem;
            color: #fff;
            border-radius: 7.73rem;
            border: 0.03rem solid #EF436D;
        }

        .reg-btn a{
            font-weight: normal;
            text-decoration:none;
            color:inherit;
            cursor:auto;
        }

        .activity-register {
            width: 10rem;
            background: linear-gradient(180deg, #EC9CD0 0%, #FFFEFE 100%);
            border: 0.03rem solid #FFF4D5;
            display: flex;
            position: relative;
            flex-direction: column;
            align-items: center;
        }

        .title {
            font-size: 0.48rem;
            font-weight: bold;
            line-height: normal;
            text-align: center;
            color: #FFFFFF;
            margin-top: 0.96rem;
        }

        .title2-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0.43rem 0 0.21rem;
        }

        .title2 {
            font-size: 0.43rem;
            line-height: 0.43rem;
            text-align: center;
            color: #EF436D;
        }

        .label {
            width: 0.054rem;
            height: 0.32rem;
            transform: rotate(-90deg);
            border-radius: 0.43rem;
            background: #EF436D;
        }

        /* 优惠券 */
        .center-box {
            width: 8.72rem;
            height: auto;
            border-radius: 0.43rem;
            background: rgba(255, 255, 255, 0.52);
            padding: 0.43rem 0.37rem;
        }

        .center-box-top {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            margin-bottom: 0.21rem;
        }

        .top-item {
            height: auto;
            width: 6.08rem;
            background: url('__CDN__/assets/img/wap/new_index/quan-bg.png') no-repeat;
            background-size: 100% 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            color: #FFFFFF;
            margin-bottom: 0.21rem;
        }

        .top-item-top {
            width: 2.53rem;
            height: 1.28rem;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: space-around;
            color: #EF436D;
            vertical-align: top;
            line-height: 0;
        }

        .top-item-top > * {
            display: block;
        }

        .top-item-btn {
            width: 3.52rem;
            height: 1.28rem;
            line-height: 1.28rem;
            text-align: center;
            font-size: 0.32rem;
            color: #EF436D;
        }


        /* 活动提示 */
        .tips-box {
            margin-top: 0.37rem;
        }

        .tips-item {
            width: 8.72rem;
            display: flex;
            align-items: center;
            color: #333333;
            font-size: 0.29rem;
            line-height: 0.37rem;
            margin-bottom: 0.21rem;
        }

        .tips-dot {
            position: relative;
            width: 0.03rem;
            height: 0.03rem;
            transform: translateZ(0);
            margin: -0.32rem 0.32rem 0 0.21rem;
        }

        .tips-dot::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            width: 0.32rem;
            height: 0.32rem;
            background: rgba(242, 105, 138, 0.6);
            border-radius: 50%;
            transform: translate(-50%, -50%) scale(0.5);
            transform-origin: center;
            box-shadow: 0 0 0.5px rgba(0, 0, 0, 0.1);
        }

        .tips-text{
            text-align: justify;
        }

        /* 按钮 */
        .bottom-btn {
            width: 2.88rem;
            height: 0.96rem;
            line-height: 0.96rem;
            text-align: center;
            color: #FFFFFF;
            font-size: 0.4rem;
            border-radius: 7.73rem;
            background: #EF436D;
            margin: 0.75rem 0 0.85rem 0;
        }

        .bgimg1 {
            position: absolute;
            width: 1.89rem;
            height: 1.89rem;
            left: 0;
            top: 0.11rem;
        }

        .bgimg2 {
            position: absolute;
            width: 1.12rem;
            height: 1.81rem;
            right: 0.11rem;
            top: 0;
        }
    </style>
    <script src="__CDN__/assets/js/vue.js"></script>
</head>

<body>
<div id="app">
    <div class="header clearfix flex_ac pad_lr20">
        <a href="__CDN__/" class="logo"><img class="home-page" src="__CDN__/assets/img/wap/logo.png"></a>

        <?php
		if($user){
	?>
        <a href="javascript:window.history.go(-1);" class="close_b back margin_a"><img src="__CDN__/assets/img/wap/header_close2.png"></a>
        <?php
		}else{
	?>
        <div class="flex_ac head_login_btn">
            <div class="login-btn">
                <a  href="{:url('/index/login/login')}">會員登陸</a>
            </div>
            <div class="reg-btn">
                <a  href="{:url('/index/login/register')}">免費註冊</a>
            </div>
        </div>
        <?php
		}
	?>
    </div>
    <div class="activity-register">
        <div class="title">註冊即送運費抵扣券</div>
        <div class="title2-box">
            <div class="title2">新會員註冊禮包</div>
            <div class="label"></div>
        </div>
        <div class="center-box">
            <div class="center-box-top">
                {volist name="list" id="vo"  }
                <div class="top-item" >
                    <div  class="top-item-top">
                        <div class="">
                            <span class="fs12">NT$</span>
                            <span class="fs20 fw">{$vo['reduce_amount']}</span>
                        </div>
                        <div class="fs12">折抵券</div>
                    </div>
                    {if $vo['reaching_amount']<100}
                    <div class="top-item-btn">無門檻</div>
                    {else /}
                    <div class="top-item-btn">滿{$vo['reaching_amount']}可用</div>
                    {/if}
                </div>
                {/volist}
            </div>
            <div class="fs11 lh" style="text-align: justify;"><span class="color-666">領取資格：</span> 首次註冊並完成實名認證之新會員禮包（共 1020
                元折扣券，NT$20/1張、NT$50/2張、NT$100/2張、NT$200/2張、NT$300/1張）
            </div>
            <div class="fs11 lh" style="margin-top: 0.21rem; text-align: justify;"><span class="color-666">使用方式：</span>
                註冊完成後自動發放至「我的優惠券」；單筆訂單結帳時可分次抵用，直至用罄為止。
            </div>
            <div class="fs11 lh" style="margin-top: 0.21rem;"><span class="color-666">有效期限：</span> 自發放日起 30 日內使用有效。
            </div>
        </div>
        <div class="title2-box">
            <div class="title2">活動註意事項</div>
            <div class="label"></div>
        </div>
        <div class="tips-box">
            <div class="tips-item">
                <div class="tips-dot"></div>
                <div class="tips-text"> 優惠券僅限單筆訂單使用，且不可與其他優惠同時使用（除長期抵扣金外）；</div>
            </div>
            <div class="tips-item">
                <div class="tips-dot"></div>
                <div class="tips-text"> 折抵後訂單金額最低需達券面金額要求，未達門檻則無法使用；</div>
            </div>
            <div class="tips-item">
                <div class="tips-dot"></div>
                <div class="tips-text"> 活動及優惠券最終解釋權歸付唄所有，如有異動，將以官方公告為準；</div>
            </div>
            <div class="tips-item">
                <div class="tips-dot"></div>
                <div class="tips-text"> 欲了解更多詳情，請至 付唄官網 或聯絡客服。祝您購物愉快！</div>
            </div>
        </div>
        <div class="bottom-btn ">
            <a href="{:url('/index/login/register/')}" class="pull-right none-default">點擊註冊</a>
        </div>

        <!-- 背景图 -->
        <image src="__CDN__/assets/img/wap/new_index/bgimg1.png" class="bgimg1" mode="scaleToFill"/>
        <image src="__CDN__/assets/img/wap/new_index/bgimg2.png" class="bgimg2" mode="scaleToFill"/>
    </div>
</div>

<script>
    function setViewportScale() {
        const designWidth = 375; // 设计稿宽度

        const currentWidth = document.documentElement.clientWidth;
        const scale = currentWidth / designWidth;

        document.getElementById('viewportMeta').content =
            `width=${designWidth}, initial-scale=${scale}, maximum-scale=${scale}, minimum-scale=${scale}, user-scalable=no`;
    }

    // 初始化时设置
    setViewportScale();
    // 处理横竖屏切换（可选）
    window.addEventListener('resize', setViewportScale);
    const app = new Vue({
        el: '#app',
        methods: {
            // closePop() {
            //     if (window.history.length > 1) {
            //         window.history.back();
            //     } else {
            //         // 如果浏览器没有历史记录，跳转到指定页面
            //         window.location.href = '/'; // 替换为你的默认首页
            //     }
            // }
        }
    })
</script>
</body>

</html>