<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta id="viewportMeta" name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title></title>
    <link rel="stylesheet" href="__CDN__/assets/css/element-ui-index.css">
    <style>
        * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        html,body{
            display: flex;
            justify-content: center;
            /* align-items: center; */
        }

        html {
            font-size: calc(100vw / 10); /* 核心公式 */
            /* 响应式限制 */
            @media (min-width: 768px) { font-size: 37.5px; }
            @media (max-width: 320px) { font-size: 32px; }
        }

        body { font-family: "Microsoft YaHei","PingFang SC"; }

        img {
            max-width: 100%;
            height: auto;
        }

        .none-default {
            text-decoration: none;
            color: inherit;
        }

        .content-box {
            font-size: 0.37rem;
            line-height: 0.48rem;
            color: #333333;
        }

        .content-box-img {
            margin-top: 0.32rem;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 0.43rem;
        }

        .content-box-img2{
            margin-top: 0.32rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1.12rem;
        }

        .content-box-img2 img {
            width: 1.92rem;
            height: 3.41rem;
        }

        .content-box-img img {
            width: 1.92rem;
            height: 3.41rem;
        }

        .fs12 {
            font-size: 0.32rem;
        }

        .fs14 {
            font-size: 0.37rem;
        }

        .fs16 {
            font-size: 0.43rem;
        }

        .fs18 {
            font-size: 0.48rem;
        }

        .fs20 {
            font-size: 0.53rem;
        }

        .fw {
            font-weight: bold;
        }

        .color-666 {
            color: #666666;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 1.25rem;
            padding: 0 0.2667rem;
        }
        .logo {
            height: 1.25rem;
        }

        .logo img {
            height: 100%;
            object-fit: contain;
        }

        .home-page {
            width: 1.96rem; /* 73.53/37.5≈1.96rem */
            height: 0.694rem; /* 26.03/37.5≈0.694rem */
        }

        .back {
            width: 0.56rem; /* 21/37.5=0.56rem */
            height: 0.56rem;
            line-height: 0;
        }

        .back img {
            display: block;
            width: 100%;
            height: 100%;
            vertical-align: middle;
        }

        .head_login_btn {
            display: flex;
            justify-content: center;
            align-items: flex-end;
        }

        .login-btn {
            height: 0.59rem;
            line-height: 0.59rem;
            font-size: 0.35rem;
            color: #666666;
            text-align: center;
        }

        .login-btn a{
            font-weight: normal;
            color:inherit;
            cursor:auto;
        }

        .reg-btn {
            text-align: center;
            line-height: 0.53rem;
            width: 1.87rem;
            height: 0.59rem;
            font-size: 0.32rem;
            background: #EF436D;
            margin-left: 0.32rem;
            color: #fff;
            border-radius: 7.73rem;
            border: 0.03rem solid #EF436D;
        }

        .reg-btn a{
            font-weight: normal;
            text-decoration:none;
            color:inherit;
            cursor:auto;
        }

        .activity-register {
            width: 10rem;
            background: linear-gradient(180deg, #EC9CD0 0%, #FFFEFE 100%);
            border: 0.03rem solid #FFF4D5;
            display: flex;
            position: relative;
            flex-direction: column;
            align-items: center;
        }

        .title {
            font-size: 0.48rem;
            font-weight: bold;
            line-height: normal;
            text-align: center;
            color: #FFFFFF;
            margin-top: 0.96rem;
        }

        .title2-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0.43rem 0 0.2133rem 0;
        }

        .title2 {
            font-size: 0.43rem;
            line-height: 0.43rem;
            text-align: center;
            color: #EF436D;
        }

        .label {
            width: 0.054rem;
            height: 0.32rem;
            transform: rotate(-90deg);
            border-radius: 0.43rem;
            background: #EF436D;
        }

        .center-box {
            width: 8.72rem;
            height: auto;
            border-radius: 0.43rem;
            background: rgba(255, 255, 255, 0.52);
            padding: 0.37rem 0.2133rem;
        }

        .center-box-item {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 0.2133rem;
        }

        .step {
            margin-top: -4.24rem;
            line-height: 0.32rem;
            font-size: 0.37rem;
        }

        /* 活动提示 */
        .tips-box {
            margin-top: 0.37rem;
        }

        .tips-item {
            width: 8.72rem;
            display: flex;
            align-items: center;
            color: #333333;
            font-size: 0.2933rem;
            line-height: 0.37rem;
            margin-bottom: 0.2133rem;
        }

        .tips-dot {
            position: relative;
            width: 0.0266rem;
            height: 0.0266rem;
            transform: translateZ(0);
            margin-right: 0.32rem;
            margin-left: 0.2133rem;
        }

        .tips-dot::after {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            width: 0.32rem;
            height: 0.32rem;
            background: rgba(242, 105, 138, 0.6);
            border-radius: 50%;
            transform: translate(-50%, -50%) scale(0.5);
            transform-origin: center;
            box-shadow: 0 0 0.5px rgba(0, 0, 0, 0.1);
        }

        .tips-text{
            text-align: justify;
            line-height: 0.32rem;
        }

        /* 按钮 */
        .bottom-btn {
            width: 2.88rem;
            height: 0.96rem;
            line-height: 0.96rem;
            text-align: center;
            color: #FFFFFF;
            font-size: 0.4rem;
            border-radius: 7.7333rem;
            background: #EF436D;
            margin: 0.7466rem 0 0.8533rem 0;
        }

        .bgimg1 {
            position: absolute;
            width: 1.8933rem;
            height: 1.8933rem;
            left: 0;
            top: 0.1066rem;
        }

        .bgimg2 {
            position: absolute;
            width: 1.12rem;
            height: 1.8133rem;
            right: 0.1066rem;
            top: 0;
        }

        .bgimg3{
            position: absolute;
            width: 0.64rem;
            height: 0.64rem;
            left: 50%;
            transform: translate(-50%);
            bottom: -1.0933rem;
        }
    </style>
    <script src="__CDN__/assets/js/vue.js"></script>
    <script src="__CDN__/assets/js/element-ui-index.js"></script>
</head>

<body>
<div id="app">
    <div class="header clearfix flex_ac pad_lr20">
        <a href="__CDN__/" class="logo"><img class="home-page" src="__CDN__/assets/img/wap/logo.png"></a>

        <?php
		if($user){
	?>
        <a href="javascript:window.history.go(-1);" class="close_b back margin_a"><img class="back" src="__CDN__/assets/img/wap/header_close2.png"></a>
        <?php
		}else{
	?>
        <div class="flex_ac head_login_btn">
            <div class="login-btn">
                <a  href="{:url('/index/login/login')}">會員登陸</a>
            </div>
            <div class="reg-btn">
                <a  href="{:url('/index/login/register')}">免費註冊</a>
            </div>
        </div>
        <?php
		}
	?>
    </div>
    <div class="activity-register">
        <div class="title">推薦好友用付唄享好禮</div>
        <div class="title2-box">
            <div class="title2">參與方式</div>
            <div class="label"></div>
        </div>
        <div class="center-box">
            <div class="center-box-item">
                <div class="step" style="color: #EF436D;">Step1:</div>
                <div class="content-box">
                    <span>老用戶進入會員中心點擊【推廣】進入【我的推廣碼】複製推廣碼發送給新用戶</span>
                    <div class="content-box-img">
                        <div class="demo-image__preview"  v-for="(url,index) in imgList1" :key="index">
                            <el-image
                                    :src="url"
                                    :preview-src-list="imgList1">
                            </el-image>
                        </div>
                    </div>
                </div>
                <!-- <div class="img-box">
                    <image src="./img/friend1.png" mode="scaleToFill" />
                    <image src="./img/friend2.png" mode="scaleToFill" />
                </div> -->
            </div>
            <div class="center-box-item" style="margin-top: 0.48rem;">
                <div class="step" style="color: #EF436D;">Step2:</div>
                <div class="content-box">新用戶註冊付唄時在【邀請碼】選項欄輸入推廣碼即可完成註冊
                    <div class="content-box-img2">
                        <div class="demo-image__preview"  v-for="(url,index) in imgList2" :key="index">
                            <el-image
                                    :src="url"
                                    :preview-src-list="imgList2">
                            </el-image>
                        </div>
                    </div>
                </div>

                <!-- <div class="img-box" style="justify-content: center;">
                    <image src="./img/friend3.png" mode="scaleToFill" />
                </div> -->
            </div>
        </div>
        <div class="title2-box" style="margin-bottom: 0;">
            <div class="title2">活動註意事項</div>
            <div class="label"></div>
        </div>
        <div class="tips-box">
            <div class="tips-item">
                <div class="tips-dot"> </div>
                <div class="tips-text">
                    <span class="color-666">活動對象：</span>
                    所有已註冊付唄會員；
                </div>
            </div>
            <div class="tips-item">
                <div class="tips-dot" style="margin: -0.2933rem 0 0 0.242rem"> </div>
                <div class="tips-text" style="margin-left: 0.32rem">
                    <span class="color-666">活動內容：</span>
                    每成功邀請 1 位新用戶註冊並完成首筆訂單，即可獲得 NT$20 無門檻抵用券；
                </div>
            </div>
            <div class="tips-item">
                <div class="tips-dot"> </div>
                <div class="tips-text">
                    <span class="color-666">活動對象：</span>
                    所有已註冊付唄會員；
                </div>
            </div>
            <div class="tips-item">
                <div class="tips-dot" style="margin: -0.2933rem 0 0 0.242rem"> </div>
                <div class="tips-text" style="margin-left: 0.32rem">
                    <span class="color-666">發放方式：</span>
                    系統自動將抵用券發放至「我的優惠券」頁面，無需額外申請；
                </div>
            </div>
            <div class="tips-item">
                <div class="tips-dot" style="margin: -0.2933rem 0 0 0.242rem"> </div>
                <div class="tips-text" style="margin-left: 0.32rem">
                    <span class="color-666">使用規則：</span>
                    無金額門檻，可直接折抵任一訂單金額，新用戶僅能被推薦 1 次，邀請人獎勵無上限累計；
                </div>
            </div>
            <div class="tips-item">
                <div class="tips-dot"> </div>
                <div class="tips-text">
                    <span class="color-666">使用期限：</span>
                    抵用券自發放日起 30 天內有效。
                </div>
            </div>

        </div>
        <div class="bottom-btn pointer">
            <a href="{:url('/index/user/index/')}" class="pull-right none-default">立即參與</a>
        </div>

        <!-- 背景图 -->
        <image src="__CDN__/assets/img/wap/new_index/bgimg1.png" class="bgimg1" mode="scaleToFill"/>
        <image src="__CDN__/assets/img/wap/new_index/bgimg2.png" class="bgimg2" mode="scaleToFill"/>
        <!--    <image src="__CDN__/assets/img/wap/new_index/close.png" class="bgimg3" mode="scaleToFill" @click="closePop" />-->
    </div>
</div>

<script>
    function setViewportScale() {
        const designWidth = 375; // 设计稿宽度
        const currentWidth = document.documentElement.clientWidth;
        const scale = currentWidth / designWidth;

        document.getElementById('viewportMeta').content =
            `width=${designWidth}, initial-scale=${scale}, maximum-scale=${scale}, minimum-scale=${scale}, user-scalable=no`;
    }
    // 初始化时设置
    setViewportScale();
    // 处理横竖屏切换（可选）
    window.addEventListener('resize', setViewportScale);
    const app = new Vue({
        el: '#app',
        data: {
            imgList1:[
                '__CDN__/assets/img/wap/new_index/friend1.png',
                '__CDN__/assets/img/wap/new_index/friend2.png'
            ],
            imgList2:[
                '__CDN__/assets/img/wap/new_index/friend3.png'
            ],
        },
        methods: {
            // closePop() {
            //     if (window.history.length > 1) {
            //         window.history.back();
            //     } else {
            //         // 如果浏览器没有历史记录，跳转到指定页面
            //         window.location.href = '/'; // 替换为你的默认首页
            //     }
            // }
        }
    })
</script>
</body>

</html>