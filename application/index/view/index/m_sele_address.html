<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 pad_b40">
    <div class="color_red pad_t10 my_close_win_title">恭喜您中獎啦！請選擇收貨地址吧！</div>
    <form action="{:url('index/index/sele_address')}">
        <input type="hidden" class="js_jpid" name="jpid" value="">
        <input type="hidden" class="js_id" name="id" value="">
        <div class="address_win pad_tb20">
		<?php
			foreach($address as $ls){
		?>
			<div class="box clearfix" data-id="<?=$ls['id']?>">
                <div class="name ellipsis"><?=$ls['address_name']?>  <span><?=substr_replace($ls['address_mobile'],'*',0,4)?></span></div>
                <div class="text"><?=$_address[$ls['city']]['name']?><?=$ls['address']?></div>
            </div>
		<?php
			}
		?>
        </div>
        <div class="text-center pad_t30">
            <a href="<?=url('index/add_address')?>" class="sub_btn2 dark js_ajax_win js_sele">新增地址</a>
            <a href="javascript:;" class="sub_btn2 js_form_sub" data-func_before="before_sub" data-text="確認" data-loadding="確認中···" data-type="new_location">確認</a>
        </div>
    </form>
</div>
<style type="text/css">
    .address_win .box{margin-bottom: 10px; padding: 0.1rem 0.2rem; box-shadow: 0px 2px 20px 0px rgba(0, 0, 0, 0.05); border-radius: 5px; background: #fff; border: 1px solid #fff}
    .address_win .box .name{line-height: 24px; position: relative; }
    .address_win .box .name span{ font-size: 12px; color: #666666 }
    .address_win .box.active{ border: 1px solid #EF436D; }
    .address_win .box .text{color: #666; line-height: 22px; font-size: 12px;}


    body .sub_btn2{
        color: #fff;
        display: inline-block;
        width: 100px;
        line-height: 28px;
        border-radius: 2px;
        text-align: center;
        color: #fff;
        background: #EF436D;
        border: 1px solid #EF436D;
        margin-right: 10px;
         font-size: 12px;
    }
    body .sub_btn2.disabled{ opacity: 0.7 }
    body .sub_btn2.dark{border-color: #DDDDDD; color: #999999; background: none}
    .small_input .moni_select{height: 34px;}
    .small_input .moni_select .moni_selected{height: 32px; line-height: 32px}
    .small_input .moni_select .moni_s_down{top: 34px}
    .small_input .moni_select .moni_selected img.arr{margin-top: 13px}
</style>
<script type="text/javascript">
    $('.js_jpid').val(jp_id);
    function before_sub(){
        if($('.js_id').val()==''){
            tip_show('請選擇收貨地址','2')
            return false;
        }
        return true
    }
    $('.address_win .box').click(function(){
        var _this=$(this);
        _this.addClass('active').siblings('.box').removeClass('active');
        $('.js_id').val(_this.attr('data-id'))
    })
</script>