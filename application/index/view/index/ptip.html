<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <style>
        * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei","Helvetica Neue",Helvetica,Arial,sans-serif;
        }

        .fs12 {
            font-size: 12px;
        }

        .fs14 {
            font-size: 14px;
        }

        .fs16 {
            font-size: 16px;
        }

        .fs18 {
            font-size: 18px;
        }

        .fs20 {
            font-size: 20px;
        }

        .fw {
            font-weight: bold;
        }

        .color-666 {
            color: #666666;
        }

        html {
            background-color: #787878;
        }

        .pointer {
            cursor: pointer;
        }

        .activity-register {
            /*关闭按钮样式同步这里宽高和margin-top*/
            /* margin-top: 100px; */
            position: relative;
            width: 867px;
            height: 780px;
            background: linear-gradient(180deg, #EC9CD0 0%, #FFFEFE 100%);
            border: 1px solid #FFF4D5;
            border-radius: 32px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .title {
            margin-top: 31px;
            font-size: 48px;
            font-weight: 500;
            line-height: normal;
            text-align: center;
            color: #FFFFFF;
        }

        .title2-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 26px 0;
        }

        .title2 {
            font-size: 20px;
            line-height: 22px;
            text-align: center;
            color: #EF436D;
        }

        .label-line {
            width: 3px;
            height: 24px;
            transform: rotate(-90deg);
            border-radius: 16px;
            background: #EF436D;
        }

        .center-box {
            width: 786px;
            height: 222px;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.52);
            padding: 28px;
        }

        .color-red{
            color: #EF436D;
        }

        .tc{
            text-align: center;
        }

        .center-box-item {
            display: flex;
            justify-content: space-between;

        }

        .center-box-item .img-box {
            width: 65%;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .center-box-item image {
            width: 214.95px;
            height: 108px;
        }


        /* 活动提示 */
        .tips-item {
            width: 786px;
            display: flex;
            align-items: center;
            color: #333333;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .tips-dot {
            width: 8px;
            height: 8px;
            background: rgba(242, 105, 138, 0.6);
            border-radius: 50%;
            margin: 0 10px;
        }

        /* 按钮 */
        .bottom-btn {
            width: 180px;
            height: 48px;
            line-height: 48px;
            text-align: center;
            color: #FFFFFF;
            font-size: 20px;
            border-radius: 290px;
            background: #EF436D;
            position: absolute;
            bottom: 32px;
            left: 50%;
            transform: translateX(-50%);
        }

        .bgimg1 {
            position: absolute;
            width: 144px;
            height: 144px;
            left: 0;
            top: 10px;
        }

        .bgimg2 {
            position: absolute;
            width: 90px;
            height: 144px;
            right: 40px;
            bottom: 10px;
        }
        .main-box{
            height: 100vh;width:100vw;background: rgba(0,0,0,0.6);position: fixed;top: 0;left: 0;z-index: 100;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    </style>
    <script src="__CDN__/assets/js/vue.js"></script>
</head>

<body>
<div id="app" class="activity-register">
    <!--    顶部关闭-->
    <a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
    <div class="title">抵扣金優惠</div>
    <div class="title2-box">
        <div class="title2">優惠内容</div>
        <div class="label-line"></div>
    </div>
    <div class="center-box">
        <div class="tc">滿 <span class="color-red fs20 fw ">100</span> 元人民幣消費，即可抵扣 <span class="color-red fs20 fw ">2</span> 元</div>
        <div class="tc">滿 <span class="color-red fs20 fw ">200</span> 元人民幣消費，即可抵扣 <span class="color-red fs20 fw ">4</span> 元</div>
        <div class="tc">滿 <span class="color-red fs20 fw ">300</span> 元人民幣消費，即可抵扣 <span class="color-red fs20 fw ">6</span> 元</div>
        <div class="tc">...</div>
        <div class="tc">（以此類推，抵扣無上限）</div>
        <!-- <view>滿 100 元人民幣消費，即可抵扣 2 元
            滿 200 元人民幣消費，即可抵扣 4 元
            滿 300 元人民幣消費，即可抵扣 6 元
            ...
            （以此類推，抵扣無上限）</view> -->
    </div>
    <div class="title2-box" style="margin-bottom: 0;">
        <div class="title2">活動註意事項</div>
        <div class="label-line"></div>
    </div>
    <div class="tips-box" style="margin-top: 26px;">
        <div class="tips-item">
            <div class="tips-dot"> </div>
            <div class="tips-text">
                適用範圍：所有使用人民幣計價之代付／代購訂單；
            </div>
        </div>
        <div class="tips-item">
            <div class="tips-dot"> </div>
            <div class="tips-text">
                自動計算：結帳時計算符合條件之抵扣金，自動扣減應付金額；
            </div>
        </div>
        <div class="tips-item">
            <div class="tips-dot"> </div>
            <div class="tips-text">
                無需申請：系統自動累計並折抵，長期有效。
            </div>
        </div>
    </div>
    <!-- <div class="bottom-btn pointer">
        立即參與
    </div> -->

    <!-- 背景图 -->
    <image src="__CDN__/assets/img/pc/new_index/bgimg1.png" class="bgimg1" mode="scaleToFill" />
    <image src="__CDN__/assets/img/pc/new_index/bgimg2.png" class="bgimg2" mode="scaleToFill" />
</div>
<script>
    const app = new Vue({
        el: '#app',
        data: {

        },
        methods: {

        }
    })
</script>
</body>

</html>