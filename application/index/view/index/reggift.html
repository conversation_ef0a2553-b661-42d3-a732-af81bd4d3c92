<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <style>
        * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei","Helvetica Neue",Helvetica,Arial,sans-serif;
        }
        .fs12 {font-size: 12px;}
        .fs14 {font-size: 14px;}
        .fs16 {font-size: 16px;}
        .fs18 {font-size: 18px;}
        .fs20 {font-size: 20px;}
        .fw{font-weight: bold;}
        .color-666 {
            color: #666666;
        }
        html {
            background-color: #787878;
        }
        .main-box{
            height: 100vh;width:100vw;background: rgba(0,0,0,0.6);position: fixed;top: 0;left: 0;z-index: 100;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .pointer {
            cursor: pointer;
        }

        .activity-register {
            /*关闭按钮样式同步这里宽高和margin-top*/
            /* margin-top: 100px; */
            position: relative;
            width: 867px;
            height: 780px;
            background: linear-gradient(180deg, #EC9CD0 0%, #FFFEFE 100%);
            border: 1px solid #FFF4D5;
            border-radius: 32px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .title {
            font-size: 48px;
            font-weight: 500;
            line-height: normal;
            text-align: center;
            color: #FFFFFF;
            margin-top: 31px;
        }

        .title2-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 20px 0;
        }

        .title2 {
            font-size: 20px;
            line-height: 22px;
            text-align: center;
            color: #EF436D;
        }

        .label-line {
            width: 3px;
            height: 24px;
            transform: rotate(-90deg);
            border-radius: 16px;
            background: #EF436D;
        }
        /* 优惠券 */
        .center-box {
            width: 786px;
            height: 282px;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.52);
            padding: 28px;
        }

        .center-box-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .top-item {
            width: 128px;
            height: 116px;
            background: url("__CDN__/assets/img/pc/new_index/quan-bg.png") no-repeat;
            background-size: 100% 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            color: #FFFFFF;
        }
        .top-item-top{
            height: 80px;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: space-around;
            padding: 10px 0;
            color: #333333;
        }
        .top-item-btn {
            width: 88px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            font-size: 12px;
            border-radius: 194px;
            background: #FFFFFF;
            color: #F2698A;
            margin-bottom: 7px;
        }


        /* 活动提示 */
        .tips-item {
            width: 786px;
            display: flex;
            align-items: center;
            color: #333333;
            font-size: 14px;
            margin-bottom: 6px;
        }

        .tips-dot {
            width: 8px;
            height: 8px;
            background: rgba(242, 105, 138, 0.6);
            border-radius: 50%;
            margin: 0 10px;
        }

        /* 按钮 */
        .bottom-btn {
            width: 180px;
            height: 48px;
            line-height: 48px;
            text-align: center;
            color: #FFFFFF;
            font-size: 20px;
            border-radius: 290px;
            background: #EF436D;
            position: absolute;
            bottom: 32px;
            left: 50%;
            transform: translateX(-50%);
        }

        .bgimg1 {
            position: absolute;
            width: 144px;
            height: 144px;
            left: 0;
            top: 10px;
        }

        .bgimg2 {
            position: absolute;
            width: 90px;
            height: 144px;
            right: 40px;
            bottom: 10px;
        }
    </style>
    <script src="__CDN__/assets/js/vue.js"></script>
</head>

<body>
<div id="app" class="activity-register">
<!--    顶部关闭-->
    <a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
    <div class="title">註冊即送運費抵扣券</div>
    <div class="title2-box">
        <div class="title2">新會員註冊禮包</div>
        <div class="label-line"></div>
    </div>
    <div class="center-box">
        <div class="center-box-top">
            {volist name="list" id="vo"  }
            <div class="top-item" >
                <div  class="top-item-top">
                    <div class="">
                        <span class="fs12">NT$</span>
                        <span class="fs20 fw">{$vo['reduce_amount']}</span>
                    </div>
                    <div class="fs14">折抵券</div>
                </div>
                {if $vo['reaching_amount']<100}
                <div class="top-item-btn">無門檻</div>
                {else /}
                <div class="top-item-btn">滿{$vo['reaching_amount']}可用</div>
                {/if}
            </div>
            {/volist}
        </div>
        <div class="fs14"> <span class="color-666">領取資格：</span>  首次註冊並完成實名認證之新會員禮包（共 1020 元折扣券，NT$20/1張、NT$50/2張、NT$100/2張、NT$200/2張、NT$300/1張）</div>
        <div class="fs14" style="margin-top: 8px;"> <span class="color-666">使用方式：</span> 註冊完成後自動發放至「我的優惠券」；單筆訂單結帳時可分次抵用，直至用罄為止。 </div>
        <div class="fs14" style="margin-top: 8px;"> <span class="color-666">有效期限：</span> 自發放日起 30 日內使用有效。 </div>
    </div>
    <div class="title2-box" >
        <div class="title2">活動註意事項</div>
        <div class="label-line"></div>
    </div>
    <div class="tips-box">
        <div class="tips-item">
            <div class="tips-dot"> </div>
            <div class="tips-text"> 優惠券僅限單筆訂單使用，且不可與其他優惠同時使用（除長期抵扣金外）；</div>
        </div>
        <div class="tips-item">
            <div class="tips-dot"> </div>
            <div class="tips-text"> 折抵後訂單金額最低需達券面金額要求，未達門檻則無法使用；</div>
        </div>
        <div class="tips-item">
            <div class="tips-dot"> </div>
            <div class="tips-text"> 活動及優惠券最終解釋權歸付唄所有，如有異動，將以官方公告為準；</div>
        </div>
        <div class="tips-item">
            <div class="tips-dot"> </div>
            <div class="tips-text"> 欲了解更多詳情，請至 付唄官網 或聯絡客服。祝您購物愉快！</div>
        </div>
    </div>
    <div class="bottom-btn pointer">
        <a href="<?=url('index/login/register')?>" style="color: #FFFFFF;">點擊註冊</a>
    </div>

    <!-- 背景图 -->
    <image src="__CDN__/assets/img/pc/new_index/bgimg1.png" class="bgimg1" mode="scaleToFill" />
    <image src="__CDN__/assets/img/pc/new_index/bgimg2.png" class="bgimg2" mode="scaleToFill" />

</div>

<script>
    const app = new Vue({
        el: '#app',
        data: {
            list:[
                {
                    price:20,
                    text:'無門檻'
                },
                {
                    price:50,
                    text:'滿500可用'
                },
                {
                    price:100,
                    text:'滿1000可用'
                },
                {
                    price:200,
                    text:'滿2000可用'
                },
                {
                    price:300,
                    text:'滿3000可用'
                },
            ]
        },
        methods: {

        }
    })
</script>
</body>

</html>