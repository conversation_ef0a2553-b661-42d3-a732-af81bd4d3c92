<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title></title>
    <link rel="stylesheet" href="__CDN__/assets/css/element-ui-index.css">
    <style>
        * {
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        .fs12 {
            font-size: 12px;
        }

        .fs14 {
            font-size: 14px;
        }

        .fs16 {
            font-size: 16px;
        }

        .fs18 {
            font-size: 18px;
        }

        .fs20 {
            font-size: 20px;
        }

        .lh {
            line-height: 18px;
        }

        .fw {
            font-weight: bold;
        }

        .color-666 {
            color: #666666;
        }

        html {
            background-color: #787878;
        }

        .pointer {
            cursor: pointer;
        }


        /* .main-box {
            height: 100vh;
            width: 100vw;
            background: rgba(0, 0, 0, 0.6);
            position: fixed;
            top: 0;
            left: 0;
            z-index: 100;
            display: flex;
            justify-content: center;
            align-items: center;
        } */


        .activity-register {
            /*关闭按钮样式同步这里宽高和margin-top*/
            /* margin-top: 100px;*/
            position: relative;
            width: 867px;
            height: 780px;
            background: linear-gradient(180deg, #EC9CD0 0%, #FFFEFE 100%);
            border: 1px solid #FFF4D5;
            border-radius: 32px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .title {
            font-size: 48px;
            font-weight: 500;
            line-height: normal;
            text-align: center;
            color: #FFFFFF;
            margin-top: 32px;
        }

        .title2-box {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 26px 0;
        }

        .title2 {
            font-size: 20px;
            line-height: 22px;
            text-align: center;
            color: #EF436D;
        }

        .label-line {
            width: 3px;
            height: 24px;
            transform: rotate(-90deg);
            border-radius: 16px;
            background: #EF436D;
        }

        .center-box {
            width: 786px;
            height: 282px;
            border-radius: 16px;
            background: rgba(255, 255, 255, 0.52);
            padding: 28px;
        }

        .center-box-item {
            display: flex;
            justify-content: space-between;

        }

        .center-box-item .img-box {
            width: 65%;
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .center-box-item image {
            z-index: 1000;
            width: 214.95px;
            height: 108px;
        }


        /* 活动提示 */
        .tips-item {
            width: 786px;
            display: flex;
            align-items: center;
            color: #333333;
            font-size: 14px;
            margin-bottom: 4px;
        }

        .tips-dot {
            width: 8px;
            height: 8px;
            background: rgba(242, 105, 138, 0.6);
            border-radius: 50%;
            margin: 0 10px;
        }

        /* 按钮 */
        .bottom-btn {
            width: 180px;
            height: 48px;
            line-height: 48px;
            text-align: center;
            color: #FFFFFF;
            font-size: 20px;
            border-radius: 290px;
            background: #EF436D;
            position: absolute;
            bottom: 32px;
            left: 50%;
            transform: translateX(-50%);
        }

        .bgimg1 {
            position: absolute;
            width: 144px;
            height: 144px;
            left: 0;
            top: 10px;
        }

        .bgimg2 {
            position: absolute;
            width: 90px;
            height: 144px;
            right: 40px;
            bottom: 10px;
        }

    </style>
    <script src="__CDN__/assets/js/vue.js"></script>
    <script src="__CDN__/assets/js/element-ui-index.js"></script>
</head>


<body>
    <div id="app" class="activity-register">
        <!--    顶部关闭-->
        <a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
        <div class="title">推薦好友用付唄享好禮</div>
        <div class="title2-box">
            <div class="title2">參與方式</div>
            <div class="label-line"></div>
        </div>
        <div class="center-box">
            <div class="center-box-item">
                <div class=" " style="width: 32%; display: flex; gap: 16px;">
                    <div class="fs16" style="color: #EF436D; width: 152px;">Step1:</div>
                    <div class="fs14 lh">老用戶進入會員中心點擊【推廣】進入【我的推廣碼】複製推廣碼發送給新用戶</div>
                </div>
                <div class="img-box">
                    <div class="demo-image__preview" v-for="(url,index) in imgList1" :key="index">
                        <el-image :z-index="20000000" style="width: 216px; height: 108px;" :src="url" :preview-src-list="imgList1">
                        </el-image>
                    </div>
<!--                    <img v-for="(url,index) in imgList1" :key="index" :src="url" class="preview-image"-->
<!--                        @click="showPreview(url)" style="width: 216px; height: 108px; margin: 0 10px; cursor: pointer;">-->
                </div>
            </div>
            <div class="center-box-item" style="margin-top: 18px;">
                <div class=" " style="width: 32%; display: flex; gap: 16px;">
                    <span class="fs16" style="color: #EF436D; width: 120px;">Step2:</span>
                    <div class="fs14 lh">新用戶註冊付唄時在【邀請碼】選項欄輸入推廣碼即可完成註冊</div>
                </div>

                <div class="img-box" style="justify-content: center;">
<!--                    <img v-for="(url,index) in imgList2" :key="index" :src="url" class="preview-image"-->
<!--                        @click="showPreview(url)" style="width: 216px; height: 108px; margin: 0 10px; cursor: pointer;">-->
                    <div class="demo-image__preview" v-for="(url,index) in imgList2" :key="index">
                        <el-image :z-index="20000000" style="width: 216px; height: 108px;" :src="url"
                            :preview-src-list="imgList2">
                        </el-image>
                    </div>
                </div>
            </div>
        </div>
        <div class="title2-box" style="margin-bottom: 0;">
            <div class="title2">活動註意事項</div>
            <div class="label-line"></div>
        </div>
        <div class="tips-box">
            <div class="tips-item">
                <div class="tips-dot"> </div>
                <div class="tips-text">
                    <span class="color-666">活動對象：</span>
                    所有已註冊付唄會員；
                </div>
            </div>
            <div class="tips-item">
                <div class="tips-dot"> </div>
                <div class="tips-text">
                    <span class="color-666">活動內容：</span>
                    每成功邀請 1 位新用戶註冊並完成首筆訂單，即可獲得 NT$20 無門檻抵用券；
                </div>
            </div>
            <div class="tips-item">
                <div class="tips-dot"> </div>
                <div class="tips-text">
                    <span class="color-666">活動對象：</span>
                    所有已註冊付唄會員；
                </div>
            </div>
            <div class="tips-item">
                <div class="tips-dot"> </div>
                <div class="tips-text">
                    <span class="color-666">發放方式：</span>
                    系統自動將抵用券發放至「我的優惠券」頁面，無需額外申請；
                </div>
            </div>
            <div class="tips-item">
                <div class="tips-dot"> </div>
                <div class="tips-text">
                    <span class="color-666">使用規則：</span>
                    無金額門檻，可直接折抵任一訂單金額，新用戶僅能被推薦 1 次，邀請人獎勵無上限累計；
                </div>
            </div>
            <div class="tips-item">
                <div class="tips-dot"> </div>
                <div class="tips-text">
                    <span class="color-666">使用期限：</span>
                    抵用券自發放日起 30 天內有效。
                </div>
            </div>

        </div>
        <div class="bottom-btn pointer">
            <a href="<?=url('index/user/index')?>" style="color: #FFFFFF;">立即參與</a>
        </div>

        <!-- 背景图 -->
        <image src="__CDN__/assets/img/pc/new_index/bgimg1.png" class="bgimg1" mode="scaleToFill" />
        <image src="__CDN__/assets/img/pc/new_index/bgimg2.png" class="bgimg2" mode="scaleToFill" />
    </div>
    <script>
        const app = new Vue({
            el: '#app',
            data: {
                imgList1: [
                    '__CDN__/assets/img/pc/new_index/friend1.png',
                    '__CDN__/assets/img/pc/new_index/friend2.png'
                ],
                imgList2: [
                    '__CDN__/assets/img/pc/new_index/friend3.png'
                ],
            },
            methods: {

            }
        })
    </script>
</body>

</html>