<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 pad_b40">
    <div class="color_red pad_t10 my_close_win_title">恭喜您中獎啦！請選擇收貨地址吧！</div>
    <form action="{:url('index/index/sele_address')}">
        <input type="hidden" class="js_jpid" name="jpid" value="">
        <input type="hidden" class="js_id" name="id" value="">
        <div class="address_win pad_tb20">
			<?php
				foreach($address as $ls){
			?>
				<div class="box clearfix" data-id="<?=$ls['id']?>">
					<div class="name pull-left ellipsis"><?=$ls['address_name']?>  <?=substr_replace($ls['address_mobile'],'*',0,4)?><img src="__CDN__/assets/img/pc/cj/sele.png" class="icon"></div>
					<div class="ellipsis text"><?=$_address[$ls['city']]['name']?><?=$ls['address']?></div>
				</div>
			<?php
				}
			?>
            <div class="pad_t10">
                <a href="<?=url('index/add_address')?>" data-width="600" class="color_red js_ajax_win">+ 新增地址</a>
            </div>
        </div>
        <div class="text-center pad_t30">
			<a href="javascript:;" class="sub_btn small js_form_sub" data-func_before="before_sub" data-text="確認" data-loadding="確認中···" data-type="new_location">確認</a>
        </div>
    </form>
</div>
<style type="text/css">
    .address_win .box{margin-bottom: 10px;}
    .address_win .box .name{width: 144px; cursor: pointer; line-height: 28px; position: relative; border: 1px solid #DDDDDD; padding: 0 15px; color: #666666; margin-right: 15px}
    .address_win .box .icon{width: 15px; position: absolute; right: -1px; bottom: 0; display: none}
    .address_win .box.active .name{color: #EF436D; border: 2px solid #EF436D; line-height: 26px}
    .address_win .box .text{color: #666; line-height: 30px}
    .address_win .box.active .icon{display: block;}
</style>
<script type="text/javascript">
    $('.js_jpid').val(jp_id);
    function before_sub(){
        if($('.js_id').val()==''){
            tip_show('請選擇收貨地址','2')
            return false;
        }
        return true
    }
    $('.address_win .box .name').click(function(){
        var _this=$(this).parents('.box');
        _this.addClass('active').siblings('.box').removeClass('active');
        $('.js_id').val(_this.attr('data-id'))
    })
</script>