<!DOCTYPE html>
<html>
    <head>
        <!-- Google tag (gtag.js) --> <script async src="https://www.googletagmanager.com/gtag/js?id=G-E8L2CV5XBK"></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'G-E8L2CV5XBK'); </script> 
        {include file="common/m_meta" /}
        {include file="common/m_script" /}
        <script async src="https://www.googletagmanager.com/gtag/js?id=AW-689144482"></script>
		<script>
		window.dataLayer = window.dataLayer || [];
		function gtag(){dataLayer.push(arguments);}
		gtag('js', new Date());

		gtag('config', 'AW-689144482');
		</script>
		<!-- Meta Pixel Code -->
		<script>
			!function(f,b,e,v,n,t,s)
			{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
			n.callMethod.apply(n,arguments):n.queue.push(arguments)};
			if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
			n.queue=[];t=b.createElement(e);t.async=!0;
			t.src=v;s=b.getElementsByTagName(e)[0];
			s.parentNode.insertBefore(t,s)}(window, document,'script',
			'https://connect.facebook.net/en_US/fbevents.js');
			fbq('init', '618910497657394');
			fbq('track', 'PageView');
			</script>
			<noscript>< img height="1" width="1" style="display:none"
			src="https://www.facebook.com/tr?id=618910497657394&ev=PageView&noscript=1"
			/></noscript>
            <!-- Default Statcounter code for paybei http://www.paybei.com.tw -->
<script type="text/javascript">
    var sc_project=13102009; 
    var sc_invisible=1; 
    var sc_security="15cc9551"; 
    </script>
    <script type="text/javascript"
    src="https://www.statcounter.com/counter/counter.js" async></script>
    <noscript><div class="statcounter"><a title="Web Analytics Made Easy -
    Statcounter" href=" " target="_blank"><img
    class="statcounter" src="https://c.statcounter.com/13102009/0/15cc9551/1/"
    alt="Web Analytics Made Easy - Statcounter"
    referrerPolicy="no-referrer-when-downgrade"></a ></div></noscript>
    <!-- End of Statcounter Code -->
    <!-- Google 标签 (gtag.js) --> <script async src=" https://www.googletagmanager.com/gtag/js?id=AW-689144482 "></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'AW-689144482'); </script>
    <script>
        window.addEventListener('load', function(event){
        if (window.location.href.indexOf("register") > 0) {
        document.addEventListener('click', function (e) {
        var dom = e.target.closest('.sub_btn');
        if (dom === null) return;
        
        var form = dom.closest('form');
        var phone = form.querySelector('input[name="mobile"]').value;
        if (phone === '' ) return;
        
        var error = form.querySelectorAll('.error[name="mobile"]').length;
        if (error>0) return;
        phone = (phone.substr(0,1) == 0) ? phone.substr(1) : phone;
        
        gtag('set', 'user_data', { 'phone_number': '+886'+phone });
        gtag('event', 'conversion', {'send_to': 'AW-689144482/1xgmCJSZwKgaEKKFzsgC'});
    
        });
        
        }
        
        });
</script> 
</head>

<!-- End Facebook Pixel Code -->
    <body style="padding-top:0.9rem;">
        {include file="common/m_header" /}
        <div class="body">
            {__CONTENT__}
        </div>
        {include file="common/m_footer" /}
        <div class="mark js_mark"></div>
        {include file="common/img_check" /}
        
    </body>
    
    <script type="text/javascript">

        

        $(function(){
            
            // 上拉加载
            if($('.js_scroll_more_list .box').length==0){
                $('.js_no_data').removeClass('hide')
            }
            if($('.js_scroll_more_list .box').length>=10){
                $('.loadding_d').removeClass('hide')
            }
            var is_pull_up=false;
            var pull_up_page=2;
            $(document).scroll(function(event) {
                var see_height = $(window).height();
                if ($('.body').height() - $(document).scrollTop() <= see_height + 10) {
                    if($('.js_scroll_more_list .box').length<10 || is_pull_up || $('.loadding_d').hasClass('disabled')){
                        return false;
                    }
                    $('.loadding_d').removeClass('disabled').find('span').html('加載中···');
                    is_pull_up=true;
                    $.get($('.js_scroll_more_list').attr('data-url'),{page:pull_up_page},function(data){
                        // 返回的列表放到 data ,如果没有数据返回 空字符串
                        if(data!=''){
                            $('.js_scroll_more_list').append(data);
                            $('.loadding_d').find('span').html('上拉加載更多');
                            
                            pull_up_page++
                        }else{
                            $('.loadding_d').addClass('disabled').find('span').html('沒有更多了');
                        }
                        is_pull_up=false;
                    })

                }
            });
        })
    </script>
</html>