<!DOCTYPE html>
<html>
    <head>
        {include file="common/m_meta" /}
        <!-- <link href="__CDN__/assets/css/user.css?v={$Think.config.site.version}" rel="stylesheet"> -->
        {include file="common/m_script" /}
    </head>
    <body style="padding-top:0.9rem;">
        {include file="common/m_header" /}
        <div class="body">
            {__CONTENT__}
        </div>
        {include file="common/m_footer" /}
        <div class="mark js_mark"></div>
        {include file="common/img_check" /}
    </body>
    <script type="text/javascript">


        function init_copy(){
          // 复制
          if($('.js_copy').length>0){
              if(document.getElementsByClassName){
                  var clipboard_btns = document.getElementsByClassName('js_copy');
                  var clipboard = new Clipboard(clipboard_btns);
                  clipboard.on('success', function(e) {
                      tip_show("{:__('Copy success')}",'1');
                  });
                  clipboard.on('error', function(e) {
                      console.log(e);
                  });
              }else{
                  // 兼容 ie7 8
                  $(".js_copy").each(function() {
                      $(this).on("click", function() {
                          var copy_span = $(this);
                          var input_hidden = '<input type="text" class="input_hidden" id="input_hidden" value=""/>';
                          copy_span.after($(input_hidden));
                          $("#input_hidden").val(copy_span.attr("data-clipboard-text"));
                          var obj = document.getElementById("input_hidden");
                          obj.select(); // 选择对象
                          document.execCommand("Copy"); // 执行浏览器复制命令
                          $("#input_hidden").remove();
                          tip_show("{:__('Copy success')}",'1');
                      })
                  })
              }
          }
        }
        $(function(){
            if($('.js_need_copy.js_scroll_more_list').length>0){
              init_copy()
            }
            // 上拉加载
            if($('.js_scroll_more_list .box').length==0){
                $('.js_no_data').removeClass('hide')
            }
            if($('.js_scroll_more_list .box').length>=10){
                $('.loadding_d').removeClass('hide')
            }
            var is_pull_up=false;
            var pull_up_page=2;
            $(document).scroll(function(event) {
                var see_height = $(window).height();
                if ($('.body').height() - $(document).scrollTop() <= see_height + 10) {
                    if($('.js_scroll_more_list .box').length<10 || is_pull_up || $('.loadding_d').hasClass('disabled')){
                        return false;
                    }
                    $('.loadding_d').removeClass('disabled').find('span').html('加載中···');
                    is_pull_up=true;
                    $.get($('.js_scroll_more_list').attr('data-url'),{page:pull_up_page},function(data){
                        // 返回的列表放到 data ,如果没有数据返回 空字符串
                        if(data!=''){
                            $('.js_scroll_more_list').append(data);
                            $('.loadding_d').find('span').html('上拉加載更多');
                            if($('.js_need_copy.js_scroll_more_list').length>0){
                              init_copy()
                            }
                            pull_up_page++
                        }else{
                            $('.loadding_d').addClass('disabled').find('span').html('沒有更多了');
                        }
                        is_pull_up=false;
                    })

                }
            });
        })
    </script>
</html>