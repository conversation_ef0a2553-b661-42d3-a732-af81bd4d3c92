<!DOCTYPE html>
<html>
    <head>
        {include file="common/meta" /}
        <!-- <link href="__CDN__/assets/css/user.css?v={$Think.config.site.version}" rel="stylesheet"> -->
        {include file="common/script" /}
        <style type="text/css">
            body{ background: #EDEFEF }
        </style>
    </head>
    <body style="padding-top: 96px;">
        {include file="common/header_user" /}
        <div class="body">
            {__CONTENT__}
        </div>
        {include file="common/footer" /}
        <div class="mark js_mark"></div>
        {include file="common/img_check" /}
    </body>
    <script type="text/javascript">
        $(function(){
            // 上拉加载
            if($('.js_scroll_more_list .box').length==0){
                $('.js_no_data').removeClass('hide')
            }
            if($('.js_scroll_more_list .box').length>=10){
                $('.loadding_d').removeClass('hide')
            }
            var is_pull_up=false;
            var pull_up_page=2;
            $(document).scroll(function(event) {
                var see_height = $(window).height();
                if ($('.body').height() - $(document).scrollTop() <= see_height + 10) {
                    if($('.js_scroll_more_list .box').length<10 || is_pull_up || $('.loadding_d').hasClass('disabled')){
                        return false;
                    }
                    $('.loadding_d').removeClass('disabled').find('span').html('加載中···');
                    is_pull_up=true;
                    $.get($('.js_scroll_more_list').attr('data-url'),{page:pull_up_page},function(data){
                        // 返回的列表放到 data ,如果没有数据返回 空字符串
                        if(data!=''){
                            $('.js_scroll_more_list').append(data);
                            $('.loadding_d').find('span').html('上拉加載更多');
                            pull_up_page++
                        }else{
                            $('.loadding_d').addClass('disabled').find('span').html('沒有更多了');
                        }
                        is_pull_up=false;
                    })

                }
            });
        })
    </script>
</html>