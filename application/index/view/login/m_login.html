<style type="text/css">
    body{ background:#fff; }
</style>
<div class="login_page">
    <div class="login_form">
        <form action="" autocomplete="off" class="js_login_forms">
            <div class="title">{:__('Sign in')}<span class="color_red">{:__('haigou')}</span></div>
            <div class="input_box full_width">
                <div class="input_tt">{:__('Phone number')}</div>
                <div class="input_rr">
                    <input type="text" name="mobile" data-type="mobile" maxlength="10" is_required="true" empty_tip="{:__('Please enter your cell phone number')}">
                </div>
            </div>
            <div class="input_box full_width">
                <div class="input_tt">{:__('Password')}</div>
                <div class="input_rr">
                    <input type="password" name="password" data-type="password" maxlength="20" is_required="true" empty_tip="{:__('Please enter password')}">
                </div>
            </div>
			<br />
			<!--<div class="input-group">-->
			<!--	<div class="input-group-addon"><span class="glyphicon glyphicon-option-horizontal" aria-hidden="true"></span></div>-->
			<!--	<input type="text" name="captcha" class="form-control" placeholder="請輸入驗證碼" data-rule="請輸入驗證碼:required;length(4)" />-->
			<!--	<span class="input-group-addon" style="padding:0;border:none;cursor:pointer;">-->
			<!--		<img src="{:rtrim('__PUBLIC__', '/')}/index.php?s=/captcha" width="100" height="30" onclick="this.src = '{:rtrim('__PUBLIC__', '/')}/index.php?s=/captcha&r=' + Math.random();"/>-->
			<!--	</span>-->
			<!--</div>-->
            <div class="clearfix login_tips pad_t20">
                <a href="{:url('/index/login/forget')}" class="pull-right forget f12">{:__('Forgot password')}</a>
                <label class="moni_check_label">
                    <a href="javascript:;" class="moni_check active">
                    </a>
                    <span class="la_btn">{:__('Remember password')}</span>
                </label>
            </div>
			
            <div class="pad_t40 margin_t40 text-center">
                <a href="javascript:;" class="sub_btn js_form_sub" data-func_before="before_sub" data-text="{:__('Start to save money forever')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Start to save money forever')}</a>
                <div class="pad_t20">
                    <a href="{:url('/index/login/register')}" class="color_666">{:__('Sign up')}</a>
					<a href="javascript:;" style="color:#EF436D;" class="js_get_line margin_l20" data-url="{:url('/index/Line/line_login')}">LINE登錄</a>
				</div>
            </div>

        </form>

        <div class="login_fixed_items clearfix">
            <a href="{:url('/index/login/agreement')}" class="js_article_win">{:__('haigou Terms')}</a>
            <a href="{:url('/index/helps/index')}">{:__('Help center')}</a>
        </div>
    </div>
</div>

<script type="text/javascript">
	var check2=null;
    var is_img_check2=false;
    function before_sub(){
        if(!is_img_check2){
            $('.js_need_widgets__img2').addClass('active');
            if(check2==null){
                check2 = WIDGETS.imgSmoothCheck({
                    selector: "#select2",
                    data: check_imgs,
                    imgHeight: 100,
                    imgWidth: 200,
                    allowableErrorValue: 3,
                    success: function () {
                        $('.js_need_widgets__img2').removeClass('active');
                        // tip_show('验证成功',1);
                        check2.refresh();
                        is_img_check2=true;
                        $('.js_form_sub').click()
                    },
                    error: function (res) {
                        // setTimeout(function(){
                        //     check2.refresh();
                        // },30)
                    }
                });
                
                return false;
            }
             return false;
        }
        is_img_check2=false;
        return true;
    }

    $(function(){
        // 获取本地存储的登录账号
        if(localStorage.haitao_account){
            // 转为 json
            var account_d=JSON.parse(localStorage.haitao_account);
            $('.js_login_forms').find('input[name="mobile"]').val(account_d.mobile);
            $('.js_login_forms').find('input[name="password"]').val(account_d.password);
        }
        $('.js_form_sub').click(function(event) {
            if($('.moni_check_label .moni_check').hasClass('active')){
                var form=$(this).parents('form');
                var account_data={
                    mobile:form.find('input[name="mobile"]').val(),
                    password:form.find('input[name="password"]').val()
                }
                var str = JSON.stringify(account_data); 
                localStorage.haitao_account= str; 
            }else{
                localStorage.haitao_account= '';
            }
        });
    })
	
	let is_ajax_get=false;
	$('.js_get_line').click(function(){
		if(is_ajax_get){return false;}
		is_ajax_get=true;
		$.post($(this).attr('data-url'),{},function(res){
			if(res.code==1){
				//window.open(res.url)
				location.href=res.url
				is_ajax_get=false;
			}else{
				is_ajax_get=false;
			}
		})
	})
</script>