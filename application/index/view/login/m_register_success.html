
<div class="register_animation">
    <div class="success-container">
        <div class="success-icon">
            <svg width="100" height="100" viewBox="0 0 60 60" fill="none">
                <circle cx="30" cy="30" r="30" fill="#28a745"/>
                <path d="M18 30l8 8 16-16" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </div>
        <h2 class="success-title">注册成功</h2>
        <p class="countdown-text"><span id="countdown">2</span> 秒后自动跳转</p>
        <button class="course-button" onclick="jumpToHome()">點擊跳轉</button>
    </div>
</div>
<script>
    // 获取首页URL - 使用ThinkPHP语法
    var homeUrl = "{:url('index/index')}";

    // 倒计时变量
    var countdown = 2;

    // 跳转到首页的函数
    function jumpToHome() {
        // window.location.href = homeUrl;
    }

    // 更新倒计时显示
    function updateCountdown() {
        var countdownElement = document.getElementById('countdown');
        if (countdownElement) {
            countdownElement.textContent = countdown;
        }

        if (countdown <= 0) {
            jumpToHome();
        } else {
            countdown--;
            setTimeout(updateCountdown, 1000);
        }
    }

    // 页面加载完成后开始倒计时
    document.addEventListener('DOMContentLoaded', function() {
        // updateCountdown();
    });
</script>

<style>
    .register_animation {
        width: 100vw;
        height: 100vh;
        background: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .success-container {
        width: 100vw;
        margin-bottom: 15vh;
        min-height: 552px;
        background: #ffffff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
    }

    .success-icon {
        margin-bottom: 16px;
        display: flex;
        justify-content: center;
    }

    .success-title {
        font-size: 16px;
        font-weight: 500;
        color: #3D3D3D;
        margin: 0 0 20px 0;
    }

    .countdown-text {
        font-size: 13px;
        color: #ff69b4;
        margin: 0 0 36px 0;
    }

    .course-button {
        margin: 0 24rpx;
        background: #ef436d;
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 25px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
    }

    .course-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 105, 180, 0.4);
    }

    .course-button:active {
        transform: translateY(0);
    }
</style>