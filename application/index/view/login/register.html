<div class="login_page">
    <div class="login_left">
        <a href="__CDN__/" class="login_logo"><img src="__CDN__/assets/img/pc/login_logo.png" alt=""></a>
        <div class="slogo text-center">
            <div class="tt color_red">{:__('haigou')}</div>
            <div class="pp">{:__('haigou slogo')}</div>
        </div>
        <div class="f_slogo text-center">{:__('haigou slogo foot')}</div>
    </div>
    <div class="over_hide login_right">
        <div class="right_link">{:__('Already have an account?')} <a href="{:url('/index/login/login')}">{:__('Sign in')}</a></div>
        <div class="login_form login_form_re">
            <form action="" autocomplete="off" class="js_login_forms">
                <div class="title">{:__('Sign up')}<span class="color_red">{:__('haigou')}</span></div>
                <div class="input_box full_width">
                    <div class="input_tt">{:__('Phone number')}</div>
                    <div class="input_rr">
                        <input type="text" name="mobile" data-type="mobile" class="js_code_input" maxlength="10" is_required="true" empty_tip="{:__('Please enter your cell phone number')}">
                    </div>
                </div>

                <div class="input_box full_width">
                    <div class="input_tt">{:__('Mobile verification code')}</div>
                    <div class="input_rr">
                        <a href="javascript:;" class="get_code" data-url="{:url('/index/login/getCode')}">{:__('Get code')}</a>
                        <input type="text" name="code" data-type="number" maxlength="6" is_required="true" empty_tip="{:__('Please enter mobile verification code')}">
                    </div>
                </div>

                <div class="input_box full_width">
                    <div class="input_tt">{:__('Password')}</div>
                    <div class="input_rr">
                        <input type="password" name="password" data-type="password" maxlength="20" is_required="true" empty_tip="{:__('Please enter password')}">
                    </div>
                </div>
                <div class="color_999 pad_t10">{:__('Please enter a password of 8 to 20 bits, including letters and numbers')}</div>
                <div class="input_box full_width">
                    <div class="input_tt">{:__('Real name')}</div>
                    <div class="input_rr">
                        <input type="text" name="realname"  maxlength="20" is_required="true" empty_tip="{:__('Please enter real name')}" >
                    </div>
                </div>

                <div class="input_box full_width">
                    <div class="input_tt">{:__('Invitation Code')} <span>({:__('Not required')})</span></div>
                    <div class="input_rr">
                        <input type="text" name="invitationCode"  maxlength="20">
                    </div>
                </div>

                <div class="clearfix login_tips pad_t20">
                    <label class="moni_check_label">
                        <a href="javascript:;" class="moni_check active">
                        </a>
                        <span class="la_btn">{:__('Have agreed')}</span> <a class="color_red js_article_win" data-width="1000" href="{:url('/index/login/agreement')}">{:__('User Agreement')}</a>
                    </label>
                </div>

                <div class="pad_t30 text-center">
                    <a href="javascript:;" class="sub_btn js_form_sub" data-text="{:__('Start to save money forever')}" data-loadding="{:__('Submitting')}"  data-type="new_location" data-func_before="is_agree">
						{:__('Start to save money forever')}
						

					</a>
                </div>

            </form>
        </div>
    </div>
</div>

<script type="text/javascript">
    function is_agree(){
        if(!$('.moni_check_label .moni_check').hasClass('active')){
            tip_show("{:__('Please agreed Agreement first')}",'2')
            return false
        }
        return true;
    }
    $(function(){
        
    })
</script>
<!-- Event snippet for 注冊-25.3.4 conversion page -->
<script>
    gtag('event', 'conversion', {
        'send_to': 'AW-689144482/TtsdCInX8qUaEKKFzsgC',
        'value': 1.0,
        'currency': 'TWD'
    });
  </script>
  
