
<div class="register_animation">
    <div class="success-container">
        <div class="success-icon">
            <svg width="60" height="60" viewBox="0 0 60 60" fill="none">
                <circle cx="30" cy="30" r="30" fill="#28a745"/>
                <path d="M18 30l8 8 16-16" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </div>
        <h2 class="success-title">注册成功</h2>
        <p class="countdown-text">25 后自动跳转</p>
        <button class="course-button" onclick="gotoHome">點擊跳轉</button>
    </div>
</div>
<script>

</script>

<style>
    .register_animation {
        width: 100vw;
        height: 100vh;
        background: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .success-container {
        width: 462px;
        height: 552px;
        text-align: center;
        padding: 40px;
        background: #ffffff;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        max-width: 400px;
    }

    .success-icon {
        margin-bottom: 24px;
        display: flex;
        justify-content: center;
    }

    .success-title {
        font-size: 24px;
        font-weight: 600;
        color: #333333;
        margin: 0 0 16px 0;
    }

    .countdown-text {
        font-size: 14px;
        color: #ff69b4;
        margin: 0 0 32px 0;
    }

    .course-button {
        background: linear-gradient(135deg, #ff69b4, #ff1493);
        color: white;
        border: none;
        padding: 12px 40px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
    }

    .course-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 105, 180, 0.4);
    }

    .course-button:active {
        transform: translateY(0);
    }
</style>