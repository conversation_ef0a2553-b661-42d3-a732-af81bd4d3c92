<style type="text/css">
    
</style>
<div id="app">
    <div class="pad_tb20 helps_div">
        <div class="transportation_user_main">
            <?php
				if($all_own['newscontent']){
			?>
				<div class="clearfix">
					<div class="notice clearfix pull-left pad_lr10">
						<div class="tt">公告：</div>
						<div class="ellipsis "><div class="ellipsis"><?=$all_own['newscontent']?></div></div>
					</div>
				</div>
			<?php
				}
			?>

            <div class="clearfix pad_t20 transportation_div">
                {include file="transportation/nav" /}
                <div class="over_hide block_div pad_lr30 pad_tb10">
                    <div class="user_common_tt pad_b20 clearfix">
                        集運地址管理
                    </div>
                    <el-form label-width="70px" style="width:620px" label-position="left"  class="js_jy_form" ref="loginform" :model="loginform" >
                        <div class="clearfix">
                            <div class="pull-left" style="width:100%">
                                <!-- 根据真实地址数据做联动 -->
                                <el-form-item label="地址信息"  >
                                    <el-select v-model="loginform.province" @change="change_pro" filterable style="width:30%;" placeholder="請選擇">
                                        <el-option :label="item.name" :value="item.id" v-for="(item,index) in address" :key="index"></el-option>
                                    </el-select>
                                    <el-select v-model="loginform.city" filterable style="width: 69%;" placeholder="請選擇市/縣">
                                        <el-option :label="item.name" :value="item.id" v-for="(item,index) in citys" :key="index"></el-option>
                                    </el-select>
                                </el-form-item>
                            </div>
                        </div>
                        <div class="clearfix">
                            <div class="pull-left" style="width: 100%">
                                <el-form-item label="詳細地址" prop="address">
                                    <el-input type="text" v-model="loginform.address" placeholder="請輸入詳細地址信息"></el-input>
                                </el-form-item>
                            </div>
                            
                        </div>

                        <div class="clearfix">
                            <div class="pull-left" style="width: 40%">
                                <el-form-item label="收貨人" prop="name">
                                    <el-input type="text" v-model="loginform.name" placeholder="請輸入"></el-input>
                                </el-form-item>
                            </div>
                        </div>
                        <div class="clearfix">
                            <div class="pull-left" style="width: 40%">
                                <el-form-item label="收貨電話" prop="mobile">
                                    <el-input type="text" v-model="loginform.mobile" placeholder="請輸入"></el-input>
                                </el-form-item>
                            </div>
                        </div>
                        

                        <div class="clearfix">
                            <div class="pull-left" style="width: 100%">
                                <el-form-item label="">
                                    <el-button type="primary" style="width: 120px;" @click="submitForm('loginform')" :loading="is_ajax">{{is_ajax?'提交中···':'保存'}}</el-button>
                                </el-form-item>
                            </div>
                            
                        </div>
                    </el-form>


                    <div class="pad_tb20">
                        <el-table
                          :data="tableData" class="address_table"  border
                          style="width: 100%;">
                          <el-table-column
                            prop="name"
                            label="收貨人">
                          </el-table-column>
                          <el-table-column
                            prop="mobile"
                            label="收貨電話">
                          </el-table-column>

                          <el-table-column
                            label="地址信息" width="500px">
                            <template slot-scope="scope">
                                <div>
                                  {{ scope.row.province+','+scope.row.city+','+scope.row.address}}
                                </div>
                            </template>
                          </el-table-column>
                          <el-table-column
                            label="操作" >
                            <template slot-scope="scope">
                                <div>
                                    <a :href="'{:url('transportation/address_add')}?id='+scope.row.id" class="color_red js_ajax_win" data-width="620">修改</a>
                                    <a href="{:url('transportation/address_dele')}" class="red margin_l20 js_ajax_dele" :data-id="scope.row.id" tips="您確認删除該地址嗎？">删除</a>
                                </div>
                            </template>
                          </el-table-column>

                          <div slot="empty">{include file="common/nodata" /}</div>
                        </el-table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var app=new Vue({
      el: '#app',
      data:function(){
        return {
            is_ajax:false,
            loginform:{
                id:'',
                province:'',
                city:'',
                name:'',
                address:'',
                mobile:''
            },
            tableData: <?=is_array($list)?json_encode($list):'[]';?>,
            is_rem:true,
            address:<?=json_encode($address)?>,
            citys:[]
        }
      },
      mounted:function(){
        $('#app').css('display','block');
        console.log(JSON.stringify(this.address))
      },
      methods:{
        change_pro(e){
            for(let i=0;i<this.address.length;i++){
                if(e==this.address[i].id){
                    this.loginform.city='';
                    this.citys=this.address[i].list;
                    break;
                }
            }
        },
        jump_url:function(url){
            window.location.href=url
        },
        resetForm:function(formName) {
            this.$refs[formName].resetFields();
        },
        edit(index){
            var item=this.tableData[index];
            this.loginform={
                id:item.id,
                province:item.province_id,
                city:item.city_id,
                name:item.name,
                address:item.address,
                mobile:item.mobile
            }
        },
        submitForm:function(formName) {
            var _this = this;
            if(_this.is_ajax){
                return flase;
            }
            if(_this.loginform.province==''){
                input_tips($('.js_jy_form input').eq(0),'請選擇', 3);
                return false;
            }
            if(_this.loginform.city==''){
                input_tips($('.js_jy_form input').eq(1),'請選擇市/縣', 3);
                return false;
            }
            if(_this.loginform.address==''){
                input_tips($('.js_jy_form input').eq(2),'請輸入詳細地址', 3);
                return false;
            }
            if(_this.loginform.name==''){
                input_tips($('.js_jy_form input').eq(3),'請輸入收貨人姓名', 3);
                return false;
            }
            if(_this.loginform.mobile==''){
                input_tips($('.js_jy_form input').eq(4),'請輸入收貨電話', 3);
                return false;
            }
            if(!/^09[0-9]{8}$/.test(_this.loginform.mobile)){
                input_tips($('.js_jy_form input').eq(4),'手機號碼格式不正確', 3);
                return false;
            }

            _this.is_ajax = true;

            $.post("{:url('/index/transportation/address')}",_this.loginform,function(res){
                if (res.code == "1") {
                    tip_show(res.msg,1)
                    _this.resetForm(formName);
                    _this.is_ajax = false;
                    // 跳转
                    setTimeout(function(){
                        window.location.reload()
                    },1000*parseInt(res.wait))
                } else {
                    tip_show(res.msg || '未知错误',2)
                    _this.is_ajax = false;
                }
            })
        }
      }
    })
    
</script>