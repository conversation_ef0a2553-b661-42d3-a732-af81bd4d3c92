<style type="text/css">
    
</style>
<div id="app2">
	<a href=" " class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt="" /></a>
    <div class="my_close_win pad_lr20 pad_b40">
        <div class="color_red pad_tb10 my_close_win_title margin_b30">編輯地址</div>
        <el-form label-width="70px" label-position="left"  class="js_jy_form2" ref="loginform" :model="loginform" >
            <div class="clearfix">
                <div class="pull-left" style="width:100%">
                    <!-- 根据真实地址数据做联动 -->
                    <el-form-item label="地址信息"  >
                        <el-select v-model="loginform.province" @change="change_pro" filterable style="width:30%;" placeholder="請選擇">
                            <el-option :label="item.name" :value="item.id" v-for="(item,index) in address" :key="index"></el-option>
                        </el-select>
                        <el-select v-model="loginform.city" filterable style="width: 69%;" placeholder="請選擇市/縣">
                            <el-option :label="item.name" :value="item.id" v-for="(item,index) in citys" :key="index"></el-option>
                        </el-select>
                    </el-form-item>
                </div>
            </div>
            <div class="clearfix">
                <div class="pull-left" style="width: 100%">
                    <el-form-item label="詳細地址" prop="address">
                        <el-input type="text" v-model="loginform.address" placeholder="請輸入詳細地址信息"></el-input>
                    </el-form-item>
                </div>
                
            </div>

            <div class="clearfix">
                <div class="pull-left" style="width: 40%">
                    <el-form-item label="收貨人" prop="name">
                        <el-input type="text" v-model="loginform.name" placeholder="請輸入"></el-input>
                    </el-form-item>
                </div>
            </div>
            <div class="clearfix">
                <div class="pull-left" style="width: 40%">
                    <el-form-item label="收貨電話" prop="mobile">
                        <el-input type="text" v-model="loginform.mobile" placeholder="請輸入"></el-input>
                    </el-form-item>
                </div>
            </div>
            

            <div class="clearfix">
                <div class="pull-left" style="width: 100%">
                    <el-form-item label="">
                        <el-button type="primary" style="width: 120px;" @click="submitForm('loginform')" :loading="is_ajax">{{is_ajax?'提交中···':'保存'}}</el-button>
                    </el-form-item>
                </div>
                
            </div>
        </el-form>
    </div>
</div>
<script type="text/javascript">
    var app2=new Vue({
      el: '#app2',
      data:function(){
        return {
            is_ajax:false,
            loginform:{
                id:"<?=$info['id']?>",   // 地址id，新增时为空
                province:parseInt("<?=$info['type']?>") || '', // 岛 id
                city:parseInt("<?=$info['city']?>") || '',// 市县 id
                name:"<?=$info['address_name']?>", 
                address:"<?=$info['address']?>",
                mobile:"<?=$info['address_mobile']?>"
            },
            address:<?=json_encode($address)?>,
            citys:[]
        }
      },
      mounted:function(){
        if(this.loginform.id){
            this.init_address();
        }
      },
      methods:{
        init_address(){
            let pid=this.loginform.province;
            for(let i=0;i<this.address.length;i++){
                if(pid==this.address[i].id){
                    console.log(1)
                    this.citys=this.address[i].list;
                    break;
                }
            }
            console.log(this.citys)
        },
        change_pro(e){
            for(let i=0;i<this.address.length;i++){
                if(e==this.address[i].id){
                    this.loginform.city='';
                    this.citys=this.address[i].list;
                    break;
                }
            }
        },
        jump_url:function(url){
            window.location.href=url
        },
        resetForm:function(formName) {
            this.$refs[formName].resetFields();
        },
        
        submitForm:function(formName) {
            var _this = this;
            if(_this.is_ajax){
                return flase;
            }
            if(_this.loginform.province==''){
                input_tips($('.js_jy_form2 input').eq(0),'請選擇', 3);
                return false;
            }
            if(_this.loginform.city==''){
                input_tips($('.js_jy_form2 input').eq(1),'請選擇市/縣', 3);
                return false;
            }
            if(_this.loginform.address==''){
                input_tips($('.js_jy_form2 input').eq(2),'請輸入詳細地址', 3);
                return false;
            }
            if(_this.loginform.name==''){
                input_tips($('.js_jy_form2 input').eq(3),'請輸入收貨人姓名', 3);
                return false;
            }
            if(_this.loginform.mobile==''){
                input_tips($('.js_jy_form2 input').eq(4),'請輸入收貨電話', 3);
                return false;
            }
            if(!/^09[0-9]{8}$/.test(_this.loginform.mobile)){
                input_tips($('.js_jy_form2 input').eq(4),'手機號碼格式不正確', 3);
                return false;
            }

            _this.is_ajax = true;

            $.post("{:url('/index/transportation/address_add')}",_this.loginform,function(res){
                if (res.code == "1") {
                    tip_show(res.msg,1)
                    _this.resetForm(formName);
                    _this.is_ajax = false;
                    // 跳转
                    setTimeout(function(){
                        window.location.reload()
                    },1000*parseInt(res.wait))
                } else {
                    tip_show(res.msg || '未知错误',2)
                    _this.is_ajax = false;
                }
            })
        }
      }
    })
    
</script>