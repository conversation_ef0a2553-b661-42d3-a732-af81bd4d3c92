<style type="text/css">
     .hide_input{width:0px;height:0px;opacity:0}
</style>
<div id="app">
    <div class="pad_tb20 helps_div">
        <div class="transportation_user_main">
            <?php
				if($all_own['newscontent']){
			?>
				<div class="clearfix">
					<div class="notice clearfix pull-left pad_lr10">
						<div class="tt">公告：</div>
						<div class="ellipsis "><div class="ellipsis"><?=$all_own['newscontent']?></div></div>
					</div>
				</div>
			<?php
				}
			?>

            <div class="clearfix pad_t20 transportation_div js_transportation_div">
                {include file="transportation/nav" /}
                <div class="over_hide block_div pad_lr30 pad_tb10">
					<div class="user_common_tt clearfix">
						一.集運地址
					</div>
					<div class="daifu_item">
						收貨人： <?=$address['address_name']?>
					</div>
					<div class="daifu_item">
						收貨電話： <?=$address['address_mobile']?>
					</div>
					<div class="daifu_item">
						收貨地址： <?=$category[$address['type']]['name'].'-'.$category[$address['city']]['name'].$address['address']?>
					</div>
					<div class="user_common_tt clearfix">
						二.包裹資訊
					</div>
					<table class="common_table table margin_t10 text-center">
						<tbody>
							<tr>
								<th>運單號碼</th>
								<th>快遞名稱</th>
								<th>是否特貨</th>
								<th>是否外島</th>
								<th>重量</th>
								<th>單價</th>
								<th>派送费</th>
								<th>超重派送费</th>
							</tr>
							<?php
								foreach($jiyun['list'] as $key=>$ls){
							?>
								<tr class="color_666">
									<td><?=$ls['waybill']?>-<?=$ls['is_transport']?'海':'空'?></td>
									<td><?=$ls['waybill_name']?></td>
									<td><?=$ls['is_sale']?'是':'否';?></td>
									<td><?=$ls['is_type']?'是':'否';?></td>
									<td><?=$ls['scale']?>KG</td>
									<td><?=$jiyun['unit_price']?>TWD</td>
									<td><?=$jiyun['peisongfei']?>TWD</td>
									<td><?=$jiyun['all_beyond']?>TWD</td>
								</tr>
							<?php
								}
							?>
							
						</tbody>
					</table>
					<div class="user_common_tt clearfix">
						三.支付詳情
					</div>
					<div class="daifu_item">
						訂單總額： <?=$info['tb_money']?>TWD
					</div>
					<div class="daifu_item">
						使用抵扣金： <?=$info['balance_money']?>TWD
					</div>
					<div class="daifu_item">
						使用優惠券： <?=$info['coupon_reduce_amount']?>TWD
					</div>
					<div class="daifu_item">
						總重量：<?=$info['scale']?>kg
					</div>
					<div class="daifu_item">
						應付新臺幣： <b class="red js_money_total"><?=$info['actual_money']?>TWD</b> （<?=$info['unit_price']?> * <?=$info['scale']?>KG + <?=$info['peisongfei']?> 派件費 + <?=$info['beyond']?> 超大派件費 + <?=$info['outer_island']?> 外島 - <?=$info['balance_money']?> 抵扣金 - <?=$info['coupon_reduce_amount']?> 優惠券减免）
					</div>
					<?php
						if($info['pay_status'] == '1'){
					?>
						<div class="daifu_item clearfix">
							<div class="pull-left pad_r20">
								我們的收款銀行帳戶: <b class="red js_money_total"><?=$sys_bank['account_name']?> - <?=$sys_bank['account_num']?> 
							</div>
						</div>
					<?php
						}else{
					?>
						<div class="daifu_item clearfix">
							<div class="pull-left pad_r20">
								中國信托虛擬收款賬戶: <b class="red js_money_total"><?=$info['cs_code']?$info['cs_code']:'請聯系客服重新獲取'?> </b>每次提交訂單均不同，請勿重複使用！
							</div>
						</div>
					<?php
						}
					?>
                </div>
            </div>
        </div>
    </div>
</div>