<style type="text/css">
    .transportation_div .el-form-item .red.st{ position: absolute; right: -20px; }
	.cangku .item{width: 100%;}
	.cangku .item .title{font-size: 16px; font-weight: bold; padding-top: 5px;}
	.cangku .item .text{padding: 5px 0;}
	.cangku .item img{height: 30px; margin-right: 10px;}
	.cangku .item .p{font-size: 14px; color: #666; line-height:18px; padding: 2px 0; width: 100%;}
	
</style>
<div id="app">
    <div class="pad_tb20 helps_div">
        <div class="transportation_user_main">
            <?php
				if($all_own['newscontent']){
			?>
				<div class="clearfix">
					<div class="notice clearfix pull-left pad_lr10">
						<div class="tt">公告：</div>
						<div class="ellipsis "><div class="ellipsis"><?=$all_own['newscontent']?></div></div>
					</div>
				</div>
			<?php
				}
			?>

            <div class="clearfix pad_t30 transportation_div">
                {include file="transportation/nav" /}
				<div class="over_hide block_div pad_lr30 pad_tb10">
					<div class="flex_ac pad_tb10">
						<?php
							if($site['cangku1']){
						?>
						<el-popover
						  placement="bottom-start"
						  width="360"
						  trigger="hover">
						  <div class="cangku flex" >
						  	<div class="item flex">
						  		<div class="flex1">
						  			<div class="title flex_ac"><img src="__CDN__/assets/img/pc/logistics2.png">空運倉庫</div>
						  			<div class="text" >
						  				<div class="">
						  					<div  class="p s">联络人：霏霏-(paybei-10518) </div>
						  				</div>
						  				<div class="">
						  					<div  class="p s">联系电话：15989852261 </div>
						  					<div  class="p s">邮政编号：518101  </div>
						  				</div>
						  				<div class="">
						  					<div  class="p">收货地址:深圳市宝安区松岗街道碧头金嘉利工业区5栋飞扬转运仓-(会员ID：10518+<?=$user_id?>)</div>
						  				</div>
						  			</div>
						  		</div>
						  	</div>
						  </div>
						  <div style="text-align: right; margin: 0">
							<a href="javascript:;" class="el-button  el-button--primary el-button--mini" @click="copyText(1)" ><!----><!----><span>複製</span></a>
						  </div>
						  <el-button type="primary" slot="reference" class="margin_r10">空運倉庫</el-button>
						</el-popover>
						<?php
							}
						?>
						
						<?php
							if($site['haicangku1']){
						?>
						<el-popover
						  placement="bottom-start"
						  width="360"
						  trigger="hover">
						  <div class="cangku flex" >
						  	<div class="item flex">
						  		<div class="flex1">
						  			<div class="title flex_ac"><img src="__CDN__/assets/img/pc/logistics3.png">海運倉庫</div>
						  			<div class="text" >
						  				<div class="">
						  					<div  class="p s">联络人：霏霏-(paybei-10518) </div>
						  				</div>
						  				<div class="">
						  					<div  class="p s">联系电话：15989852261 </div>
						  					<div  class="p s">邮政编号：518101  </div>
						  				</div>
						  				<div class="">
						  					<div  class="p">收货地址:深圳市宝安区松岗街道碧头金嘉利工业区5栋飞扬转运仓-(会员ID：10518+<?=$user_id?>)</div>
						  				</div>
						  			</div>
						  		</div>
						  	</div>
						  </div>
						  <div style="text-align: right; margin: 0">
							<a href="javascript:;" class="el-button  el-button--primary el-button--mini" @click="copyText(2)" ><!----><!----><span>複製</span></a>
						  </div>
						  <el-button type="success" slot="reference">海運倉庫</el-button>
						</el-popover>
						<?php
							}
						?>
					</div>
					
				
				<!-- <?php
					if($site['cangku1']){
				?>
					<div style="border: 1px solid #ec9e9e;background: #ffe7e7;border-radius: 5px;padding: 10px">
						<div class="user_common_tt clearfix">
							<img src="__CDN__/assets/img/pc/warm.png"> 空運倉庫 <a href="javascript:;" class="red pad_l10 js_fuzhi" data-clipboard-text="{$site.cangku1}" style="font-size: 14px; font-weight: normal;">複製</a>
						</div>
						<div class="f14 color_666 pad_l20" >
							<p>{$site.cangku1}</p>
						</div>
					</div>
				<?php
					}
				?> -->
				<!-- <br />
				<?php
					if($site['haicangku1']){
				?>
					<div style="border: 1px solid #ec9e9e;background: #ffe7e7;border-radius: 5px;padding: 10px">
						<div class="user_common_tt clearfix">
							<img src="__CDN__/assets/img/pc/warm.png"> 海運倉庫 <a href="javascript:;" class="red pad_l10 js_fuzhi" data-clipboard-text="{$site.haicangku1}" style="font-size: 14px; font-weight: normal;">複製</a>
						</div>
						<div class="f14 color_666 pad_l20" >
							<p>{$site.haicangku1}</p>
						</div>
					</div>
				<?php
					}
				?> -->
					<div class="over_hide block_div pad_lr30 pad_tb10">
						<div class="user_common_tt pad_b20 clearfix">
							填寫集運委託單
						</div>
						<el-form label-width="70px" style="width:620px" label-position="left"  class="js_jy_form" ref="loginform" :model="loginform" >
							<div class="clearfix">
								<div class="pull-left" style="width:100%">
									<el-form-item label="運單號碼" prop="number" >
										<div class="red st">*</div>
										<el-input maxlength="20" type="text" oninput="value=value.replace(/[^\w_]/g,'')" v-model="loginform.number" placeholder="請輸入您包裹的運單號碼,勿填入LP開頭的物流编号"></el-input>
									</el-form-item>
								</div>
								<div class="pull-left" style="width: 100%">
									<el-form-item label="快遞名稱" prop="express">
										<div class="red st">*</div>
										<el-select v-model="loginform.express" filterable placeholder="請選擇">
										<?php
											foreach($kuaidi as $ls){
										?>
											<el-option label="<?=$ls['name']?>" value="<?=$ls['name']?>"></el-option>
										<?php
											}
										?>
										</el-select>
									</el-form-item>
								</div>
								<div class="pull-left" style="width: 100%">
									<el-form-item label="商品名稱" prop="name">
										<div class="red st">*</div>
										<el-input type="text" v-model="loginform.name" placeholder="如衣服、包包、飾品。"></el-input>
									</el-form-item>
								</div>
							</div>
							<div class="clearfix">
								<div class="pull-left" style="width: 100%">
									<el-form-item label="商品網址" prop="weburl">
										<el-input type="text" v-model="loginform.weburl" placeholder="請輸入商品網址"></el-input>
									</el-form-item>
								</div>
							</div>

							<div class="clearfix">
								<div class="pull-left" style="width:180px;">
									<el-form-item label="是否特貨">
										<el-checkbox v-model="loginform.is_te">特貨</el-checkbox>
									</el-form-item>
								</div>
								<div class="pull-left" style="width: 100; line-height: 40px; color: #606266">
									<span class="pad_r10">預計寄外島</span>
									<el-checkbox v-model="loginform.is_wai">寄外島 <span class="red" style="color:#FF4F4F; font-size: 12px;">（澎湖、金門、媽祖、綠島）加收300NTD派件費</span></el-checkbox>
								</div>
							</div>
							<div class="clearfix">
								<div class="pull-left" style="width:280px;">
									<el-form-item label="運輸方式">
										<el-radio v-model="loginform.is_transport" label="0">空運 <font color="red">禁 液體 電池 化妝品 粉末 可走食品</font></el-radio>
										<el-radio v-model="loginform.is_transport" label="1">海快 <font color="red">禁 食品 藥品 可走液體 粉末 電池 化妝品</font></el-radio>
									</el-form-item>
								</div>
							</div>
							<div class="clearfix">
								<div class="pull-left" style="width: 100%">
									<el-form-item label="備註項目" prop="remark" >
										<el-input type="textarea" v-model="loginform.remark" placeholder="請輸入備詿項目"></el-input>
									</el-form-item>
								</div>
							</div>
							<div class="login_tips pad_b20 pad_t30 clearfix" style="padding-left: 70px;">
								<div class="a" href="javascript:;" ><img @click="is_rem=!is_rem" v-show="!is_rem" src="__CDN__/assets/img/pc/pass_check.png"><img v-show="is_rem" src="__CDN__/assets/img/pc/pass_checked.png"> <span @click="is_rem=!is_rem">我同意付唄相關條款-</span> <a href="{:url('transportation/rule')}" class="js_ajax_win color_red" data-width="1000">集運商品運送條款</a></div>
							</div>

							<div class="clearfix">
								<div class="pull-left" style="width: 100%">
									<el-form-item label="" prop="remark">
										<el-button type="primary" style="width: 120px;" @click="submitForm('loginform')" :loading="is_ajax">{{is_ajax?'提交中···':'確認委託'}}</el-button>
									</el-form-item>
								</div>
							</div>
						</el-form>
					</div>
				</div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var app=new Vue({
      el: '#app',
      data:function(){
        return {
            is_ajax:false,
            loginform:{
                number:'',
                express:'',
                name:'',
                weburl:'',
                num:'',
                remark:'',
                is_transport:'0',
                is_te:false,
                is_wai:false
            },
            is_rem:true,
			visible:false
        }
      },
      mounted:function(){
        $('#app').css('display','block');
      },
      methods:{
		  copyText(type) {
			  var textToCopy1=`{$site.cangku1}`+ `+<?=$user_id?>)` ;
			  var textToCopy2=`{$site.haicangku1}`+ `+<?=$user_id?>)` ;
			  var textToCopy=type==1?textToCopy1:textToCopy2
			// 创建一个临时的textarea元素
			const textarea = document.createElement('textarea');
			textarea.value = textToCopy;
			// 确保textarea不在视图中，将其设置为透明
			textarea.style.position = 'fixed'; // 防止滚动
			document.body.appendChild(textarea);
			textarea.select();
			try {
			  const successful = document.execCommand('copy');
			  const msg = successful ? '成功复制！' : '无法复制！';
			  tip_show('复制成功！',1)
			  console.log(msg);
			} catch (err) {
			  console.error('复制失败', err);
			}
			document.body.removeChild(textarea);
		  },
        jump_url:function(url){
            window.location.href=url
        },
        resetForm:function(formName) {
            this.$refs[formName].resetFields();
        },
        submitForm:function(formName) {
            var _this = this;
            if(_this.is_ajax){
                return flase;
            }
            if(_this.loginform.number==''){
                input_tips($('.js_jy_form input').eq(0),'請輸入您包裹的運單號碼', 3);
                return false;
            }
            if(_this.loginform.express==''){
                input_tips($('.js_jy_form input').eq(1),'請選擇快遞名稱', 3);
                return false;
            }
            if(_this.loginform.name==''){
                input_tips($('.js_jy_form input').eq(2),'請輸入商品名稱', 3);
                return false;
            }

            if(!_this.is_rem){
                tip_show("請先閱讀並同意付唄相關條款",'2');
                return false;
            }
         
            _this.is_ajax = true;

            $.post("{:url('/index/transportation/index')}",{
                number:_this.loginform.number,
                express:_this.loginform.express,
                name:_this.loginform.name,
                weburl:_this.loginform.weburl,
                num:_this.loginform.num,
                remark:_this.loginform.remark,
                is_transport:_this.loginform.is_transport,
                is_te:_this.loginform.is_te?1:0,
                is_wai:_this.loginform.is_wai?1:0
            },function(res){
                if (res.code == "1") {
                    tip_show(res.msg,1)
                    _this.resetForm(formName);
                    _this.is_ajax = false;
                    // 跳转
                    setTimeout(function(){
                        window.location.href=res.url
                    },1000*parseInt(res.wait))
                } else {
                    tip_show(res.msg || '未知错误',2)
                    _this.is_ajax = false;
                }
            })
        }
      }
    })
</script>
<script type="text/javascript">
	$(function(){
		// var clipboard = new Clipboard('.js_fuzhi',{         
		// 	text: function(trigger) { alert("复制成功！");           
		// 		return trigger.getAttribute('data-clipboard-text');        
		// 	}   
		// });
	});
</script>


