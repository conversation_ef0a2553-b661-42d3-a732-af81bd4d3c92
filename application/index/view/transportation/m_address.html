<style type="text/css">
.footer_new{display: none;}
</style>
<div class="pad_t20"></div>
<div class="helps_div order_detail">

    <div class="bg_fff">
        <div class="bg_fff pad_lr20 helps_title flex_ac color_333">
            <img src="__CDN__/assets/img/wap/address2.png" class="uicon margin_r20">
            <span class="font_b">地址管理</span>
            <a href="javascript:;" class="margin_a color_red js_edit">編輯</a>
        </div>
    </div>
    
    <div class="margin_t20 clearfix address_list">
		<?php
			foreach($list as $ls){
		?>
			<div class="box pad_lr20 pad_tb20">
				<div class="color_333"><span class="color_666">收<span class="zw"></span>貨<span class="zw"></span>人：</span><?=$ls['name']?></div>
				<div class="color_333"><span class="color_666">收貨電話：</span><?=$ls['mobile']?></div>
				<div class="color_333 min_w flex">
					<span class="color_666 flex_s">地址信息：</span>
					<div class="flex1">
						<?=$ls['province'].' '.$ls['city'].' '.$ls['address']?>
					</div>
				</div>
				<div class="flex btns">
					<a href="<?=url('transportation/address_add',array('id'=>$ls['id']))?>" class="margin_l20 js_ajax_win">編輯</a>
					<a href="<?=url('transportation/address_dele',array('id'=>$ls['id']))?>" tips="您確認删除該地址嗎？" class="dele js_ajax_dele margin_l20" data-id="1">删除</a>
				</div>
			</div>
		<?php
			}
		?>
    </div>
	<?php
		if(!$list){
	?>
		 {include file="common/nodata" /}
	<?php
		}
	?>
    <div class="pad_lr20 pad_tb20">
        <a href="{:url('transportation/address_add')}" class="sub_btn js_ajax_win red">新增</a>
    </div>
</div>
<script type="text/javascript">
   $('.js_edit').click(function(){
        let index=$('.js_edit').index(this);
        if($(this).hasClass('active')){
            $(this).removeClass('active').html('編輯');
            $('.address_list').eq(index).removeClass('active')
        }else{
            $(this).addClass('active').html('完成');
            $('.address_list').eq(index).addClass('active')
        }
   })
</script>