
<style>
    .yhq_list{padding: 0.1rem 0.3rem;}
    .yhq_list .item{cursor: pointer;}
</style>
<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div style="height:390px; overflow: auto; ">
    <div style="color: #EF436D; font-size:0.36rem; padding: 0.2rem 0.3rem;">選擇優惠券</div>
    <?php
        if($coupon){
    ?>
    <div class="js_yhq_list yhq_list flex flex_w">
        <?php
            foreach ($coupon as $key => $value) {
        ?>
        <!-- disabled   data-id data-money必给-->
        <div class="item flex_ac" data-id="<?=$value['id']?>" data-money="<?=$value['reduce_amount']?>">
            <div class="t flex flex_d">
                <div class="tt">
                    <span>NT$</span>
                    <b><?=$value['reduce_amount']?></b>
                </div>
                <div class="pp">滿<?=$value['reaching_amount']?>可用</div>
            </div>
            <div class="time"><?=date('Y-m-d',$value['start_time'])?>-<?=date('Y-m-d',$value['end_time'])?></div>
            <!-- used -->
            <?php if (time() > $value['end_time'] - 3*24*60*60 && time() < $value['end_time']) { ?>
                <img src="__CDN__/assets/img/wap/new_index/time_out.png" alt="" class="icon_img">
                <div class="jjgq">即將過期</div>
            <?php } ?>
            <!-- 选中icon -->
            <img src="__CDN__/assets/img/pc/new_index/xz.png" class="xz_icon" alt="">
        </div>
        <?php
            }
         ?>
    </div>
    <?php
        }else{
    ?>
         {include file="common/nodata" /}
    <?php
        }
    ?>
</div>
<div class="flex_ac_jc pad_tb20">
    <a href="javascript:;" class="sub_btn small js_sele_yhq">確認選中</a>
</div>
<script>
    $('.js_yhq_list .item').click(function(){
        if($(this).hasClass('disabled')){
            return false
        }
        $(this).addClass('active').siblings('.item').removeClass('active')
    })
    $('.js_sele_yhq').click(function(){
        let id = $('.js_yhq_list .item.active').attr('data-id');
        let money =parseFloat($('.js_yhq_list .item.active').attr('data-money')) 
        if($('.js_yhq_list .item.active').length == 0){
            tip_show('請選擇優惠券')
            return
        }
        // $('.yhq_item_d').addClass('active')
        // $('.js_yhq_item').html($('.js_yhq_list .item.active').clone());
        $('.js_yhq_item').html(money+'<input name="coupon_ids" type="hidden" class="js_yhq_input" data-money="'+money+'" value="'+id+'">');
    
        fun_canuse_more()
        layer.closeAll()
    })
</script>