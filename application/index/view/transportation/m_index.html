<style type="text/css">
    /*.input_box{ padding-top: 0 }
    .input_box .input_tt{ padding: 0 0.3rem; }
    .small_input .input_box .input_tt{ line-height: 40px }
    .input_box .input_rr input{ border: none }*/
    .el-form-item{ margin-bottom: 0 }
    .el-form{ font-size: 13px; color: #333 }
    .el-input__inner{ font-size: 13px; border: none }
    .el-form-item__label{ margin-bottom: 0 }
    label.el-checkbox{ margin-bottom: 0 }
    .el-textarea__inner{font-size: 13px; border: none; padding: 5px; height: 60px;}
    .text_a .el-form-item__label{ line-height: 32px; }
    .red.st{ float: left;  position: relative; margin-left: -7px; top: 14px; left: -3px}
    .js_jy_form .bg_fff.margin_b20.pad_lr20{ padding-left: 0.3rem }
    body{ padding-bottom: 1rem; }
	.ysfs .el-form-item__label{line-height: 20px; margin-top: 11px;}
</style>
<div id="app">
<div class="pad_t20"></div>
<div class="helps_div order_detail">
	<!-- <a class="help_title bg_fff margin_b20 clearfix" href="<?=url('/index/helps/list',array('type'=>'40'))?>">
	   集运服务常見問題 >> 點擊查看
	</a>
	 -->
	<div class="bg_fff margin_b20">
	    <div class="bg_fff pad_lr20 helps_title flex_ac color_333">
	        <img src="__CDN__/assets/img/wap/icon_list_help.png" class="uicon margin_r20"> <span class="font_b">集运服务常見問題</span>
	        <a href="<?=url('/index/helps/list',array('type'=>'40'))?>" class="color_red margin_a f12">點擊查看></a>
	    </div>
	</div>
	
	
	<?php
		if($site['cangku1']){
	?>
	
	<div class="bg_fff margin_b20">
	    <div class="bg_fff pad_lr20 helps_title flex_ac color_333">
	        <img src="__CDN__/assets/img/wap/icon_dizhi.png" class="uicon margin_r20"> <span class="font_b">空運倉庫</span>
	        <a href="javascript:;" data-text="{$site.cangku1}" class="color_red margin_a f12 js_view_text">點擊查看></a>
	    </div>
	</div>
	<!-- <div class="bg_fff margin_b20">
        <div class="bg_fff pad_lr20 helps_title flex_ac color_333">
            <img src="__CDN__/assets/img/wap/address2.png" class="uicon margin_r20">空運倉庫
			{$site.cangku1}
        </div>
    </div> -->
	<?php
		}
	?>
	<?php
		if($site['haicangku1']){
	?>
		<!-- <div class="bg_fff margin_b20">
			<div class="bg_fff pad_lr20 helps_title flex_ac color_333">
				<img src="__CDN__/assets/img/wap/address2.png" class="uicon margin_r20">海運倉庫
				{$site.haicangku1}
			</div>
		</div> -->
		
		<div class="bg_fff margin_b20">
		    <div class="bg_fff pad_lr20 helps_title flex_ac color_333">
		        <img src="__CDN__/assets/img/wap/icon_dizhi.png" class="uicon margin_r20"> <span class="font_b">海運倉庫</span>
		        <a href="javascript:;" data-text="{$site.haicangku1}" class="color_red margin_a f12 js_view_text">點擊查看></a>
		    </div>
		</div>
	<?php
		}
	?>
    <div class="bg_fff">
        <div class="bg_fff pad_lr20 helps_title flex_ac color_333">
            <img src="__CDN__/assets/img/wap/jy.png" class="uicon margin_r20"> <span class="font_b">委託集運</span>
            <a href="{:url('transportation/article')}" class="color_red margin_a f12">費用及注意事項></a>
        </div>
    </div>
    
    <el-form label-width="80px" label-position="left"  class="js_jy_form pad_tb30" ref="loginform" :model="loginform" >
        <div class="clearfix">
            <div class="bg_fff margin_b20 pad_lr20">
                 <div class="red st">*</div>
                <el-form-item label="運單號碼" prop="number" >
                    <el-input maxlength="20" type="text" oninput="value=value.replace(/[^\w_]/g,'')"  v-model="loginform.number" placeholder="請輸入您包裹的運單號碼,勿填入LP開頭的物流编号"></el-input>
                </el-form-item>
            </div>
            <div class="bg_fff margin_b20 pad_lr20">
                <div class="red st">*</div>
                <el-form-item label="快遞名稱" prop="express">
                    <el-select v-model="loginform.express" filterable placeholder="請選擇">
                    <?php
                        foreach($kuaidi as $ls){
                    ?>
                        <el-option label="<?=$ls['name']?>" value="<?=$ls['name']?>"></el-option>
                    <?php
                        }
                    ?>
                    </el-select>
                </el-form-item>
            </div>
            <div class="bg_fff margin_b20 pad_lr20">
                <div class="red st">*</div>
                <el-form-item label="商品名稱" prop="name">
                    <el-input type="text" v-model="loginform.name" placeholder="如衣服、包包、飾品。"></el-input>
                </el-form-item>
            </div>
        </div>
        <div class="clearfix">
            <div class="bg_fff margin_b20 pad_lr20">
                <el-form-item label="商品網址" prop="weburl">
                    <el-input type="text" v-model="loginform.weburl" placeholder="請輸入商品網址"></el-input>
                </el-form-item>
            </div>
        </div>

        <div class="clearfix">
            <div class="bg_fff margin_b20 pad_lr20">
                <el-form-item label="是否特貨" label-width="83px">
                    <el-checkbox v-model="loginform.is_te">特貨</el-checkbox>
                </el-form-item>
            </div>
            <div class="bg_fff margin_b20 pad_lr20">
                <el-form-item label="預計寄外島" label-width="83px">
                    <el-checkbox v-model="loginform.is_wai">寄外島</el-checkbox>
                </el-form-item>
            </div>
            <div class="pad_b20"> <span class="red" style="color:#FF4F4F; font-size: 12px;">（澎湖、金門、媽祖、綠島）加收300NTD派件費</span></div>
        </div>
		<div class="clearfix">
            <div class="bg_fff margin_b20 pad_lr20">
				<el-form-item label="運輸方式" label-width="43px" class="ysfs">
					<el-radio v-model="loginform.is_transport" label="0">空運 <font color="red">禁 液體 電池 化妝品 粉末 可走食品</font></el-radio>
					<el-radio v-model="loginform.is_transport" label="1">海快 <font color="red">禁 食品 藥品 可走液體 粉末 電池 化妝品</font></el-radio>
				</el-form-item>
            </div>
        </div>
        <div class="clearfix text_a">
            <div class="bg_fff margin_b20 pad_tb20 pad_lr20">
                <el-form-item label="備註項目" prop="remark" >
                    <el-input type="textarea" v-model="loginform.remark" placeholder="請輸入備詿項目"></el-input>
                </el-form-item>
            </div>
        </div>
        <div class="login_tips pad_b20 pad_t10 pad_lr20 clearfix">
            <div class="a" href="javascript:;" ><img @click="is_rem=!is_rem" v-show="!is_rem" src="__CDN__/assets/img/wap/pass_check.png"><img v-show="is_rem" src="__CDN__/assets/img/wap/pass_checked.png"> <span @click="is_rem=!is_rem">我同意付唄相關條款-</span> <a href="{:url('transportation/rule')}" class="js_ajax_win color_red" data-width="1000">集運商品運送條款</a></div>
        </div>

        <div class="clearfix pad_t20">
            <div class="margin_b20 pad_lr20">
                <el-button type="primary" style="width:100%; padding: 14px 20px;font-size: 16px;" @click="submitForm('loginform')" :loading="is_ajax">{{is_ajax?'提交中···':'確認委託'}}</el-button>
                <a href="javascript:history.go(-1);" class="sub_btn margin_t20 red" style="font-size: 16px;">取消本次申請</a>
            </div>
            
        </div>
    </el-form>


	
</div>
</div>

</div>

<script type="text/javascript">
    var app=new Vue({
      el: '#app',
      data:function(){
        return {
            is_ajax:false,
            loginform:{
                number:'',
                express:'',
                name:'',
                weburl:'',
                num:'',
                remark:'',
				is_transport:'0',
                is_te:false,
                is_wai:false
            },
            is_rem:true
        }
      },
      mounted:function(){
        $('#app').css('display','block');
      },
      methods:{
        jump_url:function(url){
            window.location.href=url
        },
        resetForm:function(formName) {
            this.$refs[formName].resetFields();
        },
        submitForm:function(formName) {
            var _this = this;
            if(_this.is_ajax){
                return flase;
            }
            if(_this.loginform.number==''){
                input_tips($('.js_jy_form input').eq(0),'請輸入您包裹的運單號碼', 3);
                return false;
            }
            if(_this.loginform.express==''){
                input_tips($('.js_jy_form input').eq(1),'請選擇快遞名稱', 3);
                return false;
            }
            if(_this.loginform.name==''){
                input_tips($('.js_jy_form input').eq(2),'請輸入商品名稱', 3);
                return false;
            }
            if(!_this.is_rem){
                tip_show("請先閱讀並同意付唄相關條款",'2');
                return false;
            }
         
            _this.is_ajax = true;

            $.post("{:url('/index/transportation/index')}",{
                number:_this.loginform.number,
                express:_this.loginform.express,
                name:_this.loginform.name,
                weburl:_this.loginform.weburl,
                num:_this.loginform.num,
                remark:_this.loginform.remark,
                is_transport:_this.loginform.is_transport,
                is_te:_this.loginform.is_te?1:0,
                is_wai:_this.loginform.is_wai?1:0
            },function(res){
                if (res.code == "1") {
                    tip_show(res.msg,1)
                    _this.resetForm(formName);
                    _this.is_ajax = false;
                    // 跳转
                    setTimeout(function(){
                        window.location.href=res.url
                    },1000*parseInt(res.wait))
                } else {
                    tip_show(res.msg || '未知错误',2)
                    _this.is_ajax = false;
                }
            })
        }
      }
    })
    
	
	$(function(){
		
		
		$('.js_view_text').click(function(){
			
			let content=`<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
			<div class="my_close_win pad_lr30 pad_tb30">
				<div>`+$(this).attr('data-text')+`</div>
				<div><a href="javascript:;" class="js_copy pull-right color_red" data-clipboard-text="`+$(this).attr('data-text')+`">{:__('Copy')}</a></div>
			</div>`
			layer.open({
			  type: 1,
			  title:false,
			  area:['6rem', 'auto'],
			  offset:"10%",
			  // anim:2,
			  skin: 'win_class_no_hide', //样式类名
			  closeBtn: 0, //不显示关闭按钮
			  shadeClose: false, //开启遮罩关闭
			  content:content,
			  move:'.layui-layer-content .my_close_win_title'
			});
			
			init_copy()
		})
		
	})
</script>
