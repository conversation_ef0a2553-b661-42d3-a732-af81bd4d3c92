<?php
	if($type == '1'){
		foreach($list as $ls){
?>
	<div class="box bg_fff margin_b20" data-id="<?=$ls['id']?>">
		<div class="flex_ac title pad_lr20">
			<a class="btns" href="<?=url('transportation/details',array('id'=>$ls['id']))?>">
				訂單單號：<?=$ls['entrust_no']?>-<?=$ls['is_transport']?'海':'空'?>
			</a>
			<!-- red  green -->
			<span class="status margin_a red"><?=$jiyun[$ls['type']]?></span>
		</div>
		<div class="common_border"></div>
		<div class="items pad_lr20 pad_tb20">
			<div class="item">
				快遞公司：<span><?=$ls['waybill_name']?></span>
			</div>
			<div class="item">
				運單號碼：<span><?=$ls['waybill']?></span>
			</div>
			<div class="item">
				委託時間：<span class="color_666"><?=date('Y-m-d H:i:s',$ls['createtime'])?></span>
			</div>
		</div>
		<div class="common_border"></div>
		<a class="btns js_ajax_win" href="<?=url('transportation/logistics',array('id'=>$ls['id']))?>">
			查看物流
		</a>
	</div>
<?php
		}
	}
?>
<?php
	if($type == '2'){
		foreach($_list as $ls){
?>
	<div class="box bg_fff margin_b20" data-id="<?=$ls['id']?>">
		<div class="flex_ac title pad_lr20">
			委託單號：<?=$ls['order_no']?>   
			<!-- red  green -->
			<span class="status margin_a green"><?=$ls['status_text']?></span>
		</div>
		<div class="common_border"></div>
		<div class="flex_ac">
			<?php
				if($ls['status'] == '1'){
			?>
				<div class="flex_s check_d pad_l20">
					<img src='__CDN__/assets/img/wap/check.png' class="img1">
					<img src='__CDN__/assets/img/wap/checked.png' class="img2">
				</div>
			<?php
				}else{
			?>
				<div class="pad_l20">
				</div>
			<?php
				}
			?>
			
			<div class="flex1">
				<div class="items pad_lr20 pad_tb20">
					<div class="item">
						快遞公司：<span><?=$ls['waybill_name']?></span>
					</div>
					<div class="item">
						運單號碼：<span><?=$ls['waybill']?></span>
					</div>
					<div class="item color_red">
						重量：<span><?=$ls['weight']?>KG</span>
					</div>
					<div class="item color_red">
						特貨：<span><?=$ls['sale_text']?></span>
					</div>
					
					<div class="item red">
						價格：<span><?=$ls['money']?>TWD </span>
					</div>
					<div class="item">
						入倉時間：<span class="color_666"><?=$ls['add_time']?></span>
					</div>
				</div>
			</div>  
		</div>
		<a class="btns js_ajax_win" href="<?=url('transportation/logistics',array('id'=>$ls['id']))?>">
			查看物流
		</a>
	</div>
<?php
		}
	}
?>
<!-- 未到仓库-->
<!-- 已到仓库-->

<!-- 已到仓库-->

<!-- 待付款-->
<?php
	if($type == '3'){
		foreach($list as $ls){
?>
<div class="box bg_fff margin_b20" data-id="<?=$ls['id']?>">
    <div class="flex_ac title pad_lr20">
        訂單單號：<?=$ls['order_no']?> 
        <!-- red  green -->
        <span class="status margin_a red"><?=$jiyun[$ls['order_status']]?></span>
    </div>
    <div class="common_border"></div>
    <div class="items pad_lr20 pad_tb20">
		<div class="item color_red">
			重量：<span><?=$ls['scale']?></span>
		</div>
		<div class="item red">
			價格：<span><?=$ls['actual_money']?>TWD </span>
		</div>
		<div class="item">
			入倉時間：<span class="color_666"><?=date('Y-m-d H:i:s',$ls['createtime'])?></span>
		</div>
    </div>
    <div class="common_border"></div>
    <a class="btns" href="<?=url('transportation/details',array('id'=>$ls['id']))?>">
        去支付
    </a>
</div>
<?php
		}
	}
?>
<!-- 待付款-->

<!-- 待发货-->
<?php
	if($type == '4'){
		foreach($list as $ls){
?>
<div class="box bg_fff margin_b20" data-id="<?=$ls['id']?>">
    <div class="flex_ac title pad_lr20">
        訂單單號：<?=$ls['order_no']?>
        <!-- red  green -->
        <span class="status margin_a red"><?=$jiyun[$ls['order_status']]?></span>
    </div>
    <div class="common_border"></div>
    <div class="items pad_lr20 pad_tb20">
		<div class="item color_red">
			重量：<span><?=$ls['scale']?></span>
		</div>
		<div class="item red">
			價格：<span><?=$ls['actual_money']?>TWD </span>
		</div>
		<div class="item">
			入倉時間：<span class="color_666"><?=date('Y-m-d H:i:s',$ls['createtime'])?></span>
		</div>
    </div>
    <div class="common_border"></div>
    <a class="btns" href="<?=url('transportation/details',array('id'=>$ls['id']))?>">
        詳情
    </a>
</div>
<?php
		}
	}
?>
<!-- 待发货-->
<!-- 已发货-->
<?php
	if($type == '5'){
		foreach($list as $ls){
?>
<div class="box bg_fff margin_b20" data-id="<?=$ls['id']?>">
    <div class="flex_ac title pad_lr20">
        訂單單號：<?=$ls['order_no']?>
        <!-- red  green -->
        <span class="status margin_a red"><?=$jiyun[$ls['order_status']]?></span>
    </div>
    <div class="common_border"></div>
    <div class="items pad_lr20 pad_tb20">
		<div class="item color_red">
			重量：<span><?=$ls['scale']?></span>
		</div>
		<div class="item red">
			價格：<span><?=$ls['actual_money']?>TWD </span>
		</div>
		<div class="item">
			入倉時間：<span class="color_666"><?=date('Y-m-d H:i:s',$ls['createtime'])?></span>
		</div>
    </div>
    <div class="common_border"></div>
    <a class="btns js_ajax_win" href="<?=url('transportation/logistics_jiyun',array('id'=>$ls['id']))?>">
        查看物流
    </a>
</div>
<?php
		}
	}
?>
<!-- 已发货-->