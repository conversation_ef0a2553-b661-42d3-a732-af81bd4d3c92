<style type="text/css">
    body{  padding-bottom: 1rem; }
    /* .footer_new{display: none;} */
</style>
<div id="app">
    <div class="jiyun_div">
        <div class="pad_tb10">
            {include file="transportation/m_nav" /}
        </div>
        <form action="" method="get" class="js_search_form search_form margin_lr20 clearfix">
            <div class="pull-left">
                <el-dropdown @command="command" trigger="click">
                  <a class="search_down_btn">
                    {{get_type_name()}}<i class="el-icon-arrow-down el-icon--right"></i>
                  </a>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item :command="item.id" v-for="(item,index) in types" :key="index">{{item.title}}</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
                <input type="hidden" name="type" v-model="type" >
            </div>
            <div class="over_hide">
                <el-input
                    placeholder="請輸入搜索" type="search" name="keyword" size="small" @keyup.enter.native="search" 
                    v-model="keyword"><i class="el-icon-search el-input__icon" @click="search" slot="suffix"></i>
                </el-input>
            </div>
        </form>

        <div class="pad_t20">
            <div class="clearfix jiyun_list js_scroll_more_list" data-url="{:url('index/transportation/list')}">
                {include file="transportation/m_list" /}
            </div>
            <!-- 上拉加载的 无数据我自己判断 -->
            <div class="loadding_d hide">
                <img src='__CDN__/assets/img/loading.png'>
                <span>上拉加载更多</span>
            </div>
            <div class="js_no_data pad_tb30 hide">
                {include file="common/nodata" /}
            </div>
            <!-- 上拉加载的 无数据我自己判断 -->
        </div>

		<?php
			if($type == '2'){
		?>
			<!-- 已到仓库 底部按钮 -->
			<a href="javascript:;" class="jyun_btn btn2" @click="sele_all"><div>全选</div></a>
			<a href="javascript:;" class="jyun_btn" @click="tong"><div>同捆<br>出货</div></a>
			<!-- 已到仓库 底部按钮 -->
		<?php
			}
		?>
        
    </div>
</div>

<!-- 底部 -->
<div class="safe_area"></div>
{include file="common/m_footer" /}
<!-- 底部 -->
<script type="text/javascript">
    var app=new Vue({
      el: '#app',
      data:function(){
        return {
            is_ajax:false,
            type:"1",  // 搜索类型
            keyword:'<?=$keyword?>', // 关键字
            
            // 搜索类型选项 
            types:[
                {
                    id:'1',
                    title:'运单编号'
                },
            ],
        }
      },
      mounted:function(){
        $('#app').css('display','block');
        check_load();
      },
      methods:{
        
        search:function(){
            $('.js_search_form').submit()
        },
        command:function(e){
            this.type=e
        },
        get_type_name:function(){
            var types=this.types;
            for(var i=0;i<types.length;i++){
                if(types[i].id==this.type){
                    return types[i].title;
                    break;
                }
            }
        },
        sele_all(){
            $('.js_scroll_more_list .box .check_d').addClass('active')
        },
        tong(){
            var _this=this;
            if(_this.is_ajax){
                return false;
            }
            if($('.js_scroll_more_list .box .check_d.active').length==0){
                tip_show('請至少選擇一組數據','2');
                return false;
            }
            _this.is_ajax = true;

            var ids=[];
            for(var i=0;i<$('.js_scroll_more_list .box .check_d.active').length;i++){
                var item=$('.js_scroll_more_list .box .check_d.active').eq(i);
                ids.push(item.parents('.box').attr('data-id'))
            }
            $.post("{:url('/index/transportation/tong')}",{
                id:ids.join(',')
            },function(res){
                if (res.code == "1") {
                    tip_show(res.msg,1)
                    // 跳转
                    setTimeout(function(){
                       window.location.href=res.url
                    },1000*parseInt(res.wait))
                } else {
                    tip_show(res.msg || '未知错误',2)
                }
                _this.is_ajax = false;
            })
        },
      }
    })


    $(document).on('click','.js_scroll_more_list .box',function(){
        var _this=$(this);
        if(_this.find('.check_d').hasClass('active')){
            _this.find('.check_d').removeClass('active')
        }else{
            _this.find('.check_d').addClass('active')
        }
    })
</script>