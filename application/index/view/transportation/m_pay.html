<style type="text/css">
    .footer_new{display: none;}
</style>
<div id="app_pay" style="display: none">
    <div class="helps_div pad_t20">
        <div class="bg_fff pad_lr20 helps_title flex_ac">
            <img src="__CDN__/assets/img/wap/jy.png" class="icon"> <span class="font_b">集運支付</span>
        </div>
    </div>
    <div class="pad_tb20 daifu_div js_transportation_div">
        <form action="<?=url('index/transportation/warehousing')?>">
        

        <div class="bg_fff pad_lr20 helps_title min_w daifu_pay_item flex_ac hide_input_d" @click="show_address=!show_address">
            <input is_required="true" name="address" value="" class="hide_input js_address_input" empty_tip="選擇集运地址">
            <span class="font_b">集运地址</span>
            <div class="flex1 ellipsis pad_lr20 color_999 js_address_html">
                
            </div>
            <img src="__CDN__/assets/img/wap/sele_style.png" class="arr" :class="show_address?'active':''" style="height: auto; width: 0.22rem">
        </div>
		<div v-show="show_address" class="pad_lr20 pad_tb20 trans_address_list js_trans_address_list">
            <?php
                foreach($address as $ls){
            ?>
                <div class="box js_box clearfix" data-id="<?=$ls['id']?>">
                    <div class="ellipsis text"><?=$category[$ls['type']]['name'].$category[$ls['city']]['name'].$ls['address']?>&nbsp;&nbsp;<?=$ls['address_name']?>&nbsp;&nbsp;<?=$ls['address_mobile']?></div>
                </div>
            <?php
                }
            ?>
            <div class="box clearfix add js_ajax_win" href="{:url('transportation/address_add')}" data-width="620">
                <div class="ops pull-right pad_t10">
                </div>
                <div class="ellipsis text text-center color_red">+新增</div>
            </div>
        </div>
		</br>
		<div class="order_detail">
		<?php
			foreach($jiyun['list'] as $key=>$ls){
		?>
			<div class="item_div">
				<div class="item clearfix">
					<div class="pull-left t">運單號碼：</div>
					<div class="over_hide r_text"><span class="color_666"><?=$ls['waybill']?>-<?=$ls['is_transport']?'海':'空'?></span></div>
				</div>
					<div class="item clearfix">
						<div class="pull-left t">快遞名稱：</div>
						<div class="over_hide r_text color_666"><?=$ls['waybill_name']?></div>
					</div>
				<div class="item clearfix">
					<div class="pull-left t">是否特貨：<?=$ls['is_sale']?'是':'否';?></div>
					<div class="pull-left t" style="margin-left:20px;">是否外島：<?=$ls['is_type']?'是':'否';?></div>
				</div>
				<div class="item clearfix">
					<div class="pull-left t">重量：<?=$ls['scale']?>KG</div>
					<div class="pull-left t" style="margin-left:20px;">單價：<?=$jiyun['unit_price']?>TWD</div>
				</div>
				<div class="item clearfix">
					
					<div class="pull-left t">派送费：<?=$jiyun['peisongfei']?>TWD</div>
					<div class="pull-left t" style="margin-left:20px;">超重派送费：<?=$jiyun['all_beyond']?>TWD</div>
				</div>
			</div>
		<?php
			}
		?>
		</div>
			
        <div class="pad_lr20 flex_ac user_taobi" @click="change_is_use">
            <img src="__CDN__/assets/img/wap/daifu_check.png" v-show="!is_use">
            <img src="__CDN__/assets/img/wap/daifu_checked.png" v-show="is_use">
            使用抵扣金 <span class="color_999">(可使用數量：<?=$user['buy_gold']?>)</span>
        </div>

        <div class="bg_fff pad_lr20 helps_title flex_ac">
            <input type="text" class="s_input" data-type="buy_gold" placeholder="請輸入使用淘幣的數量" name="buy_gold" value="<?=$jiyun['goldBuy']?>" disabled>
        </div>
        <div class="pad_lr20 flex_ac user_taobi" @click="change_is_use2">
            <img src="__CDN__/assets/img/wap/daifu_check.png"  v-show="!is_use2">
            <img src="__CDN__/assets/img/wap/daifu_checked.png" v-show="is_use2">
            使用優惠券 <span class="color_999">(可使用：2张)</span>
        </div>
        
        <div class="helps_div">
            <div class="bg_fff pad_lr20 helps_title flex_ac" style="line-height: 0.44rem;">
                <img src="__CDN__/assets/img/wap/new_index/yhq.png" class="icon"> <span class="font_b js_yhq_item margin_l20"></span>
                <a  class="margin_a js_ajax_win color_red f12" data-height="450" href="{:url('transportation/coupon')}">選擇優惠券</a>
            </div>
        </div>
        <div class="bg_fff pad_lr20 helps_title margin_t20 flex_ac">
            <span class="font_b">應付新臺幣</span>
            <!-- 这里手机端需要赋值 -->
            <span class="margin_a"><span class="red js_money_total"><?=$jiyun['all_money'] - $jiyun['goldBuy']?></span>TWD</span>
        </div>
		<div class="bg_fff pad_lr20 helps_title margin_t20 flex_ac" style="font-size: 16px;">
            <?=$jiyun['unit_price']?> * <?=$jiyun['all_scale']?>KG + <?=$jiyun['peisongfei']?> 派件費 + <?=$jiyun['all_beyond']?> 超大派件費 + <?=$jiyun['outer_island']?> 外島 - <?=$jiyun['goldBuy']?> 抵扣金
        </div>
        <div class="bg_fff pad_lr20 helps_title margin_t20 flex_ac">
            <span class="font_b">重量</span>
            <span class="margin_a"><span class=""><?=$jiyun['all_scale']?></span>kg</span>
        </div>

        <div class="bg_fff pad_lr10 margin_t20">
            <div class=" pad_lr10 helps_title flex_ac">
                還需支付：<span class="js_need_pay red">0.00</span>TWD
            </div>
            <div class="hide_input_d">
                <input name="type" v-model="type" class="hide_input" >
            </div>
            <div class="common_border"></div>
            <div class=" pad_lr10 helps_title hide_input_d daifu_pay_item flex_ac" @click="tab_type(1)">
                <input :is_required="type==1" name="bank_ids" v-model="bank_ids" class="hide_input" empty_tip="请选择银行">
                <img src="__CDN__/assets/img/wap/daifu_check.png" class="check" v-show="type!=1">
                <img src="__CDN__/assets/img/wap/daifu_checked.png" class="check" v-show="type==1">
                <img src="__CDN__/assets/img/wap/account1.png" class="icon">
                銀行支付
                <div class="sele_style margin_a" :class="show_bank?'active':''" v-show="type==1" @click.stop="show_bank=!show_bank">
                    選擇銀行卡 <img src="__CDN__/assets/img/wap/sele_style.png" class="arr">
                </div>
            </div>
            <div class="common_border"></div>

            <div v-show="show_bank==1 && type==1" >
                <div class="daifu_bank_list clearfix pad_t10 hide_input_d js_bank_lists" data-canuse-more="0">
                    
                    <?php
                        foreach($userbank as $ls){
                    ?>
                        <div class="box flex" data-id="<?=$ls['id']?>">
                            <div class="item"><?=$ls['name']?></div>
                            <div class="item">(<?=$ls['account_name']?>)</div>
                            <div class="item last">**** **** **** <?=$ls['account_six']?></div>
                        </div>
                    <?php
                        }
                    ?>
                    <a href="{:url('user/add_bank')}" class="box add margin_t20 text-center">
                        <div class="t f12 color_red" >+新增銀行卡</div>
                    </a>
                </div>
                <div class="common_border"></div>
            </div>
           <!-- <div class=" pad_lr10 helps_title daifu_pay_item daifu_pay_item_cs flex_ac" @click="tab_type(2)">
                <img src="__CDN__/assets/img/wap/daifu_check.png" class="check" v-show="type!=2">
                <img src="__CDN__/assets/img/wap/daifu_checked.png" class="check" v-show="type==2">
                <img src="__CDN__/assets/img/wap/account4.png" class="icon">
                <div class="flex1">
                    <div>超商支付 <span class="f12 color_999">(超商缴费代码将传送到行动电话)</span></div>
                    <div class="f12 color_999">超商将向您收取<span >30TWD</span>的手续费</div>
                    <div class="f12 color_999">最低金额<span >100TWD</span>,最高手续费不超过<span >6000TWD</span></div>
                </div>
            </div>-->

            <!-- 未开通 -->
            <!-- <div class=" pad_lr10 helps_title daifu_pay_item daifu_pay_item_cs flex_ac">
                <img src="__CDN__/assets/img/wap/daifu_check.png" class="check">
                <img src="__CDN__/assets/img/wap/account4.png" class="icon">
                <div class="flex1">
                    <div>超商支付 <span class="f12 color_999">(未開通)</span></div>
                </div>
                <a href="{:url('/index/daifu/apply_shop')}" class="color_red">開通</a>
            </div> -->
        </div>

       

        <div class="pad_b20 pad_t30 pad_lr20">
            <a href="javascript:;" class="sub_btn  js_form_sub" data-text="下一步" data-loadding="提交中···" data-type="new_location">支付</a>
        </div>
		</form>
    </div>
</div>

<div class="hide">
 <a href="{:url('/index/daifu/checkMobile')}" class="js_ajax_win js_check_mobile_link" data-hideclose="1" >手机验证</a> 
</div>

<script type="text/javascript">
    var app_pay=new Vue({
      el: '#app_pay',
      data:function(){
        return {
            is_rem:true,
            is_rem2:true,
            is_ajax:false,
            type:'1',
            bank_ids:'',
            is_use:true,
            show_bank:false,
            show_address:false,
            is_use2:true
        }
      },
      mounted:function(){
        $('#app_pay').css('display','block');
        
      },
      methods:{
        tab_type(type){
            this.type=type;
        },
        all_use(){
            if(is_check_mobile){
                $('.js_tao_money').val($('.js_tao_money').attr('data-max'))
            }else{
                $('.js_tao_money').click()
            }
        },
        change_is_use(){
            this.is_use=!this.is_use;
            if(!this.is_use){
                $('.js_tao_money').val('');
                fun_canuse_more()
            }
        },
        change_is_use2(){
            this.is_use2=!this.is_use2;
            if(!this.is_use2){
                $('.js_yhq_item').html('');
                fun_canuse_more()
            }
        },
        jump_url:function(url){
            window.location.href=url
        },
        
      }
    })
    

    $(document).on('click','.js_trans_address_list .js_box',function(){
        $(this).addClass('active').siblings('.js_box').removeClass('active');
        $('.js_address_input').val($(this).attr('data-id'));
        $('.js_address_html').html($(this).find('.text').html());
        app_pay.show_address=false;
    })

    // 选择银行
    $(document).on('click','.js_bank_lists .box',function(){
        var can_more=$('.js_bank_lists').attr('data-canuse-more')==1;
        function func_ids(){
            var boxs=$('.js_bank_lists .box.active');
            var ids=[];
            for(var i=0;i<boxs.length;i++){
                ids.push(boxs.eq(i).attr('data-id'))
            }
            app_pay.bank_ids=ids.join(',');
            console.log(app_pay.bank_ids)
        }
        if(can_more){
            if($(this).hasClass('active')){
                $(this).removeClass('active')
            }else{
                $(this).addClass('active')
            }
            func_ids()
        }else{
            app_pay.bank_ids=$(this).attr('data-id');
            $(this).addClass('active').siblings('.box').removeClass('active');
        }
    })

    // 判断是否能多选银行
    function fun_canuse_more(){
        var t_money2=parseFloat($('.js_money_total').html()) || 0;
        var tao_money2=parseFloat($('.js_tao_money').val()) || 0;
        var yhq_money=$('.js_yhq_input').length>0?(parseFloat($('.js_yhq_input').attr('data-money')) || 0):0;
        if(t_money2-tao_money2-yhq_money>=30000){
            $('.js_bank_lists').attr('data-canuse-more',1)
        }else{
            $('.js_bank_lists').attr('data-canuse-more',0);
            if($('.js_bank_lists .box.active').length>1){
                $('.js_bank_lists .box.active').removeClass('active');
                app_pay.bank_ids=''
            }
        }
        $('.js_need_pay').html((t_money2-tao_money2-yhq_money).toFixed(2));
    }

    fun_canuse_more()



    // 淘币手机验证
    var is_check_mobile=false;

    $(document).on('click','.js_tao_money',function(){
        if(!is_check_mobile){
            $(this).blur();
            $('.js_check_mobile_link').click();
        }
    })

    $(document).on('blur','.js_tao_money',function(){
       fun_canuse_more()
    })
    



    
</script>