<style type="text/css">
    
</style>
<div id="app">
    <div class="pad_tb20 helps_div">
        <div class="transportation_user_main">
            <?php
				if($all_own['newscontent']){
			?>
				<div class="clearfix">
					<div class="notice clearfix pull-left pad_lr10">
						<div class="tt">公告：</div>
						<div class="ellipsis "><div class="ellipsis"><?=$all_own['newscontent']?></div></div>
					</div>
				</div>
			<?php
				}
			?>

            <div class="clearfix pad_t20 transportation_div">
                {include file="transportation/nav" /}
                <div class="over_hide block_div pad_lr30 pad_tb10">
                    <!--<div class="user_common_tt clearfix">
                        <img src="__CDN__/assets/img/pc/warm.png"> 深圳倉注意事項 
                    </div>

                    <div class="f14 color_666 pad_l20" >
                        
                    </div>-->
                    <div class="pad_t20 clearfix">
                        <form action="" method="get" class="js_search_form">
                            <div class="pull-right color_666">
                                每頁
                                <el-select size="small" @change="page_change" style="display: inline-block; width: 70px;" v-model="pages" name="pages" placeholder="请选择">
                                    <el-option value="10"></el-option>
                                    <el-option value="20"></el-option>
                                    <el-option value="30"></el-option>
                                    <el-option value="40"></el-option>
                                    <el-option value="50"></el-option>
                                </el-select>
                                條數據
                            </div>
                            
                            <input type="hidden" name="type" v-model="type" >
                            <el-dropdown @command="command">
                              <el-button type="primary" size="small">
                                {{get_type_name()}}<i class="el-icon-arrow-down el-icon--right"></i>
                              </el-button>
                              <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item :command="item.id" v-for="(item,index) in types" :key="index">{{item.title}}</el-dropdown-item>
                              </el-dropdown-menu>
                            </el-dropdown>
                            <el-input
                                placeholder="請輸入搜索"
                                suffix-icon="el-icon-search" name="keyword" size="small" @keyup.enter.native="search" 
                                v-model="keyword" style="display: inline-block; width: 280px;">
                            </el-input>
                        </form>
                    </div>

                    <div class="pad_t20">
                        <div class="el-table el-table--fit el-table--enable-row-hover el-table--enable-row-transition" style="width: 100%;">
                              <div class="el-table__body-wrapper is-scrolling-none">
                                <table cellspacing="0" cellpadding="0" border="0" class="el-table__body js_order_table" style="width: 100%;">
                                    <thead>
                                    <tr>
                                      <th width="20%">
                                        <div class="cell">訂單單號</div>
                                      </th>
                                      <th width="10%">
                                        <div class="cell">重量</div>
                                      </th>

                                      <th width="10%">
                                        <div class="cell">價格</div>
                                      </th>

                                      <th width="15%">
                                        <div class="cell">發貨時間</div>
                                      </th>

                                      <th width="10%">
                                        <div class="cell">狀態</div>
                                      </th>

                                      <th width="20%">
                                        <div class="cell">操作</div>
                                      </th>
                                    </tr>
                                  </thead>
                                    <tbody>
										<?php
											foreach($list as $ls){
										?>
											<tr class="el-table__row" order_no="<?=$ls['order_no']?>">
											  <td>
												<div class="cell"><?=$ls['order_no']?></div>
											  </td>
											  <td>
												<div class="cell"><span style="color:#00A3FF"><?=$ls['scale']?>kg</span></div>
											  </td>
											  <td>
												<div class="cell"><span style="color:#FF5050"><?=$ls['actual_money']?>TWD</span></div>
											  </td>
											  <td>
												<div class="cell"> <span style="color:#1BCBA9"></span></div>
											  </td>
											  <td>
												<div class="cell"> <span style="color:#1BCBA9"><?=$jiyun[$ls['order_status']]?></span></div>
											  </td>
											  <td>
												<div class="cell">
													<?php
														if($ls['order_status'] == '2'){
													?>
														<a href="<?=url('transportation/details',array('id'=>$ls['id']))?>" class="color_red jiyun_op_btn">去支付</a>
														<a href="<?=url('transportation/cancel',array('id'=>$ls['id']))?>" class="color_red jiyun_op_btn js_ajax_confirm" tips="您確認取消嗎？">取消</a>
													<?php
														}
													?>
													<?php
														if($ls['order_status'] > '2'){
													?>
														<a href="<?=url('transportation/details',array('id'=>$ls['id']))?>" class="color_red jiyun_op_btn">詳情</a>
													<?php
														}
													?>
													<?php
														if($ls['order_status'] == '4'){
													?>
														<a href="<?=url('transportation/logistics_jiyun',array('id'=>$ls['id']))?>" class="color_red jiyun_op_btn js_ajax_win" data-width="800">查看物流</a>
													<?php
														}
													?>
												</div>
											  </td>
											</tr>
										<?php
											}
										?>
                                    </tbody>
                                </table>
                              </div>
                        </div>
						<?php
							if($list){
						?>
							{include file="common/pages" /}
						<?php
							}else{
						?>
							{include file="common/nodata" /}
						<?php
							}
						?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var app=new Vue({
      el: '#app',
      data:function(){
        return {
            is_ajax:false,
            type:"1",  // 搜索类型
            keyword:'<?=$keyword?>', // 关键字
            pages:<?=empty($_GET['pages'])?'10':$_GET['pages']?>, // 每页条数

            // 搜索类型选项 
            types:[
                {
                    id:'1',
                    title:'运单编号'
                },
            ]
        }
      },
      mounted:function(){
        $('#app').css('display','block');
      },
      methods:{
        page_change:function(e){
            setTimeout(function(){
                $('.js_search_form').submit()
            },10)
        },
        search:function(){
            $('.js_search_form').submit()
        },
        command:function(e){
            this.type=e
        },
        get_type_name:function(){
            var types=this.types;
            for(var i=0;i<types.length;i++){
                if(types[i].id==this.type){
                    return types[i].title;
                    break;
                }
            }
        }
      }
    })
    
</script>