<style type="text/css">
    
</style>
<div id="app">
    <div class="pad_tb20 helps_div">
        <div class="transportation_user_main">
            <?php
				if($all_own['newscontent']){
			?>
				<div class="clearfix">
					<div class="notice clearfix pull-left pad_lr10">
						<div class="tt">公告：</div>
						<div class="ellipsis "><div class="ellipsis"><?=$all_own['newscontent']?></div></div>
					</div>
				</div>
			<?php
				}
			?>

            <div class="clearfix pad_t20 transportation_div">
                {include file="transportation/nav" /}
                <div class="over_hide block_div pad_lr30 pad_tb10">
                    <div class="user_common_tt clearfix">
                        <img src="__CDN__/assets/img/pc/warm.png"> 注意事項 
                    </div>

                    <div class="f14 color_666 pad_l20" >
                        {$site.zhuyishuoming}
                    </div>
                    <div class="pad_t20 clearfix">
                        <form action="<?=url('index/transportation/no_warehouse')?>" method="get" class="js_search_form">
                            <div class="pull-right color_666">
                                每頁
                                <el-select size="small" @change="page_change" style="display: inline-block; width: 70px;" v-model="pages" name="pages" placeholder="请选择">
                                    <el-option value="10"></el-option>
                                    <el-option value="20"></el-option>
                                    <el-option value="30"></el-option>
                                    <el-option value="40"></el-option>
                                    <el-option value="50"></el-option>
                                </el-select>
                                條數據
                            </div>
                            
                            <input type="hidden" name="type" v-model="type" >
                            <el-dropdown @command="command">
                              <el-button type="primary" size="small">
                                {{get_type_name()}}<i class="el-icon-arrow-down el-icon--right"></i>
                              </el-button>
                              <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item :command="item.id" v-for="(item,index) in types" :key="index">{{item.title}}</el-dropdown-item>
                              </el-dropdown-menu>
                            </el-dropdown>
                            <el-input
                                placeholder="請輸入搜索"
                                suffix-icon="el-icon-search" name="keyword" size="small" @keyup.enter.native="search" 
                                v-model="keyword" style="display: inline-block; width: 280px;">
                            </el-input>
                        </form>
                    </div>

                    <div class="pad_t20">
                        <div class="el-table el-table--fit el-table--enable-row-hover el-table--enable-row-transition" style="width: 100%;">
                              
                              <div class="el-table__body-wrapper is-scrolling-none">
                                <table cellspacing="0" cellpadding="0" border="0" class="el-table__body js_order_table" style="width: 100%;">
                                    <thead>
                                    <tr>
                                      <th width="24%" align="center">
                                        <div class="cell">委託單號</div>
                                      </th>
									  <th width="19%" align="center">
                                        <div class="cell">快遞公司</div>
                                      </th>
                                      <th width="20%" align="center">
                                        <div class="cell">運單號碼</div>
                                      </th>
                                      <th width="22%" align="center">
                                        <div class="cell">委託時間</div>
                                      </th>

                                      <th width="20%" align="center">
                                        <div class="cell">狀態</div>
                                      </th>

                                      <th width="15%" align="center">
                                        <div class="cell">操作</div>
                                      </th>
                                    </tr>
                                  </thead>
                                    <tbody>
										<?php
											foreach($list as $ls){
										?>
											<tr class="el-table__row" order_no="<?=$ls['id']?>">
											  <td>
												<div class="cell"><?=$ls['entrust_no']?>-<?=$ls['is_transport']?'海':'空'?></div>
											  </td>
											  <td>
												<div class="cell"><?=$ls['waybill_name']?></div>
											  </td>
											  <td>
												<div class="cell"><?=$ls['waybill']?></div>
											  </td>
											  <td>
												<div class="cell"><?=date('Y-m-d H:i:s',$ls['createtime'])?></div>
											  </td>
											  <td>
												<div class="cell"> <span style="color:#1BCBA9"><?=$jiyun[$ls['type']]?></span></div>
											  </td>
											  <td>
												<div class="cell"><a href="<?=url('transportation/logistics',array('id'=>$ls['id']))?>" class="color_red js_ajax_win jiyun_op_btn" data-width="800">查看物流</a></div>
											  </td>
											</tr>
										<?php
											}
										?>
                                    </tbody>
                                </table>
                              </div>
                        </div>
						
						<?php
							if($list){
						?>
							{include file="common/pages" /}
						<?php
							}else{
						?>
							{include file="common/nodata" /}
						<?php
							}
						?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var app=new Vue({
      el: '#app',
      data:function(){
        return {
            is_ajax:false,
            type:"1",  // 搜索类型
            keyword:'<?=$keyword?>', // 关键字
            pages:<?=empty($_GET['pages'])?'10':$_GET['pages']?>, // 每页条数

            // 搜索类型选项 
            types:[
                {
                    id:'1',
                    title:'運單號碼'
                },
            ]
        }
      },
      mounted:function(){
        $('#app').css('display','block');
      },
      methods:{
        page_change:function(e){
            setTimeout(function(){
                $('.js_search_form').submit()
            },10)
        },
        search:function(){
            $('.js_search_form').submit()
        },
        command:function(e){
            this.type=e
        },
        get_type_name:function(){
            var types=this.types;
            for(var i=0;i<types.length;i++){
                if(types[i].id==this.type){
                    return types[i].title;
                    break;
                }
            }
        }
      }
    })
    
</script>