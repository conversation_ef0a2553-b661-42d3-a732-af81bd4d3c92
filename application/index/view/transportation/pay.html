<style type="text/css">
     .hide_input{width:0px;height:0px;opacity:0}
     .yhq_item_d{border: 1px solid #DDDDDD; display: none; margin-top: 10px;}
     .yhq_item_d.active{display: block;}
     .yhq_item_d .title{background: #F9F9F9; color: #EF436D; text-align: center; line-height: 48px; font-size: 14px;}
     .yhq_item_d .yhq_list .item{margin-bottom: 0;}
</style>
<div id="app">
    <div class="pad_tb20 helps_div">
        <div class="transportation_user_main">
            <?php
				if($all_own['newscontent']){
			?>
				<div class="clearfix">
					<div class="notice clearfix pull-left pad_lr10">
						<div class="tt">公告：</div>
						<div class="ellipsis "><div class="ellipsis"><?=$all_own['newscontent']?></div></div>
					</div>
				</div>
			<?php
				}
			?>

            <div class="clearfix pad_t20 transportation_div js_transportation_div">
                {include file="transportation/nav" /}
                <div class="over_hide block_div pad_lr30 pad_tb10">
                    <form action="<?=url('index/transportation/warehousing')?>" autocomplete="off" class="small_input">
                        <div class="user_common_tt clearfix">
                            一.集運地址
                        </div>
                        <div class="trans_address_list js_trans_address_list hide_input_d">
                            <input is_required="true" name="address" value="" class="hide_input js_address_input" empty_tip="選擇集运地址">
                            <?php
								foreach($address as $ls){
							?>
								<div class="box js_box clearfix" data-id="<?=$ls['id']?>">
									<div class="ops pull-right">
										<a href="{:url('transportation/address_add')}?id=<?=$ls['id']?>" class="color_red js_ajax_win" data-width="620">修改</a>
										<a href="{:url('transportation/address_dele')}" class="red margin_l20 js_ajax_dele" data-id="<?=$ls['id']?>" tips="您確認删除該地址嗎？">删除</a>
									</div>
									<div class="ellipsis text"><?=$category[$ls['type']]['name'].$category[$ls['city']]['name'].$ls['address']?>&nbsp;&nbsp;<?=$ls['address_name']?>&nbsp;&nbsp;<?=$ls['address_mobile']?></div>
								</div>
							<?php
								}
							?>
                            <div class="box clearfix js_ajax_win" href="{:url('transportation/address_add')}" data-width="620">
                                <div class="ops pull-right pad_t10">
                                </div>
                                <div class="ellipsis text text-center color_red">+新增</div>
                            </div>
                        </div>
						<div class="user_common_tt clearfix">
                            二.包裹資訊
                        </div>
						<table class="common_table table margin_t10 text-center">
							<tbody>
								<tr>
									<th>運單號碼</th>
									<th>快遞名稱</th>
									<th>是否特貨</th>
									<th>是否外島</th>
									<th>重量</th>
									<th>單價</th>
									<th>派送费</th>
									<th>超重派送费</th>
								</tr>
								<?php
									foreach($jiyun['list'] as $key=>$ls){
								?>
									<tr class="color_666">
										<td><?=$ls['waybill']?>-<?=$ls['is_transport']?'海':'空'?></td>
										<td><?=$ls['waybill_name']?></td>
										<td><?=$ls['is_sale']?'是':'否';?></td>
										<td><?=$ls['is_type']?'是':'否';?></td>
										<td><?=$ls['scale']?>KG</td>
										<td><?=$jiyun['unit_price']?>TWD</td>
										<td><?=$jiyun['peisongfei']?>TWD</td>
										<td><?=$jiyun['all_beyond']?>TWD</td>
									</tr>
								<?php
									}
								?>
								
							</tbody>
						</table>
                        <div class="user_common_tt clearfix">
                            三.支付詳情
                        </div>
						<?php
							if($jiyun['goldBuy']){
						?>
							 <div class="daifu_item">
								<!-- data-max  最大值 -->
								使用抵扣金: <input type="number" class="s_input" data-type="buy_gold" name="buy_gold" value="<?=$jiyun['goldBuy']?>" disabled> <span class="f12 color_999">可使用數量：<?=$user['buy_gold']?>， <span class="red">不輸入則視為不使用</span></span>
							</div>
						<?php
							}
						?>
                        
                        <div class="daifu_item">
                            使用優惠券：<a class="js_ajax_win sub_btn small" style="background: none; border: 1px solid #EF436D; color: #EF436D; box-shadow: none;" href="{:url('transportation/coupon')}" data-width="610" data-height="600">選擇優惠券</a>
                        </div>

                        <div class="yhq_item_d">
                            <div class="title">優惠券</div>
                            <div class="yhq_list flex flex_w js_yhq_item"> 
                            
                            </div>
                        </div>
                        


                        <div class="daifu_item">
                            應付新臺幣： <b class="red js_money_total" data-all="<?=$jiyun['all_money'] - $jiyun['goldBuy']?>">TWD</b> （<?=$jiyun['unit_price']?> * <?=$jiyun['all_scale']?>KG + <?=$jiyun['peisongfei']?> 派件費 + <?=$jiyun['all_beyond']?> 超大派件費 + <?=$jiyun['outer_island']?> 外島 - <?=$jiyun['goldBuy']?> 抵扣金）
                        </div>
                        <div class="daifu_item">
                            重量：<?=$jiyun['all_scale']?>kg  
                        </div>
                        <div class="daifu_item clearfix">
                            <div class="pull-left pad_r20">
                                付款方式: 
                            </div>
                            <div class="over_hide hide_input_d" style="position: relative; top: 3px">
                                <input name="type" v-model="type" class="hide_input" >
                                <el-radio-group v-model="type">
                                    <el-radio label="1">銀行支付</el-radio>
                                   <!-- <el-radio label="2">超商付款</el-radio>-->
                                </el-radio-group>
                            </div>
                        </div>


                        <div class="bank_list clearfix pad_t10 hide_input_d js_bank_lists" v-if="type==1" data-canuse-more="0">
                            <input is_required="true" name="bank_ids" v-model="bank_ids" class="hide_input" empty_tip="请选择银行">
							<?php
								foreach($userbank as $ls){
							?>
								<div class="box" data-id="<?=$ls['id']?>">
									<div class="tt ellipsis"><?=$ls['name']?>(<?=$ls['account_name']?>)</div>
									<div class="p f12 color_666">**** **** **** <?=$ls['account_six']?></div>
								</div>
							<?php
								}
							?>
                            <a href="{:url('index/account/addBank')}" class="box add text-center js_ajax_win"  data-width="600" data-height="880">
                                <img src="__CDN__/assets/img/pc/add_bank.png">
                                <div class="t f12 color_666 " >新增銀行卡</div>
                            </a>
                        </div>
                        <!-- 这个功能套页面时候再做 -->
                        <div class="color_999">超過30000金額可多選銀行</div>
                        <div class="bank_list clearfix pad_t20 hide_input_d" v-if="type==2">
                            <div>超商缴费代码将传送到行动电话</div>
							<div>超商将向您收取<span class="red">30TWD</span>的手续费，最低金额<span class="red">100TWD</span>,最高手续费不超过<span class="red">6000TWD</span></div>
                        </div>
                        <div class="pad_tb20">
                            <a href="javascript:;" class="sub_btn small js_form_sub" data-text="提交訂單" data-loadding="提交中···" data-type="new_location">提交訂單</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="hide">
 <a href="{:url('/index/daifu/checkMobile')}" class="js_ajax_win js_check_mobile_link" data-hideclose="1" >手机验证</a> 
</div>
</div>
<script type="text/javascript">
    var app=new Vue({
      el: '#app',
      data:function(){
        return {
            is_ajax:false,
            type:'1',
            bank_ids:''
        }
      },
      mounted:function(){
        $('#app').css('display','block');
      },
      methods:{

        jump_url:function(url){
            window.location.href=url
        },
        
      }
    })
    $('.js_trans_address_list .js_box').click(function(){
        $(this).addClass('active').siblings('.js_box').removeClass('active');
        $('.js_address_input').val($(this).attr('data-id'))
    })

    


    // 选择银行
    $(document).on('click','.js_bank_lists .box',function(){
        var can_more=$('.js_bank_lists').attr('data-canuse-more')==1;
        function func_ids(){
            var boxs=$('.js_bank_lists .box.active');
            var ids=[];
            for(var i=0;i<boxs.length;i++){
                ids.push(boxs.eq(i).attr('data-id'))
            }
            app.bank_ids=ids.join(',');
            console.log(app.bank_ids)
        }
        if(can_more){
            if($(this).hasClass('active')){
                $(this).removeClass('active')
            }else{
                $(this).addClass('active')
            }
            func_ids()
        }else{
            app.bank_ids=$(this).attr('data-id');
            $(this).addClass('active').siblings('.box').removeClass('active');
        }
    })

    // 判断是否能多选银行
    function fun_canuse_more(){
        var t_money2=parseFloat($('.js_money_total').attr('data-all')) || 0;
        var tao_money2=parseFloat($('.js_tao_money').val()) || 0;
        var yhq_money=$('.js_yhq_input').length>0?(parseFloat($('.js_yhq_input').attr('data-money')) || 0):0;
        if(t_money2-tao_money2-yhq_money>=30000){
            $('.js_bank_lists').attr('data-canuse-more',1)
        }else{
            $('.js_bank_lists').attr('data-canuse-more',0);
            if($('.js_bank_lists .box.active').length>1){
                $('.js_bank_lists .box.active').removeClass('active');
                app.bank_ids=''
            }
        }
        $('.js_money_total').html((t_money2-tao_money2-yhq_money).toFixed(2)+'TWD');
    }

    fun_canuse_more()



    // 淘币手机验证
    var is_check_mobile=false;

    $(document).on('click','.js_tao_money',function(){
        if(!is_check_mobile){
            $(this).blur();
            $('.js_check_mobile_link').click();
        }
    })

    $(document).on('blur','.js_tao_money',function(){
       fun_canuse_more()
    })
</script>