<style type="text/css">
    
</style>
<div id="app">
    <div class="pad_tb20 helps_div">
        <div class="transportation_user_main">
           <?php
				if($all_own['newscontent']){
			?>
				<div class="clearfix">
					<div class="notice clearfix pull-left pad_lr10">
						<div class="tt">公告：</div>
						<div class="ellipsis "><div class="ellipsis"><?=$all_own['newscontent']?></div></div>
					</div>
				</div>
			<?php
				}
			?>

            <div class="clearfix pad_t20 transportation_div">
                {include file="transportation/nav" /}
                <div class="over_hide block_div pad_lr20 pad_tb10">
                    <div class="user_common_tt clearfix">
                        <img src="__CDN__/assets/img/pc/warm.png"> 深圳倉注意事項 
                    </div>

                    <div class="f14 color_666 pad_l20" >
                        {$site.zhuyishuoming}
                    </div>
                    <div class="pad_t20 clearfix">
                        <form action="<?=url('index/transportation/warehouse')?>" method="get" class="js_search_form">
                            <div class="pull-right color_666">
                                每頁
                                <el-select size="small" @change="page_change" style="display: inline-block; width: 70px;" v-model="pages" name="pages" placeholder="请选择">
                                    <el-option value="10"></el-option>
                                    <el-option value="20"></el-option>
                                    <el-option value="30"></el-option>
                                    <el-option value="40"></el-option>
                                    <el-option value="50"></el-option>
                                </el-select>
                                條數據
                            </div>
                            <el-button type="primary" :loading="is_ajax" @click="tong" size="small">同捆出貨</el-button>
                            

                            <input type="hidden" name="type" v-model="type" >
                            <el-dropdown @command="command">
                              <el-button type="primary" size="small">
                                {{get_type_name()}}<i class="el-icon-arrow-down el-icon--right"></i>
                              </el-button>
                              <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item :command="item.id" v-for="(item,index) in types" :key="index">{{item.title}}</el-dropdown-item>
                              </el-dropdown-menu>
                            </el-dropdown>
                            <el-input
                                placeholder="請輸入搜索"
                                suffix-icon="el-icon-search" name="keyword" size="small" @keyup.enter.native="search" 
                                v-model="keyword" style="display: inline-block; width: 280px;">
                            </el-input>
                        </form>
                    </div>
                    
                    <div class="pad_t20">
                        <el-table
                          :data="tableData"
                          style="width: 100%;" @selection-change="handleSelectionChange">
							<el-table-column
								type="selection":selectable="selectable_fun"
								width="25"
								align="center">
							</el-table-column>
                          <el-table-column
                            prop="order_no"
							width="180px"
							align="center"
                            label="委託單號">
                          </el-table-column>
                          <el-table-column
                            prop="express_no"
							width="160px"
							align="center"
                            label="運單號碼">
                          </el-table-column>
						  
						  <el-table-column
                            label="特貨" width="50px" align="center">
                            <template slot-scope="scope">
                                <div>
                                  <span>{{ scope.row.sale_text}}</span>
                                </div>
                            </template>
                          </el-table-column>

                          <el-table-column
                            label="重量" width="60px" align="center">
                            <template slot-scope="scope">
                                <div>
                                  <span style="color:#00A3FF">{{ scope.row.weight}}</span>
                                </div>
                            </template>
                          </el-table-column>
						  
						  <el-table-column
                            label="單價" width="90px" align="center">
                            <template slot-scope="scope">
                                <div>
                                  <span style="color:#FF5050">{{ scope.row.money}}TWD</span>
                                </div>
                            </template>
                          </el-table-column>
						  
						  <el-table-column
                            prop="add_time" width="160px" align="center"
                            label="入倉時間">
                          </el-table-column>

                          <el-table-column
                            label="狀態" width="90px" align="center">
                            <template slot-scope="scope">
                                <div>
                                  <span :style="'color:'+(parseFloat(scope.row.status)<=2?'#1BCBA9':'#FF4F4F')">{{ scope.row.status_text}}</span>
                                </div>
                            </template>
                          </el-table-column>
						  <el-table-column
                            label="操作" width="120px" align="center">
                            <template slot-scope="scope">
                                <div>
                                    <a :href="'<?=url('transportation/onetong')?>?id='+scope.row.id" class="color_red jiyun_op_btn js_ajax_confirm" tips="您确认出货吗？" v-if="scope.row.status==1" >出货</a>
									<a :href="'<?=url('transportation/logistics')?>?id='+scope.row.id" class="color_red jiyun_op_btn js_ajax_win" data-width="800">查看物流</a>
							   </div>
                            </template>
                          </el-table-column>
                        </el-table>
                       <?php
							if($list){
						?>
							{include file="common/pages" /}
						<?php
							}else{
						?>
							{include file="common/nodata" /}
						<?php
							}
						?>
                       
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var app=new Vue({
      el: '#app',
      data:function(){
        return {
            type:"1",  // 搜索类型
            keyword:'<?=$keyword?>', // 关键字
            pages:<?=empty($_GET['pages'])?'10':$_GET['pages']?>, // 每页条数

            // 搜索类型选项 
            types:[
                {
                    id:'1',
                    title:'运单编号'
                },
            ],
            tableData: <?=is_array($_list)?json_encode($_list):'[]';?>,
            is_ajax:false,
            multipleSelection:[]
        }
      },
      mounted:function(){
        $('#app').css('display','block');
      },
      methods:{
	  selectable_fun(row){
            if(row.status==1){
                return true;
            }
            return false;
        },
        tong(){
            var _this=this;
            if(_this.is_ajax){
                return false;
            }
            if(this.multipleSelection.length==0){
                tip_show('請至少選擇一組數據','2');
                return false;
            }
            _this.is_ajax = true;
            console.log(this.multipleSelection)

            var ids=[];
            for(var i=0;i<this.multipleSelection.length;i++){
                ids.push(this.multipleSelection[i].id)
            }
            $.post("{:url('/index/transportation/tong')}",{
                id:ids.join(',')
            },function(res){
                if (res.code == "1") {
                    tip_show(res.msg,1)
                    // 跳转
                    setTimeout(function(){
                       window.location.href=res.url
                    },1000*parseInt(res.wait))
                } else {
                    tip_show(res.msg || '未知错误',2)
                   
                }
                _this.is_ajax = false;
            })
        },
        page_change:function(e){
            setTimeout(function(){
                $('.js_search_form').submit()
            },10)
        },
        search:function(){
            $('.js_search_form').submit()
        },
        command:function(e){
            this.type=e
        },
        get_type_name:function(){
            var types=this.types;
            for(var i=0;i<types.length;i++){
                if(types[i].id==this.type){
                    return types[i].title;
                    break;
                }
            }
        },
        handleSelectionChange:function(val) {
            this.multipleSelection = val;
        }
      }
    })
    
</script>