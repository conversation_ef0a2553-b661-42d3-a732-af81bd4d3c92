<div class="container">
	<div class="user_main margin_tb40 clearfix">
		<div class="user_main_line"></div>
		<div class="user_art_left pull-left">
			<div class="n_tt">
				<img src="__CDN__/assets/img/pc/icon_left_list_up.png" class="arr pull-right">
				<img src="__CDN__/assets/img/pc/icon_left_list_appeal.png" class="icon">
				{:__('Appeal list')}
			</div>
			<div class="n_tt_menu">
			<?php
				foreach($site['appeal'] as $key=>$ls){
			?>
				<a href="<?=url('user/appeal',array('type'=>$key))?>" class="<?php if($key==$type)echo 'active'?>"><?=$ls?></a>
			<?php
				}
			?>
			</div>
		</div>
		<div class="pad_b20 over_hide">
			<div class="user_art_title clearfix">
				<a href="{:url('/index/user/appeal_submit')}" class="appeal_btn">{:__('I want to appeal')}</a>
				{:__('Order related')}
			</div>
			<div class="appeal_list">
			<?php
				foreach($list as $ls){
			?>
				<div class="box">
					<div class="item clearfix">
						<span class="pull-right margin_l20 color_999"><?=date('Y-m-d',$ls['createtime'])?></span>
						<div class="ellipsis"><?=$ls['content']?></div>
					</div>
					<?php
						if(!empty($reply_result[$ls['id']])){
							foreach($reply_result[$ls['id']] as $val){
								if($val['type']){
					?>
							<div class="item color_red">{:__('Platforms')}{:__('Reply')}:<?=$val['reply_content']?>-<?=date('Y-m-d H:i:s',$val['createtime'])?></div>
					<?php
								}else{
					?>
							<div class="ellipsis"><?=$val['reply_content']?></div>
					<?php
								}
							}
						}
					?>
					<div class="item re_play_op">
						<a href="javascript:;" class=" link_btn color_red js_open_replay">{:__('Reply')}</a>
						<!--<a href="url('/index/user/appeal_dele'))" class="img_btn js_ajax_dele" tips="{:__('Are you sure you want to delete it?')}" data-id=""><img src="__CDN__/assets/img/pc/icon_appeal_delete.png"></a>-->
					</div>
					<div class="re_play_input hide">
						<div class="item">
							<input type="text" name="content" placeholder="{:__('Please enter the reply')}">
						</div>
						<div class="item">
							<a href="javascript:;" class="link_btn color_red margin_r10 js_sub_replay"  data-id="<?=$ls['id']?>" data-url="{:url('/index/user/appeal_submit_content')}">{:__('Confirm')}</a>
							<a href="javascript:;" class="link_btn js_cancel_replay">{:__('Cancel')}</a>
						</div>
					</div>
				</div>
			<?php
				}
			?>
			</div>
			<?php
				if($list){
			?>
				{include file="common/pages" /}
			<?php
				}else{
			?>
				{include file="common/nodata" /}
			<?php
				}
			?>
		</div>
	</div>
</div>

<script type="text/javascript">
	$(function(){
		$('.js_open_replay').click(function(){
			$(this).parents('.box').find('.re_play_op').addClass('hide');
			$(this).parents('.box').find('.re_play_input').removeClass('hide').find('input').focus();
		})
		$('.js_cancel_replay').click(function(){
			$(this).parents('.box').find('.re_play_op').removeClass('hide');
			$(this).parents('.box').find('.re_play_input').addClass('hide');
		})

		$('.js_sub_replay').click(function(){
			var _this=$(this);
			var _url=_this.attr('data-url');
			var _id=_this.attr('data-id');
			var _content=_this.parents('.box').find('input').val();
			if(_content==''){
				input_tips(_this.parents('.box').find('input'),"{:__('Please enter the reply')}",'3');
				return false;
			}
			layer.load(2);

			$.post(_url,{id:_id,content:_content},function(data){
				layer.closeAll();
				if(data.code==1){
					tip_show(data.msg, '1');
					setTimeout(function(){
                        location.reload()
                    },1000*parseInt(data.wait))
				}else{
					tip_show(data.msg, '2');
				}
			})

			return false
		})
	})
</script>