<style type="text/css">
	.header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .body{ padding-bottom: 1.3rem }
</style>
<div class="pad_t20 pad_lr20">
    <div class="appeal_list js_scroll_more_list" data-url="{:url('index/user/appeal_lists')}">
        {include file="user/appeal/m_lists" /}
    </div>

    <!-- 上拉加载的 无数据我自己判断 -->
    <div class="loadding_d hide">
        <img src='__CDN__/assets/img/loading.png'>
        <span>{:__('Pull-up Load More')}</span>
    </div>
    <div class="js_no_data pad_tb30 hide">
        {include file="common/nodata" /}
    </div>
    <!-- 上拉加载的 无数据我自己判断 -->
    
</div>

<div class="appeal_foot">
	<a href="{:url('/index/user/appeal_submit')}" class="appeal_btn">{:__('I want to appeal')}</a>
</div>

<script type="text/javascript">
	$(function(){
		$(document).on('click','.js_open_replay',function(){
			$(this).parents('.box').find('.re_play_op').addClass('hide');
			$(this).parents('.box').find('.re_play_input').removeClass('hide').find('input').focus();
		})
		$(document).on('click','.js_cancel_replay',function(){
			$(this).parents('.box').find('.re_play_op').removeClass('hide');
			$(this).parents('.box').find('.re_play_input').addClass('hide');
		})

		$(document).on('click','.js_sub_replay',function(){
			var _this=$(this);
			var _url=_this.attr('data-url');
			var _id=_this.attr('data-id');
			var _content=_this.parents('.box').find('input').val();
			if(_content==''){
				input_tips(_this.parents('.box').find('input'),"{:__('Please enter the reply')}",'3');
				return false;
			}
			layer.load(2);

			$.post(_url,{id:_id,content:_content},function(data){
				layer.closeAll();
				if(data.code==1){
					tip_show(data.msg, '1');
					setTimeout(function(){
                        location.reload()
                    },1000*parseInt(data.wait))
				}else{
					tip_show(data.msg, '2');
				}
			})

			return false
		})
	})
</script>