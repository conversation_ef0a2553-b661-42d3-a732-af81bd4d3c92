<?php
	foreach($list as $ls){
?>
<div class="box">
	<div class="item clearfix">
		<span class="pull-right f12 margin_l20 color_999"><?=date('Y-m-d',$ls['createtime'])?></span>
		<img src="__CDN__/assets/img/wap/appeal1.png" class="icon">
		<div class="ellipsis"><span class="color_red">[<?=$appeal_title[$ls['type']]?>]</span><?=$ls['content']?></div>
	</div>
	<?php
	if(!empty($reply_result[$ls['id']])){
		if($reply_result[$ls['id']]){
			foreach($reply_result[$ls['id']] as $val){
				if($val['type']){
	?>
			<div class="item color_red clearfix">
				<div class="clearfix">
					<img src="__CDN__/assets/img/wap/appeal2.png" class="icon">
					<div class="over_hide f12">
						{:__('Platform')}{:__('Reply')}:<?=$val['reply_content']?>
					</div>
				</div>
				<div class="f12 color_999 text-right"><?=date('Y-m-d',$val['createtime'])?></div>
			</div>
	<?php
				}else{
	?>
			<div class="item clearfix">
				<div class="clearfix">
					<img src="__CDN__/assets/img/wap/appeal2.png" class="icon">
					<div class="over_hide f12">
						<?=$val['reply_content']?>
					</div>
				</div>
				<div class="f12 color_999 text-right"><?=date('Y-m-d',$val['createtime'])?></div>
			</div>
	<?php
				}
			}
		}
		}
	?>
	
	<div class="re_play_input hide">
		<div class="item">
			<input type="text" name="content" placeholder="{:__('Please enter the reply')}">
		</div>
		<div class="item text-center">
			<a href="javascript:;" class="link_btn js_cancel_replay">{:__('Cancel')}</a>
			<a href="javascript:;" class="link_btn color_red margin_r10 js_sub_replay"  data-id="<?=$ls['id']?>" data-url="{:url('/index/user/appeal_submit_content')}">{:__('Confirm')}</a>
		</div>
	</div>
	<a href="javascript:;" class="js_open_replay re_play_op open_replay">{:__('Click reply')}</a>
</div>
<?php
	}
?>