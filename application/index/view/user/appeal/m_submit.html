<style type="text/css">
    .footer_new{ display: none }
   	.body{ padding-bottom: 0.5rem; }
   	.input_box{ background:none; overflow: visible; }
   	.moni_select{ border: 1px solid #fff; border-radius: 5px; }
</style>
<div class="bank_l pad_lr20  clearfix">

    <form action="{:url('index/user/appeal_submit')}" autocomplete="off" class="small_input edit_info_form" style="padding: 10px 0px 0 0px">
    	
    	<div class="clearfix ">
    		<div class="item_col_3 pad_r10">
    			<div class="input_box full_width">
		            <div class="input_tt">{:__('Types of complaints')}</div>
		            <div class="input_rr" style="overflow: visible; ">
		                <div class="moni_select" style="width: 100% ">
		                    <input type="text" name="type" value="" is_required="true" empty_tip="{:__('Please choose the type of complaint')}">
		                    <div class="moni_selected clearfix">
		                        <img class="arr" src="__CDN__/assets/img/arr.png">
		                        <span>{:__('Please choose the type of complaint')}</span>
		                    </div>
		                    <div class="moni_s_down">
							<?php
								foreach($site['appeal'] as $key=>$ls){
							?>
								<a href="javascript:;" value="<?=$key?>"><?=$ls?></a>
							<?php
								}
							?>
		                    </div>
		                </div>
		            </div>
		        </div>
    		</div>
    	</div>

    	<div class="clearfix pad_t10">
    		<div class="item_col_6 pad_r10">
    			<div class="input_box full_width">
		            <div class="input_rr">
		                <textarea style="height: 4rem" placeholder="{:__('Up to 50 words in the complaint')}" maxlength="50" name="content" is_required="true" empty_tip="{:__('Please enter the reply')}"></textarea>
		            </div>
		        </div>
    		</div>	
        </div>

        <div class="appeal_foot">
			<a href="javascript:;" class="appeal_btn js_form_sub" data-text="{:__('Submit')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Submit')}</a>
		</div>

    </form>
</div>
<script type="text/javascript">
	$('.js_ss_radio').click(function(){
		if(!$(this).hasClass('acitve')){
			$(this).addClass('active').siblings('.js_ss_radio').removeClass('active');
			$('.js_ss_type').val($(this).attr('data-id'))
		}
	})
</script>