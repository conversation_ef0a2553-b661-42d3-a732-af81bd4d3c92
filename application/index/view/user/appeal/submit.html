<div class="container">
	<div class="user_main margin_tb40 clearfix">
		<div class="user_main_line"></div>
		<div class="user_art_left pull-left">
			<div class="n_tt">
				<img src="__CDN__/assets/img/pc/icon_left_list_up.png" class="arr pull-right">
				<img src="__CDN__/assets/img/pc/icon_left_list_appeal.png" class="icon">
				{:__('Appeal list')}
			</div>
			<div class="n_tt_menu">
				<?php
				foreach($site['appeal'] as $key=>$ls){
			?>
				<a href="<?=url('user/appeal',array('type'=>$key))?>" class="<?php if($key=='1')echo 'active'?>"><?=$ls?></a>
			<?php
				}
			?>
			</div>
		</div>
		<div class="pad_b20 over_hide">
			<div class="user_art_title clearfix">
				{:__('I want to appeal')}
			</div>
			<div class="appeal_form_div pad_tb20 pad_lr30 ">
				<form action="">
					<div class="item clearfix pad_b10">
						<div class="pull-left pad_r10">{:__('Types of complaints')}:  <input type="text" class="js_ss_type" name="type" is_required="true" empty_tip="{:__('Please choose the type of complaint')}" style="width: 0; height: 0; opacity: 0;"></div>
						<div class="over_hide">
							<a href="javascript:;" class="moni_radio js_ss_radio margin_r30" data-id="1">
								<div class="radio_r"></div>
								{:__('Order-related issues')}
							</a>
							<a href="javascript:;" class="moni_radio js_ss_radio margin_r30" data-id="2">
								<div class="radio_r"></div>
								{:__('Membership-related issues')}
							</a>
							<a href="javascript:;" class="moni_radio js_ss_radio margin_r30" data-id="3">
								<div class="radio_r"></div>
								{:__('Issues related to data audit')}
							</a>
							<a href="javascript:;" class="moni_radio js_ss_radio" data-id="4">
								<div class="radio_r"></div>
								{:__('Other questions')}
							</a>
						</div>
					</div>
					<div class="item clearfix">
						<div class="pull-left pad_r10">{:__('Content of complaint')}: </div>
						<div class="over_hide">
							<textarea placeholder="{:__('Up to 50 words in the complaint')}" maxlength="50" name="content" is_required="true" empty_tip="{:__('Please enter the reply')}"></textarea>
							<a href="javascript:;" class="sub_btn js_form_sub"  data-text="{:__('Submit')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Submit')}</a>
						</div>
					</div>
				</form>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	$(function(){
		$('.js_ss_radio').click(function(){
			if(!$(this).hasClass('acitve')){
				$(this).addClass('active').siblings('.js_ss_radio').removeClass('active');
				$('.js_ss_type').val($(this).attr('data-id'))
			}
		})
	})
</script>