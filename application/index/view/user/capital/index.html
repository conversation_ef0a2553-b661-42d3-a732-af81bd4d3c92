<div class="container">
	<div class="user_main margin_tb40">
		<div class="pad_lr40 capital_div pad_tb40">
			<div class="clearfix">
				<div class="capital_left pad_lr20 pull-left margin_r20">
					<div class="title clearfix">
						<!-- 闭眼 active  -->
						<a href="javascript:;" class="hide_capital active js_hide_capital pull-right" data-url="{:url('/index/user/hide_capital')}">
							<img src="__CDN__/assets/img/pc/eye1.png" class="img1">
							<img src="__CDN__/assets/img/pc/eye2.png" class="img2">
						</a>
						
						{:__('Total funds')} <a href="{:url('index/login/capital')}" class="ques js_ajax_win" data-width="1000"  data-height="600"><img src="__CDN__/assets/img/pc/ques_icon.png"></a>
					</div>
					<div class="item">
	        			<div class="tts">{:__('Amoy money')}(元)</div>
	        			<div class="pp color_red js_hide_money" data-money="<?=$user['money']?>">****</div>
	        		</div>
	        		<div class="item">
	        			<div class="tts">{:__('Shopping gold')}</div>
	        			<div class="pp js_hide_money" data-money="<?=$user['buy_gold']?>">****</div>
	        		</div>

	        		<div class="text-center pad_t20 op_btns clearfix">
	        			<a href="{:url('/index/daifu/charge')}" >{:__('Recharge')}</a>
	        			<a href="{:url('/index/user/share')}" class="active js_ajax_win" data-width="800" data-height="600">{:__('Popularize')}</a>
	        			<!--<a href="{:url('/index/user/rate_fun')}" class="active js_ajax_win" data-width="800">{:__('Rate')}</a>-->
						
					</div>
				</div>
				<div class="over_hide capital_right pad_lr20">
					<div class="title clearfix">
						资金趋势 <a href="{:url('index/login/capital')}" class="ques js_ajax_win" data-width="1000"  data-height="600"><img src="__CDN__/assets/img/pc/ques_icon.png"></a>
					</div>
					<div class="clearfix chart_tabs pad_b20">
						<div class="clearfix btn_wrap js_btn_wrap">
							<a href="javascript:;" data-day="7">最近7天</a>
							<a href="javascript:;" data-day="14">最近14天</a>
							<a href="javascript:;" data-day="30">最近30天</a>
						</div>
					</div>
					<div class="chart_d">
						<div class="chart_div">
							<div class="chart_div_d" id="chart_div_d">
								
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="margin_t20 capital_list pad_lr20 pad_tb20">
				<div class="capital_nav clearfix pad_b30">
					<div class="pull-left tt">{:__('Detailed query')}</div>
					<a href="<?=url('index/user/capital',array('type'=>'1'))?>" class="<?php if($nav_url == 'user/capital')echo 'active';?>">{:__('Ambush coins')}</a>
					<a href="<?=url('index/user/capital_gold',array('type'=>'2'))?>" class="<?php if($nav_url == 'user/capital_gold')echo 'active';?>">{:__('Shopping Gold Details')}</a>
<!--					<a href="<?=url('index/user/capital_back',array('type'=>'3'))?>" class="<?php if($nav_url == 'user/capital_back')echo 'active';?>">{:__('Details of rebate')}</a>-->
				</div>

				<table class="common_table table margin_t10 text-center">
				<tbody>
					<tr>
						<th>{:__('Type')}</th>
						<th>
							<?=__($nav_url)?>
							
						</th>
						<th>
							{:__('Surplus Amoy money')}
						</th>
						<th>{:__('Operation')}</th>
						<th>{:__('Operation time')}</th>
						<th>{:__('Share')}</th>
					</tr>
					<?php
						foreach($list as $ls){
					?>
						<tr>
							<td><?=$nav_array[$ls['type']]['name']?></td>
							<?php
								if($ls['type'] == "8" || $ls['type'] == "10" || $ls['type'] == "11" || $ls['type'] == "12" || $ls['type'] == "13" || $ls['type'] == "14"){
							?>
								<td class="color_red">+<?=$ls['price']?></td>
							<?php
								}else{
							?>
								<td class="color_green">-<?=$ls['price']?></td>
							<?php
								}
							?>
							
							<td><?=$ls['surplus_price']?></td>
							<td>
								<?php
									if($ls['type'] < '8'){
								?>
									<a href="<?=url('index/user/order_detail').'?id='.$ls['order_id']?>" class="js_ajax_win color_red" data-width="1200">{:__('Click for details')}</a>
								<?php
									}else{
										echo '-';
									}
								?>
							</td>
							<td class="color_999"><?=date('Y-m-d H:i',$ls['createtime'])?></td>
							<td><a href="" class="color_red share_btn">{:__('Share')}</a></td>
						</tr>
					<?php
						}
					?>
				</tbody>
			</table>
			<?php
				if($list){
			?>
				{include file="common/pages" /}
			<?php
				}else{
			?>
				{include file="common/nodata" /}
			<?php
				}
			?>
			</div>
		</div>
	</div>
</div>
<script src="__CDN__/assets/js/pc/echarts.min.js"></script>
<script type="text/javascript">
	$('.js_hide_capital').click(function(){
		if($(this).hasClass('active')){
			$(this).removeClass('active')
		}else{
			$(this).addClass('active')
		}

		var _h=$('.js_hide_money');
		for(var i=0; i<_h.length;i++){
			var _m=_h.eq(i).attr('data-money');
			_h.eq(i).attr('data-money',_h.eq(i).html()).html(_m)
		}
	})

	var _type=<?=$type?$type:'1'?>;  // 1 f币  2购物金明细  3 返利明细
	var _echart_url="<?=url('index/user/capitalReport')?>"
</script>
<script src="__CDN__/assets/js/pc/echarts_ajax.js"></script>