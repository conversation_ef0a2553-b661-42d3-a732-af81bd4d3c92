<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .chart_div{ background: #fff; border-radius: 5px;}
    .chart_div_d{ height: 4.5rem }
    .btn_wrap a{ color: #999; font-size: 14px; margin-left: 10px; line-height: 22px; }
    .btn_wrap a span{ width:10px; height: 10px; display: inline-block; border-radius: 50%; background: #DCDFE6; margin-right: 5px;  }
	.btn_wrap a.active{ color:#F0446C; }
    .btn_wrap a.active span{ background: #F0446C; }
}
</style>
<div class="pad_t20 pad_lr20">
	<!-- <div class="help_title bg_fff clearfix"> -->
	    <!-- <img src="__CDN__/assets/img/wap/pay1.png" class="icon s">{:__('Alibaba pays')} -->
	<!-- </div> -->

	<div class="chart_div margin_t20 pad_t20">
		<div class="clearfix btn_wrap js_btn_wrap">
			<a href="javascript:;" data-day="7"><span></span>最近7天</a>
			<a href="javascript:;" data-day="14"><span></span>最近14天</a>
			<a href="javascript:;" data-day="30"><span></span>最近30天</a>
		</div>
		<div class="chart_div_d" id="chart_div_d">
			
		</div>
	</div>	
</div>

<script src="__CDN__/assets/js/pc/echarts.min.js"></script>
<script type="text/javascript">
	$('.js_hide_capital').click(function(){
		var _this=$(this);
		var _url=_this.attr('data-url');
		$.post(_url,{},function(data){
			if(data.code==1){
				tip_show(data.msg,'1');
				if(_this.hasClass('active')){
					_this.removeClass('active')
				}else{
					_this.addClass('active')
				}
			}else{
				tip_show(data.msg,'2')
			}
		})
	})

	var _type=1;  // 1 f币  2购物金明细  3 返利明细
	var _echart_url="/haigou/public/index/user/userReport.html"
</script>
<script src="__CDN__/assets/js/pc/echarts_ajax_wap.js"></script>