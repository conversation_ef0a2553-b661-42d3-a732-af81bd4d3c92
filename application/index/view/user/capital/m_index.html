<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .capital_bg{ background: url(__CDN__/assets/img/wap/icon_money_bg01.png); background-size: 100% 100%;  }
    .moni_select{ background:none; min-width: 10px; border: none }
    .order_list .box .rate .r_item{ width: 50% }
</style>

<div class="capital_bg margin_t30">
	<div class="title clearfix">
		<a href="{:url('/index/user/capitalChart')}" class="pull-right">{:__('Fund trend')} <img src="__CDN__/assets/img/wap/arr2.png"></a>
		{:__('Amoy balance')}
	</div>
	<div class="money">
		<?=$user['money']?><span>元</span>
	</div>
	<div class="text-center clearfix op_btns">
		<a href="{:url('/index/daifu/charge/set_type/7')}"><img src="__CDN__/assets/img/wap/icon_money_add.png">儲值</a>
		<a href="{:url('/index/user/rate_fun')}" class="active js_ajax_win" data-width="800">{:__('Rate')}</a>
	</div>
</div>
<div class="pad_lr20 capital_title clearfix">
	<div class="moni_select pull-right">
        <div class="moni_selected clearfix">
            <img class="arr" src="__CDN__/assets/img/arr.png">
            <span>{:__('All types')}</span>
        </div>
        <div class="moni_s_down">
			<?php
				foreach($site['order_type'] as $key=>$ls){
			?>
				<a href="<?=url('').'?type='.$key?>" class="<?php if($type == $key) echo 'active';?>"><?=$ls;?>
			<?php
				}
			?>
        </div>
    </div>
    {:__('Ambush coins')}
</div>

<div class="pad_lr20">
	<div class="order_list js_scroll_more_list" data-url="{:url('index/user/capital_lists')}">
	    {include file="user/capital/m_lists" /}
	</div>

	<!-- 上拉加载的 无数据我自己判断 -->
	<div class="loadding_d hide">
	    <img src='__CDN__/assets/img/loading.png'>
	    <span>{:__('Pull-up Load More')}</span>
	</div>
	<div class="js_no_data pad_tb30 hide">
	    {include file="common/nodata" /}
	</div>
	<!-- 上拉加载的 无数据我自己判断 -->
</div>