<?php
	if(!empty($list)){
	foreach($list as $ls){
?>
<a href="" class="box bg_fff">
    <div class="title clearfix pad_lr20">
    	<!-- add.png 系统增加  share.png  推广赠送 -->
        <img src="<?=$nav_array[$ls['type']]['image']?>"><?=$nav_array[$ls['type']]['name']?>
    </div>
    <div class="rate clearfix">
	<?php
		if($ls['type'] == "8" || $ls['type'] == "10" || $ls['type'] == "11" || $ls['type'] == "12" || $ls['type'] == "13" || $ls['type'] == "14"){
	?>
		<div class="r_item">
            <div class="pp">{:__('Increase Amoy money')}</div>
            <div class="tt"><span class="color_red">+<?=$ls['price']?></span></div>
        </div>
	<?php
		}else{
	?>
		<div class="r_item">
            <div class="pp">{:__('Reduce Amoy money')}</div>
            <div class="tt"><span class="color_green">-<?=$ls['price']?></span></div>
        </div>
	<?php
		}
	?>
        <div class="r_item">
            <div class="pp">{:__('Surplus Amoy money')}</div>
            <div class="tt"><?=$ls['surplus_price']?></div>
        </div>
    </div>
    <div class="items">
        <div class="item"><span>{:__('Operation')}：</span><span class="color_red">
	<?php
		if($ls['type'] < '8'){
	?>
		{:__('Click for details')}
	<?php
		}else{
			echo '-';
		}
	?>
		</span></div>
        <div class="item"><span>{:__('Operation time')}：</span><?=date('Y-m-d  H:i',$ls['createtime'])?></div>
    </div>
</a>
<?php
		}
	}
?>