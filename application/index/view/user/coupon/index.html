
<div class="container">
	<div class="user_main margin_tb40">
		<div class="pad_lr20 pad_tb10">
			<div class="clearfix select_filter_div">
				<a href="<?=url('index/user/coupon')?>" class="<?=$_GET?'':'active'?>"><div class="filter_item color_red">我的優惠券</div></a>
				<div class="moni_select filter_item no_border">
					<div class="moni_selected clearfix"> 
						<img class="arr" src="__CDN__/assets/img/arr.png">
						<span>{:__('Order status')}</span>
					</div>
					<div class="moni_s_down">
						<!-- 选中给 active -->
							<a href="<?=url('index/user/coupon')?>" class="<?php if(!$order_status) echo 'active'?>">全部</a>
							<a href="<?=url('index/user/coupon').'?order_status=0'?>" class="<?php if($order_status == 0) echo 'active'?>">未使用</a>
							<a href="<?=url('index/user/coupon').'?order_status=1'?>" class="<?php if($order_status == 1) echo 'active'?>">已使用</a>
					</div>
				</div>
			</div>
            <?php
                if($coupon){
            ?>
            <div class="yhq_list flex flex_w">
                <!-- disabled -->
                 <?php
                    foreach($coupon as $key=>$value){
                ?>
                    <div class="item">
                        <div class="t flex flex_d">
                            <div class="tt">
                                <span>NT$</span>
                                <b><?=$value['reduce_amount']?></b>
                            </div>
                            <div class="pp">滿<?=$value['reaching_amount']?>可用</div>
                        </div>
                        <div class="time">有效致-<?=date("Y-m-d H:i:s",$value['end_time'])?></div>
                        <!-- used -->
                        <?php if (time() > $value['end_time'] - 3*24*60*60 && time() < $value['end_time']) { ?>
                            <img src="__CDN__/assets/img/pc/new_index/used.png" alt="" class="icon_img">
                            <div class="jjgq">即將過期</div> 
                        <?php } ?>
                        <?php if (time() > $value['end_time']) { ?>
                            <img src="__CDN__/assets/img/pc/new_index/used.png" alt="" class="icon_img">
                            <div class="jjgq">過期</div> 
                        <?php } ?>     

                    </div>
                <?php
                    } 
                ?>
            </div>
            <?php
                }else{
            ?>
                {include file="common/nodata" /}
            <?php
                }
            ?>
		</div>
	</div>
</div>