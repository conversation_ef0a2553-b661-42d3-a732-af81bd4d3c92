<style type="text/css">
    .moni_select{ background:none; min-width: 100px; text-align: right; border: none }
    .moni_select .moni_s_down{text-align: center;}
    .html_content{ font-size: 0.24rem }
    .footer_new{display: none;}
    .coupon_div{width: 100%;height: 4rem; background: url(__CDN__/assets/img/wap/new_index/coupon_bg.png) center center; background-position: -0.3rem -0.28rem; background-size: 7.5rem 4.34rem; position: relative;justify-content: center;align-items: center;}
    .coupon_bg{width: 100%;height: 100%;position: absolute;top: 0;left:-0.16rem ;}
    .coupon_div .tetx1{position: absolute;top: 0.6rem;left: 0.6rem;font-size: 0.32rem;color: #fff;}
    .coupon_div .tetx2{position: relative;color: #fff;align-items: flex-end;}
    .coupon_div .tetx2 .tetx3{font-size: 0.52rem;font-weight: bold;margin-right: 0.10rem;line-height: 1;}
    .coupon_div .tetx2 .tetx4{font-size: 0.24rem;}
    
    .coupon_details .block{padding-left: 0.18rem;padding-right: 0.24rem;}
    
</style>
<div class="helps_div order_detail">
    
    <div class="coupon_div flex">
        <div class="tetx1">我的優惠券</div>
        <div class="tetx2 flex">
            <div class="tetx3"><?=$coupon_num?></div>
            <div class="text4">張</div>
        </div>
    </div>
    <div class="coupon_details">
        
        <div class="pad_lr20 capital_title clearfix">
            <div class="moni_select pull-right">
                <div class="moni_selected clearfix">
                    <img class="arr" src="__CDN__/assets/img/arr.png">
                    <span>全部</span>
                </div>
                <div class="moni_s_down">
                    <a href="<?=url('index/user/coupon')?>" class="<?php if(!$order_status) echo 'active'?>">全部</a>
                    <a href="<?=url('index/user/coupon').'?order_status=0'?>" class="<?php if($order_status == 0) echo 'active'?>">未使用</a>
                    <a href="<?=url('index/user/coupon').'?order_status=1'?>" class="<?php if($order_status == 1) echo 'active'?>">已使用</a>
                </div>
            </div>
            優惠券明細
        </div>
        <?php
            if($coupon){
        ?>
        <div class="block">
            <div class="yhq_list">
                <!-- disabled -->
                <?php
                    foreach($coupon as $key=>$value){
                ?>
                <div class="item flex_ac">
                    <div class="t flex flex_d">
                        <div class="tt">
                            <span>NT$</span>
                            <b><?=$value['reduce_amount']?></b>
                        </div>
                        <div class="pp">滿<?=$value['reaching_amount']?>可用</div>
                    </div>
                    <div class="time">有效致-<?=date('Y-m-d',$value['end_time'])?></div>
                    <!-- used -->
                    <?php if (time() > $value['end_time'] - 3*24*60*60 && time() < $value['end_time']) { ?>
                        <img src="__CDN__/assets/img/wap/new_index/time_out.png" alt="" class="icon_img">
                        <div class="jjgq">即將過期</div>
                    <?php } ?>
                    
                </div>
                <?php
                    } 
                ?>
            </div>
        </div>

        <?php
                }else{
            ?>
                {include file="common/nodata" /}
            <?php
                }
            ?>
    </div>
</div>