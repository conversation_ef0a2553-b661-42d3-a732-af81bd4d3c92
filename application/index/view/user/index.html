<div class="container user_center_main">
    <div class="user_main margin_tb20 clearfix">
		<div class="border_box pad_lr20 u_head_notice clearfix">
			<a href="{:url('/index/user/notice')}" class="pull-right more">{:__('View more')}</a>
			<div class="over_hide">
			<?php
				foreach($tongzhi as $ls){
			?>
				<a href="<?=url('/index/user/notice_detail')?>?id=<?=$ls['id']?>" class="item ellipsis pad_r30"><span class="icon">{:__('Notice')}</span><?=$ls['title']?></a>
			<?php
				}
			?>
			</div>
		</div>
		<div class="clearfix margin_t20">
			<div class="pull-right user_main_right">
				
				<div class="user_notice  clearfix border_box pad_lr20 pad_tb20">
					<div class="title clearfix">
						<a href="{:url('/index/user/notice')}">{:__('View more')} ></a>
						{:__('Platform Bulletin')}
					</div>
					<div class="u_notice pad_t10">
						<?php
							foreach($gonggao as $ls){
						?>
							<a href="<?=url('/index/user/notice_detail')?>?id=<?=$ls['id']?>&type=1" class="clearfix">
								<span class="pull-right color_999 margin_l10"><?=date('m/d',$ls['createtime'])?></span>
								<div class="ellipsis"><?=$ls['title']?></div>
							</a>
						<?php
							}
						?>
					</div>
				</div>


				<div class="user_notice margin_t20 clearfix border_box pad_lr20 pad_tb20">
					<div class="title clearfix">
						{:__('Common functions')}
					</div>
					<div class="u_gongneng clearfix">
						<a href="{:url('user/appeal')}" class="clearfix">
								{:__('Appeal list')}
						</a>
						<a href="{:url('user/order')}" class="clearfix">
								{:__('My order')}
						</a>
						<a href="{:url('user/password')}" class="clearfix active js_ajax_win" data-width="600">
								{:__('Modify login password')}
						</a>
						<a href="{:url('user/editMobile')}" class="clearfix active js_ajax_win" data-width="600">
								{:__('Modify mobile')}
						</a>
						<!--<a href="" class="add" class="clearfix">
							+{:__('Add')}
						</a>-->
					</div>
				</div>

			</div>
			<div class="user_main_left_new pull-left" style="width: 316px;">
				<div class="user_info clearfix border_box pad_lr30 pad_tb30 margin_b20">
					<div class="clearfix">
						<div class="avatar_div pull-left js_ajax_win" data-width="930" data-height="650" href="{:url('/index/user/edit_avatar')}">
							<img src="<?=$user['avatar']?>" class="avatar">
							<div class="vip">
								<img src="__CDN__/assets/img/pc/vip.png">
								<span><?=$user['level']?></span>
							</div>
						</div>
						<div class="over_hide">
							<div class="nickname ellipsis"><?=substr_replace($user['username'],'*',0,3)?></div>
							<div class="pro clearfix">
								<div class="pull-right margin_l10"><?=$bill?>%</div>
								<div class="over_hide">
									<div class="pro_bar"><div style="width: <?=$bill?>%"></div></div>
								</div>
							</div>
						</div>
					</div>
					<div class="items pad_t10">
						<div class="item clearfix">
							<div class="pull-left tt">當前經驗值:</div>
							<div class="over_hide">
								<?=$user['score']?>
							</div>
						</div>
						<div class="item clearfix">
							<div class="pull-left tt">{:__('Login account')}:</div>
							<div class="over_hide">
								<?=substr_replace($user['mobile'],'****',2,4)?>
							</div>
						</div>
						<div class="item auth clearfix">
							<div class="pull-left tt">{:__('Qualification Authentication')}:</div>
							<div class="over_hide">
								<!-- 已认证  active -->
								<a href="{:url('/index/user/editMobile')}" class="active js_ajax_win" data-width="600"><img src="__CDN__/assets/img/pc/auth1.png" class="img1"><img src="__CDN__/assets/img/pc/auth1a.png" class="img2"></a>
								<a href="{:url('/index/user/editIdcard/type/1')}" class="<?=$user['is_card']?'active':'';?> js_ajax_win" data-width="600" data-height="450"><img src="__CDN__/assets/img/pc/auth2.png" class="img1"><img src="__CDN__/assets/img/pc/auth2a.png" class="img2"></a>
								<a href="{:url('/index/user/editIdcardTwo/type/1')}" class="<?=$user['is_card_img']?'active':'';?> js_ajax_win" data-width="600"><img src="__CDN__/assets/img/pc/auth3.png" class="img1"><img src="__CDN__/assets/img/pc/auth3a.png" class="img2"></a>
								<a href="{:url('/index/Line/line_binding')}"  data-width="600"><img src="__CDN__/assets/img/pc/line.png" class="img1"></a>
							</div>
						</div>
						<div class="item clearfix">
							<div class="pull-left tt">{:__('Recent login')}:</div>
							<div class="over_hide">
								<?=date('Y-m-d H:i:s',$user['logintime'])?>
							</div>
						</div>
					</div>
					
				</div>
				
				<div class="clearfix user_money margin_b20">
					<div class="border_box money_tt pad_lr20 margin_r20 pad_tb20 pull-left">
						<div class="tt clearfix pad_b20">
							<a href="{:url('/index/user/capital/')}" class="pull-right">{:__('Fund management')}</a>
							{:__('My balance')}
						</div>
						<div class="item">
							<div class="tts">{:__('Amoy money')}(元)退款專用</div>
							<div class="pp color_red"><?=$user['money']?></div>
						</div>
						<div class="item">
							<div class="tts">{:__('Shopping gold')}</div>
							<div class="pp"><?=$user['buy_gold']?></div>
						</div>
					
						<div class="text-center pad_t20 op_btns clearfix">
							<a href="{:url('/index/daifu/charge/set_type/7')}" >儲值</a>
							<a href="{:url('/index/user/share')}" class="active js_ajax_win" data-width="800" data-height="600">{:__('Popularize')}</a>
							<a href="{:url('/index/user/rate_fun')}" class="active js_ajax_win" data-width="800" data-height="550">{:__('Rate')}</a>
						</div>
						
						<div class="pad_t20">
							<a href="{:url('/index/account/index')}" class="mana_btn">{:__('Account management')}</a>
						</div>
					</div>
				</div>

				<div class="clearfix user_money">
					<div class="border_box money_tt pad_lr20 margin_r20 pad_tb20 pull-left">
						<div class="tt clearfix pad_b20">
							<a href="{:url('/index/user/coupon/')}" class="pull-right">優惠券管理</a>
							我的優惠券
						</div>
						<div class="item">
							<div class="pp color_red"><?=$coupon?><span style="font-size: 12px; color: #333;">张</span></div>
						</div>
					</div>
				</div>

			</div>
			<div class="over_hide user_main_left">
				<!-- <div class="clearfix user_money ">
					
					<div class="border_box over_hide money_chart">
						<div class="chart_d">
							<div class="chart_tt">
								<a href="javascript:;" class="tab_btn active">最近交易</a>
								<a href="javascript:;" class="tab_btn js_tab_btn" data-type="1" data-d="7">最近7日</a>
								<a href="javascript:;" class="tab_btn js_tab_btn" data-type="2" data-d="14">最近14日</a>
								<a href="javascript:;" class="tab_btn js_tab_btn" data-type="3" data-d="30">最近30日</a>
								<a href="{:url('/index/user/order')}" class="pull-right o_detail">訂單明細</a>
							</div>
							<div class="items">
								<div class="item">
									<div class="pp">成交金額（元）</div>
									<div class="tt js_chart_nums"><?=$user['score']?></div>
								</div>
								<div class="item">
									<div class="pp">成交單數（筆）</div>
									<div class="tt js_chart_nums">0.00</div>
								</div>
								<div class="item">
									<div class="pp">退款金額（元）</div>
									<div class="tt js_chart_nums">0.00</div>
								</div>
							</div>
							<div class="chart_div">
								<div class="chart_div_d" id="chart_div_d">
									
								</div>
							</div>
						</div>
					</div>
				</div> -->

				<!-- <div class="clearfix u_banner margin_t20">
					<div class="home_banner">
						<ul class="imagelist js_home_top clearfix" data-run="0" >
							<?php
								if($banner){
									foreach($banner as $ls){
							?>
								<li class="banner_li">
									<a target="_blank"  href="javascript:;" class="block" style="background-image:url(<?=$ls['path_image']?>);">
									</a>
								</li>
							<?php
									}
								}
							?>
						</ul>
						<div class="container point-div">
							<ul class="point clearfix js_point">
							<?php
								if($banner){
									foreach($banner as $ls){
							?>
								<li class=""></li>
							<?php
									}
								}
							?>
							</ul>
						</div>
					</div>
				</div> -->


				<!--<div class="border_box user_nav pad_lr20 margin_t20 pad_tb20">
					<div class="title">{:__('Popular application')}</div>
					<div class="clearfix pad_t30">
						<a href="{:url('/index/daifu/index')}" class="box">
							<img src="__CDN__/assets/img/pc/icon_function_01alibaba.png">
							<div class="tt">{:__('Alibaba pays')}</div>
						</a>
						<a href="{:url('/index/daifu/taobao')}" class="box">
							<img src="__CDN__/assets/img/pc/icon_function_02taobao.png">
							<div class="tt">{:__('Taobao Payment')}</div>
						</a>
						<a href="{:url('/index/daifu/alipay')}" class="box">
							<img src="__CDN__/assets/img/pc/icon_function_03zhifubao.png">
							<div class="tt">{:__('Alipay Reserve Value')}</div>
						</a>
						<a href="{:url('/index/daifu/wechatpay')}" class="box">
							<img src="__CDN__/assets/img/pc/icon_function_04weichat.png">
							<div class="tt">{:__('WeChat storage value')}</div>
						</a>
						<a href="{:url('/index/daifu/game')}" class="box">
							<img src="__CDN__/assets/img/pc/icon_function_05game.png">
							<div class="tt">{:__('Game/Video Storage')}</div>
						</a>
						<a href="{:url('/index/daifu/other')}" class="box">
							<img src="__CDN__/assets/img/pc/icon_function_06other.png">
							<div class="tt">{:__('Other payment')}</div>
						</a>
						<a href="{:url('/index/daifu/usdt')}" class="box">
							<img src="__CDN__/assets/img/pc/icon_function_07usdt.png">
							<div class="tt">USDT</div>
						</a>
						<a href="{:url('/index/user/invoice_my')}" class="box">
							<img src="__CDN__/assets/img/pc/icon_gongneng_fapiao.png">
							<div class="tt">會員福利</div>
						</a>
						<a href="{:url('/index/user/prize')}" class="box">
							<img src="__CDN__/assets/img/pc/cj/icon_list_hot_09cjjl_pc.png">
							<div class="tt">中獎清單</div>
						</a>
					</div>
				</div> -->
				
				
				
				
				
				
				<style>
					.business{
						display: flex;
						flex-direction: row;
						justify-content: space-around;
						
						margin: 0px auto 0 auto;
					}
					.business-item{
						display: flex;
						flex-direction: row;
						align-items: center;
						cursor: pointer;
					}
					.business-item img{
						margin-right: 5px;
					}
					.business-item .hideimg{
						display: block;
					}
					.business-item .showimg{
						display: none;
					}
					.business-item-active .hideimg{
						display: none;
					}
					.business-item-active .showimg{
						display: block;
					}
					.business-item-active  span{
						color:#EF436D;
					}
					.business-list{
						margin:20px auto;
						margin-bottom: 0;
					}
					.business-list .business-list-item{
						width: 16.66%;
						display: flex;
						float: left;
						padding: 20px 0;
						flex-direction: column;
						justify-content: center;
						align-items: center;
						color:#333333;
					}
					.business-list .business-list-item:hover{
						color: #EF436D;
					}
					.business-list .business-list-item img{
						width: 60px;
						height: 62px;
						margin-bottom: 10px;
					}
				</style>
				<div class="border_box  pad_lr20  pad_tb20">
					<!-- <div class="title">{:__('Popular application')}</div> -->
					<div class="business">
						<div class="business-item business-item-active" type="1">
							<img src="__CDN__/assets/img/pc/images/icon_business01.png" class="hideimg" />
							<img src="__CDN__/assets/img/pc/images/icon_business01_red.png" class="showimg" />
							<span>商品代購專區</span>
						</div>
						<div class="business-item" type="2">
							<img src="__CDN__/assets/img/pc/images/icon_business02.png" class="hideimg"/>
							<img src="__CDN__/assets/img/pc/images/icon_business02_red.png" class="showimg" />
							<span>直播點數專區</span>
						</div>
						<div class="business-item" type="3">
							<img src="__CDN__/assets/img/pc/images/icon_business03.png" class="hideimg"/>
							<img src="__CDN__/assets/img/pc/images/icon_business03_red.png" class="showimg" />
							<span>遊戲點數專區</span>
						</div>
						<div class="business-item" type="4">
							<img src="__CDN__/assets/img/pc/images/icon_business04.png"  class="hideimg"/>
							<img src="__CDN__/assets/img/pc/images/icon_business04_red.png" class="showimg" />
							<span>其他代購專區</span>
						</div>
					</div>
					<div class="business-list clearfix ">
						<!-- <div class="business-list-item">
							<img src="__CDN__/assets/img/pc/images/img_busi_02zb_01dy.png" />
							<span>抖音代購</span>
						</div>
						<div class="business-list-item">
							<img src="__CDN__/assets/img/pc/images/img_busi_02zb_02hy.png" />
							<span>虎牙直播</span>
						</div>
						<div class="business-list-item">
							<img src="__CDN__/assets/img/pc/images/img_busi_02zb_03bx.png" />
							<span>比心直播</span>
						</div>
						<div class="business-list-item">
							<img src="__CDN__/assets/img/pc/images/img_busi_02zb_04yk.png" />
							<span>映客直播</span>
						</div>
						<div class="business-list-item">
							<img src="__CDN__/assets/img/pc/images/img_busi_02zb_05yy.png" />
							<span>YY直播</span>
						</div>
						<div class="business-list-item">
							<img src="__CDN__/assets/img/pc/images/img_busi_02zb_06yf.png" />
							<span>音符直播</span>
						</div>
						<div class="business-list-item">
							<img src="__CDN__/assets/img/pc/images/img_busi_02zb_07ks.png" />
							<span>一直播</span>
						</div>
						<div class="business-list-item">
							<img src="__CDN__/assets/img/pc/images/img_busi_02zb_08more.png" />
							<span>更多</span>
						</div>
						<div class="business-list-item">
							<img src="__CDN__/assets/img/pc/images/img_busi_02zb_08more.png" />
							<span>更多</span>
						</div> -->
					</div>
				</div>
				
				<style>
					.active_list .item{display: block; position: relative; margin-bottom: 15px; width: 239px; margin-right: 15px;}
					.active_list .item:nth-child(4n){margin-right: 0;}
					.active_list .item .cover{width: 100%; height: 100%; float: left; object-fit: cover;}
					.active_list .item .tt{padding: 6px 15px; background: rgba(0,0,0,0.5); position: absolute; left: 0; bottom: 0; width: 100%; z-index: 2; color: #fff; font-size: 14px;}
					.active_list .item .time{padding: 4px 10px;background: rgb(247 144 55); border-radius: 0 0 0 5px; position: absolute; font-size: 12px; right: 0; top: 0; z-index: 2; color: #fff;}
					.active_list .item2{height: auto;}
					.active_list .item2 .cover_d{height: 132px;position: relative;}
					.active_list .item2 .cover{height: 132px; float: none; border-radius: 3px;}
					.active_list .item .tt2{color: #333; margin: 10px 0;}
					.active_list .item2 .play{width: 34px; height: 24px; position: absolute; left: 50%; top: 50%; margin-left: -17px; margin-top: -12px;}
					.video_play_div{width: 100%; height: 100%; background: rgba(0,0,0,0.7); position: fixed; left: 0; top: 0; z-index: 300;}
					.video_play_div iframe{width: 900px; height: 500px; position: absolute; left: 50%; margin-left: -450px; top: 50%; margin-top: -250px;}
					.video_play_div .close_win_btns{display: block;width: 40px;height: 80px;text-align: center;line-height: 20px;position: absolute;left: 50%; margin-left: -20px; top:  50%; margin-top: -330px; z-index: 20;}

				</style>
				<div class="border_box  pad_lr20  pad_tb20 margin_t20">
					<img src="__CDN__/assets/img/cache/members_banner03.png" style="width: 100%;" alt="">
				</div>
				<!--<div class="border_box  pad_lr20  pad_tb20 margin_t20">
					<div class="active_list flex flex_w">
						<a href="" class="item">
							<img src="__CDN__/assets/img/pc/index_banner.jpg" class="cover" alt="" />
							<div class="tt">活动名称活动名称活动名称活动名称</div>
							<div class="time">32天后结束</div>
						</a>
						<a href="" class="item">
							<img src="__CDN__/assets/img/pc/index_banner.jpg" class="cover" alt="" />
							<div class="tt">活动名称活动名称活动名称活动名称</div>
							<div class="time">32天后结束</div>
						</a>
						<a href="" class="item">
							<img src="__CDN__/assets/img/pc/index_banner.jpg" class="cover" alt="" />
							<div class="tt">活动名称活动名称活动名称活动名称</div>
							<div class="time">32天后结束</div>
						</a>
						<a href="" class="item">
							<img src="__CDN__/assets/img/pc/index_banner.jpg" class="cover" alt="" />
							<div class="tt">活动名称活动名称活动名称活动名称</div>
							<div class="time">32天后结束</div>
						</a>
					</div>
				</div>	-->
				
				
				<div class="border_box  pad_lr20  pad_tb20 margin_t20">
					<div style="font-size: 16px; font-weight: bold; color: #333; margin-bottom:15px">YouTube教學視频</div>
					<div class="active_list flex flex_w">
						<?php
							foreach($video as $ls){
						?>
							<a href="javascript:;" class="item item2 js_play_video" >
								<div class="cover_d">
									<iframe width="100%" height="100%" src="<?=$ls['video_url']?>" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
								</div>
								<div class="tt2 ellipsis-2"><?=$ls['title']?></div>
								<!-- <div class="time">32天后结束</div> -->
							</a>
						<?php	
							}
						?>
						

						<!-- <script>
							$('.js_play_video').click(function(){
								let url=$(this).attr('data-url');
								let html=`
								<div class="video_play_div">
									<a href="javascript:;" class="close_win_btns"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
									<iframe frameborder="0" src="`+url+`" width="100%" height="100%" class="note-video-clip"></iframe>
								</div>
								`;
								$('body').append(html)
							})
							$(document).on('click','.close_win_btns',function(){
								$('.video_play_div').remove()
							})
							
						</script> -->
					</div>
				</div>
				
				
				
				
				
				
			</div>   
			
		
		</div>
	</div>
</div>



<!-- 新增弹窗 -->
<?php
	if($product){
?>
	<style type="text/css">
        .my_new_win{ display: block; width: 350px; height: auto; position: fixed; right: 40px; top: 40px; background: #fff; z-index: 200; border-radius: 5px; border-left: 3px solid #ff3267; padding: 20px; color: #333; transition: 0.3s; -webkit-transition: 0.3s; }
        .my_new_win.remove{ transform: scale(0); -webkit-transform: scale(0); opacity: 0 }
        .my_new_win .win_close{ display: block; width: 30px; height: 30px; text-align: center; line-height: 30px; position: absolute; right: 5px; top: 5px; z-index: 10 }
        .my_new_win .win_close img{ width: 14px; }
        .my_new_win .title{ font-size: 16px; font-weight: bold; padding-bottom: 10px;  }
        .my_new_win .item{ font-size: 14px; line-height: 22px;}
        .my_new_win .view_btn{ padding-top: 5px; color: #ff3267 }
        .my_new_win .view_btn:hover{ text-decoration: underline; }
    </style>
    <div class="my_new_win">
        <a href="javascript:;" class="win_close js_win_close"><img src="__CDN__/assets/img/pc/close2.png"></a>
        <div class="title">小唄已幫您錄入了代付商品清單</div>
			<div class="item">
				<?=$product['title']?>
			</div>
			<div class="item">金額：<b class="color_red"><?=$product['price']?></b>RMB</div>
			<div class="item">姓名：<?=$product['other_name']?></div>
			<div class="item">帳號：<?=$product['other_account']?></div>
        <div class="js_btns">
			<a href="<?=url('index/daifu/index',array('type'=>'1','set_type'=>'0'))?>" class="view_btn">查看</a>
		</div>
    </div>
    <script type="text/javascript">
        $('.js_win_close').click(function(){
            var _this=$(this);
            _this.parents('.my_new_win').addClass('remove');
            setTimeout(function(){
                _this.parents('.my_new_win').remove();
            },300)
        })
		$('.js_btns a').click(function(){
			$.post("",function(data){
				if(data.status==1){
					window.location.href=data.info
				}
			})
		})
		
    </script>
<?php
	}
?>
    


<script src="__CDN__/assets/js/pc/banner.js"></script>
<script src="__CDN__/assets/js/pc/easing.js"></script>
<script src="__CDN__/assets/js/pc/echarts.min.js"></script>


<script type="text/javascript">
	$(function(){
		my_banner($('.js_home_top'),{
			autoplay:true, // 是否自动轮播  默认为true
			autotime: 7000, //自动轮播时间, 默认600
			runtime: 500, //切换时间，  默认500
			easing:"swing", //切换速度函数 默认 swing， 其他设置参考  easing.js
			type:"left_right",//切换方式  默认 left_right，(左右切换) 。其他设置  fade, (淡入淡出)
			after_run:function(index){
				
			},
		});

		// // 数据模板  day_data  日期数据     money_data 金额数据
		// var _temp_data={
		// 	day_data:[],
		// 	money_data:[
		// 		[],
		// 		[],
		// 		[]
		// 	],
		// 	total1:'0.00', //成交金额
		// 	total2:'0.00', //成交单数
		// 	total3:'0.00'//退款金额
		// }

		// var myChart = echarts.init(document.getElementById('chart_div_d'));
		// var dataAxis = _temp_data.day_data;

		// var option = {
		// 	title: {
		//         text: '最近7日趋势：',
		//         left: 'left',
		//         textStyle:{
		//         	color:'#666',
		//         	fontSize:'14px'
		//         }
		//     },
		//     tooltip: {
		//         trigger: 'axis'
		//     },
		//     legend: {
		//         data:['成交金额','成交笔数','退款金额'],
		//         left:'150px',
		//         align:'left'
		//     },
		//     xAxis: {
		//         data: dataAxis,
		//         axisLabel: {
		//             textStyle: {
		//                 color: '#999'
		//             }
		//         },
		//         axisTick: {
		//             show: true,
		//             lineStyle:{ color:'#999'}
		//         },
		//         axisLine: {
		//             show: true,
		//             lineStyle:{ color:'#999'}
		//         },
		//         z: 10
		//     },
		//     yAxis: {
		//         axisLine: {
		//             show: false
		//         },
		//         axisTick: {
		//             show: false
		//         },
		//         axisLabel: {
		//             textStyle: {
		//                 color: '#999'
		//             }
		//         },
		//         splitLine: {
		//             show: false
		//         }
		//     },
		//     grid:{
		//     	left:'60px',
		//     	top:'40px',
		//     	right:0,
		//     	bottom:'40px'
		//     },
		//     dataZoom: [
		//         {
		//             type: 'inside'
		//         }
		//     ],
		//     series: [
		//         {
		//             name:'成交金额',
		//             type:'line',
		//             stack: '总量',
		//             data:_temp_data.money_data[0]
		//         },
		//         {
		//             name:'成交笔数',
		//             type:'line',
		//             stack: '总量',
		//             data:_temp_data.money_data[1]
		//         },
		//         {
		//             name:'退款金额',
		//             type:'line',
		//             stack: '总量',
		//             data:_temp_data.money_data[2]
		//         },
		//     ]
		// };

		// // Enable data zoom when user click bar.
		// var zoomSize = 8;
		// myChart.on('click', function (params) {
		//     console.log(dataAxis[Math.max(params.dataIndex - zoomSize / 2, 0)]);
		//     myChart.dispatchAction({
		//         type: 'dataZoom',
		//         startValue: dataAxis[Math.max(params.dataIndex - zoomSize / 2, 0)],
		//         endValue: dataAxis[Math.min(params.dataIndex + zoomSize / 2, dataAxis.length - 1)]
		//     });
		// });
		//  myChart.setOption(option);
		// // 使用刚指定的配置项和数据显示图表。

		// myChart.dispatchAction({
		//     type: 'dataZoom',
		//     startValue: dataAxis[Math.max(2 - zoomSize / 2, 0)],
		//     endValue: dataAxis[Math.min(2 + zoomSize / 2, dataAxis.length - 1)]
		// }); 


		$('.js_tab_btn').click(function(){
			if($(this).hasClass('cur')){
				return false;
			}
			$(this).addClass('cur').siblings('a').removeClass('cur');
			var _index=$('.js_tab_btn').index(this);
			var _day=$(this).attr('data-d');
			var _type=$(this).attr('data-type');
			$.get("{:url('index/user/userReport')}",{type:_type},function(data){
				if(data.code==1){
					var _temp_data=data.msg;
					$('.js_chart_nums').eq(0).html(_temp_data.rmb_deal_money)
					$('.js_chart_nums').eq(1).html(_temp_data.all_order)
					$('.js_chart_nums').eq(2).html(_temp_data.refund_money)
					
					option.title.text="最近"+_day+"日趋势：";
					dataAxis=_temp_data.day_time;
					option.xAxis.data=dataAxis;
					option.series[0].data=_temp_data.deal_money;
					option.series[1].data=_temp_data.order_num;
					option.series[2].data=_temp_data.refund;

					myChart.setOption(option);
				}else{
					tip_show(data.msg,'2')
				}
			})
			
		})

		$('.js_tab_btn').eq(0).click();
		
		
		
		function htmlTodo(type){
		  
		    // list.sort(function(){
		    //     return Math.random()-0.5;
		    // });
		    //这里开始写ajax请求数据 type为请求不同的数据 数据格式参考list,最好是有8个长度 ，注释前面的代码是随机的
		    $.ajax({ url: "{:url('/index/index/business')}?type="+type, success: function(data){
		       data = JSON.parse(data)
		       if(data.status == 1){
		            for(var i = 0,l = data.list.length;i<l;i++){
		                if(!data.list[i].image){
		                    data.list[i].image = '__CDN__/assets/img/pc/images/img_busi_02zb_07ks.png'
		                }
		                if(!data.list[i].name){
		                    data.list[i].name = '一直播'
		                }
		                if(!data.list[i].nickname){
		                    data.list[i].nickname = 'https://www.baidu.com'
		                }
		            }
		            var htmlSource = data.list
		            var html = ''
		            for(var i = 0,l = htmlSource.length;i<l;i++){
		                html += '<a class="business-list-item" href="'+htmlSource[i].nickname+".html"+'">'
		                html += '<img src="'+htmlSource[i].image+'" />'
		                html += '<span>'+htmlSource[i].name+'</span>'
		                html += '</a>'
		            }
		            $('.business-list').html(html)
		       }
		        
		    }});
		
		}
		htmlTodo(1)
		//业务移动事件
		$(".business .business-item").hover(function (){
		    var type = $(this).attr('type')
		    htmlTodo(type)
		   $(this).addClass('business-item-active')
		   $(this).siblings().removeClass('business-item-active')
		},function (){  
		    // $(this).removeClass('business-item-active') 
		});  

	})
</script>
