<?php
	if($type){
?>
	<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<?php
	}
?>
<style>
.big_tt{height: 20px; color:#1F93B6; font-size: 16px;}
.big_tt .n{width:16px;height:16px; background:#1F93B6; text-align: center; line-height:16px; border-radius: 50%; font-size:12px; color:#fff; margin-right:5px }
body .code_img_d .sub_btn{background: none; color:#EF436D; border:1px solid #EF436D}
.tip_text{color:#666666}
body .sub_btn.blue{background: #309FC0; margin-left:28px}
.small_input .input_box .input_rr input, .small_input .input_box .input_rr select, .small_input .input_box .input_rr textarea
{height:40px}
</style>
<div class="my_close_win pad_lr20 pad_b40" style="height: 465px">
    <div class="color_red pad_t10 my_close_win_title">{:__('Second authentication1')}</div>
    <?php
		if($user['is_card']){
	?>
		 <div class="text-center f18 pad_tb30">
			<div class="pad_tb20">身份證號</div>
			<div class="pad_tb20"><?=substr_replace($info['cardid'],'****',2,4)?></div>
		</div>
	<?php
		}else{
	?>
    
    <form action="{:url('/index/user/editIdcard')}" autocomplete="off" class="small_input edit_info_form" style="padding: 0px 0px 0 0px">
        <div class="clearfix pad_t10">
			<div class="flex_ac big_tt">
				<div class="n">1</div>選擇申請類別
			</div>
			<div class="input_box full_width">
				<div class="input_tt">① <span class="requird">*</span>國民身分證統一編號</div>
				<div class="input_rr">
					<input type="text" name="cardid" class="js_idcard_value" maxlength="10" onkeyup="this.value=this.value.replace(/[^\w]/g,'')" onpaste="this.value=this.value.replace(/[^\w]/g,'')" is_required="true" empty_tip="{:__('Please enter your ID number')}" value="<?=empty($info['cardid'])?'':$info['cardid']?>">
				</div>
			</div>
			<div class="input_box full_width">
				<div class="input_tt">② <span class="requird">*</span>發證日期：</div>
				<div class="input_rr clearfix" style="overflow: visible; line-height: 34px;">
					<div class="pull-left pad_r10">民国</div>
					<div class="moni_select pull-left" style="width: 120px ">
						<input type="text" name="year" value="<?=empty($info['year'])?'':$info['year']?>" is_required="true" empty_tip="{:__('Please select year')}">
						<div class="moni_selected clearfix">
							<img class="arr" src="__CDN__/assets/img/arr.png">
							<span>{:__('Please choose')}</span>
						</div>
						<div class="moni_s_down">
							<?php
								$nowyear = date('Y')-1911;
								for($i=$nowyear;$i>=94;$i--){
							?>
								<a href="javascript:;" value="<?=$i?>"><?=$i?></a>
							<?php
								}
							?>
						</div>
					</div>
					<div class="pull-left pad_lr10">{:__('Year')}</div>
					<div class="moni_select pull-left" style="width: 90px ">
						<input type="text" name="month" value="<?=empty($info['month'])?'':$info['month']?>" is_required="true" empty_tip="{:__('Please select month')}">
						<div class="moni_selected clearfix">
							<img class="arr" src="__CDN__/assets/img/arr.png">
							<span>{:__('Please choose')}</span>
						</div>
						<div class="moni_s_down">
							<?php
								for($a=1;$a<=12;$a++){
							?>
								<a href="javascript:;" value="<?=$a?>"><?=$a?></a>
							<?php
								}
							?>
						</div>
					</div>
					<div class="pull-left pad_lr10">{:__('Month')}</div>
					<div class="moni_select pull-left" style="width: 90px ">
						<input type="text" name="day" value="<?=empty($info['day'])?'':$info['day']?>" is_required="true" empty_tip="{:__('Please select day')}">
						<div class="moni_selected clearfix">
							<img class="arr" src="__CDN__/assets/img/arr.png">
							<span>{:__('Please choose')}</span>
						</div>
						<div class="moni_s_down">
						<?php
								for($b=1;$b<=31;$b++){
							?>
								<a href="javascript:;" value="<?=$b?>"><?=$b?></a>
							<?php
								}
							?>
						</div>
					</div>
					<div class="pull-left pad_lr10">{:__('Day')}</div>
				</div>
			</div>
			
			<div class="input_box full_width">
				<div class="input_tt">③ <span class="requird">*</span>{:__('Receiving and Replacing Types')}：</div>
				<div class="input_rr" style="overflow: visible; ">
					<div class="moni_select" style="width: 100% ">
						<input type="text" name="type" value="<?=empty($info['type'])?'':$info['type']?>" is_required="true" empty_tip="{:__('Please choose the type of replacement')}">
						<div class="moni_selected clearfix">
							<img class="arr" src="__CDN__/assets/img/arr.png">
							<span>{:__('Please choose')}</span>
						</div>
						<div class="moni_s_down">
							<a href="javascript:;" value="1">{:__('Initial development')}</a>
							<a href="javascript:;" value="2">{:__('Replacement')}</a>
							<a href="javascript:;" value="3">{:__('Renewal')}</a>
						</div>
					</div>
				</div>
			</div>

			<div class="tip_text pad_t10">
				<div>
					註意事項：使用數位銀行的會員，請正反面都上傳相同存摺封面，沒有實體存摺，可上傳網絡存摺，付唄不會保留您的個人資料存檔，僅用於您匯款時核對入賬使用!
				</div>
			</div>
			
			<!--<div class="flex_ac big_tt margin_t10">
				<div class="n">2</div>圖形驗證
			</div>
			<div class="input_box full_width">
				<div class="input_tt"><span class="requird">*</span>請輸入下圖的圖形驗證碼</div>
				<div class="input_rr">
					<input type="text" style="height: 40px" placeholder="" name="code" maxlength="5"  is_required="true" empty_tip="請輸入下圖的圖形驗證碼">
				</div>
			</div>
			<div class="flex_ac code_img_d pad_t20">
				<img style=" height:66px;" src="" class="js_code_img" onclick="get_img_code()"/>
				<a href="javascript:;"  class="sub_btn js_get_code_btn small margin_l20">產製新驗證碼</a>
			</div>
			<div class="tip_text pad_t20">
				<div>
					如果圖形驗證碼難以辨識，可點擊「刷新驗證碼」按鈕，刷新一張圖形驗證碼。
				</div>
				<div>
					為避免被有心人士利用作為詐騙或者洗錢通道，我們需要驗證您的證件是否是您本人，且真實有效。您所填寫的身份證資料僅用於內政部連線認證，付唄不會保留您的資料，僅會保存認證成功或者失敗這個結果。
				</div>
				<div style="color: #F53232;">如輸入資料錯誤達２次，當日無法查詢。</div>
			</div>
		</div>-->

        	<div class="text-center pad_t10">
				<a href="javascript:;" class="sub_btn small js_form_sub" data-text="{:__('Authentication')}" data-loadding="{:__('System audit in progress')}" data-type="new_location">{:__('Authentication')}</a>
				<a href="javascript:layer.closeAll();" class="sub_btn small blue" >取消</a>
        	</div>
		</div>
    </form>
	<?php
		}
	?>
</div>

<!--<script type="text/javascript">
    $('.js_idcard_value').keyup(function(){
        var _val=$(this).val();
        _val=_val.toString().toUpperCase();
        $('.js_idcard_value').val(_val)
    })
	function get_img_code(){
		$.get("{:url('/index/user/get_card_num')}",{},function(res){
		    res=JSON.parse(res)
			if(res.code==1){
				$('.js_code_img').attr('src','/'+res.img_url);
				$('.card_cookie1').val(res.cookie)
				$('.card_cookie2').val(res.captchaKey)
				$('.card_cookie3').val(res.time1)
			}else{
				layer.msg(res.sms)
			}
		})
	}
	get_img_code()
	$('.js_get_code_btn').click(function(){
		$(this).siblings('img').click()
	})
</script>-->