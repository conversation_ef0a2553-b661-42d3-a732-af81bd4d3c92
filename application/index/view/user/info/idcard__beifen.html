<?php
	if($type){
?>
	<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<?php
	}
?>
<div class="my_close_win pad_lr20 pad_b40">
    <div class="color_red pad_t10 my_close_win_title">{:__('Second authentication1')}</div>
    <?php
		if($user['is_card']){
	?>
		 <div class="text-center f18 pad_tb30">
			<div class="pad_tb20">身份證號</div>
			<div class="pad_tb20"><?=substr_replace($info['cardid'],'****',2,4)?></div>
		</div>
	<?php
		}else{
	?>
    <div class="color_666 pad_t10 f12" style="line-height: 24px;">{:__('In order to avoid fraud by co-producers and be used')}</div>
    <div class="color_red f12" style="line-height: 24px;">{:__('Please enter the data according to the size of the')}</div>
    <div class="color_red f12" style="line-height: 24px;">{:__('Please enter the data according to the size of the2')}</div>
    <form action="{:url('/index/user/editIdcard')}" autocomplete="off" class="small_input edit_info_form" style="padding: 30px 75px 0 75px">
        <div class="input_box full_width">
            <div class="input_tt">{:__('Certificate number')}：</div>
            <div class="input_rr">
                <input type="text" name="cardid" class="js_idcard_value" maxlength="10" onkeyup="this.value=this.value.replace(/[^\w]/g,'')" onpaste="this.value=this.value.replace(/[^\w]/g,'')" is_required="true" empty_tip="{:__('Please enter your ID number')}" value="<?=empty($info['cardid'])?'':$info['cardid']?>">
            </div>
        </div>
        <div class="input_box full_width">
            <div class="input_tt">{:__('Date of certification')}：</div>
            <div class="input_rr clearfix" style="overflow: visible; line-height: 34px;">
                <div class="moni_select pull-left" style="width: 120px ">
                    <input type="text" name="year" value="<?=empty($info['year'])?'':$info['year']?>" is_required="true" empty_tip="{:__('Please select year')}">
                    <div class="moni_selected clearfix">
                        <img class="arr" src="__CDN__/assets/img/arr.png">
                        <span>{:__('Please choose')}</span>
                    </div>
                    <div class="moni_s_down">
						<?php
							$nowyear = date('Y')-1911;
							for($i=$nowyear;$i>=94;$i--){
						?>
							<a href="javascript:;" value="<?=$i?>"><?=$i?></a>
						<?php
							}
						?>
                    </div>
                </div>
                <div class="pull-left pad_lr10">{:__('Year')}</div>
                <div class="moni_select pull-left" style="width: 90px ">
                    <input type="text" name="month" value="<?=empty($info['month'])?'':$info['month']?>" is_required="true" empty_tip="{:__('Please select month')}">
                    <div class="moni_selected clearfix">
                        <img class="arr" src="__CDN__/assets/img/arr.png">
                        <span>{:__('Please choose')}</span>
                    </div>
                    <div class="moni_s_down">
						<?php
							for($a=1;$a<=12;$a++){
						?>
							<a href="javascript:;" value="<?=$a?>"><?=$a?></a>
						<?php
							}
						?>
                    </div>
                </div>
                <div class="pull-left pad_lr10">{:__('Month')}</div>
                <div class="moni_select pull-left" style="width: 90px ">
                    <input type="text" name="day" value="<?=empty($info['day'])?'':$info['day']?>" is_required="true" empty_tip="{:__('Please select day')}">
                    <div class="moni_selected clearfix">
                        <img class="arr" src="__CDN__/assets/img/arr.png">
                        <span>{:__('Please choose')}</span>
                    </div>
                    <div class="moni_s_down">
					<?php
							for($b=1;$b<=31;$b++){
						?>
							<a href="javascript:;" value="<?=$b?>"><?=$b?></a>
						<?php
							}
						?>
                    </div>
                </div>
                <div class="pull-left pad_lr10">{:__('Day')}</div>
            </div>
        </div>

        <div class="input_box full_width">
            <div class="input_tt">{:__('Receiving and Replacing Types')}：</div>
            <div class="input_rr" style="overflow: visible; ">
                <div class="moni_select" style="width: 100% ">
                    <input type="text" name="type" value="<?=empty($info['type'])?'':$info['type']?>" is_required="true" empty_tip="{:__('Please choose the type of replacement')}">
                    <div class="moni_selected clearfix">
                        <img class="arr" src="__CDN__/assets/img/arr.png">
                        <span>{:__('Please choose')}</span>
                    </div>
                    <div class="moni_s_down">
                        <a href="javascript:;" value="1">{:__('Initial development')}</a>
                        <a href="javascript:;" value="2">{:__('Replacement')}</a>
						<a href="javascript:;" value="3">{:__('Renewal')}</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="text-center pad_t30">
			<?php
				if($info){
					if($info['is_state'] == '2'){
						echo __('Reasons for failure').$info['check_text'].'<br />';
			?>
				<a href="javascript:;" class="sub_btn small js_form_sub" data-text="{:__('Authentication failed, recertification')}" data-loadding="{:__('System audit in progress')}" data-type="new_location">{:__('Authentication failed, recertification')}</a>
			<?php
					}
					if($info['is_state'] == '3'){
				?>
				<a href="javascript:;" class="sub_btn small">{:__('Certification')}</a>
				<?php
					}
					if($info['is_state'] == '1'){
				?>
					<a href="javascript:;" class="sub_btn small">{:__('Certified')}</a>
				<?php
					}
				}else{
			?>
				<a href="javascript:;" class="sub_btn small js_form_sub" data-text="{:__('Authentication')}" data-loadding="{:__('System audit in progress')}" data-type="new_location">{:__('Authentication')}</a>
			<?php
				}
			?>
            
        </div>
    </form>
	<?php
		}
	?>
</div>

<script type="text/javascript">
    $('.js_idcard_value').keyup(function(){
        var _val=$(this).val();
        _val=_val.toString().toUpperCase();
        $('.js_idcard_value').val(_val)
    })
</script>