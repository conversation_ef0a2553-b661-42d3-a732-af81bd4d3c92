<?php
	if($type){
?>
	<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<?php
	}
?>
<style>
	.big_tt {
		height: 20px;
		color: #1F93B6;
		font-size: 16px;
	}

	.big_tt .n {
		width: 16px;
		height: 16px;
		background: #1F93B6;
		text-align: center;
		line-height: 16px;
		border-radius: 50%;
		font-size: 12px;
		color: #fff;
		margin-right: 5px
	}

	body .code_img_d .sub_btn {
		background: none;
		color: #EF436D;
		border: 1px solid #EF436D
	}

	body .sub_btn.blue {
		background: #309FC0;
		margin-left: 28px
	}
	
	.id_card_img_2024 .item_s{ width: 50%; }
	.id_card_img_2024 .img{width:270px;height: 190px; margin: 0 auto; position:relative}
	
	.id_card_img_2024 .img .up_icon{width: 100%; height:100%}
	.id_card_img_2024 .img input{ width: 0; height: 0; opacity: 0; position:absolute; left:0; top:0 }
	.id_card_img_2024 .img .img_src{ position: relative; background-position:center center; }
	.id_card_img_2024 .img .img_src a{display: block; width: 20px; height: 20px; position: absolute; right: 0; top: 0;}
	.id_card_img_2024 .img .img_src a img{width: 100%;float: left;height: 100%;}

</style>
<div class="my_close_win pad_lr20 pad_b40">
    <?php
		if(!$is_card){
	?>
        <div class="text-center pad_tb40 margin_t30">
    		<div>{:__('Please pass the ID card audit first')} </div>
            <a href="{:url('/index/user/editIdcard/type/1')}"  data-width="600" class="sub_btn small margin_t30 js_ajax_win">{:__('One certification')}</a>
        </div>
	<?php
		}else{
	?>
	<div class="color_red pad_t10 my_close_win_title">金融機構資料</div>
    
    <form action="{:url('/index/user/editIdcardTwo')}" autocomplete="off" class="small_input edit_info_form" style="padding: 0px">
        <div class="tip_text pad_tb20" style="color:#333">
			<div>
				請上傳存摺照片檔案，提供之照片須包含戶名與帳號，需於註冊人相符，並確認照片清晰可辨，僅支援上傳JPG/JPEG/PNG/HEIC格式，圖片大小限制10MB以內。
			</div>
		</div>
		
		
 
        <div class="id_card_img_2024 clearfix">
            <div class="item_s pull-left text-center">
                <div class="img">
                    <?php
                        if(!$card || $card['is_state'] == '2'){
                        ?>
                            <!-- 如果未上传过 -->
                            <div class="js_up_load_btn">
                                <input type="text" name="img" value=""  is_required="true" empty_tip="{:__('Please upload photos')}">
                                <img class="up_icon" src="__CDN__/assets/img/pc/card1.png">
                            </div>
                            <div class="img_src js_img_src hide" style="background-image: url(<?=$card['card_img']?>);">
                                <a href="javascript:;"><img src="__CDN__/assets/img/dele.png"></a>
                            </div>
                            <!-- 如果未上传过 -->
                        <?php
                        }else{
                        ?>
                         <!-- 如果已传  地址给-->
                        <div class="img_src js_img_src" style="background-image: url(<?=$card['card_img']?>);">
                        </div>
                        <!-- 如果已传 -->
                        <?php
                        }
                    ?>
                    
                    <div class="hide">
                        <input type="file" class="js_up_img_input_btn" data-type="img" accept="image/*" multiple data-maxMum="1" data-fileSizeMax="2" onchange="filecountup($(this),this.files)" data-filename="img" />
                    </div>
                </div>
            </div>


            <div class="item_s pull-left text-center">
                <div class="img">
                    <?php
                        if(!$card || $card['is_state'] == '2'){
                        ?>
                            <!-- 如果未上传过 -->
                            <div class="js_up_load_btn2">
                                <input type="text" name="img2" value=""  is_required="true" empty_tip="{:__('Please upload photos')}">
                                <img class="up_icon" src="__CDN__/assets/img/pc/card2.png">
                            </div>
                            <div class="img_src js_img_src2 hide" style="background-image: url(<?=$card['card_img_img']?>);">
                                <a href="javascript:;"><img src="__CDN__/assets/img/dele.png"></a>
                            </div>
                            <!-- 如果未上传过 -->
                        <?php
                        }else{
                        ?>
                         <!-- 如果已传  地址给-->
                        <div class="img_src js_img_src2" style="background-image: url(<?=$card['card_img_img']?>);">
                        </div>
                        <!-- 如果已传 -->
                        <?php
                        }
                    ?>
                    
                    <div class="hide">
                        <input type="file" class="js_up_img_input_btn2" data-type="img" accept="image/*" multiple data-maxMum="1" data-fileSizeMax="2" onchange="filecountup2($(this),this.files)" data-filename="img" />
                    </div>
                </div>
            </div>
			
			
        </div>
		<div class="tip_text pad_t20" style="color:#999">
			<div>
				註意事項：使用數位銀行的會員，請正反面都上傳相同存摺封面，沒有實體存摺，可上傳網絡存摺
			</div>
		</div>
        <div class="text-center pad_t30">
			<?php
				if($card){
					if($card['is_state'] == '2'){
						echo __('Reasons for failure').$card['check_text'].'<br />';
					}
					switch($card['is_state']){
						case 1:
			?>
					<a href="javascript:;" class="sub_btn small">{:__('Certified')}</a>
			<?php
						break;
						case 2:
			?>
				<a href="javascript:;" class="sub_btn small js_form_sub" data-text="{:__('Certification')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Authentication failed, recertification')}</a>
			<?php
						break;
						case 3:
			?>
				<a href="javascript:;" class="sub_btn small">{:__('Certification')}</a>
			<?php
						break;
					}
				}else{
			?>
				<a href="javascript:;" class="sub_btn small js_form_sub" data-text="{:__('Authentication')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Authentication')}</a>
			<?php
				}
			?>
        </div>
    </form>
	<?php
		}
	?>
</div>

<div class="js_hide_img" style="width: 0; height: 0; overflow: hidden;">
<div id="img_prev" style="width: 1000px"><img style="width: 1000px;" src="{$site.Front_card}"></div>
</div>



<div class="js_hide_img2" style="width: 0; height: 0; overflow: hidden;">
<div id="img_prev2" style="width: 1000px"><img style="width: 1000px;" src="{$site.Reverse_card}"></div>
</div>


<script type="text/javascript">
    $('.js_id_card_img').click(function(){
        layer.open({
          type: 1,
          title: false,
          closeBtn: 1,
          area: ['1000px',$('#img_prev img').height()+'px'],
          skin: 'layui-layer-nobg', //没有背景色
          shadeClose: true,
          content: $('.js_hide_img').html()
        });
    })

    $('.js_up_load_btn').click(function(){
        $('.js_up_img_input_btn').click()
    })



    function getImgData(img,dir,max_w,next){
     var image=new Image();
     image.onload=function(){
      var degree=0,drawWidth,drawHeight,width,height;
      drawWidth=max_w>this.naturalWidth?this.naturalWidth:max_w;
      drawHeight=this.naturalHeight*drawWidth/this.naturalWidth;
      
      var canvas=document.createElement('canvas');
      canvas.width=width=drawWidth;
      canvas.height=height=drawHeight; 
      var context=canvas.getContext('2d');
     
      context.drawImage(this,0,0,drawWidth,drawHeight);
      //返回校正图片
      next(canvas.toDataURL("image/jpeg",.7));
     }
     image.src=img;
    }


    //图片上传预览 2018.7.11
    function filecountup(_this,files,count){
        // 文件大小限制
        // 
        console.log(files)
        if(_this.attr('data-fileSizeMax')!=''){
            var max=parseFloat(_this.attr('data-fileSizeMax'))*1024*1024;
            if(files[0].size > max){
                tip_show("文件不能大于"+parseFloat(_this.attr('data-fileSizeMax'))+"M","2");
                return false;
            } 
        }


        var file = files[0];
        var reader = new FileReader();
        reader.onloadend = function () {
            // console.log(reader.result);
            getImgData(reader.result,'',1000,function(data){
                console.log(data)
                $('.js_up_load_btn').addClass('hide').find('input').val(data);
                $('.js_img_src').removeClass('hide').css('background-image','url('+data+')')
            })

        }
        if (file) {
            reader.readAsDataURL(file);
        }
    };


    $('.js_img_src a').click(function(){
        var a=layer.confirm("{:__('Are you sure you want to delete it?')}", {
          title:false,
          area:['500px', 'auto'],
          skin: 'my_confirm',
          btn: ["{:__('Confirm')}","{:__('Cancel')}"], //按钮
          btnAlign:'c'
        }, function(){
            layer.close(a);
            $('.js_up_load_btn').removeClass('hide').find('input').val('');
            $('.js_img_src').addClass('hide')
        });
    })





    $('.js_id_card_img2').click(function(){
        layer.open({
          type: 1,
          title: false,
          closeBtn: 1,
          area: ['1000px',$('#img_prev2 img').height()+'px'],
          skin: 'layui-layer-nobg', //没有背景色
          shadeClose: true,
          content: $('.js_hide_img2').html()
        });
    })

    $('.js_up_load_btn2').click(function(){
        $('.js_up_img_input_btn2').click()
    })

    //图片上传预览 2018.7.11
    function filecountup2(_this,files,count){
        // 文件大小限制
        // 
        console.log(files)
        if(_this.attr('data-fileSizeMax')!=''){
            var max=parseFloat(_this.attr('data-fileSizeMax'))*1024*1024;
            if(files[0].size > max){
                tip_show("文件不能大于"+parseFloat(_this.attr('data-fileSizeMax'))+"M","2");
                return false;
            } 
        }


        var file = files[0];
        var reader = new FileReader();
        reader.onloadend = function () {
            // console.log(reader.result);
            // $('.js_up_load_btn2').addClass('hide').find('input').val(reader.result);
            // $('.js_img_src2').removeClass('hide').css('background-image','url('+reader.result+')')
            getImgData(reader.result,'',1000,function(data){
                console.log(data)
                $('.js_up_load_btn2').addClass('hide').find('input').val(data);
                $('.js_img_src2').removeClass('hide').css('background-image','url('+data+')')
            })
        }
        if (file) {
            reader.readAsDataURL(file);
        }
    };


    $('.js_img_src2 a').click(function(){
        var a=layer.confirm("{:__('Are you sure you want to delete it?')}", {
          title:false,
          area:['500px', 'auto'],
          skin: 'my_confirm',
          btn: ["{:__('Confirm')}","{:__('Cancel')}"], //按钮
          btnAlign:'c'
        }, function(){
            layer.close(a);
            $('.js_up_load_btn2').removeClass('hide').find('input').val('');
            $('.js_img_src2').addClass('hide')
        });
    })

    

</script>

