
<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 ">
	<div class="color_red pad_t10 my_close_win_title">{:__('Replacement of Avatar')}</div>
	<div  class="tailoring-container">
        <!-- <div class="black-cloth" onclick="closeTailor(this)"></div> -->
        <div class="tailoring-content">
                <!-- <div class="tailoring-content-one">
                    <label title="上传图片" for="chooseImg" class="l-btn choose-btn">
                        <input type="file" accept="image/jpg,image/jpeg,image/png" name="file" id="chooseImg" class="hidden" onchange="selectImg(this)">
                        选择图片
                    </label>
                    <div class="close-tailoring"  onclick="closeTailor(this)">×</div>
                </div> -->
                <div class="tailoring-content-two">
                    <div class="tailoring-box-parcel">
                        <img id="tailoringImg">
                    </div>
                    <div class="preview-box-parcel">

                        <!-- <div class="square previewImg"></div> -->
                        <div class="circular previewImg"></div>
                        <p>{:__('Preview of avatar')}</p>
                    </div>
                </div>
                <div class="tailoring-content-three">
                    <label for="chooseImg" class="chooseImg" >
                        <input type="file" accept="image/jpg,image/jpeg,image/png" name="file" id="chooseImg" class="hidden" onchange="selectImg(this)">
                        {:__('Select pictures')}
                    </label>
                    <!-- <button class="l-btn cropper-reset-btn">复位</button>
                    <button class="l-btn cropper-rotate-btn">旋转</button>
                    <button class="l-btn cropper-scaleX-btn">换向</button> -->
                    <div class="caozuo">
                      <button class="cropper-zoomout-btn"></button>
                      <button class="cropper-zoomin-btn"></button>
                    </div>
                </div>
                <div class="form_btn">
                  <button  id="sureCut" data-url="{:url('/index/user/upload_avatar')}">{:__('Submit')}</button>
                  <button  id="cancleCut">{:__('Cancel')}</button>
                </div>
            </div>
    </div>
</div>
<link rel="stylesheet" type="text/css" href="__CDN__/assets/css/pc/cropper.min.css">
<style type="text/css">
    .tailoring-content .tailoring-content-three{ width: 100%; margin: 0; padding: 0 20px; }
    .tailoring-container .tailoring-content{ height: 360px; }
    .tailoring-content .tailoring-content-two{ padding: 10px; height: 260px; }
    .tailoring-content .tailoring-box-parcel{ width: 200px; height: 200px; position: absolute; left: 50%; margin-left: -100px; top: 25px; background-size: 50% 50% }
</style>

<script src="__CDN__/assets/js/pc/cropper.min.js"></script>

<script>

    //弹出框水平垂直居中
    (window.onresize = function () {
        var win_height = $(window).height();
        var win_width = $(window).width();
        if (win_width <= 768){
            $(".tailoring-content").css({
                "top": (win_height - $(".tailoring-content").outerHeight())/2,
                "left": 0
            });
        }else{
            $(".tailoring-content").css({
                "top": (win_height - $(".tailoring-content").outerHeight())/2,
                "left": (win_width - $(".tailoring-content").outerWidth())/2
            });
        }
    })();

    //弹出图片裁剪框
    $("#replaceImg").on("click",function () {
        $(".tailoring-container").toggle();
    });
    //图像上传
    function selectImg(file) {
        if (!file.files || !file.files[0]){
            return;
        }
         $('.tailoring-box-parcel').unbind('click')
        var reader = new FileReader();
        reader.onload = function (evt) {
            var replaceSrc = evt.target.result;
            //更换cropper的图片
            $('#tailoringImg').cropper('replace', replaceSrc,false);//默认false，适应高度，不失真
        }
        reader.readAsDataURL(file.files[0]);
    }
    //cropper图片裁剪
    $('#tailoringImg').cropper({
        aspectRatio: 1/1,//默认比例
        viewMode:1,
        preview: '.previewImg',//预览视图
        guides: false,  //裁剪框的虚线(九宫格)
        autoCropArea: 0.5,  //0-1之间的数值，定义自动剪裁区域的大小，默认0.8
        movable: false, //是否允许移动图片
        dragCrop: true,  //是否允许移除当前的剪裁框，并通过拖动来新建一个剪裁框区域
        movable: true,  //是否允许移动剪裁框
        resizable: true,  //是否允许改变裁剪框的大小
        zoomable: true,  //是否允许缩放图片大小
        mouseWheelZoom: false,  //是否允许通过鼠标滚轮来缩放图片
        touchDragZoom: true,  //是否允许通过触摸移动来缩放图片
        rotatable: true,  //是否允许旋转图片
        crop: function(e) {
            // 输出结果数据裁剪图像。
        }
    });
    //旋转
    $(".cropper-rotate-btn").on("click",function () {
        $('#tailoringImg').cropper("rotate", 45);
    });
    //复位
    $(".cropper-reset-btn").on("click",function () {
        $('#tailoringImg').cropper("reset");
    });

    //放大
    $('.cropper-zoomin-btn').on('click',function(){
          $('#tailoringImg').cropper('zoom', 0.1)
    })

    //缩小
    $('.cropper-zoomout-btn').on('click',function(){
          $('#tailoringImg').cropper('zoom', -0.1)
    })

    //取消
    $('#cancleCut').click('click',function(){
        layer.closeAll();
    })

    $('.tailoring-box-parcel').click(function(){
        var _this = $(this);
        $('.chooseImg').click();
    })

    //换向
    var flagX = true;
    $(".cropper-scaleX-btn").on("click",function () {
        if(flagX){
            $('#tailoringImg').cropper("scaleX", -1);
            flagX = false;
        }else{
            $('#tailoringImg').cropper("scaleX", 1);
            flagX = true;
        }
        flagX != flagX;
    });

    //裁剪后的处理
    $("#sureCut").on("click",function () {
			var url = $(this).attr('data-url');
        if ($("#tailoringImg").attr("src") == null ){
            return false;
        }else{
            var cas = $('#tailoringImg').cropper('getCroppedCanvas');//获取被裁剪后的canvas
            var base64url = cas.toDataURL('image/png'); //转换为base64地址形式
            $(".bigUser").prop("src",base64url);//显示为图片的形式

            //关闭裁剪框
            layer.closeAll();

            $.ajax({
              url: url,
              type: 'POST',
              dataType: 'json',
              data: {avatar_img: base64url}
            })
            .success(function(res) {
                if(res.code==1){
                  layer.msg(res.msg);
				  location.reload()
                }else{
                  layer.msg(res.msg)
                }
            })

        }
    });
    //关闭裁剪框
    function closeTailor() {
        $(".tailoring-container").toggle();
    }



</script>
