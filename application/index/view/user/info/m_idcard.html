<style>

    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .input_box{ background: none; overflow: visible; }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
    .moni_select{ border: 1px solid #fff; border-radius: 5px; }
    .body{ padding-bottom: 1.5rem }
    .moni_select .moni_s_down{ max-height: 150px }

    .input_box .input_rr .arr{ margin-top: 17px; margin-left: 7px; margin-right: 0.2rem }
	<?php
		if($type){
	?>
	.idcard_auth_win{ background: #f4f4f4; height: 100%; overflow: auto; }
    .appeal_foot{ position: relative; }
	<?php
		}
	?>
</style>

<style type="text/css">
	
	.input_box {
		background: none;
		overflow: visible;
	}

	.input_box input,.input_box select, .input_box .moni_select{border:none; border-bottom:1px solid #eee; border-radius:0}
	
	.big_tt{height: 34px; color:#fff; background:#1F93B6; font-size: 15px; border-radius:5px; padding:0 10px}
	.big_tt .n{width:16px;height:16px; background:#fff; text-align: center; line-height:16px; border-radius: 50%; font-size:12px; color:#1F93B6; margin-right:5px }
	body .code_img_d .sub_btn{background: none; width:auto; color:#EF436D; border:1px solid #EF436D}
	.tip_text{color:#666666; font-size:12px}
	body .sub_btn.blue{background: #309FC0; margin-left:0px;}
	body .sub_btn{ border-radius: 5px; width: 100%; background: #EF4B7B; color: #fff; line-height: 0.88rem }
	.wi_block{background:#fff; border-radius:5px; padding:10px}
	.requird{color:#EF436D}
</style>
<div class="pad_tb20 bg pad_lr20 <?=$type?'idcard_auth_win':''?>">
    <div class="help_title bg_fff clearfix">
        <img src="__CDN__/assets/img/wap/icon_common_09.png" class="icon">{:__('One certification')}
    </div>

    <?php
		if($user['is_card']){
	?>
    <div class="help_title bg_fff margin_t20 clearfix" style="line-height: 0.4rem; padding: 0.6rem 0">
        <div class="text-center f16 pad_tb30 ">
            <div class="pad_tb10 color_999 f14">身份證號</div>
            <div class="pad_tb10"><?=substr_replace($info['cardid'],'****',2,4)?></div>
        </div>
    </div>
	<?php
		}else{
	?>

   <form action="{:url('/index/user/editIdcard')}" class="edit_info_form">
		<div class="flex_ac big_tt margin_tb20">
			<div class="n">1</div>選擇申請類別
		</div>
		<div class="wi_block margin_b20">
			<div class="input_box full_width">
				<div class="input_tt">① <span class="requird">*</span>國民身分證統一編號</div>
				<div class="input_rr">
					<input type="text" class="js_idcard_value" placeholder="{:__('Please enter your ID number')}" name="cardid" data-type="*" onkeyup="this.value=this.value.replace(/[^\w]/g,'')" onpaste="this.value=this.value.replace(/[^\w]/g,'')" is_required="true" empty_tip="{:__('Please enter your ID number')}" value="<?=empty($info['cardid'])?'':$info['cardid']?>">
				</div>
			</div>
			<!--<input type="hidden" name="card_cookie" class="card_cookie1" value="">
			<input type="hidden" name="captchaKey" class="card_cookie2" value="">
			<input type="hidden" name="time1" class="card_cookie3" value="">-->
			<div class="input_box full_width">
				<div class="input_tt">② <span class="requird">*</span>發證日期：</div>
				<div class="flex_ac" style="width:100%">
					<div class="input_rr flex_ac clearfix" style="overflow: visible; width:100%">
							<div class="moni_select flex1"  >
							<input type="text" name="year" value="<?=empty($info['year'])?'':$info['year']?>" is_required="true" empty_tip="{:__('Please select year')}">
							<div class="moni_selected clearfix">
								<span>{:__('Please choose')}</span>
							</div>
							<div class="moni_s_down">
								<?php
									$nowyear = date('Y')-1911;
									for($i=$nowyear;$i>=94;$i--){
								?>
									<a href="javascript:;" value="<?=$i?>"><?=$i?></a>
								<?php
									}
								?>
							</div>
						</div>
						<div class="pad_lr10">{:__('Year')}</div>
					</div>
					
					<div class="input_box full_width " style="width:100%">
						<div class="input_rr flex_ac clearfix" style="overflow: visible;width:100%">
							<div class="moni_select flex1"  >
								<input type="text" name="month" value="<?=empty($info['month'])?'':$info['month']?>" is_required="true" empty_tip="{:__('Please select month')}">
								<div class="moni_selected clearfix">
									<span>{:__('Please choose')}</span>
								</div>
								<div class="moni_s_down">
									<?php
										for($a=1;$a<=12;$a++){
									?>
										<a href="javascript:;" value="<?=$a?>"><?=$a?></a>
									<?php
										}
									?>
								</div>
							</div>
							<div class="pad_lr10">{:__('Month')}</div>
						</div>
					</div>


					<div class="input_box full_width " style="width:100%">
						<div class="input_rr flex_ac clearfix" style="overflow: visible;width:100%">
							<div class="moni_select flex1"  >
								<input type="text" name="day" value="<?=empty($info['day'])?'':$info['day']?>" is_required="true" empty_tip="{:__('Please select day')}">
								<div class="moni_selected clearfix">
									<span>{:__('Please choose')}</span>
								</div>
								<div class="moni_s_down">
								   <?php
										for($b=1;$b<=31;$b++){
									?>
										<a href="javascript:;" value="<?=$b?>"><?=$b?></a>
									<?php
										}
									?>
								</div>
							</div>
							<div class="pad_lr10">{:__('Day')}</div>
						</div>
					</div>
				</div>
			</div>
			
			
			<div class="input_box full_width">
				<div class="input_tt">③ <span class="requird">*</span>{:__('Receiving and Replacing Types')}：</div>
				<div class="input_rr">
				   <!--  <img class="arr pull-right" src="__CDN__/assets/img/arr.png">
					<input type="text" class="js_type_v" name="type" value="<?=empty($info['is_state'])?'':$info['is_state']?>" is_required="true" empty_tip="{:__('Please choose the type of replacement')}" style="width: 0; height: 0; opacity: 0; position: absolute;">
					<div class="pad_lr20 js_type_t">{:__('Please choose the type of replacement')}</div> -->
					<select name="type" class="js_n_type" style="background: #fff" value="<?=empty($info['type'])?'':$info['type']?>" is_required="true" empty_tip="{:__('Please choose the type of replacement')}">
						<option value="">{:__('Please choose the type of replacement')}</option>
						<option value="1">{:__('Initial development')}</option>
						<option value="2">{:__('Replacement')}</option>
						<option value="3">{:__('Renewal')}</option>
					</select>
				</div>
			</div>
		</div>
		
		<!--<div class="flex_ac big_tt margin_b20">
			<div class="n">2</div>圖形驗證
		</div>
		<div class="wi_block margin_b20">
			<div class="input_box full_width margin_b20">
				<div class="input_tt"><span class="requird">*</span>請輸入下圖的圖形驗證碼</div>
				<div class="input_rr">
					<input type="text" style="height: 40px" placeholder="請輸入下圖的圖形驗證碼" name="code" maxlength="5" is_required="true" empty_tip="請輸入下圖的圖形驗證碼">
				</div>
			</div>
			<div class="flex_ac code_img_d pad_t20">
				<img style=" height:48px;" src="" class="js_code_img" onclick="get_img_code()"/>
				<a href="javascript:;"  class="sub_btn js_get_code_btn small margin_l20">產製新驗證碼</a>
			</div>
		</div>
		<div class="tip_text pad_t20">
			<div>
				如果圖形驗證碼難以辨識，可點擊「刷新驗證碼」按鈕，刷新一張圖形驗證碼。
			</div>
			<div>
				為避免被有心人士利用作為詐騙或者洗錢通道，我們需要驗證您的證件是否是您本人，且真實有效。您所填寫的身份證資料僅用於內政部連線認證，付唄不會保留您的資料，僅會保存認證成功或者失敗這個結果。
			</div>
			<div style="color: #F53232;">如輸入資料錯誤達２次，當日無法查詢。</div>
		</div>-->
        
        

        
        <!-- <div class="types_d js_types_d">
            <a href="javascript:;" value="1">{:__('Initial development')}</a>
			<a href="javascript:;" value="3">{:__('Replacement')}</a>
			<a href="javascript:;" value="2">{:__('Renewal')}</a>
        </div> -->

        <div class="appeal_foot">
			<?php
				if($info){
					if($info['is_state'] == '2'){
			?>
				<a href="javascript:;" class="appeal_btn js_form_sub" data-text="{:__('Authentication failed, recertification')}" data-loadding="{:__('System audit in progress')}" data-type="new_location">{:__('Authentication failed, recertification')}</a>
			<?php
					echo __('Reasons for failure').$info['check_text'].'<br />';
					}
					if($info['is_state'] == '4'){
				?>
				<a href="javascript:;" class="appeal_btn">{:__('Certification')}</a>
				<?php
					}
					if($info['is_state'] == '1'){
				?>
					<a href="javascript:;" class="appeal_btn">{:__('Certified')}</a>
				<?php
					}
				}else{
			?>
				<a href="javascript:;" class="appeal_btn js_form_sub" data-text="{:__('Authentication')}" data-loadding="{:__('System audit in progress')}" data-type="new_location">{:__('Authentication')}</a>
			<?php
				}
			?>
        </div>

    </form>
	<?php
		}
	?>
</div>

<!--<script type="text/javascript">
    $(function(){
        $('.js_types_d a').click(function(){
            var _id=$(this).attr('value');
            $('.js_type_v').val(_id);
            $('.js_type_t').html($(this).html())
        })
        $('.js_idcard_value').keyup(function(){
            var _val=$(this).val();
            _val=_val.toString().toUpperCase();
            $('.js_idcard_value').val(_val)
        })

        $('.js_n_type').find('option[value="'+$('.js_n_type').attr('value')+'"]').attr('selected',"selected")
    
	})
	
	function get_img_code(){
		$.get("{:url('/index/user/get_card_num')}",{},function(res){
		    res=JSON.parse(res)
			if(res.code==1){
				$('.js_code_img').attr('src','/'+res.img_url);
				$('.card_cookie1').val(res.cookie)
				$('.card_cookie2').val(res.captchaKey)
				$('.card_cookie3').val(res.time1)
			}else{
				layer.msg(res.sms)
			}
		})
	}
	get_img_code()
	$('.js_get_code_btn').click(function(){
		$(this).siblings('img').click()
	})
</script>-->
