<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .input_box{ background: none; overflow: visible; }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
    .moni_select{ border: 1px solid #fff; border-radius: 5px; }
    .body{ padding-bottom: 1.5rem }
    .moni_select .moni_s_down{ max-height: 150px }
    .input_box .input_rr .arr{ margin-top: 17px; margin-left: 7px; margin-right: 0.2rem }
    <?php
        if($type){
    ?>
        .idcard_auth_win{ background: #f4f4f4; height: 100%; overflow: auto; }
        .appeal_foot{ position: relative; }
    <?php
        }
    ?>
	
	.big_tt{height: 34px; color:#fff; background:#1F93B6; font-size: 15px; border-radius:5px; padding:0 10px}
	.big_tt .n{width:16px;height:16px; background:#fff; text-align: center; line-height:16px; border-radius: 50%; font-size:12px; color:#1F93B6; margin-right:5px }
	body .code_img_d .sub_btn{background: none; width:auto; color:#EF436D; border:1px solid #EF436D}
	.tip_text{color:#666666; font-size:14px}
	body .sub_btn.blue{background: #309FC0; margin-left:0px;}
	body .sub_btn{ border-radius: 5px; width: 100%; background: #EF4B7B; color: #fff; line-height: 0.88rem }
	.wi_block{background:#fff; border-radius:5px; padding:10px}
	.requird{color:#EF436D}
	.edit_info_form .icon_upload{width:6.4rem;height:4.08rem;margin-bottom:0.3rem}
	.edit_info_form .icon_upload img.up{height: 100%; object-fit: cover;}
	.edit_info_form .icon_upload{border-radius:8px}
</style>
<div class="pad_tb20 pad_lr20 <?=$type?'idcard_auth_win':''?>">
	<?php
		if(!$is_card){
	?>
		

        <div class="help_title bg_fff margin_t20 clearfix" style="line-height: 0.4rem; padding: 0.6rem 0">
            <div class="text-center f16 pad_tb30 ">
                <div class="pad_tb10 color_999 f14">{:__('Please pass the ID card audit first')}</div>
                <div class="pad_t30">
                    <a href="{:url('/index/user/editIdcard')}" class="sub_btn">{:__('One certification')}</a>
                </div>
            </div>
        </div>

	<?php
		}else{
	?>
    <div class="help_title bg_fff clearfix">
        <img src="__CDN__/assets/img/wap/icon_common_10.png" class="icon">{:__('Two certification')}
    </div>
    <form action="{:url('/index/user/editIdcardTwo')}" class="edit_info_form">
        <div class="flex_ac big_tt margin_tb20">
			金融機構資料
		</div>
		<div class="tip_text " style="color:#333">
			<div>
				請上傳存摺照片檔案，提供之照片須包含戶名與帳號，需於註冊人相符，並確認照片清晰可辨，僅支援上傳JPG/JPEG/PNG/HEIC格式，圖片大小限制10MB以內。
			</div>
		</div>
		
		<div class="flex_ac_jc flex_d pad_t30">
			<?php
				if(!$card || $card['is_state'] == '2'){
			?>
			
			<!-- 如果未上传过 -->
			<a href="javascript:;" class="icon_upload js_up_load_btn">
				<input type="text" name="img" value="" is_required="true" style="width: 0; height: 0; opacity: 0; position: absolute;" empty_tip="{:__('Please upload photos')}">
				<img src="__CDN__/assets/img/wap/card1_wap.png" class="up">
			</a>

			<div class="icon_upload js_img_src hide" style="background-image: url();">
				<a href="javascript:;"><img src="__CDN__/assets/img/dele.png"></a>
			</div>
			<!-- 如果未上传过 -->
			<?php
				}else{
			?>
			<!-- 如果已传  地址给-->
			<div class="icon_upload js_img_src" style="background-image: url(<?=$card['card_img']?>);">
			</div>
			<?php
				}
			?>

			<div class="hide">
				<input type="file" class="js_up_img_input_btn" data-type="img" accept="image/*" multiple data-maxMum="1" data-fileSizeMax="2" onchange="filecountup($(this),this.files)" data-filename="img" />
			</div>
			<?php
				if(!$card || $card['is_state'] == '2'){
			?>
			
			<!-- 如果未上传过 -->
			<a href="javascript:;" class="icon_upload js_up_load_btn2">
				<input type="text" name="img2" value="" is_required="true" style="width: 0; height: 0; opacity: 0; position: absolute;" empty_tip="{:__('Please upload photos')}">
				<img src="__CDN__/assets/img/wap/card2_wap.png" class="up">
			</a>

			<div class="icon_upload js_img_src2 hide" style="background-image: url();">
				<a href="javascript:;"><img src="__CDN__/assets/img/dele.png"></a>
			</div>
			<!-- 如果未上传过 -->
			<?php
				}else{
			?>
			 <!-- 如果已传  地址给-->
			<div class="icon_upload js_img_src" style="background-image: url(<?=$card['card_img']?>);">
			</div>
			<?php
				}
			?>

			<div class="hide">
				<input type="file" class="js_up_img_input_btn2" data-type="img" accept="image/*" multiple data-maxMum="1" data-fileSizeMax="2" onchange="filecountup2($(this),this.files)" data-filename="img" />
			</div>
        
        </div>



        <div class="appeal_foot">
			<?php
				if($card){
					if($card['is_state'] == '2'){
						echo __('Reasons for failure').$card['check_text'].'<br />';
					}
					switch($card['is_state']){
						case 1:
			?>
					<a href="javascript:;" class="appeal_btn">{:__('Certified')}</a>
			<?php
						break;
						case 2:
			?>
				<a href="javascript:;" class="appeal_btn js_form_sub" data-text="{:__('Certification')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Authentication failed, recertification')}</a>
			<?php
						break;
						case 3:
			?>
				<a href="javascript:;" class="appeal_btn">{:__('Certification')}</a>
			<?php
						break;
					}
				}else{
			?>
				<a href="javascript:;" class="appeal_btn js_form_sub" data-text="{:__('Authentication')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Authentication')}</a>
			<?php
				}
			?>
        </div>

    </form>
	<?php
		}
	?>
</div>


<script type="text/javascript">
    $('.js_up_load_btn').click(function(){
        $('.js_up_img_input_btn').click()
    })



    function getImgData(img,dir,max_w,next){
     var image=new Image();
     image.onload=function(){
      var degree=0,drawWidth,drawHeight,width,height;
      drawWidth=max_w>this.naturalWidth?this.naturalWidth:max_w;
      drawHeight=this.naturalHeight*drawWidth/this.naturalWidth;
      
      var canvas=document.createElement('canvas');
      canvas.width=width=drawWidth;
      canvas.height=height=drawHeight; 
      var context=canvas.getContext('2d');
     
      context.drawImage(this,0,0,drawWidth,drawHeight);
      //返回校正图片
      next(canvas.toDataURL("image/jpeg",.7));
     }
     image.src=img;
    }

    //图片上传预览 2018.7.11
    function filecountup(_this,files,count){
        // 文件大小限制
        // 
        console.log(files)
        if(_this.attr('data-fileSizeMax')!=''){
            var max=parseFloat(_this.attr('data-fileSizeMax'))*1024*1024;
            if(files[0].size > max){
                tip_show("文件不能大于"+parseFloat(_this.attr('data-fileSizeMax'))+"M","2");
                return false;
            } 
        }


        var file = files[0];
        var reader = new FileReader();
        reader.onloadend = function () {
            // console.log(reader.result);


            getImgData(reader.result,'',1000,function(data){
                console.log(data)
                $('.js_up_load_btn').addClass('hide').find('input').val(data);
                $('.js_img_src').removeClass('hide').css('background-image','url('+data+')')
            })

        }
        if (file) {
            reader.readAsDataURL(file);
        }
       
    };


    $('.js_img_src a').click(function(){
        var a=layer.confirm("{:__('Are you sure you want to delete it?')}", {
          title:false,
          area:['6rem', 'auto'],
          skin: 'my_confirm',
          btn: ["{:__('Confirm')}","{:__('Cancel')}"], //按钮
          btnAlign:'c'
        }, function(){
            layer.close(a);
            $('.js_up_load_btn').removeClass('hide').find('input').val('');
            $('.js_img_src').addClass('hide')
        });
    })






    $('.js_up_load_btn2').click(function(){
        $('.js_up_img_input_btn2').click()
    })

    //图片上传预览 2018.7.11
    function filecountup2(_this,files,count){
        // 文件大小限制
        // 
        console.log(files)
        if(_this.attr('data-fileSizeMax')!=''){
            var max=parseFloat(_this.attr('data-fileSizeMax'))*1024*1024;
            if(files[0].size > max){
                tip_show("文件不能大于"+parseFloat(_this.attr('data-fileSizeMax'))+"M","2");
                return false;
            } 
        }


        var file = files[0];
        var reader = new FileReader();
        reader.onloadend = function () {
            console.log(reader.result);


            getImgData(reader.result,'',1000,function(data){
                console.log(data)
                $('.js_up_load_btn2').addClass('hide').find('input').val(data);
                $('.js_img_src2').removeClass('hide').css('background-image','url('+data+')')
            })

            // $('.js_up_load_btn2').addClass('hide').find('input').val(reader.result);
            // $('.js_img_src2').removeClass('hide').css('background-image','url('+reader.result+')')

        }
        if (file) {
            reader.readAsDataURL(file);
        }
       
    };


    $('.js_img_src2 a').click(function(){
        var a=layer.confirm("{:__('Are you sure you want to delete it?')}", {
          title:false,
          area:['6rem', 'auto'],
          skin: 'my_confirm',
          btn: ["{:__('Confirm')}","{:__('Cancel')}"], //按钮
          btnAlign:'c'
        }, function(){
            layer.close(a);
            $('.js_up_load_btn2').removeClass('hide').find('input').val('');
            $('.js_img_src2').addClass('hide')
        });
    })
</script>