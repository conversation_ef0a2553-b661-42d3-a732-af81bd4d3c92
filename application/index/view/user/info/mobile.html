<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 pad_b40">
	<div class="color_red pad_t10 my_close_win_title">{:__('Replacement of mobile')}</div>
	<form action="<?=url('index/user/editMobile')?>" autocomplete="off" class="small_input" style="padding: 30px 100px 0 100px">
        <div class="input_box full_width">
            <div class="input_tt">{:__('Current login phone authentication code')} <span class="color_999">(<?=substr_replace($user['mobile'],'****',2,4)?>) <input type="hidden" class="js_o_mobile" value="<?=$user['mobile']?>"></span></div>
            <div class="input_rr">
                <a href="javascript:;" class="get_code2" data-mobile="js_o_mobile" data-url="{:url('/index/login/getCode',array('type'=>'3'))}">{:__('Get code')}</a>
                <input type="text" class="js_code_img_code" name="code" data-type="number" maxlength="6" is_required="true" empty_tip="{:__('Please enter mobile verification code')}">
            </div>
        </div>
        <div class="input_box full_width">
            <div class="input_tt">{:__('New cell phone number')}</div>
            <div class="input_rr">
                <input type="text" name="mobile" data-type="mobile" class="js_code_input" maxlength="10" is_required="true" empty_tip="{:__('Please enter your cell phone number')}">
            </div>
        </div>

        <div class="input_box full_width">
            <div class="input_tt">{:__('New Mobile Phone Verification Code')}</div>
            <div class="input_rr">
                <a href="javascript:;" class="get_code" data-is_code_img="1" data-url="{:url('/index/login/getCode',array('type'=>'8'))}">{:__('Get code')}</a>
                <input type="text" name="new_code" data-type="number" maxlength="6" is_required="true" empty_tip="{:__('Please enter mobile verification code')}">
            </div>
        </div>
        <div class="text-center pad_t30">
            <a href="javascript:;" class="sub_btn small js_form_sub" data-text="{:__('Confirm')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Confirm')}</a>
        </div>

    </form>
</div>

<script type="text/javascript">
    var timeset2=null;
    var time2=60;
    var is_img_check2=false;
    // 通用获取手机验证码
    var check2=null;
    $('.get_code2').click(function(){
        var _this=$(this);
        if(_this.hasClass('disabled')){
            return false
        }
        var _url=_this.attr('data-url');
        var _mobile=_this.parents('form').find('.js_o_mobile').val();

        if(!is_img_check2){
            $('.js_need_widgets__img2').addClass('active');
            if(check2==null){
                check2 = WIDGETS.imgSmoothCheck({
                    selector: "#select2",
                    data: check_imgs,
                    imgHeight: 100,
                    imgWidth: 200,
                    allowableErrorValue: 3,
                    success: function () {
                        $('.js_need_widgets__img2').removeClass('active');
                        // tip_show('验证成功',1);
                        check2.refresh();
                        is_img_check2=true;
                        _this.click();
                    },
                    error: function (res) {
                        setTimeout(function(){
                            check2.refresh();
                        },30)
                    }
                });
            }
            return false
        }
        is_img_check2=false;

        var _data={
            mobile:_mobile
        }

        _this.addClass('disabled').html("{:__('Sending')}");
        $.post(_url,_data,function(data){
            if(data.code=="1"){
                tip_show(data.msg, '1');
                    _this.html('60s');
                    timeset2 = setInterval(function(){
                        time2 = time2-1;
                        _this.html(time2+'s');
                        if(time2 == 0){
                          time2 = 60;
                          clearInterval(timeset2);
                          _this.removeClass('disabled').html("{:__('Get code')}")
                        } 
                    }, 1000)       
            }else{
                tip_show(data.msg, '2');
                _this.removeClass('disabled').html("{:__('Get code')}")
            }
        })
    })
</script>