<div class="container">
    <div class="user_main margin_tb40 clearfix">
        <div class="user_main_line"></div>
        <div class="user_art_left pull-left">
            <div class="n_tt">
                <img src="__CDN__/assets/img/pc/icon_left_list_up.png" class="arr pull-right">
                <img src="__CDN__/assets/img/pc/fapiao.png" class="icon">
                發票管理
            </div>
            <div class="n_tt_menu">
				<a href="<?=url('index/user/invoice')?>" class="active">上傳發票</a>
				<a href="<?=url('index/user/invoice_recovery')?>" class="">回收發票</a>
				<a href="<?=url('index/user/invoice_my')?>" class="">我的發票</a>
				<a href="<?=url('index/user/invoice_tutorial')?>" class="">發票教程</a>
            </div>
        </div>
        <div class="pad_b20 over_hide">
            <div class="user_art_title">
                上傳發票
            </div>
            <div class="pad_lr20 pad_tb20">
            	<form action="<?=url('user/invoice_submit')?>" class="user_invoice" autocomplete="off">

                    <div class="input_box margin_t10">
                        <div class="input_tt">訂單號:</div>
                        <div class="input_rr">
                            <input type="text" name="order_no" value="" is_required="true" placeholder="請輸入訂單號">
                        </div>
                    </div>

                    <div class="input_box margin_t10">
                        <div class="input_tt">選擇公司:</div>
                        <div class="input_rr">
                            <select name="company" is_required="true" class="js_company" placeholder="请選擇公司">
                            	<option value="">请選擇</option>
								<?php
									foreach($list as $ls){
								?>
									<option value="<?=$ls['id']?>"><?=$ls['title']?></option>
								<?php
									}
								?>
                            </select>
                        </div>
						<script type="text/javascript">
							$(".js_company").val(<?=$c?>);
						</script>
                    </div>
                    <div class="input_box margin_t10">
                        <div class="input_tt">發票金額:</div>
                        <div class="input_rr">
                            <input type="number" name="money" value="" data-small ="500" is_required="true" placeholder="請輸入發票金額">
                        </div>
                    </div>

                    <div class="input_box margin_t10">
                        <div class="input_tt">發票類型:</div>
                        <div class="input_rr">
                            <select name="type" is_required="true" placeholder="请選擇發票類型">
                            	<option value="">请選擇</option>
                            	<option value="0">普票</option>
                            	<option value="1">專票</option>
                            </select>
                        </div>
                    </div>

                    <div class="input_box clearfix margin_t10" style="height: auto;">
                        <div class="input_tt">上傳圖片:</div>
                        <div class="input_rr" style="overflow: visible; float: left; width: 300px">

                        	<div class="img_div">
                        		<div class="js_up_load_btn2">
	                                <input type="text" name="img" value=""  is_required="true" empty_tip="{:__('Please upload photos')}">
	                                <img class="up_icon" src="__CDN__/assets/img/pc/upload.png">
	                                <div class="pp">{:__('Click Upload Photos')}</div>
	                            </div>

	                            <img class="imgs js_up_imgs hide"></img>
	                            <a href="javascript:;" class="js_re_up hide"><img src="__CDN__/assets/img/pc/re.png">重新上傳</a>
                        	</div>

                        </div>
                    </div>

                    <div class="input_box margin_t10">
                        <div class="input_tt pad_t10"></div>
                        <div class="input_rr">
                            <a href="javascript:;" class="sub_btn small js_form_sub" data-text="確認上傳" data-loadding="{:__('Submitting')}" data-type="new_location">確認上傳</a>
                        </div>
                    </div>
                </form>


                <div class="hide">
                    <input type="file" class="js_up_img_input_btn2" data-type="img" accept="image/*" multiple data-maxMum="1" data-fileSizeMax="2" onchange="filecountup2($(this),this.files)" data-filename="img" />
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
	$('.js_id_card_img2').click(function(){
        layer.open({
          type: 1,
          title: false,
          closeBtn: 1,
          area: ['1000px',$('#img_prev2 img').height()+'px'],
          skin: 'layui-layer-nobg', //没有背景色
          shadeClose: true,
          content: $('.js_hide_img2').html()
        });
    })

    $('.js_up_load_btn2,.js_re_up').click(function(){
        $('.js_up_img_input_btn2').click()
    })



    function getImgData(img,dir,max_w,next){
     var image=new Image();
     image.onload=function(){
      var degree=0,drawWidth,drawHeight,width,height;
      drawWidth=max_w>this.naturalWidth?this.naturalWidth:max_w;
      drawHeight=this.naturalHeight*drawWidth/this.naturalWidth;
      
      var canvas=document.createElement('canvas');
      canvas.width=width=drawWidth;
      canvas.height=height=drawHeight; 
      var context=canvas.getContext('2d');
     
      context.drawImage(this,0,0,drawWidth,drawHeight);
      //返回校正图片
      next(canvas.toDataURL("image/jpeg",.7));
     }
     image.src=img;
    }

    //图片上传预览 2018.7.11
    function filecountup2(_this,files,count){
        // 文件大小限制
        // 
        console.log(files)
        if(_this.attr('data-fileSizeMax')!=''){
            var max=parseFloat(_this.attr('data-fileSizeMax'))*1024*1024;
            if(files[0].size > max){
                tip_show("文件不能大于"+parseFloat(_this.attr('data-fileSizeMax'))+"M","2");
                return false;
            } 
        }


        var file = files[0];
        var reader = new FileReader();
        reader.onloadend = function () {
            // console.log(reader.result);
            // $('.js_up_load_btn2').addClass('hide').find('input').val(reader.result);
            // $('.js_img_src2').removeClass('hide').css('background-image','url('+reader.result+')')
            getImgData(reader.result,'',2000,function(data){
                console.log(data)
                $('.js_up_load_btn2').addClass('hide').find('input').val(data);
                $('.js_up_imgs').removeClass('hide').attr('src',data);
                $('.js_re_up').removeClass('hide')
            })
        }
        if (file) {
            reader.readAsDataURL(file);
        }
    };


    $('.js_img_src2 a').click(function(){
        var a=layer.confirm("{:__('Are you sure you want to delete it?')}", {
          title:false,
          area:['500px', 'auto'],
          skin: 'my_confirm',
          btn: ["{:__('Confirm')}","{:__('Cancel')}"], //按钮
          btnAlign:'c'
        }, function(){
            layer.close(a);
            $('.js_up_load_btn2').removeClass('hide').find('input').val('');
            $('.js_up_imgs').addClass('hide')
        });
    })
</script>