
<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }

    .invoice_list .box{ margin-bottom: 0.2rem; font-size: 0.24rem; color: #656565; background: #fff; border-radius: 5px;}
    .invoice_list .box .title{ line-height: 0.76rem; padding: 0 0.2rem; border-bottom: 1px solid #f1f1f1}
    .invoice_list .box .title span{ color: #333 }
    .invoice_list .box .con{ padding: 0.1rem 0.2rem; color: #333 }
    .invoice_list .box .item{ line-height:0.56rem; font-size: 0.24rem; }
    .invoice_list .box .title.f{ border: none; border-top: 1px solid #f1f1f1; text-align: center; display: block; color: #F8436F }
</style>
<div class="pad_t20 pad_lr20">
    <div class="common_nav clearfix">
        <a href="<?=url('index/user/invoice')?>" class="">上傳發票</a>
        <a href="<?=url('index/user/invoice_recovery')?>" class="">回收發票</a>
        <a href="<?=url('index/user/invoice_my')?>" class="active">我的發票</a>
        <a href="<?=url('index/user/invoice_tutorial')?>" class="">發票教程</a>
    </div>

    <div class="invoice_list margin_t20 js_scroll_more_list" data-url="{:url('index/user/myinvoice_lists')}">
        {include file="user/invoice/m_mylists" /}
    </div>

    <!-- 上拉加载的 无数据我自己判断 -->
    <div class="loadding_d hide">
        <img src='__CDN__/assets/img/loading.png'>
        <span>{:__('Pull-up Load More')}</span>
    </div>
    <div class="js_no_data pad_tb30 hide">
        {include file="common/nodata" /}
    </div>
    <!-- 上拉加载的 无数据我自己判断 -->
</div>



<script type="text/javascript">
    $(document).on('click','.js_view_img',function(){

        var image=new Image();
        var w=$(window).width();
        var max_h=$(window).height()-100;
        image.onload=function(){
            if(image.width>=image.height){
                var h=image.height*w/image.width;
                if(h>max_h){
                    var h=max_h;
                    w=image.width*h/image.height
                }
            }else{
                var h=max_h;
                w=image.width*h/image.height
            }
            
            layer.open({
              type: 1,
              title: false,
              closeBtn: 1,
              area: [w+'px',h+'px'],
              skin: '', //没有背景色
              shadeClose: true,
              content: '<img style="width:100%;" src="'+image.src+'">'
            });
        }
        image.src=$(this).attr('data-url');
    })
</script>
