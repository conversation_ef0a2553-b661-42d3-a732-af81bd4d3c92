<div class="container">
    <div class="user_main margin_tb40 clearfix">
        <div class="user_main_line"></div>
        <div class="user_art_left pull-left">
            <div class="n_tt">
                <img src="__CDN__/assets/img/pc/icon_left_list_up.png" class="arr pull-right">
                <img src="__CDN__/assets/img/pc/fapiao.png" class="icon">
                發票管理
            </div>
            <div class="n_tt_menu">
				<a href="<?=url('index/user/invoice')?>" class="">上傳發票</a>
				<a href="<?=url('index/user/invoice_recovery')?>" class="">回收發票</a>
				<a href="<?=url('index/user/invoice_my')?>" class="active">我的發票</a>
				<a href="<?=url('index/user/invoice_tutorial')?>" class="">發票教程</a>
            </div>
        </div>
        <div class="pad_b20 over_hide">
            <div class="user_art_title">
                我的發票
            </div>
            <div class="pad_lr20 pad_tb20 clearfix">

            	<table class="common_table table margin_t10 text-center">
                   	<tbody>
                       	<tr>
  							<th>訂單號</th>
  							<th>發票金額</th>
  							<th>處理進度</th>
  							<th>返現金額</th>
  							<th>上傳時間</th>
  							<th>返利時間</th>
  							<th>發票照片</th>
                     	</tr>
						<?php
							foreach($list as $ls){
						?>
							<tr>
								<td><?=$ls['order_no']?></td>
								<td><?=$ls['money']?></td>
								<td><?=$invoice_status[$ls['invoice_status']]?></td>
								<td class="color_red"><?=$ls['rebate_money']?></td>
								<td><?=date('Y/m/d H:i',$ls['createtime'])?></td>
								<td><?=$ls['rebate_time']?date('Y/m/d H:i',$ls['rebate_time']):'-'?></td>
								<td><a href="javascript:;" style="min-width: 50px" class="sub_btn js_view_img small" data-url="<?=$ls['invoice_image']?>">查看</a></td>
							</tr>
						<?php
							}
						?>
                     	

                 	</tbody>
            	</table>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
	$('.js_view_img').click(function(){

		var image=new Image();
		var w=1000;
		var max_h=$(window).height()-100;
     	image.onload=function(){
     		if(image.width>=image.height){
     			var h=image.height*w/image.width;
     			if(h>max_h){
     				var h=max_h;
     				w=image.width*h/image.height
     			}
     		}else{
     			var h=max_h;
     			w=image.width*h/image.height
     		}
     		
     		layer.open({
	          type: 1,
	          title: false,
	          closeBtn: 1,
	          area: [w+'px',h+'px'],
	          skin: '', //没有背景色
	          shadeClose: true,
	          content: '<img style="width:100%;" src="'+image.src+'">'
	        });
     	}
     	image.src=$(this).attr('data-url');
    })
</script>