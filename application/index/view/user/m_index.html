<style type="text/css">
    .body{ padding-bottom: 1rem!important }
</style>
<div class="clearfix user_info pad_lr20 pad_t40 pad_b30">
    <a class="pull-right yhfx" href="{:url('/index/index/jdraw')}"><img src="__CDN__/assets/img/wap/new_index/jdraw-icon.png" style="width: 38px;height: 24px"><div class="ellipsis">{:__('To-draw return')}</div></a>
    <div class="avatar_div pull-left js_ajax_win" data-height="450" href="{:url('/index/user/edit_avatar')}">
        <img src="__CDN__/assets/img/wap/avatar.png" class="avatar">
        <div class="vip">
            <img src="__CDN__/assets/img/wap/vip.png">
            <span><?=$user['level']?></span>
        </div>
    </div>
	<?php
		if($user){
	?>
		<!-- 登陆 -->
		<div class="over_hide">
			<div class="nickname ellipsis"><?=$user['username']?></div>
			<div class="auth clearfix">
				<a href="{:url('/index/user/editMobile')}" class="active" ><img src="__CDN__/assets/img/wap/auth1.png" class="img1"><img src="__CDN__/assets/img/wap/auth1a.png" class="img2"></a>
				<a href="{:url('/index/user/editIdcard')}" class=" <?=$user['is_card']?'active':'';?>" ><img src="__CDN__/assets/img/wap/auth2.png" class="img1"><img src="__CDN__/assets/img/wap/auth2a.png" class="img2"></a>
				<a href="{:url('/index/user/editIdcardTwo')}" class=" <?=$user['is_card_img']?'active':'';?>" ><img src="__CDN__/assets/img/wap/auth3.png" class="img1"><img src="__CDN__/assets/img/wap/auth3a.png" class="img2"></a>
				<span class="pad_l10">{:__('Amoy money')}：<?=$user['money']?></span>
				<span class="pad_l10">{:__('Shopping gold')}：<?=$user['buy_gold']?></span>
			</div>
		</div>
		<!-- 登陆 -->
	<?php
		}else{
	?>
		 <!-- 未登录 -->
		<div class="over_hide">
			<a href="{:url('/index/login/login')}" class="no_login">登录/注册</a>
		</div>
		<!-- 未登录 -->
	<?php
		}
	?>
</div>
<div class="pad_lr20 pad_b30">
    <div class="bg_fff u_order_nav">
        <div class="title clearfix pad_lr20">
            <a href="{:url('/index/user/order')}" class="more pull-right">{:__('All orders')} <img src="__CDN__/assets/img/wap/arr.png"></a>
            {:__('My order')}
        </div>
        <div class="items text-center clearfix">
			<?php
				$set_type = '1';
				if($max_num){
					$set_type = $max_num;
				}
			?>
            <a class="item" href="<?=url('index/user/order',array('_type'=>'1','set_type'=>$set_type))?>">
                <img src="__CDN__/assets/img/wap/order1.png">
                <span><?=$unpaid?></span>
                <div>{:__('Unpaid')}</div>
            </a>
            <a class="item" href="{:url('index/user/order',array('_type'=>'2'))}">
                <img src="__CDN__/assets/img/wap/order2.png">
                <!-- <span>1</span> -->
                <div>{:__('Paid')}</div>
            </a>
            <a class="item" href="{:url('index/user/order',array('_type'=>'3'))}">
                <img src="__CDN__/assets/img/wap/order3.png">
                <!-- <span>1</span> -->
                <div>{:__('Completed')}</div>
            </a>
            <a class="item" href="{:url('index/user/order',array('_type'=>'4'))}">
                <img src="__CDN__/assets/img/wap/order4.png">
                <!-- <span>1</span> -->
                <div>{:__('Refunded')}</div>
            </a>
        </div>
    </div>

    <div class="pad_lr20 pad_tb20 bg_fff u_notice margin_t20 clearfix">
        <img class="icon pull-left margin_r20" src="__CDN__/assets/img/wap/tongzhi.png">
        <div class="l over_hide">
		<?php
			foreach($tongzhi as $key=>$ls){
				if($key <= '1'){
		?>
			<a href="<?=url('/index/user/notice_detail')?>?id=<?=$ls['id']?>" class="ellipsis"><?=$ls['title']?></a>
		<?php
				}
			}
		?>
		<a href="<?=url('index/user/notice')?>">查看更多>></a>
        </div>
    </div>
	<div class="pad_lr20 pad_tb20 bg_fff u_notice margin_t20 clearfix">
        <img class="icon pull-left margin_r20" src="__CDN__/assets/img/wap/gonggao.png">
        <div class="l over_hide">
		<?php
			foreach($gonggao as $key=>$ls){
				if($key <= '1'){
		?>
			<a href="<?=url('/index/user/notice_detail')?>?id=<?=$ls['id']?>" class="ellipsis"><?=$ls['title']?></a>
		<?php
				}
			}
		?>
		<a href="<?=url('index/user/notice').'?type=1'?>">查看更多>></a>
        </div>
    </div>
	
	<style>
		.serverwrap {
			display: flex;
			flex-direction: row;
			flex-wrap: wrap;
			padding: 0.5rem 0.4rem;
		}
	
		.serverwrap .serveritem {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 50%;
			padding-top: 0.4rem;
			padding-bottom: 0.5rem;
			box-sizing: border-box;
			color:#333333;
	
		}
	
		.serverwrap .serveritem:nth-child(1) {
			border-right: 1px solid #eee;
			border-bottom: 1px solid #eee;
		}
	
		.serverwrap .serveritem:nth-child(2) {
			border-bottom: 1px solid #eee;
		}
	
		.serverwrap .serveritem:nth-child(3) {
			border-right: 1px solid #eee;
		}
	
		.serverwrap .serveritem img {
			width: 1.5rem;
			height: 1.5rem;
		}
	
		.serverwrap .serveritem .titles {
			font-size: 0.16rem;
		}
	
		.server-more {
			display: block;
			width: fit-content;
			padding: 0 0.5rem;
			box-sizing: border-box;
			border: 1px solid #EF436D;
			height: 32px;
			line-height: 32px;
			border-radius: 32px;
			font-size: 0.24rem;
			color: #EE436D;
			text-align: center;
			box-shadow: 0 5px 8px rgba(238, 67, 109, 0.1) !important;
			margin: 0rem auto 0.5rem auto;
		}
	</style>
	<div class="bg_fff u_order_nav pad_b10 margin_t20">
	    <div class="title clearfix pad_lr20">
	        {:__('Popular functions')}
		</div>
		<div class="serverwrap ">
			<a class="serveritem" href="<?=url('index/daifu/nav')?>">
				<img src="__CDN__/assets/img/wap/images/icon_business01.png" />
				<div class="titles">商品代購專區</div>
			</a>
			<a class="serveritem"   href="<?=url('index/daifu/nav')?>">
				<img src="__CDN__/assets/img/wap/images/icon_business02.png" />
				<div class="titles">直播點數專區</div>
			</a>
			<a class="serveritem"   href="<?=url('index/daifu/nav')?>">
				<img src="__CDN__/assets/img/wap/images/icon_business03.png" />
				<div class="titles">遊戲點數專區</div>
			</a>
			<a class="serveritem"   href="<?=url('index/daifu/nav')?>">
				<img src="__CDN__/assets/img/wap/images/icon_business04.png" />
				<div class="titles">其他代購專區</div>
			</a>
		</div>
		<a class="server-more"   href="<?=url('index/daifu/nav')?>">
			查看更多>>
		</a>
	</div>
	
    <!--<div class="bg_fff u_order_nav margin_t20">
        <div class="title clearfix pad_lr20">
            {:__('Popular functions')}
			<div class="fb-like" style="float: right;line-height: 2.5;" data-href="https://www.paybei.com.tw/" data-width="" data-layout="button" data-action="like" data-size="large" data-share="true"></div>
				<div id="fb-root"></div>
				<script async defer crossorigin="anonymous" src="https://connect.facebook.net/zh_CN/sdk.js#xfbml=1&version=v7.0" nonce="KdPpPI0i"></script>
        </div>
        <div class="items items_func text-center clearfix">
            <a class="item" href="{:url('/index/daifu/index')}">
                <img src="__CDN__/assets/img/wap/pay0.png">
                <div class="ellipsis">{:__('Alibaba pays')}</div>
            </a>
            <a class="item" href="{:url('/index/daifu/zhikouling')}">
                <img src="__CDN__/assets/img/wap/pay2.png">
                <div class="ellipsis">{:__('Squeak')}</div>
            </a>
            <a class="item" href="{:url('/index/daifu/alipay')}">
                <img src="__CDN__/assets/img/wap/pay3.png">
                <div class="ellipsis">{:__('Alipay Reserve Value')}</div>
            </a>
            <a class="item" href="{:url('/index/daifu/wechatpay')}">
                <img src="__CDN__/assets/img/wap/pay4.png">
                <div class="ellipsis">{:__('WeChat storage value')}</div>
            </a>
            <a class="item" href="{:url('/index/daifu/game')}">
                <img src="__CDN__/assets/img/wap/pay5.png">
                <div class="ellipsis">{:__('Game/Video Storage')}</div>
            </a>
            <a class="item" href="{:url('/index/daifu/other')}">
                <img src="__CDN__/assets/img/wap/pay6.png">
                <div class="ellipsis">{:__('Other payment')}</div>
            </a>
            <a class="item" href="{:url('/index/daifu/charge')}">
                <img src="__CDN__/assets/img/wap/pay7.png">
                <div class="ellipsis">{:__('Coin recharge')}</div>
            </a>
			<a class="item" href="{:url('/index/daifu/usdt')}">
                <img src="__CDN__/assets/img/wap/pay9.png">
                <div class="ellipsis">USDT</div>
            </a>
			<a class="item" href="{:url('/index/user/invoice_my')}">
                <img src="__CDN__/assets/img/wap/pay10.png">
                <div class="ellipsis">會員福利</div>
            </a>
			<a class="item" href="{:url('/index/user/prize')}">
                <img src="__CDN__/assets/img/wap/pay10.png">
                <div class="ellipsis">中獎清單</div>
            </a>
        </div>
    </div>-->
    <a href="{:url('/index/helps/index')}" class="bg_fff margin_t20 u_help">
        <img src="__CDN__/assets/img/wap/help.png"> {:__('Help center')}
    </a>
    <div class="bg_fff u_order_nav margin_t20">
        <div class="title clearfix pad_lr20">
            {:__('Common functions')}
        </div>
        <div class="items items_func2 text-center clearfix">
            <a class="item" href="{:url('/index/user/capital/set_type/7')}">
                <img src="__CDN__/assets/img/wap/icon_common_01.png">
                <div>{:__('Ambush coins')}</div>
            </a>
            <a class="item" href="{:url('/index/user/capital_gold')}">
                <img src="__CDN__/assets/img/wap/icon_common_02.png">
                <div>{:__('Shopping Gold Details')}</div>
            </a>
            <a class="item" href="{:url('/index/user/capital_back')}">
                <img src="__CDN__/assets/img/wap/icon_common_03.png">
                <div>{:__('Details of rebate')}</div>
            </a>
            <a class="item" href="{:url('/index/user/coupon')}">
                <img src="__CDN__/assets/img/wap/new_index/yhq_icon.png">
                <div>优惠券</div>
            </a>
            <a class="item" href="{:url('/index/user/password')}">
                <img src="__CDN__/assets/img/wap/icon_common_05.png">
                <div>{:__('Modify password')}</div>
            </a>
            <a class="item" href="{:url('/index/user/editMobile')}">
                <img src="__CDN__/assets/img/wap/icon_common_06.png">
                <div>{:__('Edit mobile')}</div>
            </a>
			 <a class="item" href="{:url('/index/user/rate_fun')}">
                <img src="__CDN__/assets/img/wap/icon_common_11.png">
                <div>{:__('Rate')}</div>
            </a>
            <a class="item" href="{:url('/index/user/appeal')}">
                <img src="__CDN__/assets/img/wap/icon_common_07.png">
                <div>{:__('Statement list')}</div>
            </a>
            <!-- <a class="item" href="{:url('/index/user/order')}">
                <img src="__CDN__/assets/img/wap/icon_common_08.png">
                <div>{:__('Contact customer')}</div>
            </a> -->

            <a class="item js_ajax_win" href="{:url('/index/user/customer')}">
                <img src="__CDN__/assets/img/wap/icon_common_08.png">
                <div>{:__('Contact customer')}</div>
            </a>


            <a class="item" href="{:url('//index/user/editIdcard')}">
                <img src="__CDN__/assets/img/wap/icon_common_09.png">
                <div>{:__('One certification')}</div>
            </a>
            <!--<a class="item" href="{:url('//index/user/editIdcardTwo')}">
                <img src="__CDN__/assets/img/wap/icon_common_10.png">
                <div>{:__('Two certification')}</div>
            </a>-->
           
			<a class="item" href="{:url('/index/account/index')}">
                <img src="__CDN__/assets/img/wap/foot4.png">
                <div>{:__('Account management')}</div>
            </a>
			<a class="item" href="{:url('/index/account/address')}">
			    <img src="__CDN__/assets/img/wap/icon_dizhi.png">
			    <div>地址管理</div>
			</a>
			<a class="item" href="{:url('/index/transportation/no_warehouse')}">
			    <img src="__CDN__/assets/img/wap/jiyun.png">
			    <div>集運訂單</div>
			</a>
			<?php
				if(!$lineid){
			?>
				<a class="item js_get_line" href="javascript:;" data-url="{:url('/index/Line/line_binding')}">
					<img src="__CDN__/assets/img/wap/line.png">
					<div>綁定LINE</div>
				</a>
			<?php
				}else{
			?>
				<a class="item " href="javascript:;" >
					<img src="__CDN__/assets/img/wap/line.png">
					<div>已綁定</div>
				</a>
				
			<?php
				}
			?>
			<a class="item" href="{:url('/index/user/share')}">
                <img src="__CDN__/assets/img/wap/icon_common_04.png">
                <div>{:__('My promotion')}</div>
            </a>
        </div>
    </div>
	<a href="{:url('/index/login/logout')}" class="margin_t20 u_help" style="background:#f16184;color: #f5f4f4;">
       登     出
    </a>
</div>




<script>
let is_ajax_get=false;
$('.js_get_line').click(function(){
	if(is_ajax_get){return false;}
	is_ajax_get=true;
	$.post($(this).attr('data-url'),{},function(res){
		if(res.code==1){
			//window.open(res.url)
			location.href=res.url
			is_ajax_get=false;
		}else{
			is_ajax_get=false;
		}
	})
})
</script>

<!-- 新增弹窗 -->
<?php
	if($product){
?>
    <style type="text/css">
        .my_new_win{ display: block; width: 90%; height: auto; position: fixed; left: 5%; top: 50%; margin-top: -100px; background: #fff; z-index: 200; border-radius: 5px; border-left: 3px solid #ff3267; padding: 20px; color: #333; transition: 0.3s; -webkit-transition: 0.3s; }
        .my_new_win.remove{ transform: scale(0); -webkit-transform: scale(0); opacity: 0 }
        .my_new_win .win_close{ display: block; width: 30px; height: 30px; text-align: center; line-height: 30px; position: absolute; right: 5px; top: 5px; z-index: 10 }
        .my_new_win .win_close img{ width: 14px; }
        .my_new_win .title{ font-size: 16px; font-weight: bold; padding-bottom: 10px;  }
        .my_new_win .item{ font-size: 14px; line-height: 22px;}
        .my_new_win .view_btn{ padding-top: 5px; color: #ff3267 }
        .my_new_win .view_btn:hover{ text-decoration: underline; }
        .my_new_win_mark{ background: rgba(0,0,0,0.7); position: fixed; left: 0; top: 0; width: 100%; height: 100%; z-index: 199 }
    </style>
    <div class="my_new_win">
        <a href="javascript:;" class="win_close js_win_close"><img src="__CDN__/assets/img/pc/close2.png"></a>
        <div class="title">小貝已幫您錄入了代付商品清單</div>
        <div class="item">
            <?=$product['title']?>
        </div>
        <div class="item">金額：<b class="color_red"><?=$product['price']?></b>RMB</div>
        <div class="item">姓名：<?=$product['other_name']?></div>
        <div class="item">帳號：<?=$product['other_account']?></div>
        <div class="js_btns">
        <a href="<?=url('index/daifu/index',array('type'=>'1','set_type'=>'0'))?>" class="view_btn">查看</a>
        </div>
    </div>
    <div class="my_new_win_mark"></div>
    <script type="text/javascript">
        $('.js_win_close').click(function(){
            var _this=$(this);
            _this.parents('.my_new_win').addClass('remove');
            $('.my_new_win_mark').remove();
            setTimeout(function(){
                _this.parents('.my_new_win').remove();
            },300)
        })
        $('.js_btns a').click(function(){
            $.post("",function(data){
                if(data.status==1){
                    window.location.href=data.info
                }
            })
        })
    </script>
<?php
	}
?>
