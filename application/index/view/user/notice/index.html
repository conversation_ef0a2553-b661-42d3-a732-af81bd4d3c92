<div class="container">
	<div class="user_main margin_tb40 clearfix">
		<div class="user_main_line"></div>
		<div class="user_art_left pull-left">
			<div class="n_tt">
				<img src="__CDN__/assets/img/pc/icon_left_list_up.png" class="arr pull-right">
				<img src="__CDN__/assets/img/pc/icon_left_list_message.png" class="icon">
				{:__('Message center')}
			</div>
			<div class="n_tt_menu">
				<a href="<?=url('index/user/notice')?>" class="<?=$type?'':'active'?>">{:__('Station notice')}</a>
				<a href="<?=url('index/user/notice').'?type=1'?>" class="<?=$type?'active':''?>">{:__('Platform Bulletin')}</a>
				<a href="<?=url('index/user/site_map')?>" class="<?=$type?'active':''?>">網站地圖</a>
			</div>
		</div>
		<div class="pad_b20 over_hide">
			<div class="user_art_title">
				{:__('Station notice')}
				<!-- {:__('Platform Bulletin')} -->
			</div>
			<div class="user_art_list">
				<?php
					foreach($list as $ls){
				?>
					<a href="<?=url('index/user/notice_detail').'?id='.$ls['id']?>&type=<?=$type?>" class="box clearfix">
						<span class="pull-right margin_l20 color_999"><?=date('Y-m-d',$ls['createtime'])?></span>
						<div class="ellipsis"><?=$ls['title']?></div>
					</a>
				<?php
					}
				?>
			</div>
			<?php
				if($list){
			?>
				{include file="common/pages" /}
			<?php
				}else{
			?>
				{include file="common/nodata" /}
			<?php
				}
			?>
		</div>
	</div>
</div>