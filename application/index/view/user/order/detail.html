<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 ">
	<div class="order_detail">
		<div class="order_title">
			{:__('Order details')}
		</div>
		<table class="common_table table margin_t10 text-center">
			<tbody>
				<tr>
					<th class="l" colspan="2">{:__('Order number')}：<?=$order['order']['order_no']?></th>
					<th colspan="2">{:__('Order status')}：<span class="color_red"><?=$site['order_status'][$order['order']['order_status']]?></span></th>
					<th class="r" colspan="2">{:__('Order time')}:<?=date('Y-m-d H:i:s',$order['order']['createtime'])?></th>
				</tr>
				<tr>
					<td>{:__('Order amount')}</td>
					<td>{:__('Coin deduction')}</td>
					<td>{:__('Rate')}</td>
					<td>{:__('Gold Deduction')}</td>
					<td>{:__('Handling fee')}</td>
					<td>{:__('Need TWD')}</td>
				</tr>
				<tr class="color_666">
					<td class="color_red"><?=$order['order']['rmb_money']?>RMB</td>
					<td><?=$order['order']['balance_money']?></td>
					<td><?=$order['order']['exchange']?></td>
					<td><?=empty($order['order']['gold_money'])?'0':$order['order']['gold_money']?></td>
					<td><?=$order['order']['service']?></td>
					<td class="color_red"><?=$order['order']['actual_money']?>TWD</td>
				</tr>
				<tr class="color_666">
					<td colspan="6">{:__('Formula for calculating NT$payable')}：（<?=$order['order']['rmb_money']?>-<?=$order['order']['balance_money']?>）*<?=$order['order']['exchange']?>-<?=empty($order['order']['gold_money'])?'0':$order['order']['gold_money']?>+<?=$order['order']['service']?>=<?=$order['order']['actual_money']?>TWD</td>
				</tr>
			</tbody>
		</table>
		
		<table class="common_table table margin_t10 text-center">
			<tbody>
				<tr>
					<th colspan="20" class="l">{:__('Commodity message')}1</th>
				</tr>
		<?php
			if($order['order']['type'] == '0' || $order['order']['type'] == '1'){
		?>
			<tr class="color_666">
				<td>{:__('Purchaser')}</td>
				<td>{:__('Name title')}</td>
				<td>{:__('Amount')}</td>
			</tr>
			<?php
				$product_json = json_decode($order['order']['product_json'],true);
				foreach($product_json['alipay'] as $ls){
					foreach($product_json['product'][$ls['alipay_order_id']] as $val){
			?>
					<tr class="color_666">
						<td><?=$ls['other_name']?></td>
						<td><?=$val['title']?></td>
						<td class="color_red"><?=$val['price']?>RMB</td>
					</tr>
			<?php
					}
			?>
				<tr class="color_999">
					<td colspan="3">
						<div class="f12 text-left" style="line-height: 22px"><?=$ls['peerpay_link']?></div>
					</td>
				</tr>
			<?php
				}
			}else{
				if($order['order']['type'] == '3'){
			?>
				<tr>
					<td>{:__('Type')}</td>
					<td>{:__('Stored value account')}</td>
				</tr>
				<tr class="color_666">
					<td class="color_red">{:__('Alipay Reserve Value')}</td>
					<td><?=$order['order']['alipay_account']?>-<?=$order['order']['wechat_account']?></td>
				</tr>
			<?php
				}
				if($order['order']['type'] == '4'){
			?>
				<tr>
					<td>{:__('Type')}</td>
					<td>{:__('Stored value account')}</td>
				</tr>
				<tr class="color_666">
					<td class="color_red">{:__('WeChat storage value')}</td>
					<td><?=$order['order']['alipay_account']?></td>
				</tr>
			<?php
				}
				if($order['order']['type'] == '5'){
			?>
				<tr>
					<td>{:__('Type')}</td>
					<td>{:__('Platform link')}</td>
					<td>{:__('Game Video Platform')}({:__('Game Video Account')})</td>
				</tr>
				<tr class="color_666">
					<td>{:__('Game/Video Storage')}</td>
					<td><?=$order['order']['alipay_account']?></td>
					<td><?=$order['order']['wechat_account']?></td>
				</tr>
				<tr>
					<td>商品說明:<?=$order['order']['url_path']?></td>
				</tr>
			<?php
				}
				if($order['order']['type'] == '6'){
			?>
				<tr>
					<td>{:__('Type')}</td>
					<td>{:__('Other purchasing')}({:__('Platform link')})</td>
				</tr>
				<tr class="color_666">
					<td>{:__('Other payment')}</td>
					<td><?=$order['order']['url_path']?></td>
				</tr>
			<?php
				}
			?>
			<?php
			}
		?>
			</tbody>
			<tbody>
				<tr class="color_666">
					<?php
						if(!empty($order['order']['live_img'])){
					?>
						<td>
							<div class="text-left pad_l10">
								採購單/出貨單:<br />
								<?php
									$live_img = json_decode($order['order']['live_img'],true);
									foreach($live_img as $img_ls){
								?>
									<img width="100px" src="<?=$img_ls?>" />
								<?php
									}
								?>
							</div>
						</td>
					<?php
						}
					?>
				</tr>
				<tr class="color_666">
					<?php
						if(!empty($order['order']['live_url'])){
					?>
						<td><div class="text-left pad_l10">URL: <?=$order['order']['live_url']?></div></td>
					<?php
						}
						if(!empty($order['order']['live_id'])){
					?>
						<td><div class="text-left pad_l10">暱稱: <?=$order['order']['live_id']?></div></td>
					<?php
						}
						if(!empty($order['order']['live_account'])){
					?>
						<td><div class="text-left pad_l10">帳號: <?=$order['order']['live_account']?></div></td>
					<?php
						}
						if(!empty($order['order']['live_pwd'])){
					?>
						<td><div class="text-left pad_l10">密碼: <?=$order['order']['live_pwd']?></div></td>
					<?php
						}
						if(!empty($order['order']['live_value'])){
					?>
						<td><div class="text-left pad_l10">面額: <?=$order['order']['live_value']?></div></td>
					<?php
						}
						if(!empty($order['order']['live_game'])){
					?>
						<td><div class="text-left pad_l10">遊戲區組: <?=$order['order']['live_game']?></div></td>
					<?php
						}
						if(!empty($order['order']['live_type'])){
					?>
						<td><div class="text-left pad_l10">類型: <?=$order['order']['live_type']?></div></td>
					<?php
						}
						if(!empty($order['order']['live_money']) && $order['order']['type'] != '15'){
					?>
						<td><div class="text-left pad_l10">面值: <?=$order['order']['live_money']?></div></td>
					<?php
						}
						if(!empty($order['order']['user_remarks'])){
					?>
						<td><div class="text-left pad_l10">備註: <?=$order['order']['user_remarks']?></div></td>
					<?php
						}
					?>
				</tr>
				<tr class="color_666">
					<?php
						if(!empty($order['order']['address'])){
					?>
						<td>
							<div class="text-left pad_l10">
								收貨地址: 
								<?php
									$address = json_decode($order['order']['address'],true);
									echo $address['type'].$address['city'].$address['address'].'&nbsp&nbsp&nbsp&nbsp'.$address['address_name'].'('.$address['address_mobile'].')'
								?>
							</div>
						</td>
					<?php
						}
					?>
				</tr>
				<tr class="color_666">
					<?php
						if(!empty($order['order']['qiyebank'])){
					?>
						<td>
							<div class="text-left pad_l10">
								企業帳戶: 
								<?php
									$qiyebank = json_decode($order['order']['qiyebank'],true);
									echo $qiyebank['head_bank'].'('.$qiyebank['bank_name'].')'.$qiyebank['bank_username'].';'.$qiyebank['bank_account'];
								?>
							</div>
						</td>
					<?php
						}
					?>
				</tr>
			</tbody>
		</table>

		{include file="daifu/success_common" /}
	</div>
</div>