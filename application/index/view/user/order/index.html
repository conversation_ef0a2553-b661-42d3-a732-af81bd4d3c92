<div class="container">
	<div class="user_main margin_tb40">
		<div class="pad_lr20 pad_tb10">
			<div class="clearfix select_filter_div">
				<a href="<?=url('index/user/order')?>" class="<?=$_GET?'':'active'?>"><div class="filter_item color_red">{:__('All orders')}</div></a>
				<div class="moni_select filter_item no_border">
					<div class="moni_selected clearfix"> 
						<img class="arr" src="__CDN__/assets/img/arr.png">
						<span>{:__('Order status')}</span>
					</div>
					<div class="moni_s_down">
						<!-- 选中给 active -->
						<?php
							$map = $_GET;
							if(empty($_GET['order_status'])){
								$_GET['order_status'] = '';
							}else{
								$_GET['order_status'] = $_GET['order_status'];
							}
							unset($map['order_status']);
							foreach($site['order_status'] as $key=>$ls){
						?>
							<a href="<?=url('index/user/order').'?order_status='.$key.'&'.http_build_query($map)?>" class="<?php if($_GET['order_status'] == $key) echo 'active'?>"><?=$ls?></a>
						<?php
							}
						?>
					</div>
				</div>
				<div class="moni_select filter_item no_border">
					<div class="moni_selected clearfix">
						<img class="arr" src="__CDN__/assets/img/arr.png">
						<span>{:__('Payment status')}</span>
					</div>
					<div class="moni_s_down">
						<!-- 选中给 active -->
						<?php
							$map = $_GET;
							if(empty($_GET['pay_type'])){
								$_GET['pay_type'] = '';
							}else{
								$_GET['pay_type'] = $_GET['pay_type'];
							}
							unset($map['pay_type']);
							foreach($site['pay_type'] as $key=>$ls){
						?>
							<a href="<?=url('index/user/order').'?pay_type='.$key.'&'.http_build_query($map)?>" class="<?php if($_GET['pay_type'] == $key) echo 'active'?>"><?=$ls?></a>
						<?php
							}
						?>
					</div>
				</div>
				<div class="moni_select filter_item no_border">
					<div class="moni_selected clearfix">
						<img class="arr" src="__CDN__/assets/img/arr.png">
						<span>{:__('Payment method')}</span>
					</div>
					<div class="moni_s_down">
						<!-- 选中给 active -->
						<?php
							$map = $_GET;
							if(empty($_GET['pay_status'])){
								$_GET['pay_status'] = '';
							}else{
								$_GET['pay_status'] = $_GET['pay_status'];
							}
							unset($map['pay_status']);
							foreach($site['pay_status'] as $key=>$ls){
						?>
							<a href="<?=url('index/user/order').'?pay_status='.$key.'&'.http_build_query($map)?>" class="<?php if($_GET['pay_status'] == $key) echo 'active'?>"><?=$ls?></a>
						<?php
							}
						?>
					</div>
				</div>
				<div class="moni_select filter_item no_border">
					<div class="moni_selected clearfix">
						<img class="arr" src="__CDN__/assets/img/arr.png">
						<span>{:__('Order type')}</span>
					</div>
					<div class="moni_s_down">
						<!-- 选中给 active -->
						<?php
							$map = $_GET;
							if(empty($_GET['type'])){
								$_GET['type'] = '';
							}else{
								$_GET['type'] = $_GET['type'];
							}
							unset($map['type']);
							foreach($special as $key=>$ls){
								if($ls['pid'] != '0'){
						?>
							<a href="<?=url('index/user/order').'?type='.$ls['key_id'].'&'.http_build_query($map)?>" class="<?php if($_GET['type'] == $ls['key_id']) echo 'active'?>"><?=$ls['name']?></a>
						<?php
								}
							}
						?>
					</div>
				</div>
			</div>

			<table class="common_table table margin_t10 text-center">
				<tbody>
					<tr>
						<th>{:__('Type')}</th>
						<th>{:__('order_no')}</th>
						<th>{:__('Order amount')}(RMB)</th>
						<th>{:__('Rate')}</th>
						<th>{:__('Shopping gold')}</th>
						<th>{:__('Amoy money')}</th>
						<th>{:__('Handling fee')}</th>
						<th>{:__('Need TWD')}</th>
						<th>{:__('Payment status')}</th>
						<th>{:__('Submission time')}</th>
						<th>{:__('Order status')}</th>
					</tr>
					<?php
						foreach($list as $ls){
					?>
						<tr>
							<td  href="<?=url('index/user/order_detail',array('id'=>$ls['id'],'set_type'=>$ls['type']))?>" class="js_ajax_win" data-width="1200"><?=$special[$ls['type']]['name']?></td>
							<td  href="<?=url('index/user/order_detail',array('id'=>$ls['id'],'set_type'=>$ls['type']))?>" class="js_ajax_win" data-width="1200"><?=$ls['order_no']?></td>
							<td  href="<?=url('index/user/order_detail',array('id'=>$ls['id'],'set_type'=>$ls['type']))?>" class="js_ajax_win" data-width="1200"><?=$ls['rmb_money']?></td>
							<td  href="<?=url('index/user/order_detail',array('id'=>$ls['id'],'set_type'=>$ls['type']))?>" class="js_ajax_win" data-width="1200"><?=$ls['exchange']?></td>
							<td  href="<?=url('index/user/order_detail',array('id'=>$ls['id'],'set_type'=>$ls['type']))?>" class="js_ajax_win" data-width="1200"><?=empty($ls['gold_money'])?'0':$ls['gold_money']?></td>
							<td  href="<?=url('index/user/order_detail',array('id'=>$ls['id'],'set_type'=>$ls['type']))?>" class="js_ajax_win" data-width="1200"><?=$ls['balance_money']?></td>
							<td  href="<?=url('index/user/order_detail',array('id'=>$ls['id'],'set_type'=>$ls['type']))?>" class="js_ajax_win" data-width="1200"><?=$ls['service']?></td>
							<td  href="<?=url('index/user/order_detail',array('id'=>$ls['id'],'set_type'=>$ls['type']))?>" class="js_ajax_win" data-width="1200"><?=$ls['actual_money']?></td>
							<td  href="<?=url('index/user/order_detail',array('id'=>$ls['id'],'set_type'=>$ls['type']))?>" class="js_ajax_win" data-width="1200" class="color_red"><?=$site['pay_type'][$ls['pay_type']]?></td>
							<td  href="<?=url('index/user/order_detail',array('id'=>$ls['id'],'set_type'=>$ls['type']))?>" class="js_ajax_win" data-width="1200"><div style="line-height: 17px; font-size: 12px"><?=date('Y-m-d H:i',$ls['createtime'])?></div></td>
							<td  href="<?=url('index/user/order_detail',array('id'=>$ls['id'],'set_type'=>$ls['type']))?>" class="js_ajax_win" data-width="1200" class="color_red"><?=$site['order_status'][$ls['order_status']]?></td>
						</tr>
					<?php
						}
					?>
				</tbody>
			</table>
			<?php
				if($list){
			?>
				{include file="common/pages" /}
			<?php
				}else{
			?>
				{include file="common/nodata" /}
			<?php
				}
			?>
		</div>
	</div>
</div>