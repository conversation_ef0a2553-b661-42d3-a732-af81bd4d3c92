<style type="text/css">
	body{ padding-bottom: 0; }
	.footer_new{ display: none }
    .common_nav a{position: relative;}
    .common_nav a .num{
        display: block;
        position: absolute;
        left: 0.9rem;
        top: 0.1rem;
        background: red;
        color: #fff;
        font-size: 0.22rem;
        padding: 0 0.06rem;
        line-height: 0.32rem;
        min-width: 0.32rem;
        text-align: center;
        border-radius: 50%;
    }
</style>
<div class="pad_t20 pad_lr20">
    <div class="common_nav clearfix">
		<?php
			$map = $_GET;
			if(empty($_GET['_type'])){
				$_GET['_type'] = '';
			}else{
				$_GET['_type'] = $_GET['_type'];
			}
			unset($map['_type']);
		?>
        <a href="<?=url('index/user/order').'?_type=1&'.http_build_query($map)?>" class="<?php if($_type == '1')echo 'active'?>">{:__('Unpaid')}<span></span></a>
        <a href="<?=url('index/user/order').'?_type=2&'.http_build_query($map)?>" class="<?php if($_type == '2')echo 'active'?>">{:__('Paid')}<span></span></a>
        <a href="<?=url('index/user/order').'?_type=3&'.http_build_query($map)?>" class="<?php if($_type == '3')echo 'active'?>">{:__('Completed')}<span></span></a>
        <a href="<?=url('index/user/order').'?_type=4&'.http_build_query($map)?>" class="<?php if($_type == '4')echo 'active'?>">{:__('Refunded')}<span></span></a>
    </div>
	<br />
	<div class="common_nav clearfix">
		<?php
			$map = $_GET;
			if(empty($_GET['set_type'])){
				$_GET['set_type'] = '';
			}else{
				$_GET['set_type'] = $_GET['set_type'];
			}
			unset($map['set_type']);
		?>
        <a style="width: 20%;" href="<?=url('index/user/order').'?set_type=1&'.http_build_query($map)?>" class="<?php if($set_type == '1')echo 'active'?>">
			代付
            <div class="num">
				<?php
					if(!empty($_GET['_type'])){
						if($_GET['_type'] == '1' && $df_order_count){
							echo $df_order_count;
						}
					}
				?>
			</div>
		</a>
        <a style="width: 20%;" href="<?=url('index/user/order').'?set_type=2&'.http_build_query($map)?>" class="<?php if($set_type == '2')echo 'active'?>">
			直播
            <div class="num">
				<?php
					if(!empty($_GET['_type'])){
						if($_GET['_type'] == '1' && $order_live_count){
							echo $order_live_count;
						}
					}
				?>
			</div>
		</a>
        <a style="width: 20%;" href="<?=url('index/user/order').'?set_type=3&'.http_build_query($map)?>" class="<?php if($set_type == '3')echo 'active'?>">
			遊戲
            <div class="num">
				<?php
					if(!empty($_GET['_type'])){
						if($_GET['_type'] == '1' && $order_game_count){
							echo $order_game_count;
						}
					}
				?>
			</div>
		</a>
        <a style="width: 20%;" href="<?=url('index/user/order').'?set_type=4&'.http_build_query($map)?>" class="<?php if($set_type == '4')echo 'active'?>">
			代購
            <div class="num">
				<?php
					if(!empty($_GET['_type'])){
						if($_GET['_type'] == '1' && $order_new_df_count){
							echo $order_new_df_count;
						}
					}
				?>
			</div>
		</a>
        <a style="width: 20%;" href="<?=url('index/user/order').'?set_type=5&'.http_build_query($map)?>" class="<?php if($set_type == '5')echo 'active'?>">
			其他
            <div class="num">
				<?php
					if(!empty($_GET['_type'])){
						if($_GET['_type'] == '1' && $order_other_count){
							echo $order_other_count;
						}
					}
				?>
			</div>
		</a>
    </div>

    <div class="order_list margin_t20 js_scroll_more_list" data-url="{:url('index/user/order_lists')}">
        {include file="user/order/m_lists" /}
    </div>

    <!-- 上拉加载的 无数据我自己判断 -->
    <div class="loadding_d hide">
        <img src='__CDN__/assets/img/loading.png'>
        <span>{:__('Pull-up Load More')}</span>
    </div>
    <div class="js_no_data pad_tb30 hide">
        {include file="common/nodata" /}
    </div>
    <!-- 上拉加载的 无数据我自己判断 -->
    
</div>