<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
</style>
<div class="pad_t20 pad_lr20">
    <div class="help_title bg_fff clearfix">
        <span class="pull-right f12"><span class="color_666">{:__('Numbering')}：</span><?=$order['order']['order_no']?></span>
        <!-- pay1 pay2 pay3···· -->
       <?=$info['name']?>
    </div>

    <div class="order_detail">
        <div class="item_tt clearfix">
            {:__('RMB')}
        </div>
        <div class="item_div">
            <span class="pull-right color_333">RMB</span>
            <?=$order['order']['rmb_money']?>
        </div>
        <div class="item_tt clearfix">
            {:__('Conversion TWD')} <span class="f12 color_999">({:__('Rate')}：<?=$order['order']['exchange']?>)</span>
        </div>
        <div class="item_div ">
            <span class="pull-right color_red">TWD</span>
            <span class="color_red"><?=$order['order']['tb_money']?></span>
        </div>
		<!----字段配置----->
		<?php
			if(!empty($order['order']['live_img'])){
		?>
			<div class="item_tt clearfix">
				採購單/出貨單:
			</div>
			<div class="item_div">
				<div class="wx_imgs clearfix">
					<?php
						$live_img = json_decode($order['order']['live_img'],true);
						foreach($live_img as $img_ls){
					?>
						<img src="<?=$img_ls?>">
					<?php
						}
					?>
				</div>
			</div>
		<?php
			}
			if($order['order']['live_url']){
		?>
			<div class="item_tt clearfix">
				URL: 
			</div>
			<div class="item_div">
				<?=$order['order']['live_url']?>
			</div>
		<?php
			}
			if($order['order']['live_id']){
		?>
			<div class="item_tt clearfix">
				暱稱: 
			</div>
			<div class="item_div">
				<?=$order['order']['live_id']?>
			</div>
		<?php
			}
			if($order['order']['live_account']){
		?>
			<div class="item_tt clearfix">
				帳號: 
			</div>
			<div class="item_div">
				<?=$order['order']['live_account']?>
			</div>
		<?php
			}
			if($order['order']['live_pwd']){
		?>
			<div class="item_tt clearfix">
				密碼: 
			</div>
			<div class="item_div">
				<?=$order['order']['live_pwd']?>
			</div>
		<?php
			}
			if($order['order']['live_value']){
		?>
			<div class="item_tt clearfix">
				面額: 
			</div>
			<div class="item_div">
				<?=$order['order']['live_value']?>
			</div>
		<?php
			}
			if($order['order']['live_game']){
		?>
			<div class="item_tt clearfix">
				遊戲區組: 
			</div>
			<div class="item_div">
				<?=$order['order']['live_game']?>
			</div>
		<?php
			}
			if($order['order']['live_type']){
		?>
			<div class="item_tt clearfix">
				類型: 
			</div>
			<div class="item_div">
				<?=$order['order']['live_type']?>
			</div>
		<?php
			}
			if($order['order']['live_money']){
		?>
			<div class="item_tt clearfix">
				面值: 
			</div>
			<div class="item_div">
				<?=$order['order']['live_money']?>
			</div>
		<?php
			}
			if(!empty($order['order']['user_remarks'])){
		?>
			<div class="item_tt clearfix">
				備註: 
			</div>
			<div class="item_div">
				<?=$order['order']['user_remarks']?>
			</div>
		<?php
			}
			if(!empty($order['order']['address'])){
		?>
			<div class="item_tt clearfix">
				收貨地址: 
			</div>
			<div class="item_div">
				<?php
					$address = json_decode($order['order']['address'],true);
					echo $address['type'].$address['city'].$address['address'].'&nbsp&nbsp&nbsp&nbsp'.$address['address_name'].'('.$address['address_mobile'].')'
				?>
			</div>
		<?php
			}
			if(!empty($order['order']['qiyebank'])){
		?>
			<div class="item_tt clearfix">
				企業帳戶: 
			</div>
			<div class="item_div">
				<?php
					$qiyebank = json_decode($order['order']['qiyebank'],true);
					echo $qiyebank['head_bank'].'('.$qiyebank['bank_name'].')'.$qiyebank['bank_username'].';'.$qiyebank['bank_account'];
				?>
			</div>
		<?php
			}
		?>
		<!----字段配置----->
        <div class="item_tt">
            {:__('Need TWD')}
        </div>
        <div class="order_rate">
            <div class="rate clearfix">
                <div class="r_item">
                    <div class="pp">{:__('Order amount')}</div>
                    <div class="tt"><?=$order['order']['rmb_money']?>RMB</div>
                </div>
                <div class="c_item">
                    <span class="color_666">{:__('Rate')}：</span><?=$order['order']['exchange']?>
                </div>
                <div class="r_item">
                    <div class="pp">{:__('Need TWD')}</div>
                    <div class="tt"><span class="color_red"><?=$order['order']['actual_money']?></span>TWD</div>
                </div>
            </div>

            <div class="items">
                <div class="item"><span class="color_666">{:__('Handling fee')}：</span><?=$order['order']['service']?>TWD</div>
                <div class="item"><span class="color_666">{:__('Amoy money')}：</span><?=$order['order']['balance_money']?></div>
                <div class="item"><span class="color_666">{:__('Calculation formula')}：</span>(<?=$order['order']['rmb_money']?>-<?=$order['order']['balance_money']?>)*<?=$order['order']['exchange']?>+<?=$order['order']['service']?>=<?=$order['order']['actual_money']?>TWD</div>
            </div>
        </div>

        <div class="item_tt">
            {:__('Payment method')}
        </div>
			<div class="order_payment pad_lr20">
				<div class="clearfix zffs">
					<div class="status color_red"><?=$site['pay_type'][$order['order']['pay_type']]?></div>
					<!-- account1银行  account2 微信  account3支付宝 -->
					<img src="__CDN__/assets/img/wap/account1.png">
					<?=$site['pay_status'][$order['order']['pay_status']]?>
				</div>
				<?php
					if($order['order']['pay_status'] == '1'){
						foreach($order['bank'] as $ls){
				?>
				<div class="clearfix color_666">
					<div class="d">台湾银行</div>
					<div class="d text-center"><?=$ls['name']?></div>
					<div class="d text-right"><?=$ls['account_six']?></div>
				</div>
				<?php
						}
					}
					if($order['order']['pay_status'] == '2'){
				?>
					{:__('The Super Merchant Payment Code has been sent to')}<?=$user['mobile'];?>
				<?php
					}
				?>
			</div>
			<?php
				if($order['order']['pay_status'] == '1'){
			?>
				<div class="order_payment margin_t20">
					<div class="ttt pad_lr20">{:__('Our Payment Bank Account')}</div>
					<div class="pad_lr20 pad_tb20">
						<?php
							if(!empty($order['sys_bank'])){
						?>
							<div class="item color_red"><?=$order['sys_bank']['account_name']?></div>
							<div class="item"><span class="color_666">{:__('Account')}：</span><?=$order['sys_bank']['account_num']?></div>
						<?php
							}else{
						?>
							<div class="item color_red">-</div>
							<div class="item"><span class="color_666">{:__('Account')}：</span>-</div>
						<?php
							}
						?>
					</div>
				</div>
			<?php
				}
			?>
			<?php
				if($order['order']['pay_status'] == '3'){
			?>
				<div class="order_payment margin_t20">
					<div class="ttt pad_lr20">{:__('Our Payment Bank Account')}</div>
					<div class="pad_lr20 pad_tb20">
							<div class="item color_red">822 中國信託</div>
							<div class="item"><span class="color_666">{:__('Account')}：</span><?=$order['order']['cs_code']?></div>
					</div>
				</div>
			<?php
				}
			?>
			
			
			
        


        <!--<div class="tipsd pad_tb20">
            <p>{:__('We do not accept over-the-counter remittance')}</p>
            <p>{:__('Receiving bank account is for this order only')}</p>
            <p>{:__('Please use ATM/WEBATM pipeline to transfer to the account')}</p>
            <p>{:__('We don not need to inform you after payment is completed')}</p>
            <p>{:__('Bank transfers usually take time to arrive. If the')}</p>
            <p>{:__('If you can not transfer money and may encounter bank')}</p>
        </div>
        <div class="order_payment pad_lr20">
            <div class="clearfix zffs">
                <div class="status color_999">{:__('Electronic invoice')}({:__('Compilation')})</div>
                <img src="__CDN__/assets/img/wap/fapiao.png">
                {:__('Invoice type')}
            </div>
            <div class="items">
                <div class="item"><span class="color_666">{:__('Unified numbering')}：</span>A123456789</div>
                <div class="item"><span class="color_666">{:__('Invoice rise')}：</span>某某</div>
                <div class="item"><span class="color_666">{:__('Mail box')}：</span><EMAIL></div>
            </div>
            <div class="fapiaotip">
                {:__('* E-invoice will be issued three days after')}
            </div>
        </div>-->
		<?php
			if($order['order']['order_status'] == '1'){
		?>
			<div class="pad_tb30">
				<a href="javascript:;" class="sub_btn">已完成</a>
			</div>
		<?php
			}else{
		?>
			<div class="pad_tb30">
				<a href="<?=url('index/daifu/cancelDf',array('id'=>$order['order']['id'],'set_type'=>$order['order']['type']))?>" class="sub_btn js_ajax_dele" tips="{:__('Do you confirm the cancellation?')}" data-id="<?=$order['order']['id']?>">{:__('Cancellation of payment')}</a>
			</div>
		<?php
			}
		?>
    </div>
</div>