<style type="text/css">
.order_list .box{ margin-bottom: 0 }
.order_list .box .items{border-top: none}
.order_list .box .items .item span{color: #666}
.order_list .dele_btn{display: block; margin-top: 5px; margin-bottom: 0.2rem; border-radius: 5px; line-height: 0.88rem; color: #666666; font-size: 0.32rem; text-align: center;}
</style>
<div class="pad_t20 pad_lr20">
    
    <div class="order_list margin_t20 js_scroll_more_list" data-url="{:url('index/user/prize_lists')}">
        {include file="user/prize/m_lists" /}
    </div>

    <!-- 上拉加载的 无数据我自己判断 -->
    <div class="loadding_d hide">
        <img src='__CDN__/assets/img/loading.png'>
        <span>{:__('Pull-up Load More')}</span>
    </div>
    <div class="js_no_data pad_tb30 hide">
        {include file="common/nodata" /}
    </div>
    <!-- 上拉加载的 无数据我自己判断 -->
    
</div>

<a href="<?=url('index/sele_address')?>" class="js_ajax_win js_sele_a hide">選擇收貨地址</a>

<script type="text/javascript">
    $('.js_list_sele_a').click(function(){
        jp_id=$(this).attr('data-id');
        $('.js_sele_a').click()
    })
</script>