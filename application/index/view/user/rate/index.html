<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 pad_b40">
	<div class="pad_t10 win_rate_tab js_rate_tab clearfix">
		<?php
			foreach ($exchange as $key => $value) {
				$category = explode(',', $value['df_ids']);
		?>
		<a href="javascript:;" data-id="{$category[0]}" data-text="{$value['df_id']}">{$value['title']}<span></span></a>
		<?php
			}

		?>
		<!-- <a href="javascript:;" data-id="4" >{:__('WeChat storage value')}<span></span></a>
		<a href="javascript:;" data-id="5" >{:__('Game/Video Storage')}<span></span></a>
		<a href="javascript:;" data-id="6" >{:__('Other payment')}<span></span></a>
		<a href="javascript:;" data-id="7" >{:__('Coin storage')}<span></span></a> -->
	</div>
	<div class="js_sm_text" style="border: 1px dashed #EE436D; padding: 10px; margin-top: 10px; border-radius: 5px; color: #EE436D; background:#ffebf0; line-height: 20px;">
		
	</div>
	<div style="padding: 68px 180px 0px 180px;">
		<div class="rate_tt"><span class="js_rate_tt">{:__('Alibaba pays')}</span>{:__('Rate')}： <span class="color_red js_dafu_rate_val">4.55200</span></div>
		<div class="rate_input clearfix">
			<div class="pull-left tts">{:__('RMB')}</div>
			<div class="over_hide rate_input_r">
				<span class="pull-right color_999 margin_l10 margin_r20">RMB</span>
				<div class="over_hide">
					<input type="text" data-type="money" class="js_money_v" data-id="1"  placeholder="{:__('Please enter RMB amount')}">
				</div>
			</div>
		</div>
		<div class="rate_change">
			<a href="javascript:;" class="js_rate_change" data-url="{:url('/index/user/rate_fun')}"><img src="__CDN__/assets/img/pc/rate_change.png" alt=""></a>
		</div>
		<div class="rate_input rate_input_red clearfix">
			<div class="pull-left tts">{:__('TWD')}</div>
			<div class="over_hide rate_input_r">
				<span class="pull-right color_999 margin_l10 margin_r20">TWD</span>
				<div class="over_hide">
					<input type="text" data-type="money" class="js_money_v" data-id="2" placeholder="{:__('Please enter TWD amount')}">
				</div>
			</div>
		</div>
		<div class="rate_tt clearfix"><span class="color_666 pull-right">{:__('Save for you')}<span class="color_red"><span class="js_save_money">20</span>TWD</span></span>{:__('Current level')}：VIP<?=$level?> </div>
		<div class="rate_tt clearfix text-right color_999">{:__('Next level of savings')}： <span class="js_next_rate">4.55000</span></div>
		
	</div>
</div>

<script type="text/javascript">
    
    $('.js_rate_tab a').click(function(){
    	$(this).addClass('active').siblings('a').removeClass('active');
    	$('.js_rate_tt').html($(this).html());
    	$('.js_money_v').val('');
		$('.js_sm_text').html($(this).attr('data-text'))
    	var _this=$('.js_money_v[data-id="1"]');
    	if(_this.val()==''){
    		_this.val(0)
    	}
    	var _type=_this.attr('data-id');
        if(_this.val()==''){
            return false
        }
        setTimeout(function(){
            var _url=$('.js_rate_change').attr('data-url');
            $.post(_url,{money:_this.val(),category:$('.js_rate_tab a.active').attr('data-id'),type:_type},function(data){
                $('.js_dafu_rate_val').html(data.exchange);
                var _otyps=_type==1?2:1;
                $('.js_save_money').html(data.save_money);
                $('.js_next_rate').html(data.next_rate);
                $('.js_money_v[data-id="'+_otyps+'"]').val(data.money)
            })
        },20)
    })
     $('.js_rate_tab a').eq(0).click()



    $(document).on('keyup','.js_money_v',function(){
        var _this=$(this);
        var _type=_this.attr('data-id');
        if(_this.val()==''){
            return false
        }
        setTimeout(function(){
            var _url=$('.js_rate_change').attr('data-url');
            $.post(_url,{money:_this.val(),category:$('.js_rate_tab a.active').attr('data-id'),type:_type},function(data){
                $('.js_dafu_rate_val').html(data.exchange);
                var _otyps=_type==1?2:1;
                $('.js_save_money').html(data.save_money);
                $('.js_next_rate').html(data.next_rate);
                $('.js_money_v[data-id="'+_otyps+'"]').val(data.money)
            })
        },20)
    })

</script>