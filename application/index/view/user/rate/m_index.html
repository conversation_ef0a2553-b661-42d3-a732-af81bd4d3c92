<style type="text/css">
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .common_nav{ overflow: auto; height: 0.88rem; width: 100%; }
    .common_nav a{ width: auto; margin:0 10px; }

    .input_box{ background: none }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
    .rate_tt{ font-size: 0.24rem; line-height: 0.44rem }
</style>
<div class="pad_t20 pad_lr20">
    <div class="common_nav clearfix">
    	<div class="common_nav_s js_rate_tab clearfix">
            <?php
                foreach ($exchange as $key => $value) {
                    $category = explode(',', $value['df_ids']);
            ?>
            <a href="javascript:;" data-id="{$category[0]}" data-text="{$value['df_id']}">{$value['title']}<span></span></a>
            <?php
                }

		?>
            <!-- <a href="javascript:;" data-id="4" >{:__('WeChat storage value')}<span></span></a>
            <a href="javascript:;" data-id="5" >{:__('Game/Video Storage')}<span></span></a>
            <a href="javascript:;" data-id="6" >{:__('Other payment')}<span></span></a>
            <a href="javascript:;" data-id="7" >{:__('Coin storage')}<span></span></a> -->
		</div>
    </div>
	
	<div class="js_sm_text" style="border: 1px dashed #EE436D; padding: 10px; margin-top: 10px; border-radius: 5px; color: #EE436D; background:#ffebf0; line-height: 20px;">
		
	</div>
	
    <div class="input_box full_width">
        <div class="input_tt">{:__('Recharge amount')}</div>
        <div class="input_rr">
            <div class="pull-right pad_l10 pad_r20">RMB</div>
            <div class="over_hide">
                <input type="text" data-type="money" name="money" class="js_money_v" data-id="1"  placeholder="{:__('Please enter RMB amount')}">
            </div>
        </div>
    </div>

    <div class="input_box full_width">
        <div class="input_tt">{:__('Conversion TWD')} <span class="f12 color_999">({:__('Rate')}： <span class="js_dafu_rate_val">4.5530</span>)</span></div>
        <div class="input_rr">
            <div class="pull-right pad_l10 pad_r20 color_red">TWD</div>
            <div class="over_hide">
                <input type="text" data-type="money" class="js_money_v" data-id="2" placeholder="{:__('Please enter TWD amount')}">
            </div>
        </div>
    </div>
    <div class="pad_t20"></div>
    <div class="rate_tt clearfix"><span class="color_666 pull-right">{:__('Save for you')}<span class="color_red"><span class="js_save_money">0</span>TWD</span></span>{:__('Current level')}：VIP<?=$level?> </div>
    <div class="rate_tt clearfix text-right color_999">{:__('Next level of savings')}： <span class="js_next_rate">0</span></div>


    <div class="pad_tb30 margin_t20 order_detail hide">
	    	<a href="javascript:;" class="sub_btn margin_b30 red js_rate_change" data-url="{:url('/index/user/rate_fun')}">{:__('query')}</a>
    </div>
</div>

<script type="text/javascript">
	var a=$('.common_nav_s a');
	var _w=0;
	for(var i=0;i<a.length;i++){
		_w+=a.eq(i).width()+21
	}
	$('.common_nav_s').css('width',_w);


    $('.js_rate_tab a').click(function(){
        $(this).addClass('active').siblings('a').removeClass('active');
        $('.js_rate_tt').html($(this).html());
        $('.js_money_v').val('');
		$('.js_sm_text').html($(this).attr('data-text'))
        var _this=$('.js_money_v[data-id="1"]');
        if(_this.val()==''){
            _this.val(0)
        }
        var _type=_this.attr('data-id');
        if(_this.val()==''){
            return false
        }
        setTimeout(function(){
            var _url=$('.js_rate_change').attr('data-url');
            $.post(_url,{money:_this.val(),category:$('.js_rate_tab a.active').attr('data-id'),type:_type},function(data){
                $('.js_dafu_rate_val').html(data.exchange);
                var _otyps=_type==1?2:1;
                $('.js_save_money').html(data.save_money);
                $('.js_next_rate').html(data.next_rate);
                $('.js_money_v[data-id="'+_otyps+'"]').val(data.money)
            })
        },20)
    })
     $('.js_rate_tab a').eq(0).click()
    
    // 储值 汇率换算
    $(document).on('keyup','.js_money_v',function(){
        var _this=$(this);
        var _type=_this.attr('data-id');
        if(_this.val()==''){
            return false
        }
        setTimeout(function(){
            var _url=$('.js_rate_change').attr('data-url');
            $.post(_url,{money:_this.val(),category:$('.js_rate_tab a.active').attr('data-id'),type:_type},function(data){
                $('.js_dafu_rate_val').html(data.exchange);
                var _otyps=_type==1?2:1;
                $('.js_save_money').html(data.save_money);
                $('.js_next_rate').html(data.next_rate);
                $('.js_money_v[data-id="'+_otyps+'"]').val(data.money)
            })
        },20)
    })
</script>