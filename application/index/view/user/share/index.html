
<a href="javascript:;" class="close_win_btn"><img src="__CDN__/assets/img/win_close.png" alt=""></a>
<div class="my_close_win pad_lr20 pad_b40">
	<div class="pad_t40 margin_tb10 share_list_win clearfix">
		<div class="clearfix pad_b40">
			<div class="share_ma pull-left text-center">
				<img src="__CDN__/assets/img/pc/share_icon.png" alt="">
				<div>
					{:__('My Promotion Code')}：	<span class="color_333"><?=$user['invitation_code']?></span> <a href="javascript:;" class="js_copy" data-clipboard-text="<?=$user['invitation_code']?>">{:__('Copy')}</a>
				</div>
			</div>
			<div class="over_hide">
				<div class="num_item num_item1">
					{:__('Promotion number')}： <span class="color_red"><?=$_count?></span>
				</div>
				<div class="num_item">
					{:__('Total Income')}： <span class="color_red"><?=$user['spread_profit']?></span>
				</div>
			</div>
		</div>
		<table class="common_table">
			<tbody>
				<tr>
					<th width="20%">{:__('Nickname')}</th>
					<th>{:__('Profit')}</th>
					<th>{:__('Event log')}</th>
					<th>{:__('Time')}</th>
				</tr>
				<?php
					foreach($list as $ls){
				?>
					<tr>
						<td class="color_666"><?=$ls['username']?></td>
						<td class="color_red"><?=$ls['price']?></td>
						<td class="color_red"><?=$ls['msg']?></td>
						<td class="color_999"><?=date('Y-m-d',$ls['createtime'])?></td>
					</tr>
				<?php
					}
				?>
			</tbody>
		</table>
	</div>
</div>
<script src="__CDN__/assets/js/pc/copy.js?v={$Think.config.site.version}" ></script>
<script type="text/javascript">
    // 复制
    if($('.js_copy').length>0){
        if(document.getElementsByClassName){
            var clipboard_btns = document.getElementsByClassName('js_copy');
            var clipboard = new Clipboard(clipboard_btns);
            clipboard.on('success', function(e) {
                tip_show("{:__('Copy success')}",'1');
            });
            clipboard.on('error', function(e) {
                console.log(e);
            });
        }else{
            // 兼容 ie7 8
            $(".js_copy").each(function() {
                $(this).on("click", function() {
                    var copy_span = $(this);
                    var input_hidden = '<input type="text" class="input_hidden" id="input_hidden" value=""/>';
                    copy_span.after($(input_hidden));
                    $("#input_hidden").val(copy_span.attr("data-clipboard-text"));
                    var obj = document.getElementById("input_hidden");
                    obj.select(); // 选择对象
                    document.execCommand("Copy"); // 执行浏览器复制命令
                    $("#input_hidden").remove();
                    tip_show("{:__('Copy success')}",'1');
                })
            })
        }
    }
</script>