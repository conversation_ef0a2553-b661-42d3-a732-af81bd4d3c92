<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
</style>
<div class="pad_tb20 pad_lr20">
    <div class="help_title bg_fff clearfix">
    	<a href="javascript:;" class="js_copy pull-right color_red" data-clipboard-text="<?=$user['invitation_code']?>">{:__('Copy')}</a>
        <img src="__CDN__/assets/img/wap/share_icon.png" class="icon">{:__('My Promotion Code')}  <span class="color_666"><?=$user['invitation_code']?></span>
    </div>
    <div class="clearfix share_nums">
        <div class="box box1">
            <div class="pp">{:__('Promotion number')}</div>
            <div class="tt"><?=$_count?> <span>人</span></div>
        </div>
        <div class="box box2">
            <div class="pp">{:__('Total Income')}</div>
            <div class="tt"><?=$user['spread_profit']?></div>
        </div>
    </div>
	<div class="margin_t20">
        <div class="f16 pad_b10">{:__('Promotion details')}</div>
        <div class="clearfix pad_lr20 f12 pad_b20 pad_t10 color_666">
            <span class="pull-right">{:__('Time')}</span>
            <span class="pull-left">{:__('Nickname')}</span>
            <div class="over_hide text-center">{:__('Profit')}</div>
        </div>
		<div class="share_list js_scroll_more_list" data-url="{:url('index/user/share_lists')}">
		    {include file="user/share/m_lists" /}
		</div>

		<!-- 上拉加载的 无数据我自己判断 -->
		<div class="loadding_d hide">
		    <img src='__CDN__/assets/img/loading.png'>
		    <span>{:__('Pull-up Load More')}</span>
		</div>
		<div class="js_no_data pad_tb30 hide">
		    {include file="common/nodata" /}
		</div>
		<!-- 上拉加载的 无数据我自己判断 -->
	</div>
</div>

<script src="__CDN__/assets/js/pc/copy.js?v={$Think.config.site.version}" ></script>
<script type="text/javascript">
    // 复制
    if($('.js_copy').length>0){
        if(document.getElementsByClassName){
            var clipboard_btns = document.getElementsByClassName('js_copy');
            var clipboard = new Clipboard(clipboard_btns);
            clipboard.on('success', function(e) {
                tip_show("{:__('Copy success')}",'1');
            });
            clipboard.on('error', function(e) {
                console.log(e);
            });
        }else{
            // 兼容 ie7 8
            $(".js_copy").each(function() {
                $(this).on("click", function() {
                    var copy_span = $(this);
                    var input_hidden = '<input type="text" class="input_hidden" id="input_hidden" value=""/>';
                    copy_span.after($(input_hidden));
                    $("#input_hidden").val(copy_span.attr("data-clipboard-text"));
                    var obj = document.getElementById("input_hidden");
                    obj.select(); // 选择对象
                    document.execCommand("Copy"); // 执行浏览器复制命令
                    $("#input_hidden").remove();
                    tip_show("{:__('Copy success')}",'1');
                })
            })
        }
    }
</script>