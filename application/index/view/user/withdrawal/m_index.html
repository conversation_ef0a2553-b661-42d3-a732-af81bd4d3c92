<style>
    .header .close_b.close{ display: block; }
    body{ padding-bottom: 0; }
    .footer_new{ display: none }
    .input_box{ background: none }
    .input_rr{ background: #fff; border-radius: 5px; }
    .input_rr input{ border: none }
    .js_r_all{ cursor: pointer; }
</style>
<div class="pad_t20 pad_lr20">
    <div class="help_title bg_fff clearfix">
        <img src="__CDN__/assets/img/wap/withdrawal.png" class="icon s">{:__('Rebate cash withdrawal')}
    </div>

    <form action="">
	    <div class="input_box full_width">
	        <div class="input_tt">{:__('Quantity of withdrawals')}</div>
	        <div class="input_rr">
	        	<div class="pull-right pad_l10 pad_r20 color_red js_r_all" data-m="<?=$user['withdrawal']?>">{:__('Cash withdrawal all')}<?=$user['withdrawal']?>元</div>
	        	<div class="over_hide">
	        		<input type="text" class="js_input_v"  data-max="<?=$user['withdrawal']?>" placeholder="{:__('Please enter the amount of withdrawal money')}" data-type="money" name="money" maxlength="10" is_required="true" empty_tip="{:__('Please enter the amount of withdrawal money')}">
	        	</div>
	        </div>
	    </div>
	    <div class="f12 color_999 pad_t20">{$site.withdrawaltxt}</div>
	    <div class="pad_tb30 margin_t20 order_detail">
	    	<a href="javascript:;" class="sub_btn margin_b30 red js_form_sub" data-text="{:__('Confirmation of withdrawals')}" data-loadding="{:__('Submitting')}" data-type="new_location">{:__('Confirmation of withdrawals')}</a>
	    </div>
    </form>
</div>
<script type="text/javascript">
	$(function(){
		$('.js_r_all').click(function(){
			$('.js_input_v').val($(this).attr('data-m'))
		})
	})
</script>