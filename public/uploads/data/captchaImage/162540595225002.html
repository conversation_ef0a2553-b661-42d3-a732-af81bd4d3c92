


<!DOCTYPE html>
<html lang="zh-Hant-TW">
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="UTF-8" />
    <title>國民身分證領補換資料查詢作業</title>
    <meta content="no-cache" http-equiv="Cache-Control" />
    <meta content="no-cache" http-equiv="Pragma" />
    <meta content="0" http-equiv="Expires" />
    <meta content="webapply" http-equiv="keywords" />

    <meta name="DC.Title" content="國民身分證領補換資料查詢作業" />
    <meta name="DC.Creator" content="內政部戶政司" />
    <meta name="DC.Publisher" content="內政部戶政司" />
    <meta name="DC.Source" content="內政部戶政司" />
    <meta name="DC.Type" content="text/html" />
    <meta name="DC.Format" content="text/html" />
    <meta name="DC.Language" content="繁體中文" />
    <meta name="DC.Rights" content="內政部戶政司" />
    <meta name="DC.Description" content="內政部戶政司全球資訊網" />
    

    <!-- secure information for MOICA validation rule -->
    <meta name="app.secure" content="normal" />

    <!-- default header name is X-CSRF-TOKEN -->
    <meta name="_csrf" content=""/>
    <meta name="_csrf_header" content=""/>

    <!-- icon & css recources -->
    





<script type="text/javascript" src="/apply-idCard/webjars/jquery/3.5.1/jquery.min.js"></script>






<script type="text/javascript" src="/apply-idCard/assets/bootstrap/js/bootstrap.min.js"></script>
<link rel="stylesheet" type="text/css" href="/apply-idCard/assets/bootstrap/css/bootstrap.min.css" />

<script type="text/javascript" src="/apply-idCard/assets/jquery-ui/jquery-ui.min.js"></script>
<link rel="stylesheet" href="/apply-idCard/assets/jquery-ui/jquery-ui.min.css" type="text/css" />
<link rel="stylesheet" href="/apply-idCard/assets/jquery-ui-themes/redmond/jquery-ui.min.css" type="text/css" />
<link rel="stylesheet" href="/apply-idCard/assets/jquery-ui-themes/redmond/theme.css" type="text/css" />

<script type="text/javascript" src="/apply-idCard/webjars/github-com-macek-jquery-serialize-object/2.5.0/dist/jquery.serialize-object.min.js" defer></script>

<script type="text/javascript" src="/apply-idCard/webjars/mobile-detect/1.3.6/mobile-detect.min.js"></script>

<script src="/apply-idCard/plugins/promise-polyfill/polyfill.min.js" defer></script>

<link rel="stylesheet" type="text/css" href="/apply-idCard/plugins/sweetalert2/sweetalert2.min.css" />
<script src="/apply-idCard/plugins/sweetalert2/sweetalert2.all.min.js" defer></script>

<link rel="stylesheet" type="text/css" href="/apply-idCard/plugins/jquery-confirm/jquery-confirm.min.css" />
<script src="/apply-idCard/plugins/jquery-confirm/jquery-confirm.min.js" defer></script>


<link rel="Shortcut Icon" href="/apply-idCard/global/images/classic/favicon.ico" />
<link rel="stylesheet" href="/apply-idCard/global/css/webapply.css" type="text/css" />
<link rel="stylesheet" href="/apply-idCard/global/css/aw3.form.css" type="text/css" />
<script type="text/javascript" src="/apply-idCard/global/js/aw3.polyfill.js"></script>
<script type="text/javascript" src="/apply-idCard/global/js/awUtils.js"></script>
<script type="text/javascript" src="/apply-idCard/global/js/errorcode.js"></script>
<script type="text/javascript" src="/apply-idCard/global/js/validator.js"></script>
<script type="text/javascript" src="/apply-idCard/global/js/aw3-global.Utils.js"></script>
<script type="text/javascript" src="/apply-idCard/global/js/aw3.font.js"></script>


    <!-- javeScript recources -->
    

<link rel="stylesheet" href="/apply-idCard/resources/css/idcard.css" type="text/css" />


    <!-- javeScript -->
    

</head>

<!-- style="margin: .8em; padding: .8em;" -->

<body style="height: 100%">
	<div class="container" style="padding: 6px; min-height: 300px;">
		<div>





<div class="container">
	<div class="col-xs-12 col-md-12">
		<div class="form-heading">
			<h1>國民身分證領補換資料查詢作業</h1>
		</div>
		<form class="form-horizontal">
			<div class="form-group-odd">
				<fieldset>
					<div class="col-xs-12 p-sm-top">
						<ul class="list-unstyled line-loose">
							<li>一、首先，請核對手上所持 國民身分證當事人人貌及基本資料是否相符。</li>
							<li>二、第二步，運用 <a class="strong" href="/documents/html/apply-idCard/188.html" data-portal="188" target="_blank" title="在新視窗開啟：國民身分證快速辨識">國民身分證快速辨識</a>
								及 <a class="strong" href="/documents/html/apply-idCard/189.html" data-portal="189" target="_blank" title="在新視窗開啟：國民身分證真偽辨識資料">國民身分證真偽辨識資料</a>，查驗國民身分證是否具有各項防偽變造項目。
							</li>
							<li>三、第三步，請依所持國民身分證記載之「統一編號」及「發證日期」輸入後，再自行選擇下列 <span class="color-red strong">&#9314;</span> 至 <span class="color-red strong">&#9315;</span> 查證項目中之１至２項，輸入資料。</li>
							<li>四、第四步，請依 <span class="color-red strong">2.</span> 圖形驗證所顯示之驗證碼，輸入字元。</li>
							<li>五、如輸入資料錯誤達２次，當日無法查詢。</li>
						</ul>
					</div>
				</fieldset>
			</div>

			<div class="form-group-even p-sm-top">
				<fieldset>
					
					<div class="col-xs-6 col-md-6 col-lg-6 text-right">
						<img class="img-thumbnail" src="/apply-idCard/resources/images/newid1-1.jpg" alt="中華民國國民身分證正面" />
					</div>
					<div class="col-xs-6 col-md-6 col-lg-6 text-left">
						<img class="img-thumbnail" src="/apply-idCard/resources/images/newid2-1.jpg" alt="中華民國國民身分證反面" />
					</div>
				</fieldset>
			</div>
		</form>
	</div>
</div></div>
		<div></div>
		<div></div>
		<div>




<div class="container">
	<div class="col-xs-12 col-md-12">
		<form action="/apply-idCard/app/idcard/IDCardReissue/query"
			class="form-horizontal" method="post">
			<div class="form-group-odd">
				<div class="text-center p-sm-top">
					<strong class="color-red">資料起始日期： 94年12月21日</strong>
				</div>
				<fieldset>
					<legend>1. 選擇申請類別</legend>
					<div class="form-group col-xs-12">
						<input type="hidden" name="applyType" id="applyType" value="94CARD" />
					</div>
					<div id="94CARDView">
						<div class="col-xs-12 row">
							<div class="col-xs-12">
								<label for="idnum"><span class="color-red">&#9312;
										*</span>國民身分證統一編號：</label>
							</div>
							<div class="col-xs-12 col-sm-6 col-md-4 col-lg-4">
								<input id="idnum94" name="idnum" class="form-control text-uppercase" placeholder="請輸入統一編號" type="text" value="E224394391" maxlength="10"/>
								
							</div>
						</div>
						<div class="col-xs-12 p-sm-top row">
							<div class="col-xs-12">
								<label for="applyTWY"><span class="color-red">&#9313;
										*</span>發證日期：</label>
							</div>
							<div class="col-xs-12">
								民國
								<select id="applyTWY" name="applyTWY" class="form-control inline min-control" required="required"><option value=""></option><option value="110">110</option><option value="109" selected="selected">109</option><option value="108">108</option><option value="107">107</option><option value="106">106</option><option value="105">105</option><option value="104">104</option><option value="103">103</option><option value="102">102</option><option value="101">101</option><option value="100">100</option><option value="99">99</option><option value="98">98</option><option value="97">97</option><option value="96">96</option><option value="95">95</option><option value="94">94</option></select>
								<label for="applyTWY">年</label>
								<select id="applyMM" name="applyMM" class="form-control inline min-control" required="required"><option value=""></option><option value="1">1</option><option value="2">2</option><option value="3">3</option><option value="4">4</option><option value="5">5</option><option value="6">6</option><option value="7">7</option><option value="8" selected="selected">8</option><option value="9">9</option><option value="10">10</option><option value="11">11</option><option value="12">12</option></select>
								<label for="applyMM">月</label>
								<select id="applyDD" name="applyDD" class="form-control inline min-control" required="required"><option value=""></option><option value="1">1</option><option value="2">2</option><option value="3">3</option><option value="4">4</option><option value="5">5</option><option value="6">6</option><option value="7">7</option><option value="8">8</option><option value="9">9</option><option value="10">10</option><option value="11">11</option><option value="12">12</option><option value="13">13</option><option value="14">14</option><option value="15">15</option><option value="16">16</option><option value="17">17</option><option value="18">18</option><option value="19">19</option><option value="20">20</option><option value="21">21</option><option value="22">22</option><option value="23">23</option><option value="24">24</option><option value="25">25</option><option value="26">26</option><option value="27">27</option><option value="28" selected="selected">28</option><option value="29">29</option><option value="30">30</option><option value="31">31</option></select>
								<label for="applyDD">日</label>
								
								
								
							</div>
						</div>
						<div class="col-xs-12 row p-sm-top">
							<div class="col-xs-12 p-sm-top">
								<label for="siteId"><span class="color-red">&#9314;
										*</span>發證地點：</label>
							</div>
							<div class="col-xs-12 col-sm-6 col-md-8 col-lg-8 p-sm-top">
								<select id="siteId" name="siteId" class="form-control">
									<option value="0">請選擇</option>
									<option value="10001">北縣</option><option value="10002">宜縣</option><option value="10003">桃縣</option><option value="10004">竹縣</option><option value="10005">苗縣</option><option value="10006">中縣</option><option value="10007">彰縣</option><option value="10008">投縣</option><option value="10009">雲縣</option><option value="10010">嘉縣</option><option value="10011">南縣</option><option value="10012">高縣</option><option value="10013">屏縣</option><option value="10014">東縣</option><option value="10015">花縣</option><option value="10016">澎縣</option><option value="10017">基市</option><option value="10018">竹市</option><option value="10020">嘉市</option><option value="09007">連江</option><option value="09020">金門</option><option value="63000">北市</option><option value="64000">高市</option><option value="65000">新北市</option><option value="66000">中市</option><option value="67000">南市</option><option value="68000">桃市</option>
								</select>
								
							</div>
						</div>
						<div class="col-xs-12 row p-sm-top">
							<div class="col-xs-12">
								<label for="applyReason"><span class="color-red">&#9315;
										*</span>領補換類別：</label>
							</div>
							<div class="col-xs-12 col-sm-6 col-md-8 col-lg-8 p-sm-top">
								<select id="applyReason" name="applyReason" class="form-control">
									<option value="0">請選擇</option>
									<option value="1">初發</option>
									<option value="2">補發</option>
									<option value="3">換發</option>
								</select>
								
							</div>
						</div>
					</div>
					<div id="NEWEIDView" class="hide">
						<div class="col-xs-12 row">
							<div class="col-xs-12">
								<label for="idnum"><span class="color-red">&#9312;
										*</span>國民身分證統一編號：</label>
							</div>
							<div class="col-xs-12 col-sm-6 col-md-4 col-lg-4">
								<input id="idnumNew" name="idnumNew" class="form-control text-uppercase" placeholder="請輸入統一編號" type="text" value="" maxlength="10"/>
								
							</div>
						</div>
						<div class="col-xs-12 row">
							<div class="col-xs-12">
								<label for="printYyymmdd"><span class="color-red">&#9312;
										*</span> 製證日期：</label>
							</div>
							<div class="col-xs-12 col-sm-7 col-md-4 col-lg-4">
								















<script src="/apply-idCard/global/js/aw3.datepick.js" type="text/javascript" defer></script>
<script src="/apply-idCard/global/js/aw3.datepick.tw.js" type="text/javascript" defer></script>
<script src="/apply-idCard/global/js/aw3.dateUtils.js" type="text/javascript" defer></script>
<script src="/apply-idCard/global/js/aw3.twDatepicker.js" type="text/javascript" defer></script>
<link href="/apply-idCard/global/css/aw3.datepick.css" rel="stylesheet" />
<script type="text/javascript" charset="UTF-8" defer>
$(function () {
	var id = 'printYyymmdd';
	var value = ''
	twDatepicker.register(id, value,'/apply-idCard/global/images/jquery-ui',false);
});
</script>
<div>
	<label for="twDatepicker_year_printYyymmdd">&nbsp;民國&nbsp;</label>
	<input type="text" id="twDatepicker_year_printYyymmdd" class="form-control yyymmdd text-integer" maxlength="3" data-range="-10:0" />
	<label for="twDatepicker_year_printYyymmdd">&nbsp;年&nbsp;</label>
	<select id="twDatepicker_mon_printYyymmdd" class="form-control inline min-control yyymmdd">
		<option value="">月份</option>
		<option value="01">01</option>
		<option value="02">02</option>
		<option value="03">03</option>
		<option value="04">04</option>
		<option value="05">05</option>
		<option value="06">06</option>
		<option value="07">07</option>
		<option value="08">08</option>
		<option value="09">09</option>
		<option value="10">10</option>
		<option value="11">11</option>
		<option value="12">12</option>
	</select>
	<label for="twDatepicker_mon_printYyymmdd">&nbsp;月&nbsp;</label>
	<select id="twDatepicker_day_printYyymmdd" class="form-control inline min-control yyymmdd"></select>
	<label for="twDatepicker_day_printYyymmdd">&nbsp;日&nbsp;</label>
	<input type="hidden" id="twDatepicker_printYyymmdd" name="printYyymmdd" />
</div>

								
							</div>
						</div>

						<div class="col-xs-12 row">
							<div class="col-xs-12">
								<label for="eidNo"><span class="color-red">&#9314;
										*</span>卡片序號(eID背面條碼)：</label>
							</div>
							<div class="col-xs-12 col-sm-6 col-md-4 col-lg-4">
								<input id="eidNo" name="eidNo" class="form-control" placeholder="請輸入證件編號" required="required" type="text" value="" maxlength="15"/>
								
							</div>
						</div>
					</div>
					<div id="TEMPAPPLYView" class="hide">
						<div class="col-xs-12 row">
							<div class="col-xs-12">
								<label for="idnum"><span class="color-red">&#9312;
										*</span>臨時證明書檢查碼：</label>
							</div>
							<div class="col-xs-12 col-sm-8 col-md-4 col-lg-4">
								<input id="tempApplyNo" name="tempApplyNo" class="form-control" placeholder="請輸入臨時證明書檢查碼" required="required" type="text" value="" maxlength="30"/>
								
							</div>
						</div>
					</div>
				</fieldset>
			</div>
			<div class="form-group-even">
				<fieldset>
					<legend>2. 圖形驗證</legend>
					<div class="form-group row col-xs-12">
						<div class="col-xs-12 captcha p-sm-top">
							<script src="/apply-idCard/global/js/aw3.captcha.js" type="text/javascript" defer></script>
<script type="text/javascript">
$(function(){
	captcha.register({root: '/apply-idCard/captcha/', id: 'captcha-refresh', width: '240px', height: '100px', lang: ''});
});
</script>
<div class="row">
	<div class="col-xs-12">
		<label for="imageBlock_captcha-refresh"><span class="color-red">*</span> 圖形驗證：</label>
		<span id="captchaBox_captcha-refresh"></span>
		<button type="button" class="captcha btn btn-primary" id="imageBlock_captcha-refresh">產製新驗證碼</button>
		<button type="button" class="captcha btn btn-primary" id="voiceplay_captcha-refresh">語音播放</button>
		</div>
</div>
<div class="row">
	<div class="col-xs-12 col-sm-6 col-md-4">
		<label for="captchaInput_captcha-refresh">圖形驗證碼：</label>
		<input id="captchaInput_captcha-refresh" name="captchaInput" placeholder="請輸入圖形驗證碼" class="form-control" required="required" type="text" value="HZEU5" maxlength="5" autocomplete="off"/><label for="captchaKey_captcha-refresh" style="display:none;">圖形驗證碼：</label><input type="text" style="display:none;" name="captchaKey" id="captchaKey_captcha-refresh" value="c8200e1623a6431d883c6ad91de32912" />
		<div id="captchaInput.errors" class="error-message">驗證碼輸入錯誤</div></div>
</div>
						</div>
					</div>
				</fieldset>
			</div>
			<div class="form-group-odd">
				<fieldset>
					<div class="col-xs-12 p-sm-top">
						<ul class="list-unstyled">
							<li><label><i class="glyphicon glyphicon-ok"></i>
									如果圖形驗證碼難以辨識，可點擊「刷新驗證碼」按鈕，刷新一張圖形驗證碼。</label></li>
							<li><label><i class="glyphicon glyphicon-ok"></i>
									為確保個人隱私，請輸驗證碼。圖形驗證碼最佳解析度為 1024 &#215; 768。</label></li>
							<li><label><i class="glyphicon glyphicon-ok"></i>
									未經本部同意不得非法連結運用本網頁資料，請勿使用轉址或來路不明網站連結至本網頁。</label></li>
						</ul>
					</div>
					<div class="text-center col-xs-12 p-sm-top">
						<span class="color-red"><strong>請確實逐項核對輸入項目</strong></span>
					</div>
				</fieldset>
			</div>
			<div class="form-group row text-center p-sm-top">
				<button type="button" class="btn btn-primary query">送出</button>
				<button type="reset" class="btn btn-danger">取消</button>
			</div>
		</form>
	</div>
</div>
<div class="m-xl-y"></div>
<div class="p-xl-y"></div>

<script type="text/javascript">
	(function(){var e=new MobileDetect(window.navigator.userAgent),d=window.top.document.querySelector("iframe");if(d&&e.mobile())d.style.width=(window.top.screen.width||window.top.innerWidth)-50+"px"})();
$(function(){var e=$("#94CARDView"),d=$("#NEWEIDView"),f=$("#TEMPAPPLYView"),g=$("input[name=applyType]");g.on("click",function(){switch($(this).val()){case "94CARD":e.removeClass("hide");d.addClass("hide");f.addClass("hide");$("#idnum94").prop("disabled",!1);$("#idnumNew").prop("disabled",!0);break;case "NEWEID":e.addClass("hide");d.removeClass("hide");f.addClass("hide");$("#idnum94").prop("disabled",!0);$("#idnumNew").prop("disabled",!1);break;case "TEMPAPPLY":e.addClass("hide"),d.addClass("hide"),
f.removeClass("hide")}});g.filter(":checked").trigger("click");var h=$("button.query").on("click",function(){var c=$(this).closest("form");aw.loading("open");c.find("button").blur().attr("disabled",!0);var a=c.serializeObject(),b=[];a.applyType=="94CARD"?(console.log(a.idnum.length),(!a.idnum||a.idnum.length!==10)&&b.push("\u8acb\u8f38\u516510\u78bc\u8eab\u5206\u8b49\u5b57\u865f\uff01"),(!a.applyTWY||a.applyTWY.length===0||!a.applyMM||a.applyMM.length===0||!a.applyDD||a.applyDD.length===0)&&b.push("\u8acb\u8f38\u5165\u5b8c\u6574\u7684\u767c\u8b49\u65e5\u671f\uff01"),
a.siteId=="0"&&a.applyReason=="0"&&b.push("\u8acb\u8f38\u5165\u6240\u9700\u67e5\u9a57\u9805\u76ee\uff1a\u767c\u8b49\u5730\u9ede\u6216\u9818\u88dc\u63db\u985e\u5225\uff01")):a.applyType=="NEWEID"?(console.log(a.idnumNew.length),(!a.idnumNew||a.idnumNew.length!==10)&&b.push("\u8acb\u8f38\u516510\u78bc\u8eab\u5206\u8b49\u5b57\u865f\uff01"),a.printYyymmdd||b.push("\u8acb\u8f38\u5165\u88fd\u8b49\u65e5\u671f\uff01"),a.eidNo||b.push("\u8acb\u8f38\u5165\u8b49\u4ef6\u7de8\u865f\uff01")):a.applyType=="TEMPAPPLY"&&
(a.tempApplyNo||b.push("\u8acb\u8f38\u5165\u81e8\u6642\u8b49\u660e\u66f8\u6aa2\u67e5\u78bc\uff01"));(!a.captchaInput||a.captchaInput.length===0)&&b.push("\u8acb\u8f38\u5165\u5716\u5f62\u9a57\u8b49\u78bc\uff01");if(b.length>0)return aw.loading({close:!0,afterClose:function(){c.find("button").attr("disabled",!1);aw.dialog(b.join("\n"))}}),!1;window.setTimeout(function(){c.submit()},300)});$("form input:text:first").focus();h.closest("form").find(":text").on("keypress",function(c){c.which==13&&(c.preventDefault(),
$(c.target).blur(),h.trigger("click"))});aw.resizeIframe();aw.portal.goTop()});
</script>
</div>
		<div></div>
	</div>
	



	<script async src="https://www.googletagmanager.com/gtag/js?id=UA-28221981-1"></script>
	<script>
	  window.dataLayer = window.dataLayer || [];
	  function gtag(){dataLayer.push(arguments);}
	  gtag('js', new Date());

	  gtag('config', 'UA-28221981-1');
	</script>

</body>
</html>
