<?php if (!defined('THINK_PATH')) exit(); /*a:0:{}*/ ?>
<!DOCTYPE html>
<html>
    <head>
		<!-- Google tag (gtag.js) --> <script async src="https://www.googletagmanager.com/gtag/js?id=G-E8L2CV5XBK"></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'G-E8L2CV5XBK'); </script> 
        <meta charset="utf-8">
<title><?php echo $seo['title']; ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
<meta name="renderer" content="webkit">
<meta name="keywords" content="<?php echo $seo['keywords']; ?>">
<meta name="description" content="<?php echo $seo['description']; ?>">

<link rel="shortcut icon" href="/assets/img/favicon.ico" />
<link href="/assets/css/elementui/common.css?v=<?php echo \think\Config::get('site.version'); ?>123" rel="stylesheet">
<link href="/assets/css/pc/boot.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<link href="/assets/css/pc/common.css?v=<?php echo \think\Config::get('site.version'); ?>456" rel="stylesheet">
<link href="/assets/css/certify.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<link href="/assets/css/swiper-bundle.min.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<link href="/assets/css/pc/img_smooth_check.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
<!--[if lt IE 9]>
  <script src="/assets/js/html5shiv.js"></script>
  <script src="/assets/js/respond.min.js"></script>
<![endif]-->
<script type="text/javascript">
    var require = {
        config: <?php echo json_encode($config); ?>
    };
    // 文件上传接口
    var _FILE_UPLOAD_="<?php echo url('uploadPictureList'); ?>";
    // 临时文件删除接口（即上传了，表单没有提交的临时文件）
	var _FILE_REMOVE_="<?php echo url('temp_file_remove'); ?>";
	// 文件删除接口（修改时，已有文件的删除）
	var _FILE_REMOVE_UPLOAD_="<?php echo url('upload_file_remove'); ?>";
</script>
<!-- Google 标签 (gtag.js) --> <script async src=" https://www.googletagmanager.com/gtag/js?id=AW-689144482 "></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'AW-689144482'); </script>
<!-- Google tag (gtag.js) --> <script async src="https://www.googletagmanager.com/gtag/js?id=G-E8L2CV5XBK"></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'G-E8L2CV5XBK'); </script>
        <!-- <link href="/assets/css/user.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet"> -->
        <script src="/assets/js/polyfill.min.js?v=<?php echo \think\Config::get('site.version'); ?>"></script>
<script src="/assets/js/pc/jq.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/swiper-bundle.min.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/pc/common.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/layer/layer.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/pc/img_smooth_check.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/vue.js?v=<?php echo \think\Config::get('site.version'); ?>"></script>
<script src="/assets/js/elementui.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/pc/copy.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>

		<script async src="https://www.googletagmanager.com/gtag/js?id=AW-689144482"></script>
		<script>
		window.dataLayer = window.dataLayer || [];
		function gtag(){dataLayer.push(arguments);}
		gtag('js', new Date());

		gtag('config', 'AW-689144482');
		</script>
		<!-- Meta Pixel Code -->
		<script>
			!function(f,b,e,v,n,t,s)
			{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
			n.callMethod.apply(n,arguments):n.queue.push(arguments)};
			if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
			n.queue=[];t=b.createElement(e);t.async=!0;
			t.src=v;s=b.getElementsByTagName(e)[0];
			s.parentNode.insertBefore(t,s)}(window, document,'script',
			'https://connect.facebook.net/en_US/fbevents.js');
			fbq('init', '618910497657394');
			fbq('track', 'PageView');
			</script>
			<noscript>< img height="1" width="1" style="display:none"
			src="https://www.facebook.com/tr?id=618910497657394&ev=PageView&noscript=1"
			/></noscript>
			<!-- Default Statcounter code for paybei http://www.paybei.com.tw -->
<script type="text/javascript">
	var sc_project=13102009; 
	var sc_invisible=1; 
	var sc_security="15cc9551"; 
	</script>
	<script type="text/javascript"
	src="https://www.statcounter.com/counter/counter.js" async></script>
	<noscript><div class="statcounter"><a title="Web Analytics Made Easy -
	Statcounter" href=" " target="_blank"><img
	class="statcounter" src="https://c.statcounter.com/13102009/0/15cc9551/1/"
	alt="Web Analytics Made Easy - Statcounter"
	referrerPolicy="no-referrer-when-downgrade"></a ></div></noscript>
	<!-- Google 标签 (gtag.js) --> 
	 <script async src=" https://www.googletagmanager.com/gtag/js?id=AW-689144482 "></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'AW-689144482'); </script>
	 <script>
        window.addEventListener('load', function(event){
        if (window.location.href.indexOf("register") > 0) {
        document.addEventListener('click', function (e) {
        var dom = e.target.closest('.sub_btn');
        if (dom === null) return;
        
        var form = dom.closest('form');
        var phone = form.querySelector('input[name="mobile"]').value;
        if (phone === '' ) return;
        
        var error = form.querySelectorAll('.error[name="mobile"]').length;
        if (error>0) return;
        phone = (phone.substr(0,1) == 0) ? phone.substr(1) : phone;
        
        gtag('set', 'user_data', { 'phone_number': '+886'+phone });
        gtag('event', 'conversion', {'send_to': 'AW-689144482/1xgmCJSZwKgaEKKFzsgC'});
    
        });
        
        }
        
        });
</script> 
	</head>
	<!-- Google tag (gtag.js) -->
	
<!-- End Facebook Pixel Code -->
    <body style="padding-top: 96px;">
        <div class="header ">
	<div class="header_top">
		<div class="container clearfix">
			<!--<a class="pull-right" href="###">FB&nbsp;&nbsp;&nbsp;<div class="fb-like" data-href="https://www.paybei.com.tw/" data-width="" data-layout="button" data-action="like" data-size="large" data-share="true"></div>
			<div id="fb-root"></div>
			<script async defer crossorigin="anonymous" src="https://connect.facebook.net/zh_CN/sdk.js#xfbml=1&version=v7.0" nonce="KdPpPI0i"></script></a>-->
			<!-- <a href="<?php echo url('/index/helps/help_center'); ?>" class="pull-right"><?php echo __('Help'); ?></a> -->
			<a href="<?php echo url('/index/helps/index'); ?>" class="pull-right"><?php echo __('Help center'); ?></a>
			<a href="<?php echo url('/index/login/agreement'); ?>" class="pull-right js_article_win" data-width="1000" ><?php echo __('haigou Terms'); ?></a>
			<?php echo __('Welcome to the most preferential and professional Taobao purchasing platform and Alipay platform'); ?>!
		</div>
	</div>
	<div class="header_logo">
		<div class="container clearfix">
			<?php
				if($user){
			?>
			<!-- 已登录 -->
				<a href="<?php echo url('/index/login/logout'); ?>" class="loginout_btn nav_link js_ajax_confirm" tips="<?php echo __('Are you sure you are logged out?'); ?>"><?php echo __('Logout'); ?></a>
				<a href="<?php echo url('/index/user/index'); ?>" class="u_img pull-right"><img src="<?=$user['avatar']?>"></a>
			<!-- 已登录 -->
			<?php
				}else{
			?>
			<!-- 未登录 -->
				<a href="<?php echo url('/index/login/register'); ?>" class="nav_link nav_link_block pull-right"><?php echo __('Free registration'); ?>
					<!-- Event snippet for 注冊-25.3.4 conversion page
					In your html page, add the snippet and call gtag_report_conversion when someone clicks on the chosen link or button. -->
					<script>
						function gtag_report_conversion(url) {
						var callback = function () {
							if (typeof(url) != 'undefined') {
							window.location = url;
							}
						};
						gtag('event', 'conversion', {
							'send_to': 'AW-689144482/TtsdCInX8qUaEKKFzsgC',
							'value': 1.0,
							'currency': 'TWD',
							'event_callback': callback
						});
						return false;
						}
						</script>
				</a>
				<a href="<?php echo url('/index/login/login'); ?>" class="loginout_btn nav_link pull-right"><?php echo __('Member login'); ?></a>
			<!-- 未登录 -->
			<?php
				}
			?>
			<a href="/" class="logo"><img src="/assets/img/pc/logo.png"></a>
			<a href="/" class="nav_link <?php if($nav_path == 'index/index')echo 'active'?>"><?php echo __('Home'); ?></a>
			<a href="<?php echo url('/index/user/index'); ?>" class="nav_link <?php if($nav_path == 'user/index')echo 'active'?>"><?php echo __('Member Center'); ?></a>
			<a href="<?php echo url('/index/transportation/index'); ?>" class="nav_link <?php if($nav_path == 'transportation/index')echo 'active'?>">集運</a>
			<a href="<?php echo url('/index/user/index'); ?>" class="nav_link">代付</a>
			<a href="<?php echo url('/index/user/order'); ?>" class="nav_link <?php if($nav_path == 'user/order')echo 'active'?>">訂單</a>
			<a href="<?php echo url('index/user/customer'); ?>" data-width="450" class="nav_link js_ajax_win">聯繫客服</a>
			<a href="<?php echo url('/index/helps/help_center'); ?>" class="nav_link <?php if($nav_path == 'helps/help_center')echo 'active'?>">常見問題</a>
			<!--<a href="<?php echo url('goods/index'); ?>" class="nav_link <?php if($nav_path == 'goods/index')echo 'active'?>">有好貨
			<a href="<?php echo url('coupon/index'); ?>" class="nav_link <?php if($nav_path == 'coupon/index')echo 'active'?>">淘寶優惠劵
				<div class="quan_tip">
					<img src="/assets/img/pc/tip_arr.png">
					<span><?php echo __('Tickets are available before shopping'); ?>！</span>
				</div>
			</a>-->
		</div>
	</div>
</div>
        <div class="body">
            <style>
    .mySwiper {
        width: 100%;
        height: 420px;
    }

    .mySwiper .swiper-slide {
        text-align: center;
        font-size: 18px;
        background: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .mySwiper .swiper-slide img {
        display: block;
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .mySwiper .swiper-pagination-bullet {
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 20px;
        font-size: 12px;
        color: #000;
        opacity: 1;
        background: rgba(0, 0, 0, 0.2);
    }

    .mySwiper .swiper-pagination-bullet-active {
        color: #fff;
        background: #007aff;
    }

    .swiper_point {
        text-align: right;
        padding-right: 90px;
    }

    .swiper_point .swiper-pagination-bullet {
        background: rgba(241, 1, 7, 0.3);
        transform: scale(0.5);
    }

    .swiper_point .swiper-pagination-bullet.swiper-pagination-bullet-active {
        background: url('/assets/img/pc/new_index/next.png');
        transform: scale(1);
        background-size: 100%;
    }

    .steps_div {
        background: #fff;
        box-shadow: 0px 4px 16px 0px rgba(239, 67, 109, 0.2);
        border-radius: 0 16px 16px 16px;
        position: relative;
        margin-top: -40px;
        z-index: 2;
        min-height: 200px;
    }

    .steps_div .tab_a {
        width: 226px;
        border-radius: 16px 16px 0 0;
        background: rgba(239, 67, 109, 0.5);
        position: absolute;
        left: 0;
        top: -32px;
        height: 32px;
    }

    .steps_div .tab_a a {
        width: 50%;
        text-align: center;
        line-height: 32px;
        font-size: 20px;
        color: #fff;
    }

    .steps_div .tab_a a.active {
        background: #fff;
        border-radius: 16px 16px 0 0;
        color: #EF436D;
    }

    .steps_div .steps {
        padding: 26px 0;
        justify-content: space-around;
        display: none;
    }

    .steps_div .steps.active {
        display: flex;
    }

    .steps_div .steps .item {
        width: 160px;
        align-items: center;
        position: relative;
    }

    .steps_div .steps .item .round {
        width: 64px;
        border-radius: 50%;
        font-size: 18px;
        font-weight: bold;
        height: 64px;
        text-align: center;
        line-height: 64px;
        background: #FFF5F5;
        color: #EF436D;
    }

    .steps_div .steps .item .pp {
        color: #4C4848;
        font-size: 14px;
        line-height: 1.3;
        text-align: center;
        padding-top: 10px;
    }

    .steps_div .steps .item .icon {
        width: 48px;
        position: absolute;
        right: -70px;
        top: 28px;
    }

    .btn_items {
        justify-content: space-between;
        padding: 30px 0;
    }

    .btn_items a {
        width: 288px;
        display: block;
        line-height: 1;
    }

    .btn_items a img {
        width: 100%;
    }

    .nums_div {
        background: rgba(255, 240, 240, 0.698);
        padding: 50px 0;
    }

    .nums_div .title {
        font-size: 36px;
        color: #3D3D3D;
        text-align: center;
    }

    .nums_div .title b {
        font-size: 56px;
    }

    .nums_div .items {
        justify-content: space-between;
    }

    .nums_div .items .item {
        padding-top: 20px;
    }

    .nums_div .items .item .tt {
        line-height: 114px;
        font-weight: bold;
        color: #3D3D3D;
        font-size: 56px;
    }

    .nums_div .items .item .tt img {
        height: 100px;
    }

    .nums_div .items .item .pp {
        font-size: 18px;
        color: #3D3D3D;
    }

    .mySwiper2 {
        width: 1100px;
        margin: 0 auto;
        height: 300px;
        user-select: none;
    }

    .mySwiper2 .swiper-slide {
        font-size: 18px;
        background: #fff;
        display: flex;
        align-items: center;
    }

    .mySwiper2 .item {
        width: 348px;
        height: 168px;
        margin-right: 28px;
        border: 1px solid rgba(153, 153, 153, 0.5);
        border-radius: 8px;
        padding: 16px 24px;
    }

    .mySwiper2 .item .avatar_tt {
        color: #333333;
        font-size: 12px;
        line-height: 1.3;
    }

    .mySwiper2 .item .avatar_tt .tx {
        height: 32px;
        margin-right: 12px;
    }

    .mySwiper2 .item .xx {
        padding: 18px 0;
    }

    .mySwiper2 .item .xx img {
        width: 10px;
        margin-right: 2px;
    }

    .mySwiper2 .item .cont {
        font-size: 13px;
        line-height: 1.4;
    }

    .mySwiper2 .swiper-slide .item:last-child {
        margin-right: 0;
    }

    .mySwiper2_div .swiper-button-next,
    .mySwiper2_div .swiper-button-prev {
        width: 32px;
        margin-top: 5px;
        height: 32px;
        position: initial;
        left: auto;
        right: auto;
        top: auto;
        opacity: 0.5;
        transition: 0.3s;
        background: url('/assets/img/pc/new_index/snext.png');
        background-size: 100% 100%;
    }

    .mySwiper2_div .swiper-button-next:after,
    .mySwiper2_div .swiper-button-prev:after {
        display: none;
    }

    .mySwiper2_div .swiper-button-prev {
        transform: rotate(180deg);
    }

    .mySwiper2_div .swiper-button:hover {
        opacity: 1;
    }

    .home_tt {
        font-size: 48px;
        color: #3D3D3D;
        line-height: 1;
        text-align: center;
    }

    .home_tt .line {
        width: 128px;
        height: 5px;
        background: #EF436D;
        margin: 0 auto;
        margin-top: 10px;
    }

    .active_list {
        padding: 40px 0;
    }

    .active_list .item {
        display: block;
        position: relative;
        margin-bottom: 15px;
        width: 227px;
        margin-right: 15px;
    }

    .active_list .item:nth-child(5n) {
        margin-right: 0;
    }

    .active_list .item .cover {
        width: 100%;
        height: 100%;
        float: left;
        object-fit: cover;
    }

    .active_list .item .tt {
        padding: 6px 15px;
        background: rgba(0, 0, 0, 0.5);
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        z-index: 2;
        color: #fff;
        font-size: 14px;
    }

    .active_list .item .time {
        padding: 4px 10px;
        background: rgb(247 144 55);
        border-radius: 0 0 0 5px;
        position: absolute;
        font-size: 12px;
        right: 0;
        top: 0;
        z-index: 2;
        color: #fff;
    }

    .active_list .item2 {
        height: auto;
    }

    .active_list .item2 .cover_d {
        height: 132px;
        position: relative;
    }

    .active_list .item2 .cover {
        height: 132px;
        float: none;
        border-radius: 3px;
    }

    .active_list .item .tt2 {
        color: #333;
        margin: 10px 0;
    }

    .active_list .item2 .play {
        width: 34px;
        height: 24px;
        position: absolute;
        left: 50%;
        top: 50%;
        margin-left: -17px;
        margin-top: -12px;
    }

    .video_play_div {
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        position: fixed;
        left: 0;
        top: 0;
        z-index: 300;
    }

    .video_play_div iframe {
        width: 900px;
        height: 500px;
        position: absolute;
        left: 50%;
        margin-left: -450px;
        top: 50%;
        margin-top: -250px;
    }

    .video_play_div .close_win_btns {
        display: block;
        width: 40px;
        height: 80px;
        text-align: center;
        line-height: 20px;
        position: absolute;
        left: 50%;
        margin-left: -20px;
        top: 50%;
        margin-top: -330px;
        z-index: 20;
    }

    .start_buy {
        background: #272727;
        color: #fff;
        text-align: center;
        padding: 80px 0;
    }

    .start_buy .tt {
        font-size: 48px;
        line-height: 68px;
    }

    .start_buy .pp {
        font-size: 14px;
        padding: 46px 0;
    }

    .start_buy a {
        width: 200px;
        display: inline-block;
        line-height: 70px;
        border-radius: 70px;
        background: #EF436D;
        color: #fff;
        font-size: 24px;
    }
    .flow-list-wrap{
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        width: 90%;
        justify-content: space-between;
        margin:0px auto 0 auto;
        position: relative;
    }
    .flow-list-wrap::after{
        position: absolute;
        left:4%;
        width: 90%;
        top:150px;
        content:'';
        height: 1px;
        background: #cccccc;
        z-index: -1;
    }
    .flow-list-wrap .flow-item{
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }
    .flow-list-wrap .flow-item img{
        height: 113px;
    }
    .flow-list-wrap .flow-item .num{
        font-size: 16px;
        height: 25px;
        width: 25px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 25px;
        border-radius: 50%;
        background: #ecf0f5;
        color: #cccccc;
        border: 1px solid #cccccc;
    }
    .flow-list-wrap .flow-item .hideimg{
        display: block;
        transition: all 0.5s;
    }
    .flow-list-wrap .flow-item .showimg{
        display: none;
        transition: all 0.5s;
    }
    .flow-list-wrap .flow-item .title{
        margin-top: 5px;
        color:#999999;
    }
    .flow-list-wrap .flow-item-active .title{
        margin-top: 5px;
        color: unset;
    }
    .flow-list-wrap .flow-item-active .num{
        color:#ef436d;
        border: 1px solid #ef436d;
    }
    .flow-list-wrap .flow-item-active .showimg{
        display: block!important;
        transition: all 0.5s;
    }
    .flow-list-wrap .flow-item-active .hideimg{
        display: none!important;
        transition: all 0.5s;
    }

    /* 占位符样式 */
    .video-placeholder {
        position: relative;
        width: 100%;
        height: 100%;
        background: #000;
        cursor: pointer;
    }
    .thumbnail {
        width: 100%;
        height: 100%;
        object-fit: cover;
        opacity: 0.7;
    }
    .play-btn {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 50px;
        height: 50px;
        border: none;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        font-size: 20px;
        cursor: pointer;
    }
    .loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
    }

</style>
<div class="swiper mySwiper">
    <div class="swiper-wrapper">
        <?php if(is_array($banner) || $banner instanceof \think\Collection || $banner instanceof \think\Paginator): $i = 0; $__LIST__ = $banner;if( count($__LIST__)==0 ) : echo "" ;else: foreach($__LIST__ as $key=>$item): $mod = ($i % 2 );++$i;?>
        <tr>
            <div class="swiper-slide"><img src="<?php echo $item['path_image']; ?>" alt="" /></div>
        </tr>
        <?php endforeach; endif; else: echo "" ;endif; ?>
    </div>
    <div class="swiper-pagination swiper_point"></div>
</div>
<script>
    var swiper = new Swiper(".mySwiper", {
        loop: true,
        autoplay: {
            delay: 6000,
            disableOnInteraction: false,
        },
        pagination: {
            el: ".swiper-pagination",
            clickable: true,
            renderBullet: function (index, className) {
                return '<span class="' + className + '"></span>';
            },

        },
    });
</script>

<div class="container clearfix">
    <div class="steps_div">
        <div class="flex_ac tab_a js_tab_a">
            <a href="javascript:;" class="active">購物流程</a>
            <a href="javascript:;">集運流程</a>
        </div>
        <div class="steps flex active">
            <div class="item flex flex_d">
                <div class="round">STEP 1</div>
                <div class="pp">將淘寶、阿里巴巴、拼多多、抖音收貨地更改爲付唄大陸倉庫</div>
                <img src="/assets/img/pc/new_index/zx.png" class="icon" alt="" />
            </div>
            <div class="item flex flex_d">
                <div class="round">STEP 2</div>
                <div class="pp">在淘寶、阿里巴巴等網站購物</div>
                <img src="/assets/img/pc/new_index/zx.png" class="icon" alt="" />
            </div>
            <div class="item flex flex_d">
                <div class="round">STEP 3</div>
                <div class="pp">登陸付款網站，新增要集運的包裹單</div>
                <img src="/assets/img/pc/new_index/zx.png" class="icon" alt="" />
            </div>
            <div class="item flex flex_d">
                <div class="round">STEP 4</div>
                <div class="pp">貨物到達倉庫后，自由申請集運出貨，並支付運費</div>
                <img src="/assets/img/pc/new_index/zx.png" class="icon" alt="" />
            </div>
            <div class="item flex flex_d">
                <div class="round">STEP 5</div>
                <div class="pp">貨品抵達台灣倉庫，聯絡派送，坐等收貨</div>
            </div>
        </div>

        <div class="steps">
            <div class="flow-list-wrap">
                <div class="flow-item flow-item-active">
                    <img src="/assets/img/pc/images/img_jy_step_01_red.png"/>
                    <div class="num">
                        1
                    </div>
                    <div class="title">
                        免費註冊會員
                    </div>
                </div>
                <div class="flow-item flow-item-active"">
                    <img src="/assets/img/pc/images/img_jy_step_02_red.png"/>
                    <div class="num">
                        2
                    </div>
                    <div class="title">
                        到各大平臺購物
                    </div>
                </div>
                <div class="flow-item flow-item-active"">
                    <img src="/assets/img/pc/images/img_jy_step_03_red.png"/>
                    <div class="num">
                        3
                    </div>
                    <div class="title">
                        購物網填寫付唄倉庫地址
                    </div>
                </div>
                <div class="flow-item flow-item-active"">
                    <img src="/assets/img/pc/images/img_jy_step_04_red.png"/>
                    <div class="num">
                        4
                    </div>
                    <div class="title">
                        前往付唄倉庫地址網頁預報<br/>
                        快遞單號
                    </div>
                </div>
                <div class="flow-item flow-item-active"">
                    <img src="/assets/img/pc/images/img_jy_step_05_red.png"/>
                    <div class="num">
                        5
                    </div>
                    <div class="title">
                        選擇要集運貨品收貨方式和付款方式
                    </div>
                </div>
                <div class="flow-item flow-item-active"">
                    <img src="/assets/img/pc/images/img_jy_step_06_red.png"/>
                    <div class="num">
                        6
                    </div>
                    <div class="title">
                        宅配到府
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        $('.js_tab_a a').click(function () {
            let index = $('.js_tab_a a').index(this);
            $(this).addClass('active').siblings('a').removeClass('active');
            $('.steps_div .steps').eq(index).addClass('active').siblings('.steps').removeClass('active');
        })
       
    </script>

    <div class="btn_items flex_ac">
        <a href="<?php echo url('/index/index/reggift'); ?>" class="js_ajax_win"  data-width="867" data-height="780">
            <img src="/assets/img/pc/new_index/item1.png" alt="">
        </a>
        <a href="<?php echo url('/index/index/rmfriends'); ?>" class="js_ajax_win"  data-width="867" data-height="780">
            <img src="/assets/img/pc/new_index/item2.png" alt="">
        </a>
        <a href="<?php echo url('/index/index/jdraw'); ?>" class="js_ajax_win"  data-width="867" data-height="780">
            <img src="/assets/img/pc/new_index/item3.png" alt="">
        </a>
        <a href="<?php echo url('/index/index/ptip'); ?>" class="js_ajax_win"  data-width="867" data-height="780">
            <img src="/assets/img/pc/new_index/item4.png" alt="">
        </a>
    </div>
</div>

<div class="nums_div">
    <div class="container clearfix">
        <div class="title">從2018年成立至今，付唄由 <b>0</b> 發展至</div>
        <div class="items flex_ac">
            <div class="item">
                <div class="tt"><img src="/assets/img/pc/new_index/num1.png" alt=""></div>
                <div class="pp">會員人數</div>
            </div>
            <div class="item">
                <div class="tt"><img src="/assets/img/pc/new_index/num2.png" alt=""></div>
                <div class="pp">完成采購數</div>
            </div>

            <div class="item">
                <div class="tt"><span class="timer" data-to="97.35" data-speed="1500">97.35</span>%</div>
                <div class="pp">會員滿意度</div>
            </div>
            <div class="item">
                <div class="tt"><span class="timer" data-to="99.93" data-speed="1500">99.93</span>%</div>
                <div class="pp">持續使用率</div>
            </div>
        </div>
    </div>
</div>
<!-- <script src="/assets/js/number.js"></script>
<script>
    let no_run=true;
    $(window).scroll(function () {
        var scrollTop = $(window).scrollTop();
        if(scrollTop>400 && no_run){
            no_run=false
            $('.timer').each(count); 
            function count(options) {
                var $this = $(this);
                options = $.extend({}, options || {}, $this.data('countToOptions') || {});
                $this.countTo(options);
            }
            $(".math").removeClass("timer");
        }
    })
    
</script> -->





<div class="container clearfix" style="padding: 70px 0 30px 0;">
    <div class="home_tt">
        <div>會員的評價</div>
        <div class="line"></div>
    </div>

    <div class="flex_ac_jc mySwiper2_div">
        <div class="swiper-button swiper-button-prev"></div>
        <div class="swiper mySwiper2">
            <div class="swiper-wrapper">
                <div class="swiper-slide">
                    <div class="item">
                        <div class="flex_ac avatar_tt">
                            <img src="/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                            <div class="flex1">
                                <div>陶**</div>
                                <div>1小時前</div>
                            </div>
                            <div>8則評論</div>
                        </div>
                        <div class="flex_ac xx">
                            <img src="/assets/img/pc/new_index/xx.png" class="active">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                        </div>
                        <div class="cont">
                            原本以為沒差，結果每次運費都折，久了超有感！
                        </div>
                    </div>
                    <div class="item">
                        <div class="flex_ac avatar_tt">
                            <img src="/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                            <div class="flex1">
                                <div>沈**</div>
                                <div>1小時前</div>
                            </div>
                            <div>8 則評論</div>
                        </div>
                        <div class="flex_ac xx">
                            <img src="/assets/img/pc/new_index/xx.png" class="active">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                        </div>
                        <div class="cont">
                            商品完整送達，第一次空運寄送，速度還可以接受。
                        </div>
                    </div>
                    <div class="item">
                        <div class="flex_ac avatar_tt">
                            <img src="/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                            <div class="flex1">
                                <div>劉**</div>
                                <div>7小時前</div>
                            </div>
                            <div>3則評論</div>
                        </div>
                        <div class="flex_ac xx">
                            <img src="/assets/img/pc/new_index/xx.png" class="active">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                        </div>
                        <div class="cont">
                            皆已順利收到...謝謝! 
                        </div>
                    </div>
                    
                    
                    
                </div>
                <div class="swiper-slide">
                    <div class="item">
                        <div class="flex_ac avatar_tt">
                            <img src="/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                            <div class="flex1">
                                <div>陶**</div>
                                <div>9小時前</div>
                            </div>
                            <div>9則評論</div>
                        </div>
                        <div class="flex_ac xx">
                            <img src="/assets/img/pc/new_index/xx.png" class="active">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png"> 
                        </div>
                        <div class="cont">
                            原本以為沒差，結果每次運費都折，久了超有感！真的是太方便了！
                        </div>
                    </div>
                    <div class="item">
                        <div class="flex_ac avatar_tt">
                            <img src="/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                            <div class="flex1">
                                <div>袁**</div>
                                <div>12小時前</div>
                            </div>
                            <div>6則評論</div>
                        </div>
                        <div class="flex_ac xx">
                            <img src="/assets/img/pc/new_index/xx.png" class="active">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                        </div>
                        <div class="cont">
                            服務超棒 有問題都可以及時回復。 
                        </div>
                    </div>
                    <div class="item">
                        <div class="flex_ac avatar_tt">
                            <img src="/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                            <div class="flex1">
                                <div>季**</div>
                                <div>21小時前</div>
                            </div>
                            <div>17則評論</div>
                        </div>
                        <div class="flex_ac xx">
                            <img src="/assets/img/pc/new_index/xx.png" class="active">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                        </div>
                        <div class="cont">
                            不像其他支付方式還要輸入一堆資訊，幾秒就能完成交易，超級方便！
                        </div>
                    </div>
                    
                    
                </div>
                <div class="swiper-slide">
                    
                    <div class="item">
                        <div class="flex_ac avatar_tt">
                            <img src="/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                            <div class="flex1">
                                <div>熊**</div>
                                <div>1天前</div>
                            </div>
                            <div>11 則評論</div>
                        </div>
                        <div class="flex_ac xx">
                            <img src="/assets/img/pc/new_index/xx.png" class="active">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                        </div>
                        <div class="cont">
                            從商品下單到收貨，服務佳，運送也很快！
                        </div>
                    </div>
                    <div class="item">
                        <div class="flex_ac avatar_tt">
                            <img src="/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                            <div class="flex1">
                                <div>李**</div>
                                <div>2天 前</div>
                            </div>
                            <div>21則評論</div>
                        </div>
                        <div class="flex_ac xx">
                            <img src="/assets/img/pc/new_index/xx.png" class="active">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                        </div>
                        <div class="cont">
                            集運省下來的錢，剛好拿去買杯珍奶，爽！
                        </div>
                    </div>
                    <div class="item">
                        <div class="flex_ac avatar_tt">
                            <img src="/assets/img/pc/new_index/pjtx.png" alt="" class="tx">
                            <div class="flex1">
                                <div>李**</div>
                                <div>12天前</div>
                            </div>
                            <div>17則評論</div>
                        </div>
                        <div class="flex_ac xx">
                            <img src="/assets/img/pc/new_index/xx.png" class="active">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                            <img src="/assets/img/pc/new_index/xx.png">
                        </div>
                        <div class="cont">
                            真是太合我心意了，集運省下來的錢，剛好拿去買杯珍奶，爽！ 
                        </div>
                    </div>
                    
                </div>
            </div>
            <!-- <div class="swiper-pagination swiper_point"></div> -->


        </div>

        <div class="swiper-button swiper-button-next"></div>
    </div>

</div>

<script>
    var swiper2 = new Swiper(".mySwiper2", {
        autoplay: {
            delay: 2500,
            disableOnInteraction: false,
        },
        navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
        },
        loop: true,
    });
    document.addEventListener("DOMContentLoaded", () => {
        document.querySelectorAll('.js_play_video').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const coverDiv = this.querySelector('.cover_d');
                const placeholder = coverDiv.querySelector('.video-placeholder');
                const iframe = coverDiv.querySelector('iframe');

                if (!iframe.src) {
                    placeholder.innerHTML = '<div class="loading">加载中...</div>';
                    iframe.src = iframe.dataset.src;
                    iframe.style.display = 'block';
                    placeholder.style.display = 'none';

                    // 通过 YouTube API 触发自动播放
                    const player = new YT.Player(iframe, {
                        events: {
                            'onReady': (event) => event.target.playVideo()
                        }
                    });
                }
            });
        });
    });
</script>

<div class="container clearfix">
    <div class="home_tt">
        <div>代付教學視頻</div>
        <div class="line"></div>
    </div>


    <div class="active_list flex flex_w">
        <?php
            foreach($video as $ls){
        ?>
            <a href="javascript:;" class="item item2 js_play_video" >
                <div class="cover_d">
                    <div class="video-placeholder">
                        <button class="play-btn">▶</button>
                    </div>
                    <iframe width="100%" height="100%" data-src="<?=$ls['video_url']?>&autoplay=1&enablejsapi=1" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>
                <div class="tt2 ellipsis-2"><?=$ls['title']?></div>
                <!-- <div class="time">32天后结束</div> -->
            </a>
        <?php	
            }
        ?>
       
        <!-- <script>
            $('.js_play_video').click(function () {
                let url = $(this).attr('data-url');
                let html = `
                <div class="video_play_div">
                    <a href="javascript:;" class="close_win_btns"><img src="/assets/img/win_close.png" alt=""></a>
                    <iframe frameborder="0" src="` + url + `" width="100%" height="100%" class="note-video-clip"></iframe>
                </div>
                `;
                $('body').append(html)
            })
            $(document).on('click', '.close_win_btns', function () {
                $('.video_play_div').remove()
            })
        </script> -->
    </div>

</div>


<div class="start_buy">
    <div class="tt">
        <div>加入數百萬人使用的付唄購買你</div>
        <div>最愛的產品！</div>
    </div>
    <div class="pp">很感謝您遇見我們的時間，有片刻的停留。我們是一群過著簡單的生活，有美好理想的一群人。將每一點點小心思都放在這繁瑣重複的工作上，只希望帶給您的不僅僅是產品，更是一份美麗的心情！
    </div>
    <a href="<?=url('index/login/register')?>">開始~永遠省錢</a>
</div>


        </div>
        <div class="footer">
	<div class="container clearfix">
		2017-<?=date('Y')?><?php echo $site['copyright']; ?>
	</div>
</div>


<!-- 左侧浮窗 -->
<div class="fixed_left js_fixed_left">
	<img src="/assets/img/pc/fixed_icon.png" class="icon">
	<div class="l_links js_l_links">
	<?php
		foreach($help_list as $ls){
	?>
		<a href="<?=url('index/helps/detail',array('type'=>$ls['type'],'id'=>$ls['id']))?>"><?=$ls['title']?></a>
	<?php
		}
	?>
	</div>
	<div class="n_links">
		<a href="<?php echo url('index/user/customer'); ?>" class="js_ajax_win" data-width="450"><img src="/assets/img/pc/fixed_icon1.png">我的客服</a>
		<a href="<?php echo url('index/user/appeal'); ?>"><img src="/assets/img/pc/fixed_icon2.png">意見反映</a>
		<a href="<?php echo url('index/helps/index'); ?>"><img src="/assets/img/pc/fixed_icon3.png">帮助中心</a>
		<a href="javascript:;" class="js_close_more"><img src="/assets/img/pc/fixed_icon5.png">收起</a>
	</div>


	
	<div class="fixed_text js_fixed_text">
		<!-- 每个对应上面 l_links 里的 一个 -->
		<?php
			foreach($help_list as $ls){
		?>
			<div class="box">
				<div class="tt clearfix">
					<a href="<?=url('index/helps/detail',array('type'=>$ls['type'],'id'=>$ls['id']))?>" class="js_fixed_close_more"><img src="/assets/img/pc/fixed_icon4.png" class="margin_r30"></a>
					<div class="ellipsis text-right"><?=$ls['title']?></div>
				</div>
				<div class="content margin_tb10 pad_lr20 ellipsis-3">
					<?=$ls['newscontent']?>
				</div>
				<a href="<?=url('index/helps/index',array('type'=>$ls['type'],'id'=>$ls['id']))?>" class="m">查看更多<img src="/assets/img/pc/help_arr.png"></a>
			</div>
		<?php
			}
		?>
	</div>
</div>
<!-- 右侧浮窗 -->

<div class="new_fixed_right">
	<a href="<?php echo url('index/user/customer'); ?>" class="js_ajax_win" data-width="450"><img src="/assets/img/pc/new_index/nav1.png"></a>
	<a href="javascript:;" class="js_open_moren"><img src="/assets/img/pc/new_index/nav2.png"></a>
	<a href="<?php echo url('index/user/appeal'); ?>"><img src="/assets/img/pc/new_index/nav3.png"></a>
	<a href="javascript:;" class="js_to_top"><img src="/assets/img/pc/new_index/nav4.png"></a>
</div>

<div style="width: 108px; height: 98px; position: fixed; right: 18px; top: 80%; z-index: 5000;">
	<a href="<?php echo url('/index/index/jdraw'); ?>" class="js_ajax_win"  data-width="867" data-height="780">
		<img src="/assets/img/pc/new_index/jdraw-icon.png" alt="">
	</a>
</div>

<script type="text/javascript">
    $('.body').css('min-height',$(window).height()-108-$('.footer').height());
    $('.user_main').css('min-height',$(window).height()-108-$('.footer').height()-80);
    $('.js_l_links a').click(function(){
    	var _index=$('.js_l_links a').index(this);
    	$('.js_fixed_text .box').eq(_index).addClass('active').siblings('.box').removeClass('active');
    	$('.js_fixed_text,.js_fixed_left').addClass('active')
    })
    $('.js_fixed_close_more').click(function(){
    	$('.js_fixed_text,.js_fixed_left').removeClass('active')
    })

    $('.js_to_top').click(function(){
    	$('html,body').animate({
    		scrollTop:0
    	},300)
    })
    $('.js_open_moren').click(function(){
    	$('.new_fixed_right').addClass('active');
		$('.js_fixed_left').addClass('show')
    })
    $('.js_close_more').click(function(){
    	$('.new_fixed_right').removeClass('active');
		$('.js_fixed_left').removeClass('show')
    })

</script>

        <div class="mark js_mark"></div>
        <style type="text/css">
	.widgets__img_check_box_mark{ display: none ; cursor: pointer; width: 100%; height: 100%; position: fixed; left: 0; top: 0; z-index: 198912151; background: rgba(0,0,0,0.8) }
	.widgets__img_check_box_mark.active{ display: block ;}

	.widgets__img_check_box div{ box-sizing: content-box; }
	.widgets__img_check_box{width:300px;margin:0 auto; padding: 20px 0; background: #fff; border-radius: 10px; display: none; position: fixed; left: 50%; margin-left: -150px; top: 50%; margin-top: -130px!important; z-index: 198912152; }
	.widgets__img_check_box.active{ display: block; }
</style>
<div class="widgets__img_check_box_mark js_need_widgets__img"></div>

<div class="widgets__img_check_box js_need_widgets__img" id="select" style="">
	<div class="widgets__img_display">
		<div class="widgets__img_cnt">
			<img src1="a.jpg" class="widgets__img_src" />
			<canvas class="widgets__img_fragment_hollow"></canvas>
			<div class="widgets__img_fragment_cnt">
				<canvas class="widgets__img_fragment widgets__img_fragment_shadow"></canvas>
				<canvas class="widgets__img_fragment widgets__img_fragment_content"></canvas>
			</div>
			<div class="widgets__icon_refresh"></div>
		</div>
	</div>
	<div class="widgets__smooth_cnt">
	<div class="widgets__smooth_bar"></div>
	<div class="widgets__smooth_circle" style="left: 0"></div>
	</div>
</div>


<div class="widgets__img_check_box_mark js_need_widgets__img2"></div>
<div class="widgets__img_check_box js_need_widgets__img2" id="select2" style="">
	<div class="widgets__img_display">
		<div class="widgets__img_cnt">
			<img src1="a.jpg" class="widgets__img_src" />
			<canvas class="widgets__img_fragment_hollow"></canvas>
			<div class="widgets__img_fragment_cnt">
				<canvas class="widgets__img_fragment widgets__img_fragment_shadow"></canvas>
				<canvas class="widgets__img_fragment widgets__img_fragment_content"></canvas>
			</div>
			<div class="widgets__icon_refresh"></div>
		</div>
	</div>
	<div class="widgets__smooth_cnt">
	<div class="widgets__smooth_bar"></div>
	<div class="widgets__smooth_circle" style="left: 0"></div>
	</div>
</div>
<script type="text/javascript">
	var check_imgs=["/assets/img/pc/t1.png", "/assets/img/pc/t2.png", "/assets/img/pc/t3.png", "/assets/img/pc/t4.png"];
	$('.widgets__img_check_box_mark').click(function(){
		$('.js_need_widgets__img,.js_need_widgets__img2').removeClass('active')
	})
</script>
        
    </body>

</html>