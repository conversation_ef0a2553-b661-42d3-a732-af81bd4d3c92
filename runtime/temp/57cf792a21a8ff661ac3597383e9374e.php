<?php if (!defined('THINK_PATH')) exit(); /*a:0:{}*/ ?>
<!DOCTYPE html>
<html>
    <head>
        <!-- Google tag (gtag.js) --> <script async src="https://www.googletagmanager.com/gtag/js?id=G-E8L2CV5XBK"></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'G-E8L2CV5XBK'); </script> 
        <meta charset="utf-8">
<title><?php echo $seo['title']; ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
<meta name="renderer" content="webkit">
<meta name="keywords" content="<?php echo $seo['keywords']; ?>">
<meta name="description" content="<?php echo $seo['description']; ?>">

<link rel="shortcut icon" href="/assets/img/favicon.ico" />
<link href="/assets/css/elementui/common.css?v=<?php echo \think\Config::get('site.version'); ?>123" rel="stylesheet">
<link href="/assets/css/pc/boot.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<link href="/assets/css/pc/common.css?v=<?php echo \think\Config::get('site.version'); ?>456" rel="stylesheet">
<link href="/assets/css/certify.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<link href="/assets/css/swiper-bundle.min.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<link href="/assets/css/pc/img_smooth_check.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
<!--[if lt IE 9]>
  <script src="/assets/js/html5shiv.js"></script>
  <script src="/assets/js/respond.min.js"></script>
<![endif]-->
<script type="text/javascript">
    var require = {
        config: <?php echo json_encode($config); ?>
    };
    // 文件上传接口
    var _FILE_UPLOAD_="<?php echo url('uploadPictureList'); ?>";
    // 临时文件删除接口（即上传了，表单没有提交的临时文件）
	var _FILE_REMOVE_="<?php echo url('temp_file_remove'); ?>";
	// 文件删除接口（修改时，已有文件的删除）
	var _FILE_REMOVE_UPLOAD_="<?php echo url('upload_file_remove'); ?>";
</script>
<!-- Google 标签 (gtag.js) --> <script async src=" https://www.googletagmanager.com/gtag/js?id=AW-689144482 "></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'AW-689144482'); </script>
<!-- Google tag (gtag.js) --> <script async src="https://www.googletagmanager.com/gtag/js?id=G-E8L2CV5XBK"></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'G-E8L2CV5XBK'); </script>
        <!-- <link href="/assets/css/user.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet"> -->
        <script src="/assets/js/polyfill.min.js?v=<?php echo \think\Config::get('site.version'); ?>"></script>
<script src="/assets/js/pc/jq.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/swiper-bundle.min.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/pc/common.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/layer/layer.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/pc/img_smooth_check.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/vue.js?v=<?php echo \think\Config::get('site.version'); ?>"></script>
<script src="/assets/js/elementui.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/pc/copy.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>

        <!-- Meta Pixel Code -->
    <script>
        !function(f,b,e,v,n,t,s)
        {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
        n.callMethod.apply(n,arguments):n.queue.push(arguments)};
        if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
        n.queue=[];t=b.createElement(e);t.async=!0;
        t.src=v;s=b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t,s)}(window, document,'script',
        'https://connect.facebook.net/en_US/fbevents.js');
        fbq('init', '618910497657394');
        fbq('track', 'PageView');
        </script>
        <noscript>< img height="1" width="1" style="display:none"
        src="https://www.facebook.com/tr?id=618910497657394&ev=PageView&noscript=1"
        /></noscript>
        <!-- End Meta Pixel Code -->
         <!-- Default Statcounter code for paybei http://www.paybei.com.tw -->
<script type="text/javascript">
    var sc_project=13102009; 
    var sc_invisible=1; 
    var sc_security="15cc9551"; 
    </script>
    <script type="text/javascript"
    src="https://www.statcounter.com/counter/counter.js" async></script>
    <noscript><div class="statcounter"><a title="Web Analytics Made Easy -
    Statcounter" href=" " target="_blank"><img
    class="statcounter" src="https://c.statcounter.com/13102009/0/15cc9551/1/"
    alt="Web Analytics Made Easy - Statcounter"
    referrerPolicy="no-referrer-when-downgrade"></a ></div></noscript>
    <!-- End of Statcounter Code -->
    <!-- Google 标签 (gtag.js) --> <script async src=" https://www.googletagmanager.com/gtag/js?id=AW-689144482 "></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'AW-689144482'); </script>
     
    <script>
        window.addEventListener('load', function(event){
        if (window.location.href.indexOf("register") > 0) {
        document.addEventListener('click', function (e) {
        var dom = e.target.closest('.sub_btn');
        if (dom === null) return;
        
        var form = dom.closest('form');
        var phone = form.querySelector('input[name="mobile"]').value;
       phone = (phone.substr(0,1) == 0) ? phone.substr(1) : phone;
        
       if (phone === '') return;
         gtag('set', 'user_data', { 'phone_number': '+886'+phone });
                 gtag('event', 'conversion', {'send_to': 'AW-689144482/1xgmCJSZwKgaEKKFzsgC'});
    
        });
        
        }
        
        });
        </script>    
</head>
    <body>
        <div class="body">
            <div class="login_page">
    <div class="login_left">
        <a href="/" class="login_logo"><img src="/assets/img/pc/login_logo.png" alt=""></a>
        <div class="slogo text-center">
            <div class="tt color_red"><?php echo __('haigou'); ?></div>
            <div class="pp"><?php echo __('haigou slogo'); ?></div>
        </div>
        <div class="f_slogo text-center"><?php echo __('haigou slogo foot'); ?></div>
    </div>
    <div class="over_hide login_right">
        <div class="right_link"><?php echo __('Already have an account?'); ?> <a href="<?php echo url('/index/login/login'); ?>"><?php echo __('Sign in'); ?></a></div>
        <div class="login_form login_form_re">
            <form action="" autocomplete="off" class="js_login_forms">
                <div class="title"><?php echo __('Sign up'); ?><span class="color_red"><?php echo __('haigou'); ?></span></div>
                <div class="input_box full_width">
                    <div class="input_tt"><?php echo __('Phone number'); ?></div>
                    <div class="input_rr">
                        <input type="text" name="mobile" data-type="mobile" class="js_code_input" maxlength="10" is_required="true" empty_tip="<?php echo __('Please enter your cell phone number'); ?>">
                    </div>
                </div>

                <div class="input_box full_width">
                    <div class="input_tt"><?php echo __('Mobile verification code'); ?></div>
                    <div class="input_rr">
                        <a href="javascript:;" class="get_code" data-url="<?php echo url('/index/login/getCode'); ?>"><?php echo __('Get code'); ?></a>
                        <input type="text" name="code" data-type="number" maxlength="6" is_required="true" empty_tip="<?php echo __('Please enter mobile verification code'); ?>">
                    </div>
                </div>

                <div class="input_box full_width">
                    <div class="input_tt"><?php echo __('Password'); ?></div>
                    <div class="input_rr">
                        <input type="password" name="password" data-type="password" maxlength="20" is_required="true" empty_tip="<?php echo __('Please enter password'); ?>">
                    </div>
                </div>
                <div class="color_999 pad_t10"><?php echo __('Please enter a password of 8 to 20 bits, including letters and numbers'); ?></div>
                <div class="input_box full_width">
                    <div class="input_tt"><?php echo __('Real name'); ?></div>
                    <div class="input_rr">
                        <input type="text" name="realname"  maxlength="20" is_required="true" empty_tip="<?php echo __('Please enter real name'); ?>" >
                    </div>
                </div>

                <div class="input_box full_width">
                    <div class="input_tt"><?php echo __('Invitation Code'); ?> <span>(<?php echo __('Not required'); ?>)</span></div>
                    <div class="input_rr">
                        <input type="text" name="invitationCode"  maxlength="20">
                    </div>
                </div>

                <div class="clearfix login_tips pad_t20">
                    <label class="moni_check_label">
                        <a href="javascript:;" class="moni_check active">
                        </a>
                        <span class="la_btn"><?php echo __('Have agreed'); ?></span> <a class="color_red js_article_win" data-width="1000" href="<?php echo url('/index/login/agreement'); ?>"><?php echo __('User Agreement'); ?></a>
                    </label>
                </div>

                <div class="pad_t30 text-center">
                    <a href="javascript:;" class="sub_btn js_form_sub" data-text="<?php echo __('Start to save money forever'); ?>" data-loadding="<?php echo __('Submitting'); ?>"  data-type="new_location" data-func_before="is_agree">
						<?php echo __('Start to save money forever'); ?>
						

					</a>
                </div>

            </form>
        </div>
    </div>
</div>

<script type="text/javascript">
    function is_agree(){
        if(!$('.moni_check_label .moni_check').hasClass('active')){
            tip_show("<?php echo __('Please agreed Agreement first'); ?>",'2')
            return false
        }
        return true;
    }
    $(function(){
        
    })
</script>
<!-- Event snippet for 注冊-25.3.4 conversion page -->
<script>
    gtag('event', 'conversion', {
        'send_to': 'AW-689144482/TtsdCInX8qUaEKKFzsgC',
        'value': 1.0,
        'currency': 'TWD'
    });
  </script>
  

        </div>
        <div class="mark js_mark"></div>
        <style type="text/css">
	.widgets__img_check_box_mark{ display: none ; cursor: pointer; width: 100%; height: 100%; position: fixed; left: 0; top: 0; z-index: 198912151; background: rgba(0,0,0,0.8) }
	.widgets__img_check_box_mark.active{ display: block ;}

	.widgets__img_check_box div{ box-sizing: content-box; }
	.widgets__img_check_box{width:300px;margin:0 auto; padding: 20px 0; background: #fff; border-radius: 10px; display: none; position: fixed; left: 50%; margin-left: -150px; top: 50%; margin-top: -130px!important; z-index: 198912152; }
	.widgets__img_check_box.active{ display: block; }
</style>
<div class="widgets__img_check_box_mark js_need_widgets__img"></div>

<div class="widgets__img_check_box js_need_widgets__img" id="select" style="">
	<div class="widgets__img_display">
		<div class="widgets__img_cnt">
			<img src1="a.jpg" class="widgets__img_src" />
			<canvas class="widgets__img_fragment_hollow"></canvas>
			<div class="widgets__img_fragment_cnt">
				<canvas class="widgets__img_fragment widgets__img_fragment_shadow"></canvas>
				<canvas class="widgets__img_fragment widgets__img_fragment_content"></canvas>
			</div>
			<div class="widgets__icon_refresh"></div>
		</div>
	</div>
	<div class="widgets__smooth_cnt">
	<div class="widgets__smooth_bar"></div>
	<div class="widgets__smooth_circle" style="left: 0"></div>
	</div>
</div>


<div class="widgets__img_check_box_mark js_need_widgets__img2"></div>
<div class="widgets__img_check_box js_need_widgets__img2" id="select2" style="">
	<div class="widgets__img_display">
		<div class="widgets__img_cnt">
			<img src1="a.jpg" class="widgets__img_src" />
			<canvas class="widgets__img_fragment_hollow"></canvas>
			<div class="widgets__img_fragment_cnt">
				<canvas class="widgets__img_fragment widgets__img_fragment_shadow"></canvas>
				<canvas class="widgets__img_fragment widgets__img_fragment_content"></canvas>
			</div>
			<div class="widgets__icon_refresh"></div>
		</div>
	</div>
	<div class="widgets__smooth_cnt">
	<div class="widgets__smooth_bar"></div>
	<div class="widgets__smooth_circle" style="left: 0"></div>
	</div>
</div>
<script type="text/javascript">
	var check_imgs=["/assets/img/pc/t1.png", "/assets/img/pc/t2.png", "/assets/img/pc/t3.png", "/assets/img/pc/t4.png"];
	$('.widgets__img_check_box_mark').click(function(){
		$('.js_need_widgets__img,.js_need_widgets__img2').removeClass('active')
	})
</script>
    </body>

</html>