<?php if (!defined('THINK_PATH')) exit(); /*a:0:{}*/ ?>
<!DOCTYPE html>
<html>
    <head>
        <!-- Google tag (gtag.js) --> <script async src="https://www.googletagmanager.com/gtag/js?id=G-E8L2CV5XBK"></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'G-E8L2CV5XBK'); </script> 
        <meta charset="utf-8">
<title><?php echo $seo['title']; ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover">
<meta name="format-detection" content="telephone=no"/>
<meta name="renderer" content="webkit">
<meta name="keywords" content="<?php echo $seo['keywords']; ?>">
<meta name="description" content="<?php echo $seo['description']; ?>">

<link rel="shortcut icon" href="/assets/img/favicon.ico" />
<link href="/assets/css/elementui/common.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<link href="/assets/css/pc/boot.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<link href="/assets/css/wap/common_wap.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<link href="/assets/css/pc/img_smooth_check.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<link href="/assets/css/swiper-bundle.min.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<link href="/assets/css/m_certify.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
<!--[if lt IE 9]>
  <script src="/assets/js/html5shiv.js"></script>
  <script src="/assets/js/respond.min.js"></script>
<![endif]-->
<script type="text/javascript">
    var require = {
        config: <?php echo json_encode($config); ?>
    };
    // 文件上传接口
    var _FILE_UPLOAD_="<?php echo url('uploadPictureList'); ?>";
    // 临时文件删除接口（即上传了，表单没有提交的临时文件）
	var _FILE_REMOVE_="<?php echo url('temp_file_remove'); ?>";
	// 文件删除接口（修改时，已有文件的删除）
	var _FILE_REMOVE_UPLOAD_="<?php echo url('upload_file_remove'); ?>";
</script>
<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=AW-689144482"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'AW-689144482');
</script>
<!-- Google 标签 (gtag.js) --> <script async src=" https://www.googletagmanager.com/gtag/js?id=AW-689144482 "></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'AW-689144482'); </script>
 <!-- Google tag (gtag.js) --> <script async src="https://www.googletagmanager.com/gtag/js?id=G-E8L2CV5XBK"></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'G-E8L2CV5XBK'); </script>
        <!-- <link href="/assets/css/user.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet"> -->
        <script src="/assets/js/pc/jq.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/wap/common.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/swiper-bundle.min.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/layer/layer.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/pc/img_smooth_check.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>

<script src="/assets/js/vue.js?v=<?php echo \think\Config::get('site.version'); ?>"></script>
<script src="/assets/js/elementui.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/pc/copy.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
        <!-- Meta Pixel Code -->
<script>
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '618910497657394');
    fbq('track', 'PageView');
    </script>
    <noscript>< img height="1" width="1" style="display:none"
    src="https://www.facebook.com/tr?id=618910497657394&ev=PageView&noscript=1"
    /></noscript>
    <!-- End Meta Pixel Code -->
     <!-- Default Statcounter code for paybei http://www.paybei.com.tw -->
<script type="text/javascript">
    var sc_project=13102009; 
    var sc_invisible=1; 
    var sc_security="15cc9551"; 
    </script>
    <script type="text/javascript"
    src="https://www.statcounter.com/counter/counter.js" async></script>
    <noscript><div class="statcounter"><a title="Web Analytics Made Easy -
    Statcounter" href=" " target="_blank"><img
    class="statcounter" src="https://c.statcounter.com/13102009/0/15cc9551/1/"
    alt="Web Analytics Made Easy - Statcounter"
    referrerPolicy="no-referrer-when-downgrade"></a ></div></noscript>
    <!-- End of Statcounter Code -->
    <!-- Google 标签 (gtag.js) --> <script async src=" https://www.googletagmanager.com/gtag/js?id=AW-689144482 "></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'AW-689144482'); </script>
<!-- Google tag (gtag.js) --> <script async src="https://www.googletagmanager.com/gtag/js?id=G-E8L2CV5XBK"></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'G-E8L2CV5XBK'); </script>    
<script>
    window.addEventListener('load', function(event){
    if (window.location.href.indexOf("register") > 0) {
    document.addEventListener('click', function (e) {
    var dom = e.target.closest('.sub_btn');
    if (dom === null) return;
    
    var form = dom.closest('form');
    var phone = form.querySelector('input[name="mobile"]').value;
   phone = (phone.substr(0,1) == 0) ? phone.substr(1) : phone;
    
   if (phone === '') return;
     gtag('set', 'user_data', { 'phone_number': '+886'+phone });
             gtag('event', 'conversion', {'send_to': 'AW-689144482/1xgmCJSZwKgaEKKFzsgC'});

    });
    
    }
    
    });
    </script>
</head>
    <body style="padding-top:0.9rem;">
        <div class="header clearfix flex_ac pad_lr20">
	<a href="/" class="logo "><img src="/assets/img/wap/logo.png"></a>
	<a href="javascript:window.history.go(-1);" class="close_b margin_a"><img src="/assets/img/wap/header_close.png"></a>

</div>
        <div class="body">
            
<div class="register_animation">
    <div class="success-container">
        <div class="success-icon">
            <svg width="100" height="100" viewBox="0 0 60 60" fill="none">
                <circle cx="30" cy="30" r="30" fill="#28a745"/>
                <path d="M18 30l8 8 16-16" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </div>
        <h2 class="success-title">注册成功</h2>
        <p class="countdown-text"><span id="countdown">2</span> 秒后自动跳转</p>
        <button class="course-button" onclick="jumpToHome()">點擊跳轉</button>
    </div>
</div>
<script>
    // 获取首页URL - 使用ThinkPHP语法
    var homeUrl = "<?php echo url('index/index'); ?>";

    // 倒计时变量
    var countdown = 2;

    // 跳转到首页的函数
    function jumpToHome() {
        // window.location.href = homeUrl;
    }

    // 更新倒计时显示
    function updateCountdown() {
        var countdownElement = document.getElementById('countdown');
        if (countdownElement) {
            countdownElement.textContent = countdown;
        }

        if (countdown <= 0) {
            jumpToHome();
        } else {
            countdown--;
            setTimeout(updateCountdown, 1000);
        }
    }

    // 页面加载完成后开始倒计时
    document.addEventListener('DOMContentLoaded', function() {
        // updateCountdown();
    });
</script>

<style>
    .register_animation {
        width: 100vw;
        height: 100vh;
        background: #ffffff;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .success-container {
        width: 100vw;
        margin-bottom: 15vh;
        min-height: 552px;
        background: #ffffff;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        text-align: center;
    }

    .success-icon {
        margin-bottom: 16px;
        display: flex;
        justify-content: center;
    }

    .success-title {
        font-size: 16px;
        font-weight: 500;
        color: #3D3D3D;
        margin: 0 0 20px 0;
    }

    .countdown-text {
        font-size: 13px;
        color: #ff69b4;
        margin: 0 0 36px 0;
    }

    .course-button {
        margin: 0 24rpx;
        background: #ef436d;
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 25px;
        font-size: 13px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(255, 105, 180, 0.3);
    }

    .course-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(255, 105, 180, 0.4);
    }

    .course-button:active {
        transform: translateY(0);
    }
</style>
        </div>
        <div class="mark js_mark"></div>
        <style type="text/css">
	.widgets__img_check_box_mark{ display: none ; cursor: pointer; width: 100%; height: 100%; position: fixed; left: 0; top: 0; z-index: 198912151; background: rgba(0,0,0,0.8) }
	.widgets__img_check_box_mark.active{ display: block ;}

	.widgets__img_check_box div{ box-sizing: content-box; }
	.widgets__img_check_box{width:300px;margin:0 auto; padding: 20px 0; background: #fff; border-radius: 10px; display: none; position: fixed; left: 50%; margin-left: -150px; top: 50%; margin-top: -130px!important; z-index: 198912152; }
	.widgets__img_check_box.active{ display: block; }
</style>
<div class="widgets__img_check_box_mark js_need_widgets__img"></div>

<div class="widgets__img_check_box js_need_widgets__img" id="select" style="">
	<div class="widgets__img_display">
		<div class="widgets__img_cnt">
			<img src1="a.jpg" class="widgets__img_src" />
			<canvas class="widgets__img_fragment_hollow"></canvas>
			<div class="widgets__img_fragment_cnt">
				<canvas class="widgets__img_fragment widgets__img_fragment_shadow"></canvas>
				<canvas class="widgets__img_fragment widgets__img_fragment_content"></canvas>
			</div>
			<div class="widgets__icon_refresh"></div>
		</div>
	</div>
	<div class="widgets__smooth_cnt">
	<div class="widgets__smooth_bar"></div>
	<div class="widgets__smooth_circle" style="left: 0"></div>
	</div>
</div>


<div class="widgets__img_check_box_mark js_need_widgets__img2"></div>
<div class="widgets__img_check_box js_need_widgets__img2" id="select2" style="">
	<div class="widgets__img_display">
		<div class="widgets__img_cnt">
			<img src1="a.jpg" class="widgets__img_src" />
			<canvas class="widgets__img_fragment_hollow"></canvas>
			<div class="widgets__img_fragment_cnt">
				<canvas class="widgets__img_fragment widgets__img_fragment_shadow"></canvas>
				<canvas class="widgets__img_fragment widgets__img_fragment_content"></canvas>
			</div>
			<div class="widgets__icon_refresh"></div>
		</div>
	</div>
	<div class="widgets__smooth_cnt">
	<div class="widgets__smooth_bar"></div>
	<div class="widgets__smooth_circle" style="left: 0"></div>
	</div>
</div>
<script type="text/javascript">
	var check_imgs=["/assets/img/pc/t1.png", "/assets/img/pc/t2.png", "/assets/img/pc/t3.png", "/assets/img/pc/t4.png"];
	$('.widgets__img_check_box_mark').click(function(){
		$('.js_need_widgets__img,.js_need_widgets__img2').removeClass('active')
	})
</script>
    </body>

</html>