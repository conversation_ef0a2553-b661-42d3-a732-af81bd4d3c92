<?php if (!defined('THINK_PATH')) exit(); /*a:0:{}*/ ?>
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
<title><?php echo $seo['title']; ?></title>
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
<meta name="renderer" content="webkit">
<meta name="keywords" content="<?php echo $seo['keywords']; ?>">
<meta name="description" content="<?php echo $seo['description']; ?>">

<link rel="shortcut icon" href="/assets/img/favicon.ico" />
<link href="/assets/css/elementui/common.css?v=<?php echo \think\Config::get('site.version'); ?>123" rel="stylesheet">
<link href="/assets/css/pc/boot.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<link href="/assets/css/pc/common.css?v=<?php echo \think\Config::get('site.version'); ?>456" rel="stylesheet">
<link href="/assets/css/certify.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<link href="/assets/css/swiper-bundle.min.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<link href="/assets/css/pc/img_smooth_check.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet">
<!-- HTML5 shim, for IE6-8 support of HTML5 elements. All other JS at the end of file. -->
<!--[if lt IE 9]>
  <script src="/assets/js/html5shiv.js"></script>
  <script src="/assets/js/respond.min.js"></script>
<![endif]-->
<script type="text/javascript">
    var require = {
        config: <?php echo json_encode($config); ?>
    };
    // 文件上传接口
    var _FILE_UPLOAD_="<?php echo url('uploadPictureList'); ?>";
    // 临时文件删除接口（即上传了，表单没有提交的临时文件）
	var _FILE_REMOVE_="<?php echo url('temp_file_remove'); ?>";
	// 文件删除接口（修改时，已有文件的删除）
	var _FILE_REMOVE_UPLOAD_="<?php echo url('upload_file_remove'); ?>";
</script>
<!-- Google 标签 (gtag.js) --> <script async src=" https://www.googletagmanager.com/gtag/js?id=AW-689144482 "></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'AW-689144482'); </script>
<!-- Google tag (gtag.js) --> <script async src="https://www.googletagmanager.com/gtag/js?id=G-E8L2CV5XBK"></script> <script> window.dataLayer = window.dataLayer || []; function gtag(){dataLayer.push(arguments);} gtag('js', new Date()); gtag('config', 'G-E8L2CV5XBK'); </script>
        <!-- <link href="/assets/css/user.css?v=<?php echo \think\Config::get('site.version'); ?>" rel="stylesheet"> -->
        <script src="/assets/js/polyfill.min.js?v=<?php echo \think\Config::get('site.version'); ?>"></script>
<script src="/assets/js/pc/jq.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/swiper-bundle.min.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/pc/common.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/layer/layer.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/pc/img_smooth_check.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/vue.js?v=<?php echo \think\Config::get('site.version'); ?>"></script>
<script src="/assets/js/elementui.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>
<script src="/assets/js/pc/copy.js?v=<?php echo \think\Config::get('site.version'); ?>" ></script>

        <style type="text/css">
            body{ background: #EDEFEF }
        </style>
    </head>
    <body style="padding-top: 96px;">
        <div class="header">
	<div class="header_top">
		<div class="container clearfix">
			<!--<a class="pull-right" href="###">FB&nbsp;&nbsp;&nbsp;<div class="fb-like" data-href="https://www.paybei.com.tw/" data-width="" data-layout="button" data-action="like" data-size="large" data-share="true"></div>
			<div id="fb-root"></div>
			<script async defer crossorigin="anonymous" src="https://connect.facebook.net/zh_CN/sdk.js#xfbml=1&version=v7.0" nonce="KdPpPI0i"></script></a>-->
			
			<a href="<?php echo url('/index/helps/index'); ?>" class="pull-right"><?php echo __('Help center'); ?></a>
			<a href="<?php echo url('/index/login/agreement'); ?>" class="pull-right js_article_win" data-width="1000" ><?php echo __('haigou Terms'); ?></a>
			<?php echo __('Welcome to the most preferential and professional Taobao purchasing platform and Alipay platform'); ?>!
		</div>
	</div>
	<div class="header_logo">
		<div class="container clearfix">
			<?php
				if($user){
			?>
			<!-- 已登录 -->
				<a href="<?php echo url('/index/login/logout'); ?>" class="loginout_btn nav_link js_ajax_confirm" tips="<?php echo __('Are you sure you are logged out?'); ?>"><?php echo __('Logout'); ?></a>
				<a href="<?php echo url('/index/user/index'); ?>" class="u_img pull-right"><img src="<?=$user['avatar']?>"></a>
			<!-- 已登录 -->
			<?php
				}else{
			?>
			<!-- 未登录 -->
				<a href="<?php echo url('/index/login/register'); ?>" class="nav_link nav_link_block pull-right"><?php echo __('Free registration'); ?></a>
				<a href="<?php echo url('/index/login/login'); ?>" class="loginout_btn nav_link pull-right"><?php echo __('Member login'); ?></a>
			<!-- 未登录 -->
			<?php
				}
			?>
			<a href="/" class="logo"><img src="/assets/img/pc/logo.png"></a>
			<a href="/" class="nav_link <?php if($nav_path == 'index/index')echo 'active'?>"><?php echo __('Home'); ?></a>
			<a href="<?php echo url('/index/user/index'); ?>" class="nav_link <?php if($nav_path == 'user/index')echo 'active'?>"><?php echo __('Member Center'); ?></a>
			<a href="<?php echo url('/index/transportation/index'); ?>" class="nav_link <?php if($nav_path == 'transportation/index')echo 'active'?>">集運</a>
			<a href="<?php echo url('/index/user/index'); ?>" class="nav_link">代付</a>
			<a href="<?php echo url('/index/user/order'); ?>" class="nav_link <?php if($nav_path == 'user/order')echo 'active'?>">訂單</a>
			<a href="<?php echo url('/index/helps/help_center'); ?>" class="nav_link <?php if($nav_path == 'helps/help_center')echo 'active'?>">常見問題</a>
			<!--<a href="<?php echo url('goods/index'); ?>" class="nav_link <?php if($nav_path == 'goods/index')echo 'active'?>">有好貨
			<a href="<?php echo url('coupon/index'); ?>" class="nav_link <?php if($nav_path == 'coupon/index')echo 'active'?>">淘寶優惠劵
				<div class="quan_tip">
					<img src="/assets/img/pc/tip_arr.png">
					<span><?php echo __('Tickets are available before shopping'); ?>！</span>
				</div>
			</a>-->
		</div>
	</div>
</div>
        <div class="body">
            <div class="container user_center_main">
    <div class="user_main margin_tb20 clearfix">
		<div class="border_box pad_lr20 u_head_notice clearfix">
			<a href="<?php echo url('/index/user/notice'); ?>" class="pull-right more"><?php echo __('View more'); ?></a>
			<div class="over_hide">
			<?php
				foreach($tongzhi as $ls){
			?>
				<a href="<?=url('/index/user/notice_detail')?>?id=<?=$ls['id']?>" class="item ellipsis pad_r30"><span class="icon"><?php echo __('Notice'); ?></span><?=$ls['title']?></a>
			<?php
				}
			?>
			</div>
		</div>
		<div class="clearfix margin_t20">
			<div class="pull-right user_main_right">
				
				<div class="user_notice  clearfix border_box pad_lr20 pad_tb20">
					<div class="title clearfix">
						<a href="<?php echo url('/index/user/notice'); ?>"><?php echo __('View more'); ?> ></a>
						<?php echo __('Platform Bulletin'); ?>
					</div>
					<div class="u_notice pad_t10">
						<?php
							foreach($gonggao as $ls){
						?>
							<a href="<?=url('/index/user/notice_detail')?>?id=<?=$ls['id']?>&type=1" class="clearfix">
								<span class="pull-right color_999 margin_l10"><?=date('m/d',$ls['createtime'])?></span>
								<div class="ellipsis"><?=$ls['title']?></div>
							</a>
						<?php
							}
						?>
					</div>
				</div>


				<div class="user_notice margin_t20 clearfix border_box pad_lr20 pad_tb20">
					<div class="title clearfix">
						<?php echo __('Common functions'); ?>
					</div>
					<div class="u_gongneng clearfix">
						<a href="<?php echo url('user/appeal'); ?>" class="clearfix">
								<?php echo __('Appeal list'); ?>
						</a>
						<a href="<?php echo url('user/order'); ?>" class="clearfix">
								<?php echo __('My order'); ?>
						</a>
						<a href="<?php echo url('user/password'); ?>" class="clearfix active js_ajax_win" data-width="600">
								<?php echo __('Modify login password'); ?>
						</a>
						<a href="<?php echo url('user/editMobile'); ?>" class="clearfix active js_ajax_win" data-width="600">
								<?php echo __('Modify mobile'); ?>
						</a>
						<!--<a href="" class="add" class="clearfix">
							+<?php echo __('Add'); ?>
						</a>-->
					</div>
				</div>

			</div>
			<div class="user_main_left_new pull-left" style="width: 316px;">
				<div class="user_info clearfix border_box pad_lr30 pad_tb30 margin_b20">
					<div class="clearfix">
						<div class="avatar_div pull-left js_ajax_win" data-width="930" data-height="650" href="<?php echo url('/index/user/edit_avatar'); ?>">
							<img src="<?=$user['avatar']?>" class="avatar">
							<div class="vip">
								<img src="/assets/img/pc/vip.png">
								<span><?=$user['level']?></span>
							</div>
						</div>
						<div class="over_hide">
							<div class="nickname ellipsis"><?=substr_replace($user['username'],'*',0,3)?></div>
							<div class="pro clearfix">
								<div class="pull-right margin_l10"><?=$bill?>%</div>
								<div class="over_hide">
									<div class="pro_bar"><div style="width: <?=$bill?>%"></div></div>
								</div>
							</div>
						</div>
					</div>
					<div class="items pad_t10">
						<div class="item clearfix">
							<div class="pull-left tt">當前經驗值:</div>
							<div class="over_hide">
								<?=$user['score']?>
							</div>
						</div>
						<div class="item clearfix">
							<div class="pull-left tt"><?php echo __('Login account'); ?>:</div>
							<div class="over_hide">
								<?=substr_replace($user['mobile'],'****',2,4)?>
							</div>
						</div>
						<div class="item auth clearfix">
							<div class="pull-left tt"><?php echo __('Qualification Authentication'); ?>:</div>
							<div class="over_hide">
								<!-- 已认证  active -->
								<a href="<?php echo url('/index/user/editMobile'); ?>" class="active js_ajax_win" data-width="600"><img src="/assets/img/pc/auth1.png" class="img1"><img src="/assets/img/pc/auth1a.png" class="img2"></a>
								<a href="<?php echo url('/index/user/editIdcard/type/1'); ?>" class="<?=$user['is_card']?'active':'';?> js_ajax_win" data-width="600" data-height="450"><img src="/assets/img/pc/auth2.png" class="img1"><img src="/assets/img/pc/auth2a.png" class="img2"></a>
								<a href="<?php echo url('/index/user/editIdcardTwo/type/1'); ?>" class="<?=$user['is_card_img']?'active':'';?> js_ajax_win" data-width="600"><img src="/assets/img/pc/auth3.png" class="img1"><img src="/assets/img/pc/auth3a.png" class="img2"></a>
								<a href="<?php echo url('/index/Line/line_binding'); ?>"  data-width="600"><img src="/assets/img/pc/line.png" class="img1"></a>
							</div>
						</div>
						<div class="item clearfix">
							<div class="pull-left tt"><?php echo __('Recent login'); ?>:</div>
							<div class="over_hide">
								<?=date('Y-m-d H:i:s',$user['logintime'])?>
							</div>
						</div>
					</div>
					
				</div>
				
				<div class="clearfix user_money margin_b20">
					<div class="border_box money_tt pad_lr20 margin_r20 pad_tb20 pull-left">
						<div class="tt clearfix pad_b20">
							<a href="<?php echo url('/index/user/capital/'); ?>" class="pull-right"><?php echo __('Fund management'); ?></a>
							<?php echo __('My balance'); ?>
						</div>
						<div class="item">
							<div class="tts"><?php echo __('Amoy money'); ?>(元)退款專用</div>
							<div class="pp color_red"><?=$user['money']?></div>
						</div>
						<div class="item">
							<div class="tts"><?php echo __('Shopping gold'); ?></div>
							<div class="pp"><?=$user['buy_gold']?></div>
						</div>
					
						<div class="text-center pad_t20 op_btns clearfix">
							<a href="<?php echo url('/index/daifu/charge/set_type/7'); ?>" >儲值</a>
							<a href="<?php echo url('/index/user/share'); ?>" class="active js_ajax_win" data-width="800" data-height="600"><?php echo __('Popularize'); ?></a>
							<a href="<?php echo url('/index/user/rate_fun'); ?>" class="active js_ajax_win" data-width="800" data-height="550"><?php echo __('Rate'); ?></a>
						</div>
						
						<div class="pad_t20">
							<a href="<?php echo url('/index/account/index'); ?>" class="mana_btn"><?php echo __('Account management'); ?></a>
						</div>
					</div>
				</div>

				<div class="clearfix user_money">
					<div class="border_box money_tt pad_lr20 margin_r20 pad_tb20 pull-left">
						<div class="tt clearfix pad_b20">
							<a href="<?php echo url('/index/user/coupon/'); ?>" class="pull-right">優惠券管理</a>
							我的優惠券
						</div>
						<div class="item">
							<div class="pp color_red"><?=$coupon?><span style="font-size: 12px; color: #333;">张</span></div>
						</div>
					</div>
				</div>

			</div>
			<div class="over_hide user_main_left">
				<!-- <div class="clearfix user_money ">
					
					<div class="border_box over_hide money_chart">
						<div class="chart_d">
							<div class="chart_tt">
								<a href="javascript:;" class="tab_btn active">最近交易</a>
								<a href="javascript:;" class="tab_btn js_tab_btn" data-type="1" data-d="7">最近7日</a>
								<a href="javascript:;" class="tab_btn js_tab_btn" data-type="2" data-d="14">最近14日</a>
								<a href="javascript:;" class="tab_btn js_tab_btn" data-type="3" data-d="30">最近30日</a>
								<a href="<?php echo url('/index/user/order'); ?>" class="pull-right o_detail">訂單明細</a>
							</div>
							<div class="items">
								<div class="item">
									<div class="pp">成交金額（元）</div>
									<div class="tt js_chart_nums"><?=$user['score']?></div>
								</div>
								<div class="item">
									<div class="pp">成交單數（筆）</div>
									<div class="tt js_chart_nums">0.00</div>
								</div>
								<div class="item">
									<div class="pp">退款金額（元）</div>
									<div class="tt js_chart_nums">0.00</div>
								</div>
							</div>
							<div class="chart_div">
								<div class="chart_div_d" id="chart_div_d">
									
								</div>
							</div>
						</div>
					</div>
				</div> -->

				<!-- <div class="clearfix u_banner margin_t20">
					<div class="home_banner">
						<ul class="imagelist js_home_top clearfix" data-run="0" >
							<?php
								if($banner){
									foreach($banner as $ls){
							?>
								<li class="banner_li">
									<a target="_blank"  href="javascript:;" class="block" style="background-image:url(<?=$ls['path_image']?>);">
									</a>
								</li>
							<?php
									}
								}
							?>
						</ul>
						<div class="container point-div">
							<ul class="point clearfix js_point">
							<?php
								if($banner){
									foreach($banner as $ls){
							?>
								<li class=""></li>
							<?php
									}
								}
							?>
							</ul>
						</div>
					</div>
				</div> -->


				<!--<div class="border_box user_nav pad_lr20 margin_t20 pad_tb20">
					<div class="title"><?php echo __('Popular application'); ?></div>
					<div class="clearfix pad_t30">
						<a href="<?php echo url('/index/daifu/index'); ?>" class="box">
							<img src="/assets/img/pc/icon_function_01alibaba.png">
							<div class="tt"><?php echo __('Alibaba pays'); ?></div>
						</a>
						<a href="<?php echo url('/index/daifu/taobao'); ?>" class="box">
							<img src="/assets/img/pc/icon_function_02taobao.png">
							<div class="tt"><?php echo __('Taobao Payment'); ?></div>
						</a>
						<a href="<?php echo url('/index/daifu/alipay'); ?>" class="box">
							<img src="/assets/img/pc/icon_function_03zhifubao.png">
							<div class="tt"><?php echo __('Alipay Reserve Value'); ?></div>
						</a>
						<a href="<?php echo url('/index/daifu/wechatpay'); ?>" class="box">
							<img src="/assets/img/pc/icon_function_04weichat.png">
							<div class="tt"><?php echo __('WeChat storage value'); ?></div>
						</a>
						<a href="<?php echo url('/index/daifu/game'); ?>" class="box">
							<img src="/assets/img/pc/icon_function_05game.png">
							<div class="tt"><?php echo __('Game/Video Storage'); ?></div>
						</a>
						<a href="<?php echo url('/index/daifu/other'); ?>" class="box">
							<img src="/assets/img/pc/icon_function_06other.png">
							<div class="tt"><?php echo __('Other payment'); ?></div>
						</a>
						<a href="<?php echo url('/index/daifu/usdt'); ?>" class="box">
							<img src="/assets/img/pc/icon_function_07usdt.png">
							<div class="tt">USDT</div>
						</a>
						<a href="<?php echo url('/index/user/invoice_my'); ?>" class="box">
							<img src="/assets/img/pc/icon_gongneng_fapiao.png">
							<div class="tt">會員福利</div>
						</a>
						<a href="<?php echo url('/index/user/prize'); ?>" class="box">
							<img src="/assets/img/pc/cj/icon_list_hot_09cjjl_pc.png">
							<div class="tt">中獎清單</div>
						</a>
					</div>
				</div> -->
				
				
				
				
				
				
				<style>
					.business{
						display: flex;
						flex-direction: row;
						justify-content: space-around;
						
						margin: 0px auto 0 auto;
					}
					.business-item{
						display: flex;
						flex-direction: row;
						align-items: center;
						cursor: pointer;
					}
					.business-item img{
						margin-right: 5px;
					}
					.business-item .hideimg{
						display: block;
					}
					.business-item .showimg{
						display: none;
					}
					.business-item-active .hideimg{
						display: none;
					}
					.business-item-active .showimg{
						display: block;
					}
					.business-item-active  span{
						color:#EF436D;
					}
					.business-list{
						margin:20px auto;
						margin-bottom: 0;
					}
					.business-list .business-list-item{
						width: 16.66%;
						display: flex;
						float: left;
						padding: 20px 0;
						flex-direction: column;
						justify-content: center;
						align-items: center;
						color:#333333;
					}
					.business-list .business-list-item:hover{
						color: #EF436D;
					}
					.business-list .business-list-item img{
						width: 60px;
						height: 62px;
						margin-bottom: 10px;
					}
				</style>
				<div class="border_box  pad_lr20  pad_tb20">
					<!-- <div class="title"><?php echo __('Popular application'); ?></div> -->
					<div class="business">
						<div class="business-item business-item-active" type="1">
							<img src="/assets/img/pc/images/icon_business01.png" class="hideimg" />
							<img src="/assets/img/pc/images/icon_business01_red.png" class="showimg" />
							<span>商品代購專區</span>
						</div>
						<div class="business-item" type="2">
							<img src="/assets/img/pc/images/icon_business02.png" class="hideimg"/>
							<img src="/assets/img/pc/images/icon_business02_red.png" class="showimg" />
							<span>直播點數專區</span>
						</div>
						<div class="business-item" type="3">
							<img src="/assets/img/pc/images/icon_business03.png" class="hideimg"/>
							<img src="/assets/img/pc/images/icon_business03_red.png" class="showimg" />
							<span>遊戲點數專區</span>
						</div>
						<div class="business-item" type="4">
							<img src="/assets/img/pc/images/icon_business04.png"  class="hideimg"/>
							<img src="/assets/img/pc/images/icon_business04_red.png" class="showimg" />
							<span>其他代購專區</span>
						</div>
					</div>
					<div class="business-list clearfix ">
						<!-- <div class="business-list-item">
							<img src="/assets/img/pc/images/img_busi_02zb_01dy.png" />
							<span>抖音代購</span>
						</div>
						<div class="business-list-item">
							<img src="/assets/img/pc/images/img_busi_02zb_02hy.png" />
							<span>虎牙直播</span>
						</div>
						<div class="business-list-item">
							<img src="/assets/img/pc/images/img_busi_02zb_03bx.png" />
							<span>比心直播</span>
						</div>
						<div class="business-list-item">
							<img src="/assets/img/pc/images/img_busi_02zb_04yk.png" />
							<span>映客直播</span>
						</div>
						<div class="business-list-item">
							<img src="/assets/img/pc/images/img_busi_02zb_05yy.png" />
							<span>YY直播</span>
						</div>
						<div class="business-list-item">
							<img src="/assets/img/pc/images/img_busi_02zb_06yf.png" />
							<span>音符直播</span>
						</div>
						<div class="business-list-item">
							<img src="/assets/img/pc/images/img_busi_02zb_07ks.png" />
							<span>一直播</span>
						</div>
						<div class="business-list-item">
							<img src="/assets/img/pc/images/img_busi_02zb_08more.png" />
							<span>更多</span>
						</div>
						<div class="business-list-item">
							<img src="/assets/img/pc/images/img_busi_02zb_08more.png" />
							<span>更多</span>
						</div> -->
					</div>
				</div>
				
				<style>
					.active_list .item{display: block; position: relative; margin-bottom: 15px; width: 239px; margin-right: 15px;}
					.active_list .item:nth-child(4n){margin-right: 0;}
					.active_list .item .cover{width: 100%; height: 100%; float: left; object-fit: cover;}
					.active_list .item .tt{padding: 6px 15px; background: rgba(0,0,0,0.5); position: absolute; left: 0; bottom: 0; width: 100%; z-index: 2; color: #fff; font-size: 14px;}
					.active_list .item .time{padding: 4px 10px;background: rgb(247 144 55); border-radius: 0 0 0 5px; position: absolute; font-size: 12px; right: 0; top: 0; z-index: 2; color: #fff;}
					.active_list .item2{height: auto;}
					.active_list .item2 .cover_d{height: 132px;position: relative;}
					.active_list .item2 .cover{height: 132px; float: none; border-radius: 3px;}
					.active_list .item .tt2{color: #333; margin: 10px 0;}
					.active_list .item2 .play{width: 34px; height: 24px; position: absolute; left: 50%; top: 50%; margin-left: -17px; margin-top: -12px;}
					.video_play_div{width: 100%; height: 100%; background: rgba(0,0,0,0.7); position: fixed; left: 0; top: 0; z-index: 300;}
					.video_play_div iframe{width: 900px; height: 500px; position: absolute; left: 50%; margin-left: -450px; top: 50%; margin-top: -250px;}
					.video_play_div .close_win_btns{display: block;width: 40px;height: 80px;text-align: center;line-height: 20px;position: absolute;left: 50%; margin-left: -20px; top:  50%; margin-top: -330px; z-index: 20;}

				</style>
				<div class="border_box  pad_lr20  pad_tb20 margin_t20">
					<img src="/assets/img/cache/members_banner03.png" style="width: 100%;" alt="">
				</div>
				<!--<div class="border_box  pad_lr20  pad_tb20 margin_t20">
					<div class="active_list flex flex_w">
						<a href="" class="item">
							<img src="/assets/img/pc/index_banner.jpg" class="cover" alt="" />
							<div class="tt">活动名称活动名称活动名称活动名称</div>
							<div class="time">32天后结束</div>
						</a>
						<a href="" class="item">
							<img src="/assets/img/pc/index_banner.jpg" class="cover" alt="" />
							<div class="tt">活动名称活动名称活动名称活动名称</div>
							<div class="time">32天后结束</div>
						</a>
						<a href="" class="item">
							<img src="/assets/img/pc/index_banner.jpg" class="cover" alt="" />
							<div class="tt">活动名称活动名称活动名称活动名称</div>
							<div class="time">32天后结束</div>
						</a>
						<a href="" class="item">
							<img src="/assets/img/pc/index_banner.jpg" class="cover" alt="" />
							<div class="tt">活动名称活动名称活动名称活动名称</div>
							<div class="time">32天后结束</div>
						</a>
					</div>
				</div>	-->
				
				
				<div class="border_box  pad_lr20  pad_tb20 margin_t20">
					<div style="font-size: 16px; font-weight: bold; color: #333; margin-bottom:15px">YouTube教學視频</div>
					<div class="active_list flex flex_w">
						<?php
							foreach($video as $ls){
						?>
							<a href="javascript:;" class="item item2 js_play_video" >
								<div class="cover_d">
									<iframe width="100%" height="100%" src="<?=$ls['video_url']?>" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
								</div>
								<div class="tt2 ellipsis-2"><?=$ls['title']?></div>
								<!-- <div class="time">32天后结束</div> -->
							</a>
						<?php	
							}
						?>
						

						<!-- <script>
							$('.js_play_video').click(function(){
								let url=$(this).attr('data-url');
								let html=`
								<div class="video_play_div">
									<a href="javascript:;" class="close_win_btns"><img src="/assets/img/win_close.png" alt=""></a>
									<iframe frameborder="0" src="`+url+`" width="100%" height="100%" class="note-video-clip"></iframe>
								</div>
								`;
								$('body').append(html)
							})
							$(document).on('click','.close_win_btns',function(){
								$('.video_play_div').remove()
							})
							
						</script> -->
					</div>
				</div>
				
				
				
				
				
				
			</div>   
			
		
		</div>
	</div>
</div>



<!-- 新增弹窗 -->
<?php
	if($product){
?>
	<style type="text/css">
        .my_new_win{ display: block; width: 350px; height: auto; position: fixed; right: 40px; top: 40px; background: #fff; z-index: 200; border-radius: 5px; border-left: 3px solid #ff3267; padding: 20px; color: #333; transition: 0.3s; -webkit-transition: 0.3s; }
        .my_new_win.remove{ transform: scale(0); -webkit-transform: scale(0); opacity: 0 }
        .my_new_win .win_close{ display: block; width: 30px; height: 30px; text-align: center; line-height: 30px; position: absolute; right: 5px; top: 5px; z-index: 10 }
        .my_new_win .win_close img{ width: 14px; }
        .my_new_win .title{ font-size: 16px; font-weight: bold; padding-bottom: 10px;  }
        .my_new_win .item{ font-size: 14px; line-height: 22px;}
        .my_new_win .view_btn{ padding-top: 5px; color: #ff3267 }
        .my_new_win .view_btn:hover{ text-decoration: underline; }
    </style>
    <div class="my_new_win">
        <a href="javascript:;" class="win_close js_win_close"><img src="/assets/img/pc/close2.png"></a>
        <div class="title">小唄已幫您錄入了代付商品清單</div>
			<div class="item">
				<?=$product['title']?>
			</div>
			<div class="item">金額：<b class="color_red"><?=$product['price']?></b>RMB</div>
			<div class="item">姓名：<?=$product['other_name']?></div>
			<div class="item">帳號：<?=$product['other_account']?></div>
        <div class="js_btns">
			<a href="<?=url('index/daifu/index',array('type'=>'1','set_type'=>'0'))?>" class="view_btn">查看</a>
		</div>
    </div>
    <script type="text/javascript">
        $('.js_win_close').click(function(){
            var _this=$(this);
            _this.parents('.my_new_win').addClass('remove');
            setTimeout(function(){
                _this.parents('.my_new_win').remove();
            },300)
        })
		$('.js_btns a').click(function(){
			$.post("",function(data){
				if(data.status==1){
					window.location.href=data.info
				}
			})
		})
		
    </script>
<?php
	}
?>
    


<script src="/assets/js/pc/banner.js"></script>
<script src="/assets/js/pc/easing.js"></script>
<script src="/assets/js/pc/echarts.min.js"></script>


<script type="text/javascript">
	$(function(){
		my_banner($('.js_home_top'),{
			autoplay:true, // 是否自动轮播  默认为true
			autotime: 7000, //自动轮播时间, 默认600
			runtime: 500, //切换时间，  默认500
			easing:"swing", //切换速度函数 默认 swing， 其他设置参考  easing.js
			type:"left_right",//切换方式  默认 left_right，(左右切换) 。其他设置  fade, (淡入淡出)
			after_run:function(index){
				
			},
		});

		// // 数据模板  day_data  日期数据     money_data 金额数据
		// var _temp_data={
		// 	day_data:[],
		// 	money_data:[
		// 		[],
		// 		[],
		// 		[]
		// 	],
		// 	total1:'0.00', //成交金额
		// 	total2:'0.00', //成交单数
		// 	total3:'0.00'//退款金额
		// }

		// var myChart = echarts.init(document.getElementById('chart_div_d'));
		// var dataAxis = _temp_data.day_data;

		// var option = {
		// 	title: {
		//         text: '最近7日趋势：',
		//         left: 'left',
		//         textStyle:{
		//         	color:'#666',
		//         	fontSize:'14px'
		//         }
		//     },
		//     tooltip: {
		//         trigger: 'axis'
		//     },
		//     legend: {
		//         data:['成交金额','成交笔数','退款金额'],
		//         left:'150px',
		//         align:'left'
		//     },
		//     xAxis: {
		//         data: dataAxis,
		//         axisLabel: {
		//             textStyle: {
		//                 color: '#999'
		//             }
		//         },
		//         axisTick: {
		//             show: true,
		//             lineStyle:{ color:'#999'}
		//         },
		//         axisLine: {
		//             show: true,
		//             lineStyle:{ color:'#999'}
		//         },
		//         z: 10
		//     },
		//     yAxis: {
		//         axisLine: {
		//             show: false
		//         },
		//         axisTick: {
		//             show: false
		//         },
		//         axisLabel: {
		//             textStyle: {
		//                 color: '#999'
		//             }
		//         },
		//         splitLine: {
		//             show: false
		//         }
		//     },
		//     grid:{
		//     	left:'60px',
		//     	top:'40px',
		//     	right:0,
		//     	bottom:'40px'
		//     },
		//     dataZoom: [
		//         {
		//             type: 'inside'
		//         }
		//     ],
		//     series: [
		//         {
		//             name:'成交金额',
		//             type:'line',
		//             stack: '总量',
		//             data:_temp_data.money_data[0]
		//         },
		//         {
		//             name:'成交笔数',
		//             type:'line',
		//             stack: '总量',
		//             data:_temp_data.money_data[1]
		//         },
		//         {
		//             name:'退款金额',
		//             type:'line',
		//             stack: '总量',
		//             data:_temp_data.money_data[2]
		//         },
		//     ]
		// };

		// // Enable data zoom when user click bar.
		// var zoomSize = 8;
		// myChart.on('click', function (params) {
		//     console.log(dataAxis[Math.max(params.dataIndex - zoomSize / 2, 0)]);
		//     myChart.dispatchAction({
		//         type: 'dataZoom',
		//         startValue: dataAxis[Math.max(params.dataIndex - zoomSize / 2, 0)],
		//         endValue: dataAxis[Math.min(params.dataIndex + zoomSize / 2, dataAxis.length - 1)]
		//     });
		// });
		//  myChart.setOption(option);
		// // 使用刚指定的配置项和数据显示图表。

		// myChart.dispatchAction({
		//     type: 'dataZoom',
		//     startValue: dataAxis[Math.max(2 - zoomSize / 2, 0)],
		//     endValue: dataAxis[Math.min(2 + zoomSize / 2, dataAxis.length - 1)]
		// }); 


		$('.js_tab_btn').click(function(){
			if($(this).hasClass('cur')){
				return false;
			}
			$(this).addClass('cur').siblings('a').removeClass('cur');
			var _index=$('.js_tab_btn').index(this);
			var _day=$(this).attr('data-d');
			var _type=$(this).attr('data-type');
			$.get("<?php echo url('index/user/userReport'); ?>",{type:_type},function(data){
				if(data.code==1){
					var _temp_data=data.msg;
					$('.js_chart_nums').eq(0).html(_temp_data.rmb_deal_money)
					$('.js_chart_nums').eq(1).html(_temp_data.all_order)
					$('.js_chart_nums').eq(2).html(_temp_data.refund_money)
					
					option.title.text="最近"+_day+"日趋势：";
					dataAxis=_temp_data.day_time;
					option.xAxis.data=dataAxis;
					option.series[0].data=_temp_data.deal_money;
					option.series[1].data=_temp_data.order_num;
					option.series[2].data=_temp_data.refund;

					myChart.setOption(option);
				}else{
					tip_show(data.msg,'2')
				}
			})
			
		})

		$('.js_tab_btn').eq(0).click();
		
		
		
		function htmlTodo(type){
		  
		    // list.sort(function(){
		    //     return Math.random()-0.5;
		    // });
		    //这里开始写ajax请求数据 type为请求不同的数据 数据格式参考list,最好是有8个长度 ，注释前面的代码是随机的
		    $.ajax({ url: "<?php echo url('/index/index/business'); ?>?type="+type, success: function(data){
		       data = JSON.parse(data)
		       if(data.status == 1){
		            for(var i = 0,l = data.list.length;i<l;i++){
		                if(!data.list[i].image){
		                    data.list[i].image = '/assets/img/pc/images/img_busi_02zb_07ks.png'
		                }
		                if(!data.list[i].name){
		                    data.list[i].name = '一直播'
		                }
		                if(!data.list[i].nickname){
		                    data.list[i].nickname = 'https://www.baidu.com'
		                }
		            }
		            var htmlSource = data.list
		            var html = ''
		            for(var i = 0,l = htmlSource.length;i<l;i++){
		                html += '<a class="business-list-item" href="'+htmlSource[i].nickname+".html"+'">'
		                html += '<img src="'+htmlSource[i].image+'" />'
		                html += '<span>'+htmlSource[i].name+'</span>'
		                html += '</a>'
		            }
		            $('.business-list').html(html)
		       }
		        
		    }});
		
		}
		htmlTodo(1)
		//业务移动事件
		$(".business .business-item").hover(function (){
		    var type = $(this).attr('type')
		    htmlTodo(type)
		   $(this).addClass('business-item-active')
		   $(this).siblings().removeClass('business-item-active')
		},function (){  
		    // $(this).removeClass('business-item-active') 
		});  

	})
</script>

        </div>
        <div class="footer">
	<div class="container clearfix">
		2017-<?=date('Y')?><?php echo $site['copyright']; ?>
	</div>
</div>


<!-- 左侧浮窗 -->
<div class="fixed_left js_fixed_left">
	<img src="/assets/img/pc/fixed_icon.png" class="icon">
	<div class="l_links js_l_links">
	<?php
		foreach($help_list as $ls){
	?>
		<a href="<?=url('index/helps/detail',array('type'=>$ls['type'],'id'=>$ls['id']))?>"><?=$ls['title']?></a>
	<?php
		}
	?>
	</div>
	<div class="n_links">
		<a href="<?php echo url('index/user/customer'); ?>" class="js_ajax_win" data-width="450"><img src="/assets/img/pc/fixed_icon1.png">我的客服</a>
		<a href="<?php echo url('index/user/appeal'); ?>"><img src="/assets/img/pc/fixed_icon2.png">意見反映</a>
		<a href="<?php echo url('index/helps/index'); ?>"><img src="/assets/img/pc/fixed_icon3.png">帮助中心</a>
		<a href="javascript:;" class="js_close_more"><img src="/assets/img/pc/fixed_icon5.png">收起</a>
	</div>


	
	<div class="fixed_text js_fixed_text">
		<!-- 每个对应上面 l_links 里的 一个 -->
		<?php
			foreach($help_list as $ls){
		?>
			<div class="box">
				<div class="tt clearfix">
					<a href="<?=url('index/helps/detail',array('type'=>$ls['type'],'id'=>$ls['id']))?>" class="js_fixed_close_more"><img src="/assets/img/pc/fixed_icon4.png" class="margin_r30"></a>
					<div class="ellipsis text-right"><?=$ls['title']?></div>
				</div>
				<div class="content margin_tb10 pad_lr20 ellipsis-3">
					<?=$ls['newscontent']?>
				</div>
				<a href="<?=url('index/helps/index',array('type'=>$ls['type'],'id'=>$ls['id']))?>" class="m">查看更多<img src="/assets/img/pc/help_arr.png"></a>
			</div>
		<?php
			}
		?>
	</div>
</div>
<!-- 右侧浮窗 -->

<div class="new_fixed_right">
	<a href="<?php echo url('index/user/customer'); ?>" class="js_ajax_win" data-width="450"><img src="/assets/img/pc/new_index/nav1.png"></a>
	<a href="javascript:;" class="js_open_moren"><img src="/assets/img/pc/new_index/nav2.png"></a>
	<a href="<?php echo url('index/user/appeal'); ?>"><img src="/assets/img/pc/new_index/nav3.png"></a>
	<a href="javascript:;" class="js_to_top"><img src="/assets/img/pc/new_index/nav4.png"></a>
</div>

<div style="width: 108px; height: 98px; position: fixed; right: 18px; top: 80%; z-index: 5000;">
	<a href="<?php echo url('/index/index/jdraw'); ?>" class="js_ajax_win"  data-width="867" data-height="780">
		<img src="/assets/img/pc/new_index/jdraw-icon.png" alt="">
	</a>
</div>

<script type="text/javascript">
    $('.body').css('min-height',$(window).height()-108-$('.footer').height());
    $('.user_main').css('min-height',$(window).height()-108-$('.footer').height()-80);
    $('.js_l_links a').click(function(){
    	var _index=$('.js_l_links a').index(this);
    	$('.js_fixed_text .box').eq(_index).addClass('active').siblings('.box').removeClass('active');
    	$('.js_fixed_text,.js_fixed_left').addClass('active')
    })
    $('.js_fixed_close_more').click(function(){
    	$('.js_fixed_text,.js_fixed_left').removeClass('active')
    })

    $('.js_to_top').click(function(){
    	$('html,body').animate({
    		scrollTop:0
    	},300)
    })
    $('.js_open_moren').click(function(){
    	$('.new_fixed_right').addClass('active');
		$('.js_fixed_left').addClass('show')
    })
    $('.js_close_more').click(function(){
    	$('.new_fixed_right').removeClass('active');
		$('.js_fixed_left').removeClass('show')
    })

</script>

        <div class="mark js_mark"></div>
        <style type="text/css">
	.widgets__img_check_box_mark{ display: none ; cursor: pointer; width: 100%; height: 100%; position: fixed; left: 0; top: 0; z-index: 198912151; background: rgba(0,0,0,0.8) }
	.widgets__img_check_box_mark.active{ display: block ;}

	.widgets__img_check_box div{ box-sizing: content-box; }
	.widgets__img_check_box{width:300px;margin:0 auto; padding: 20px 0; background: #fff; border-radius: 10px; display: none; position: fixed; left: 50%; margin-left: -150px; top: 50%; margin-top: -130px!important; z-index: 198912152; }
	.widgets__img_check_box.active{ display: block; }
</style>
<div class="widgets__img_check_box_mark js_need_widgets__img"></div>

<div class="widgets__img_check_box js_need_widgets__img" id="select" style="">
	<div class="widgets__img_display">
		<div class="widgets__img_cnt">
			<img src1="a.jpg" class="widgets__img_src" />
			<canvas class="widgets__img_fragment_hollow"></canvas>
			<div class="widgets__img_fragment_cnt">
				<canvas class="widgets__img_fragment widgets__img_fragment_shadow"></canvas>
				<canvas class="widgets__img_fragment widgets__img_fragment_content"></canvas>
			</div>
			<div class="widgets__icon_refresh"></div>
		</div>
	</div>
	<div class="widgets__smooth_cnt">
	<div class="widgets__smooth_bar"></div>
	<div class="widgets__smooth_circle" style="left: 0"></div>
	</div>
</div>


<div class="widgets__img_check_box_mark js_need_widgets__img2"></div>
<div class="widgets__img_check_box js_need_widgets__img2" id="select2" style="">
	<div class="widgets__img_display">
		<div class="widgets__img_cnt">
			<img src1="a.jpg" class="widgets__img_src" />
			<canvas class="widgets__img_fragment_hollow"></canvas>
			<div class="widgets__img_fragment_cnt">
				<canvas class="widgets__img_fragment widgets__img_fragment_shadow"></canvas>
				<canvas class="widgets__img_fragment widgets__img_fragment_content"></canvas>
			</div>
			<div class="widgets__icon_refresh"></div>
		</div>
	</div>
	<div class="widgets__smooth_cnt">
	<div class="widgets__smooth_bar"></div>
	<div class="widgets__smooth_circle" style="left: 0"></div>
	</div>
</div>
<script type="text/javascript">
	var check_imgs=["/assets/img/pc/t1.png", "/assets/img/pc/t2.png", "/assets/img/pc/t3.png", "/assets/img/pc/t4.png"];
	$('.widgets__img_check_box_mark').click(function(){
		$('.js_need_widgets__img,.js_need_widgets__img2').removeClass('active')
	})
</script>
    </body>
    <script type="text/javascript">
        $(function(){
            // 上拉加载
            if($('.js_scroll_more_list .box').length==0){
                $('.js_no_data').removeClass('hide')
            }
            if($('.js_scroll_more_list .box').length>=10){
                $('.loadding_d').removeClass('hide')
            }
            var is_pull_up=false;
            var pull_up_page=2;
            $(document).scroll(function(event) {
                var see_height = $(window).height();
                if ($('.body').height() - $(document).scrollTop() <= see_height + 10) {
                    if($('.js_scroll_more_list .box').length<10 || is_pull_up || $('.loadding_d').hasClass('disabled')){
                        return false;
                    }
                    $('.loadding_d').removeClass('disabled').find('span').html('加載中···');
                    is_pull_up=true;
                    $.get($('.js_scroll_more_list').attr('data-url'),{page:pull_up_page},function(data){
                        // 返回的列表放到 data ,如果没有数据返回 空字符串
                        if(data!=''){
                            $('.js_scroll_more_list').append(data);
                            $('.loadding_d').find('span').html('上拉加載更多');
                            pull_up_page++
                        }else{
                            $('.loadding_d').addClass('disabled').find('span').html('沒有更多了');
                        }
                        is_pull_up=false;
                    })

                }
            });
        })
    </script>
</html>