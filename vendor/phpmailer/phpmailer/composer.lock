{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#composer-lock-the-lock-file", "This file is @generated automatically"], "content-hash": "7e4b1bef833056eed0df39fad5399d7a", "packages": [], "packages-dev": [{"name": "cilex/cilex", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/Cilex/Cilex.git", "reference": "7acd965a609a56d0345e8b6071c261fbdb926cb5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Cilex/Cilex/zipball/7acd965a609a56d0345e8b6071c261fbdb926cb5", "reference": "7acd965a609a56d0345e8b6071c261fbdb926cb5", "shasum": ""}, "require": {"cilex/console-service-provider": "1.*", "php": ">=5.3.3", "pimple/pimple": "~1.0", "symfony/finder": "~2.1", "symfony/process": "~2.1"}, "require-dev": {"phpunit/phpunit": "3.7.*", "symfony/validator": "~2.1"}, "suggest": {"monolog/monolog": ">=1.0.0", "symfony/validator": ">=1.0.0", "symfony/yaml": ">=1.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"Cilex": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The PHP micro-framework for Command line tools based on the Symfony2 Components", "homepage": "http://cilex.github.com", "keywords": ["cli", "microframework"], "time": "2014-03-29T14:03:13+00:00"}, {"name": "cilex/console-service-provider", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/Cilex/console-service-provider.git", "reference": "25ee3d1875243d38e1a3448ff94bdf944f70d24e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Cilex/console-service-provider/zipball/25ee3d1875243d38e1a3448ff94bdf944f70d24e", "reference": "25ee3d1875243d38e1a3448ff94bdf944f70d24e", "shasum": ""}, "require": {"php": ">=5.3.3", "pimple/pimple": "1.*@dev", "symfony/console": "~2.1"}, "require-dev": {"cilex/cilex": "1.*@dev", "silex/silex": "1.*@dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"Cilex\\Provider\\Console": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://beausimensen.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Console Service Provider", "keywords": ["cilex", "console", "pimple", "service-provider", "silex"], "time": "2012-12-19T10:50:58+00:00"}, {"name": "doctrine/annotations", "version": "v1.2.7", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "f25c8aab83e0c3e976fd7d19875f198ccf2f7535"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/f25c8aab83e0c3e976fd7d19875f198ccf2f7535", "reference": "f25c8aab83e0c3e976fd7d19875f198ccf2f7535", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "php": ">=5.3.2"}, "require-dev": {"doctrine/cache": "1.*", "phpunit/phpunit": "4.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\Annotations\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "http://www.doctrine-project.org", "keywords": ["annotations", "doc<PERSON>", "parser"], "time": "2015-08-31T12:32:49+00:00"}, {"name": "doctrine/instantiator", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/8e884e78f9f0eb1329e445619e04456e64d8051d", "reference": "8e884e78f9f0eb1329e445619e04456e64d8051d", "shasum": ""}, "require": {"php": ">=5.3,<8.0-DEV"}, "require-dev": {"athletic/athletic": "~0.1.8", "ext-pdo": "*", "ext-phar": "*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ocramius.github.com/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://github.com/doctrine/instantiator", "keywords": ["constructor", "instantiate"], "time": "2015-06-14T21:17:01+00:00"}, {"name": "doctrine/lexer", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "83893c552fd2045dd78aef794c31e694c37c0b8c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/83893c552fd2045dd78aef794c31e694c37c0b8c", "reference": "83893c552fd2045dd78aef794c31e694c37c0b8c", "shasum": ""}, "require": {"php": ">=5.3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Doctrine\\Common\\Lexer\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Base library for a lexer that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "http://www.doctrine-project.org", "keywords": ["lexer", "parser"], "time": "2014-09-09T13:34:57+00:00"}, {"name": "erusev/parsedown", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/erusev/parsedown.git", "reference": "20ff8bbb57205368b4b42d094642a3e52dac85fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/erusev/parsedown/zipball/20ff8bbb57205368b4b42d094642a3e52dac85fb", "reference": "20ff8bbb57205368b4b42d094642a3e52dac85fb", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"psr-0": {"Parsedown": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://erusev.com"}], "description": "<PERSON><PERSON>r for <PERSON>down.", "homepage": "http://parsedown.org", "keywords": ["markdown", "parser"], "time": "2016-11-02T15:56:58+00:00"}, {"name": "herrera-io/json", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/kherge-php/json.git", "reference": "60c696c9370a1e5136816ca557c17f82a6fa83f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kherge-php/json/zipball/60c696c9370a1e5136816ca557c17f82a6fa83f1", "reference": "60c696c9370a1e5136816ca557c17f82a6fa83f1", "shasum": ""}, "require": {"ext-json": "*", "justinrainbow/json-schema": ">=1.0,<2.0-dev", "php": ">=5.3.3", "seld/jsonlint": ">=1.0,<2.0-dev"}, "require-dev": {"herrera-io/phpunit-test-case": "1.*", "mikey179/vfsstream": "1.1.0", "phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"files": ["src/lib/json_version.php"], "psr-0": {"Herrera\\Json": "src/lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kevin.herrera.io/", "role": "Developer"}], "description": "A library for simplifying JSON linting and validation.", "homepage": "http://herrera-io.github.com/php-json", "keywords": ["json", "lint", "schema", "validate"], "abandoned": "kherge/json", "time": "2013-10-30T16:51:34+00:00"}, {"name": "herrera-io/phar-update", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/kherge-abandoned/php-phar-update.git", "reference": "00a79e1d5b8cf3c080a2e3becf1ddf7a7fea025b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kherge-abandoned/php-phar-update/zipball/00a79e1d5b8cf3c080a2e3becf1ddf7a7fea025b", "reference": "00a79e1d5b8cf3c080a2e3becf1ddf7a7fea025b", "shasum": ""}, "require": {"herrera-io/json": "1.*", "kherge/version": "1.*", "php": ">=5.3.3"}, "require-dev": {"herrera-io/phpunit-test-case": "1.*", "mikey179/vfsstream": "1.1.0", "phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"files": ["src/lib/constants.php"], "psr-0": {"Herrera\\Phar\\Update": "src/lib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://kevin.herrera.io/", "role": "Developer"}], "description": "A library for self-updating <PERSON><PERSON>.", "homepage": "http://herrera-io.github.com/php-phar-update", "keywords": ["phar", "update"], "abandoned": true, "time": "2013-10-30T17:23:01+00:00"}, {"name": "jms/metadata", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/metadata.git", "reference": "6a06970a10e0a532fb52d3959547123b84a3b3ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/metadata/zipball/6a06970a10e0a532fb52d3959547123b84a3b3ab", "reference": "6a06970a10e0a532fb52d3959547123b84a3b3ab", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"doctrine/cache": "~1.0", "symfony/cache": "~3.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-0": {"Metadata\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Class/method/property metadata management in PHP", "keywords": ["annotations", "metadata", "xml", "yaml"], "time": "2016-12-05T10:18:33+00:00"}, {"name": "jms/parser-lib", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/parser-lib.git", "reference": "c509473bc1b4866415627af0e1c6cc8ac97fa51d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/parser-lib/zipball/c509473bc1b4866415627af0e1c6cc8ac97fa51d", "reference": "c509473bc1b4866415627af0e1c6cc8ac97fa51d", "shasum": ""}, "require": {"phpoption/phpoption": ">=0.9,<2.0-dev"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"JMS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "description": "A library for easily creating recursive-descent parsers.", "time": "2012-11-18T18:08:43+00:00"}, {"name": "jms/serializer", "version": "0.16.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/serializer.git", "reference": "c8a171357ca92b6706e395c757f334902d430ea9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/serializer/zipball/c8a171357ca92b6706e395c757f334902d430ea9", "reference": "c8a171357ca92b6706e395c757f334902d430ea9", "shasum": ""}, "require": {"doctrine/annotations": "1.*", "jms/metadata": "~1.1", "jms/parser-lib": "1.*", "php": ">=5.3.2", "phpcollection/phpcollection": "~0.1"}, "require-dev": {"doctrine/orm": "~2.1", "doctrine/phpcr-odm": "~1.0.1", "jackalope/jackalope-doctrine-dbal": "1.0.*", "propel/propel1": "~1.7", "symfony/filesystem": "2.*", "symfony/form": "~2.1", "symfony/translation": "~2.0", "symfony/validator": "~2.0", "symfony/yaml": "2.*", "twig/twig": ">=1.8,<2.0-dev"}, "suggest": {"symfony/yaml": "Required if you'd like to serialize data to YAML format."}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.15-dev"}}, "autoload": {"psr-0": {"JMS\\Serializer": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>", "homepage": "https://github.com/schmitt<PERSON>h", "role": "Developer of wrapped JMSSerializerBundle"}], "description": "Library for (de-)serializing data of any complexity; supports XML, JSON, and YAML.", "homepage": "http://jmsyst.com/libs/serializer", "keywords": ["deserialization", "jaxb", "json", "serialization", "xml"], "time": "2014-03-18T08:39:00+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/justinrainbow/json-schema.git", "reference": "cc84765fb7317f6b07bd8ac78364747f95b86341"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/justinrainbow/json-schema/zipball/cc84765fb7317f6b07bd8ac78364747f95b86341", "reference": "cc84765fb7317f6b07bd8ac78364747f95b86341", "shasum": ""}, "require": {"php": ">=5.3.29"}, "require-dev": {"json-schema/json-schema-test-suite": "1.1.0", "phpdocumentor/phpdocumentor": "~2", "phpunit/phpunit": "~3.7"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "time": "2016-01-25T15:43:01+00:00"}, {"name": "kherge/version", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/kherge-abandoned/Version.git", "reference": "f07cf83f8ce533be8f93d2893d96d674bbeb7e30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kherge-abandoned/Version/zipball/f07cf83f8ce533be8f93d2893d96d674bbeb7e30", "reference": "f07cf83f8ce533be8f93d2893d96d674bbeb7e30", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-0": {"KevinGH\\Version": "src/lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.kevingh.com/"}], "description": "A parsing and comparison library for semantic versioning.", "homepage": "http://github.com/kherge/Version", "abandoned": true, "time": "2012-08-16T17:13:03+00:00"}, {"name": "monolog/monolog", "version": "1.22.1", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "1e044bc4b34e91743943479f1be7a1d5eb93add0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/1e044bc4b34e91743943479f1be7a1d5eb93add0", "reference": "1e044bc4b34e91743943479f1be7a1d5eb93add0", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "jakub-onderka/php-parallel-lint": "0.9", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpunit/phpunit": "~4.5", "phpunit/phpunit-mock-objects": "2.3.0", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "~5.3"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "time": "2017-03-13T07:08:03+00:00"}, {"name": "nikic/php-parser", "version": "v1.4.1", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "f78af2c9c86107aa1a34cd1dbb5bbe9eeb0d9f51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/f78af2c9c86107aa1a34cd1dbb5bbe9eeb0d9f51", "reference": "f78af2c9c86107aa1a34cd1dbb5bbe9eeb0d9f51", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"files": ["lib/bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "time": "2015-09-19T14:15:08+00:00"}, {"name": "phpcollection/phpcollection", "version": "0.5.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-collection.git", "reference": "f2bcff45c0da7c27991bbc1f90f47c4b7fb434a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-collection/zipball/f2bcff45c0da7c27991bbc1f90f47c4b7fb434a6", "reference": "f2bcff45c0da7c27991bbc1f90f47c4b7fb434a6", "shasum": ""}, "require": {"phpoption/phpoption": "1.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.4-dev"}}, "autoload": {"psr-0": {"PhpCollection": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "General-Purpose Collection Library for PHP", "keywords": ["collection", "list", "map", "sequence", "set"], "time": "2015-05-17T12:39:23+00:00"}, {"name": "phpdocumentor/fileset", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/Fileset.git", "reference": "bfa78d8fa9763dfce6d0e5d3730c1d8ab25d34b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/Fileset/zipball/bfa78d8fa9763dfce6d0e5d3730c1d8ab25d34b0", "reference": "bfa78d8fa9763dfce6d0e5d3730c1d8ab25d34b0", "shasum": ""}, "require": {"php": ">=5.3.3", "symfony/finder": "~2.1"}, "require-dev": {"phpunit/phpunit": "~3.7"}, "type": "library", "autoload": {"psr-0": {"phpDocumentor": ["src/", "tests/unit/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Fileset component for collecting a set of files given directories and file paths", "homepage": "http://www.phpdoc.org", "keywords": ["files", "fileset", "phpdoc"], "time": "2013-08-06T21:07:42+00:00"}, {"name": "phpdocumentor/graphviz", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/phpDocumentor/GraphViz.git", "reference": "a906a90a9f230535f25ea31caf81b2323956283f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/GraphViz/zipball/a906a90a9f230535f25ea31caf81b2323956283f", "reference": "a906a90a9f230535f25ea31caf81b2323956283f", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "autoload": {"psr-0": {"phpDocumentor": ["src/", "tests/unit"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2016-02-02T13:00:08+00:00"}, {"name": "phpdocumentor/phpdocumentor", "version": "v2.9.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/phpDocumentor2.git", "reference": "be607da0eef9b9249c43c5b4820d25d631c73667"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/phpDocumentor2/zipball/be607da0eef9b9249c43c5b4820d25d631c73667", "reference": "be607da0eef9b9249c43c5b4820d25d631c73667", "shasum": ""}, "require": {"cilex/cilex": "~1.0", "erusev/parsedown": "~1.0", "herrera-io/phar-update": "1.0.3", "jms/serializer": ">=0.12", "monolog/monolog": "~1.6", "php": ">=5.3.3", "phpdocumentor/fileset": "~1.0", "phpdocumentor/graphviz": "~1.0", "phpdocumentor/reflection": "^3.0", "phpdocumentor/reflection-docblock": "~2.0", "symfony/config": "~2.3", "symfony/console": "~2.3", "symfony/event-dispatcher": "~2.1", "symfony/process": "~2.0", "symfony/stopwatch": "~2.3", "symfony/validator": "~2.2", "twig/twig": "~1.3", "zendframework/zend-cache": "~2.1", "zendframework/zend-config": "~2.1", "zendframework/zend-filter": "~2.1", "zendframework/zend-i18n": "~2.1", "zendframework/zend-serializer": "~2.1", "zendframework/zend-servicemanager": "~2.1", "zendframework/zend-stdlib": "~2.1", "zetacomponents/document": ">=1.3.1"}, "require-dev": {"behat/behat": "~3.0", "mikey179/vfsstream": "~1.2", "mockery/mockery": "~0.9@dev", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.4", "symfony/expression-language": "~2.4"}, "suggest": {"ext-twig": "Enabling the twig extension improves the generation of twig based templates.", "ext-xslcache": "Enabling the XSLCache extension improves the generation of xml based templates."}, "bin": ["bin/phpdoc.php", "bin/phpdoc"], "type": "library", "extra": {"branch-alias": {"dev-develop": "2.9-dev"}}, "autoload": {"psr-0": {"phpDocumentor": ["src/", "tests/unit/"], "Cilex\\Provider": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Documentation Generator for PHP", "homepage": "http://www.phpdoc.org", "keywords": ["api", "application", "dga", "documentation", "phpdoc"], "time": "2016-05-22T09:50:56+00:00"}, {"name": "phpdocumentor/reflection", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/Reflection.git", "reference": "793bfd92d9a0fc96ae9608fb3e947c3f59fb3a0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/Reflection/zipball/793bfd92d9a0fc96ae9608fb3e947c3f59fb3a0d", "reference": "793bfd92d9a0fc96ae9608fb3e947c3f59fb3a0d", "shasum": ""}, "require": {"nikic/php-parser": "^1.0", "php": ">=5.3.3", "phpdocumentor/reflection-docblock": "~2.0", "psr/log": "~1.0"}, "require-dev": {"behat/behat": "~2.4", "mockery/mockery": "~0.8", "phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"phpDocumentor": ["src/", "tests/unit/", "tests/mocks/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Reflection library to do Static Analysis for PHP Projects", "homepage": "http://www.phpdoc.org", "keywords": ["phpDocumentor", "phpdoc", "reflection", "static analysis"], "time": "2016-05-21T08:42:32+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "d68dbdc53dc358a816f00b300704702b2eaff7b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/d68dbdc53dc358a816f00b300704702b2eaff7b8", "reference": "d68dbdc53dc358a816f00b300704702b2eaff7b8", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "suggest": {"dflydev/markdown": "~1.0", "erusev/parsedown": "~1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-0": {"phpDocumentor": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "time": "2015-02-03T12:10:50+00:00"}, {"name": "phpoption/phpoption", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "94e644f7d2051a5f0fcf77d81605f152eecff0ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/94e644f7d2051a5f0fcf77d81605f152eecff0ed", "reference": "94e644f7d2051a5f0fcf77d81605f152eecff0ed", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "4.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-0": {"PhpOption\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache2"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "time": "2015-07-25T16:39:46+00:00"}, {"name": "phpspec/prophecy", "version": "v1.7.0", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "93d39f1f7f9326d746203c7c056f300f7f126073"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/93d39f1f7f9326d746203c7c056f300f7f126073", "reference": "93d39f1f7f9326d746203c7c056f300f7f126073", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2", "sebastian/comparator": "^1.1|^2.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0"}, "require-dev": {"phpspec/phpspec": "^2.5|^3.2", "phpunit/phpunit": "^4.8 || ^5.6.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-0": {"Prophecy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "time": "2017-03-02T20:05:34+00:00"}, {"name": "phpunit/php-code-coverage", "version": "2.2.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "eabf68b476ac7d0f73793aada060f1c1a9bf8979"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/eabf68b476ac7d0f73793aada060f1c1a9bf8979", "reference": "eabf68b476ac7d0f73793aada060f1c1a9bf8979", "shasum": ""}, "require": {"php": ">=5.3.3", "phpunit/php-file-iterator": "~1.3", "phpunit/php-text-template": "~1.2", "phpunit/php-token-stream": "~1.3", "sebastian/environment": "^1.3.2", "sebastian/version": "~1.0"}, "require-dev": {"ext-xdebug": ">=2.1.4", "phpunit/phpunit": "~4"}, "suggest": {"ext-dom": "*", "ext-xdebug": ">=2.2.1", "ext-xmlwriter": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "time": "2015-10-06T15:47:00+00:00"}, {"name": "phpunit/php-file-iterator", "version": "1.4.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "3cc8f69b3028d0f96a9078e6295d86e9bf019be5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/3cc8f69b3028d0f96a9078e6295d86e9bf019be5", "reference": "3cc8f69b3028d0f96a9078e6295d86e9bf019be5", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "time": "2016-10-03T07:40:28+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "1.0.9", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-timer/zipball/3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "reference": "3dcf38ca72b158baf0bc245e9184d3fdffa9c46f", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "time": "2017-02-26T11:10:40+00:00"}, {"name": "phpunit/php-token-stream", "version": "1.4.11", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "e03f8f67534427a787e21a385a67ec3ca6978ea7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/e03f8f67534427a787e21a385a67ec3ca6978ea7", "reference": "e03f8f67534427a787e21a385a67ec3ca6978ea7", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "time": "2017-02-27T10:12:30+00:00"}, {"name": "phpunit/phpunit", "version": "4.8.35", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "791b1a67c25af50e230f841ee7a9c6eba507dc87"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/791b1a67c25af50e230f841ee7a9c6eba507dc87", "reference": "791b1a67c25af50e230f841ee7a9c6eba507dc87", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "php": ">=5.3.3", "phpspec/prophecy": "^1.3.1", "phpunit/php-code-coverage": "~2.1", "phpunit/php-file-iterator": "~1.4", "phpunit/php-text-template": "~1.2", "phpunit/php-timer": "^1.0.6", "phpunit/phpunit-mock-objects": "~2.3", "sebastian/comparator": "~1.2.2", "sebastian/diff": "~1.2", "sebastian/environment": "~1.3", "sebastian/exporter": "~1.2", "sebastian/global-state": "~1.0", "sebastian/version": "~1.0", "symfony/yaml": "~2.1|~3.0"}, "suggest": {"phpunit/php-invoker": "~1.1"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.8.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "time": "2017-02-06T05:18:07+00:00"}, {"name": "phpunit/phpunit-mock-objects", "version": "2.3.8", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects.git", "reference": "ac8e7a3db35738d56ee9a76e78a4e03d97628983"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/phpunit-mock-objects/zipball/ac8e7a3db35738d56ee9a76e78a4e03d97628983", "reference": "ac8e7a3db35738d56ee9a76e78a4e03d97628983", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": ">=5.3.3", "phpunit/php-text-template": "~1.2", "sebastian/exporter": "~1.2"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "suggest": {"ext-soap": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Mock Object library for PHPUnit", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/phpunit-mock-objects/", "keywords": ["mock", "xunit"], "time": "2015-10-02T06:51:40+00:00"}, {"name": "pimple/pimple", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/silexphp/Pimple.git", "reference": "2019c145fe393923f3441b23f29bbdfaa5c58c4d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/silexphp/Pimple/zipball/2019c145fe393923f3441b23f29bbdfaa5c58c4d", "reference": "2019c145fe393923f3441b23f29bbdfaa5c58c4d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-0": {"Pimple": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}], "description": "Pimple is a simple Dependency Injection Container for PHP 5.3", "homepage": "http://pimple.sensiolabs.org", "keywords": ["container", "dependency injection"], "time": "2013-11-22T08:30:29+00:00"}, {"name": "psr/log", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2016-10-10T12:19:37+00:00"}, {"name": "sebastian/comparator", "version": "1.2.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "reference": "2b7424b55f5047b47ac6e5ccb20b2aea4011d9be", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/diff": "~1.2", "sebastian/exporter": "~1.2 || ~2.0"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "http://www.github.com/sebastian<PERSON>mann/comparator", "keywords": ["comparator", "compare", "equality"], "time": "2017-01-29T09:50:25+00:00"}, {"name": "sebastian/diff", "version": "1.4.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "13edfd8706462032c2f52b4b862974dd46b71c9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/diff/zipball/13edfd8706462032c2f52b4b862974dd46b71c9e", "reference": "13edfd8706462032c2f52b4b862974dd46b71c9e", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff"], "time": "2015-12-08T07:14:41+00:00"}, {"name": "sebastian/environment", "version": "1.3.8", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "be2c607e43ce4c89ecd60e75c6a85c126e754aea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/environment/zipball/be2c607e43ce4c89ecd60e75c6a85c126e754aea", "reference": "be2c607e43ce4c89ecd60e75c6a85c126e754aea", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.8 || ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "time": "2016-08-18T05:49:44+00:00"}, {"name": "sebastian/exporter", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "42c4c2eec485ee3e159ec9884f95b431287edde4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/42c4c2eec485ee3e159ec9884f95b431287edde4", "reference": "42c4c2eec485ee3e159ec9884f95b431287edde4", "shasum": ""}, "require": {"php": ">=5.3.3", "sebastian/recursion-context": "~1.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "time": "2016-06-17T09:04:28+00:00"}, {"name": "sebastian/global-state", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/bc37d50fea7d017d3d340f230811c9f1d7280af4", "reference": "bc37d50fea7d017d3d340f230811c9f1d7280af4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.2"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "time": "2015-10-12T03:26:01+00:00"}, {"name": "sebastian/recursion-context", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "b19cc3298482a335a95f3016d2f8a6950f0fbcd7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/b19cc3298482a335a95f3016d2f8a6950f0fbcd7", "reference": "b19cc3298482a335a95f3016d2f8a6950f0fbcd7", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"phpunit/phpunit": "~4.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "time": "2016-10-03T07:41:43+00:00"}, {"name": "sebastian/version", "version": "1.0.6", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "58b3a85e7999757d6ad81c787a1fbf5ff6c628c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/58b3a85e7999757d6ad81c787a1fbf5ff6c628c6", "reference": "58b3a85e7999757d6ad81c787a1fbf5ff6c628c6", "shasum": ""}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "time": "2015-06-21T13:59:46+00:00"}, {"name": "seld/jsonlint", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/Seldaek/jsonlint.git", "reference": "791f8c594f300d246cdf01c6b3e1e19611e301d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/791f8c594f300d246cdf01c6b3e1e19611e301d8", "reference": "791f8c594f300d246cdf01c6b3e1e19611e301d8", "shasum": ""}, "require": {"php": "^5.3 || ^7.0"}, "require-dev": {"phpunit/phpunit": "^4.5"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "time": "2017-03-06T16:42:24+00:00"}, {"name": "symfony/config", "version": "v2.8.18", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "06ce6bb46c24963ec09323da45d0f4f85d3cecd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/06ce6bb46c24963ec09323da45d0f4f85d3cecd2", "reference": "06ce6bb46c24963ec09323da45d0f4f85d3cecd2", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/filesystem": "~2.3|~3.0.0"}, "require-dev": {"symfony/yaml": "~2.7|~3.0.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Config Component", "homepage": "https://symfony.com", "time": "2017-03-01T18:13:50+00:00"}, {"name": "symfony/console", "version": "v2.8.18", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "81508e6fac4476771275a3f4f53c3fee9b956bfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/81508e6fac4476771275a3f4f53c3fee9b956bfa", "reference": "81508e6fac4476771275a3f4f53c3fee9b956bfa", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/debug": "^2.7.2|~3.0.0", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/event-dispatcher": "~2.1|~3.0.0", "symfony/process": "~2.1|~3.0.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/process": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Console Component", "homepage": "https://symfony.com", "time": "2017-03-04T11:00:12+00:00"}, {"name": "symfony/debug", "version": "v2.8.18", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "e90099a2958d4833a02d05b504cc06e1c234abcc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/e90099a2958d4833a02d05b504cc06e1c234abcc", "reference": "e90099a2958d4833a02d05b504cc06e1c234abcc", "shasum": ""}, "require": {"php": ">=5.3.9", "psr/log": "~1.0"}, "conflict": {"symfony/http-kernel": ">=2.3,<2.3.24|~2.4.0|>=2.5,<2.5.9|>=2.6,<2.6.2"}, "require-dev": {"symfony/class-loader": "~2.2|~3.0.0", "symfony/http-kernel": "~2.3.24|~2.5.9|^2.6.2|~3.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Debug Component", "homepage": "https://symfony.com", "time": "2017-02-18T19:13:35+00:00"}, {"name": "symfony/event-dispatcher", "version": "v2.8.18", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "bb4ec47e8e109c1c1172145732d0aa468d967cd0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/bb4ec47e8e109c1c1172145732d0aa468d967cd0", "reference": "bb4ec47e8e109c1c1172145732d0aa468d967cd0", "shasum": ""}, "require": {"php": ">=5.3.9"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^2.0.5|~3.0.0", "symfony/dependency-injection": "~2.6|~3.0.0", "symfony/expression-language": "~2.6|~3.0.0", "symfony/stopwatch": "~2.3|~3.0.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony EventDispatcher Component", "homepage": "https://symfony.com", "time": "2017-02-21T08:33:48+00:00"}, {"name": "symfony/filesystem", "version": "v2.8.18", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "e542d4765092d22552b1bf01ddccfb01d98ee325"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/e542d4765092d22552b1bf01ddccfb01d98ee325", "reference": "e542d4765092d22552b1bf01ddccfb01d98ee325", "shasum": ""}, "require": {"php": ">=5.3.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Filesystem Component", "homepage": "https://symfony.com", "time": "2017-02-18T17:06:33+00:00"}, {"name": "symfony/finder", "version": "v2.8.18", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "5fc4b5cab38b9d28be318fcffd8066988e7d9451"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/5fc4b5cab38b9d28be318fcffd8066988e7d9451", "reference": "5fc4b5cab38b9d28be318fcffd8066988e7d9451", "shasum": ""}, "require": {"php": ">=5.3.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Finder Component", "homepage": "https://symfony.com", "time": "2017-02-21T08:33:48+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "e79d363049d1c2128f133a2667e4f4190904f7f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/e79d363049d1c2128f133a2667e4f4190904f7f4", "reference": "e79d363049d1c2128f133a2667e4f4190904f7f4", "shasum": ""}, "require": {"php": ">=5.3.3"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3-dev"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "time": "2016-11-14T01:06:16+00:00"}, {"name": "symfony/process", "version": "v2.8.18", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "41336b20b52f5fd5b42a227e394e673c8071118f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/41336b20b52f5fd5b42a227e394e673c8071118f", "reference": "41336b20b52f5fd5b42a227e394e673c8071118f", "shasum": ""}, "require": {"php": ">=5.3.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Process Component", "homepage": "https://symfony.com", "time": "2017-03-04T12:20:59+00:00"}, {"name": "symfony/stopwatch", "version": "v2.8.18", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "9e4369666d02ee9b8830da878b7f6a769eb96f4b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/9e4369666d02ee9b8830da878b7f6a769eb96f4b", "reference": "9e4369666d02ee9b8830da878b7f6a769eb96f4b", "shasum": ""}, "require": {"php": ">=5.3.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Stopwatch Component", "homepage": "https://symfony.com", "time": "2017-02-18T17:06:33+00:00"}, {"name": "symfony/translation", "version": "v2.8.18", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "b538355bc99db2ec7cc35284ec76d92ae7d1d256"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/b538355bc99db2ec7cc35284ec76d92ae7d1d256", "reference": "b538355bc99db2ec7cc35284ec76d92ae7d1d256", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/config": "<2.7"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "~2.8", "symfony/intl": "~2.7.25|^2.8.18|~3.2.5", "symfony/yaml": "~2.2|~3.0.0"}, "suggest": {"psr/log": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Translation Component", "homepage": "https://symfony.com", "time": "2017-03-04T12:20:59+00:00"}, {"name": "symfony/validator", "version": "v2.8.18", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "8d4bfa7ec24e70ebc28d0cea5f2702d3f1257a63"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/8d4bfa7ec24e70ebc28d0cea5f2702d3f1257a63", "reference": "8d4bfa7ec24e70ebc28d0cea5f2702d3f1257a63", "shasum": ""}, "require": {"php": ">=5.3.9", "symfony/polyfill-mbstring": "~1.0", "symfony/translation": "~2.4|~3.0.0"}, "require-dev": {"doctrine/annotations": "~1.0", "doctrine/cache": "~1.0", "egulias/email-validator": "^1.2.1", "symfony/config": "~2.2|~3.0.0", "symfony/expression-language": "~2.4|~3.0.0", "symfony/http-foundation": "~2.3|~3.0.0", "symfony/intl": "~2.7.25|^2.8.18|~3.2.5", "symfony/property-access": "~2.3|~3.0.0", "symfony/yaml": "^2.0.5|~3.0.0"}, "suggest": {"doctrine/annotations": "For using the annotation mapping. You will also need doctrine/cache.", "doctrine/cache": "For using the default cached annotation reader and metadata cache.", "egulias/email-validator": "Strict (RFC compliant) email validation", "symfony/config": "", "symfony/expression-language": "For using the 2.4 Expression validator", "symfony/http-foundation": "", "symfony/intl": "", "symfony/property-access": "For using the 2.4 Validator API", "symfony/yaml": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Validator Component", "homepage": "https://symfony.com", "time": "2017-02-28T02:24:56+00:00"}, {"name": "symfony/yaml", "version": "v2.8.18", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "2a7bab3c16f6f452c47818fdd08f3b1e49ffcf7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/2a7bab3c16f6f452c47818fdd08f3b1e49ffcf7d", "reference": "2a7bab3c16f6f452c47818fdd08f3b1e49ffcf7d", "shasum": ""}, "require": {"php": ">=5.3.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.8-dev"}}, "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Yaml Component", "homepage": "https://symfony.com", "time": "2017-03-01T18:13:50+00:00"}, {"name": "twig/twig", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "9935b662e24d6e634da88901ab534cc12e8c728f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/9935b662e24d6e634da88901ab534cc12e8c728f", "reference": "9935b662e24d6e634da88901ab534cc12e8c728f", "shasum": ""}, "require": {"php": ">=5.2.7"}, "require-dev": {"psr/container": "^1.0", "symfony/debug": "~2.7", "symfony/phpunit-bridge": "~3.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.32-dev"}}, "autoload": {"psr-0": {"Twig_": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}, {"name": "Twig Team", "homepage": "http://twig.sensiolabs.org/contributors", "role": "Contributors"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "http://twig.sensiolabs.org", "keywords": ["templating"], "time": "2017-02-27T00:07:03+00:00"}, {"name": "zendframework/zend-cache", "version": "2.5.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-cache.git", "reference": "5999e5a03f7dcf82abbbe67eea74da641f959684"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-cache/zipball/5999e5a03f7dcf82abbbe67eea74da641f959684", "reference": "5999e5a03f7dcf82abbbe67eea74da641f959684", "shasum": ""}, "require": {"php": ">=5.3.23", "zendframework/zend-eventmanager": "~2.5", "zendframework/zend-serializer": "~2.5", "zendframework/zend-servicemanager": "~2.5", "zendframework/zend-stdlib": "~2.5"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "zendframework/zend-session": "~2.5"}, "suggest": {"ext-apc": "APC >= 3.1.6 to use the APC storage adapter", "ext-dba": "DBA, to use the DBA storage adapter", "ext-memcached": "Memcached >= 1.0.0 to use the Memcached storage adapter", "ext-mongo": "Mongo, to use MongoDb storage adapter", "ext-wincache": "WinCache, to use the WinCache storage adapter", "mongofill/mongofill": "Alternative to ext-mongo - a pure PHP implementation designed as a drop in replacement", "zendframework/zend-serializer": "Zend\\Serializer component", "zendframework/zend-session": "Zend\\Session component"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev", "dev-develop": "2.6-dev"}}, "autoload": {"psr-4": {"Zend\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides a generic way to cache any data", "homepage": "https://github.com/zendframework/zend-cache", "keywords": ["cache", "zf2"], "time": "2015-06-03T15:31:59+00:00"}, {"name": "zendframework/zend-config", "version": "2.5.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-config.git", "reference": "ec49b1df1bdd9772df09dc2f612fbfc279bf4c27"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-config/zipball/ec49b1df1bdd9772df09dc2f612fbfc279bf4c27", "reference": "ec49b1df1bdd9772df09dc2f612fbfc279bf4c27", "shasum": ""}, "require": {"php": ">=5.3.23", "zendframework/zend-stdlib": "~2.5"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "zendframework/zend-filter": "~2.5", "zendframework/zend-i18n": "~2.5", "zendframework/zend-json": "~2.5", "zendframework/zend-mvc": "~2.5", "zendframework/zend-servicemanager": "~2.5"}, "suggest": {"zendframework/zend-filter": "Zend\\Filter component", "zendframework/zend-i18n": "Zend\\I18n component", "zendframework/zend-json": "Zend\\Json to use the Json reader or writer classes", "zendframework/zend-servicemanager": "Zend\\ServiceManager for use with the Config Factory to retrieve reader and writer instances"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev", "dev-develop": "2.6-dev"}}, "autoload": {"psr-4": {"Zend\\Config\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides a nested object property based user interface for accessing this configuration data within application code", "homepage": "https://github.com/zendframework/zend-config", "keywords": ["config", "zf2"], "time": "2015-06-03T15:32:00+00:00"}, {"name": "zendframework/zend-eventmanager", "version": "2.5.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-eventmanager.git", "reference": "d94a16039144936f107f906896349900fd634443"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-eventmanager/zipball/d94a16039144936f107f906896349900fd634443", "reference": "d94a16039144936f107f906896349900fd634443", "shasum": ""}, "require": {"php": ">=5.3.23", "zendframework/zend-stdlib": "~2.5"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev", "dev-develop": "2.6-dev"}}, "autoload": {"psr-4": {"Zend\\EventManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "homepage": "https://github.com/zendframework/zend-eventmanager", "keywords": ["eventmanager", "zf2"], "time": "2015-06-03T15:32:01+00:00"}, {"name": "zendframework/zend-filter", "version": "2.5.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-filter.git", "reference": "93e6990a198e6cdd811064083acac4693f4b29ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-filter/zipball/93e6990a198e6cdd811064083acac4693f4b29ae", "reference": "93e6990a198e6cdd811064083acac4693f4b29ae", "shasum": ""}, "require": {"php": ">=5.3.23", "zendframework/zend-stdlib": "~2.5"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "zendframework/zend-config": "~2.5", "zendframework/zend-crypt": "~2.5", "zendframework/zend-i18n": "~2.5", "zendframework/zend-loader": "~2.5", "zendframework/zend-servicemanager": "~2.5", "zendframework/zend-uri": "~2.5"}, "suggest": {"zendframework/zend-crypt": "Zend\\Crypt component", "zendframework/zend-i18n": "Zend\\I18n component", "zendframework/zend-servicemanager": "Zend\\ServiceManager component", "zendframework/zend-uri": "Zend\\Uri component for UriNormalize filter"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev", "dev-develop": "2.6-dev"}}, "autoload": {"psr-4": {"Zend\\Filter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides a set of commonly needed data filters", "homepage": "https://github.com/zendframework/zend-filter", "keywords": ["filter", "zf2"], "time": "2015-06-03T15:32:01+00:00"}, {"name": "zendframework/zend-i18n", "version": "2.5.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-i18n.git", "reference": "509271eb7947e4aabebfc376104179cffea42696"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-i18n/zipball/509271eb7947e4aabebfc376104179cffea42696", "reference": "509271eb7947e4aabebfc376104179cffea42696", "shasum": ""}, "require": {"php": ">=5.3.23", "zendframework/zend-stdlib": "~2.5"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "zendframework/zend-cache": "~2.5", "zendframework/zend-config": "~2.5", "zendframework/zend-eventmanager": "~2.5", "zendframework/zend-filter": "~2.5", "zendframework/zend-servicemanager": "~2.5", "zendframework/zend-validator": "~2.5", "zendframework/zend-view": "~2.5"}, "suggest": {"ext-intl": "Required for most features of Zend\\I18n; included in default builds of PHP", "zendframework/zend-cache": "Zend\\Cache component", "zendframework/zend-config": "Zend\\Config component", "zendframework/zend-eventmanager": "You should install this package to use the events in the translator", "zendframework/zend-filter": "You should install this package to use the provided filters", "zendframework/zend-resources": "Translation resources", "zendframework/zend-servicemanager": "Zend\\ServiceManager component", "zendframework/zend-validator": "You should install this package to use the provided validators", "zendframework/zend-view": "You should install this package to use the provided view helpers"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev", "dev-develop": "2.6-dev"}}, "autoload": {"psr-4": {"Zend\\I18n\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "homepage": "https://github.com/zendframework/zend-i18n", "keywords": ["i18n", "zf2"], "time": "2015-06-03T15:32:01+00:00"}, {"name": "zendframework/zend-json", "version": "2.5.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-json.git", "reference": "c74eaf17d2dd37dc1e964be8dfde05706a821ebc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-json/zipball/c74eaf17d2dd37dc1e964be8dfde05706a821ebc", "reference": "c74eaf17d2dd37dc1e964be8dfde05706a821ebc", "shasum": ""}, "require": {"php": ">=5.3.23", "zendframework/zend-stdlib": "~2.5"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "zendframework/zend-http": "~2.5", "zendframework/zend-server": "~2.5", "zendframework/zendxml": "~1.0"}, "suggest": {"zendframework/zend-http": "Zend\\Http component", "zendframework/zend-server": "Zend\\Server component", "zendframework/zendxml": "To support Zend\\Json\\Json::fromXml() usage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev", "dev-develop": "2.6-dev"}}, "autoload": {"psr-4": {"Zend\\Json\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides convenience methods for serializing native PHP to JSON and decoding JSON to native PHP", "homepage": "https://github.com/zendframework/zend-json", "keywords": ["json", "zf2"], "time": "2015-06-03T15:32:01+00:00"}, {"name": "zendframework/zend-math", "version": "2.5.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-math.git", "reference": "9f02a1ac4d3374d3332c80f9215deec9c71558fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-math/zipball/9f02a1ac4d3374d3332c80f9215deec9c71558fc", "reference": "9f02a1ac4d3374d3332c80f9215deec9c71558fc", "shasum": ""}, "require": {"php": ">=5.3.23"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "ircmaxell/random-lib": "~1.1", "phpunit/phpunit": "~4.0", "zendframework/zend-servicemanager": "~2.5"}, "suggest": {"ext-bcmath": "If using the bcmath functionality", "ext-gmp": "If using the gmp functionality", "ircmaxell/random-lib": "Fallback random byte generator for Zend\\Math\\Rand if OpenSSL/Mcrypt extensions are unavailable", "zendframework/zend-servicemanager": ">= current version, if using the BigInteger::factory functionality"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev", "dev-develop": "2.6-dev"}}, "autoload": {"psr-4": {"Zend\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "homepage": "https://github.com/zendframework/zend-math", "keywords": ["math", "zf2"], "time": "2015-06-03T15:32:02+00:00"}, {"name": "zendframework/zend-serializer", "version": "2.5.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-serializer.git", "reference": "b7208eb17dc4a4fb3a660b85e6c4af035eeed40c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-serializer/zipball/b7208eb17dc4a4fb3a660b85e6c4af035eeed40c", "reference": "b7208eb17dc4a4fb3a660b85e6c4af035eeed40c", "shasum": ""}, "require": {"php": ">=5.3.23", "zendframework/zend-json": "~2.5", "zendframework/zend-math": "~2.5", "zendframework/zend-stdlib": "~2.5"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "zendframework/zend-servicemanager": "~2.5"}, "suggest": {"zendframework/zend-servicemanager": "To support plugin manager support"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev", "dev-develop": "2.6-dev"}}, "autoload": {"psr-4": {"Zend\\Serializer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "provides an adapter based interface to simply generate storable representation of PHP types by different facilities, and recover", "homepage": "https://github.com/zendframework/zend-serializer", "keywords": ["serializer", "zf2"], "time": "2015-06-03T15:32:02+00:00"}, {"name": "zendframework/zend-servicemanager", "version": "2.5.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-servicemanager.git", "reference": "3b22c403e351d92526c642cba0bd810bc22e1c56"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-servicemanager/zipball/3b22c403e351d92526c642cba0bd810bc22e1c56", "reference": "3b22c403e351d92526c642cba0bd810bc22e1c56", "shasum": ""}, "require": {"php": ">=5.3.23"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "zendframework/zend-di": "~2.5", "zendframework/zend-mvc": "~2.5"}, "suggest": {"ocramius/proxy-manager": "ProxyManager 0.5.* to handle lazy initialization of services", "zendframework/zend-di": "Zend\\Di component"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev", "dev-develop": "2.6-dev"}}, "autoload": {"psr-4": {"Zend\\ServiceManager\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "homepage": "https://github.com/zendframework/zend-servicemanager", "keywords": ["servicemanager", "zf2"], "time": "2015-06-03T15:32:02+00:00"}, {"name": "zendframework/zend-stdlib", "version": "2.5.1", "source": {"type": "git", "url": "https://github.com/zendframework/zend-stdlib.git", "reference": "cc8e90a60dd5d44b9730b77d07b97550091da1ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zendframework/zend-stdlib/zipball/cc8e90a60dd5d44b9730b77d07b97550091da1ae", "reference": "cc8e90a60dd5d44b9730b77d07b97550091da1ae", "shasum": ""}, "require": {"php": ">=5.3.23"}, "require-dev": {"fabpot/php-cs-fixer": "1.7.*", "phpunit/phpunit": "~4.0", "zendframework/zend-config": "~2.5", "zendframework/zend-eventmanager": "~2.5", "zendframework/zend-filter": "~2.5", "zendframework/zend-inputfilter": "~2.5", "zendframework/zend-serializer": "~2.5", "zendframework/zend-servicemanager": "~2.5"}, "suggest": {"zendframework/zend-eventmanager": "To support aggregate hydrator usage", "zendframework/zend-filter": "To support naming strategy hydrator usage", "zendframework/zend-serializer": "Zend\\Serializer component", "zendframework/zend-servicemanager": "To support hydrator plugin manager usage"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev", "dev-develop": "2.6-dev"}}, "autoload": {"psr-4": {"Zend\\Stdlib\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "homepage": "https://github.com/zendframework/zend-stdlib", "keywords": ["stdlib", "zf2"], "time": "2015-06-03T15:32:03+00:00"}, {"name": "zetacomponents/base", "version": "1.9", "source": {"type": "git", "url": "https://github.com/zetacomponents/Base.git", "reference": "f20df24e8de3e48b6b69b2503f917e457281e687"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zetacomponents/Base/zipball/f20df24e8de3e48b6b69b2503f917e457281e687", "reference": "f20df24e8de3e48b6b69b2503f917e457281e687", "shasum": ""}, "require-dev": {"zetacomponents/unit-test": "*"}, "type": "library", "autoload": {"classmap": ["src"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}], "description": "The Base package provides the basic infrastructure that all packages rely on. Therefore every component relies on this package.", "homepage": "https://github.com/zetacomponents", "time": "2014-09-19T03:28:34+00:00"}, {"name": "zetacomponents/document", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/zetacomponents/Document.git", "reference": "688abfde573cf3fe0730f82538fbd7aa9fc95bc8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zetacomponents/Document/zipball/688abfde573cf3fe0730f82538fbd7aa9fc95bc8", "reference": "688abfde573cf3fe0730f82538fbd7aa9fc95bc8", "shasum": ""}, "require": {"zetacomponents/base": "*"}, "require-dev": {"zetacomponents/unit-test": "dev-master"}, "type": "library", "autoload": {"classmap": ["src"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}], "description": "The Document components provides a general conversion framework for different semantic document markup languages like XHTML, Docbook, RST and similar.", "homepage": "https://github.com/zetacomponents", "time": "2013-12-19T11:40:00+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=5.0.0"}, "platform-dev": []}